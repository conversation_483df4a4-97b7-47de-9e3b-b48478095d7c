import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { catchError, finalize, forkJoin, of } from 'rxjs';
import { FormeDto } from 'src/app/models/FormeDto';
import { ProduitCard } from 'src/app/models/ProduitCard';
import { ProductListComponent } from 'src/app/pages/product-list/product-list.component';
import { FormeService } from 'src/app/services/forme.service';
import { ProduitService } from 'src/app/services/produit.service';

@Component({
  selector: 'app-products-by-form',
  standalone: true,
  imports: [ProductListComponent],
  providers:[ProduitService, FormeService],
  template: `
    <div class="nom-header">
      <h1>{{ forme?.nom}}</h1>
    </div>
    <app-product-list [produits]="produits"></app-product-list>
  `,
  styleUrl: './products-by-form.component.scss',
})
export class ProductsByFormComponent implements OnInit {
  formeId!: number;
  forme?: FormeDto;
  produits: ProduitCard[] = [];
  loading = true;
  error: string | null = null;
  constructor(
    private route: ActivatedRoute,
    private produitService: ProduitService,
    private formeService: FormeService
  ) {}

  ngOnInit() {
    this.route.params.subscribe((params) => {
      this.formeId = +params['id'];
      this.chargerDonnees();
    });
  }
  chargerDonnees(): void {
    this.loading = true;
    this.error = null;
    
    forkJoin([
      this.produitService.getByForme(this.formeId).pipe(
        catchError(err => {
          console.error('Erreur produits:', err);
          return of([] as ProduitCard[]);
        })
      ),
      this.formeService.getById(this.formeId).pipe(
        catchError(err => {
          console.error('Erreur forme:', err);
          return of({
            id: this.formeId,
            nom: 'Forme inconnue',
            categorieId: 0,
            imageUrl: ''
          } as FormeDto);
        })
      )
    ]).pipe(
      finalize(() => this.loading = false)
    ).subscribe({
      next: ([produits, forme]) => {
        this.produits = produits;
        this.forme = forme;
      },
      error: (err) => {
        console.error(err);
        this.error = 'Erreur lors du chargement des données';
      }
    });
  }
}
