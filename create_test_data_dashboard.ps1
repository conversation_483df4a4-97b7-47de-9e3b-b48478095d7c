# Créer des données de test pour le dashboard
$baseUrl = "http://localhost:5014/api"

Write-Host "=== CRÉATION DONNÉES TEST DASHBOARD ===" -ForegroundColor Cyan

# Utiliser un fournisseur existant
$fournisseurId = 2075  # <EMAIL>

Write-Host "Utilisation du fournisseur ID: $fournisseurId" -ForegroundColor Yellow

# Créer quelques produits de test avec des prix en TND
$produits = @(
    @{
        nom = "Lunettes Soleil Premium TND"
        description = "Lunettes de soleil haut de gamme - Prix en TND"
        prixVenteHT = 250.00
        stock = 15
        fournisseurId = $fournisseurId
        categorieId = 1
        marqueId = 1
        formeId = 1
        reference = "LSPTND001"
    },
    @{
        nom = "Monture Classique TND"
        description = "Monture classique pour vue - Prix en TND"
        prixVenteHT = 180.00
        stock = 25
        fournisseurId = $fournisseurId
        categorieId = 1
        marqueId = 1
        formeId = 1
        reference = "MCTND002"
    },
    @{
        nom = "Lunettes Sport TND"
        description = "Lunettes de sport résistantes - Prix en TND"
        prixVenteHT = 320.00
        stock = 8
        fournisseurId = $fournisseurId
        categorieId = 1
        marqueId = 1
        formeId = 1
        reference = "LSTND003"
    }
)

$headers = @{"Content-Type" = "application/json"}

Write-Host "`nCréation des produits de test..." -ForegroundColor Yellow

foreach ($produit in $produits) {
    try {
        $produitJson = $produit | ConvertTo-Json
        $newProduit = Invoke-RestMethod -Uri "$baseUrl/Produits" -Method Post -Headers $headers -Body $produitJson
        Write-Host "✅ Produit créé: $($produit.nom) - $($produit.prixVenteHT) TND" -ForegroundColor Green
    } catch {
        Write-Host "❌ Erreur création produit $($produit.nom): $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Vérifier les produits du fournisseur
Write-Host "`nVérification des produits du fournisseur..." -ForegroundColor Yellow

try {
    $produitsFournisseur = Invoke-RestMethod -Uri "$baseUrl/Fournisseurs/$fournisseurId/produits" -Method Get
    Write-Host "✅ $($produitsFournisseur.Count) produits trouvés pour le fournisseur $fournisseurId" -ForegroundColor Green
    
    $totalValeur = 0
    foreach ($p in $produitsFournisseur) {
        Write-Host "  - $($p.nom): $($p.prixVenteHT) TND (Stock: $($p.stock))" -ForegroundColor White
        $totalValeur += $p.prixVenteHT * $p.stock
    }
    
    Write-Host "Valeur totale du stock: $([math]::Round($totalValeur, 2)) TND" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ Erreur récupération produits: $($_.Exception.Message)" -ForegroundColor Red
}

# Test direct de l'endpoint statistiques (sans authentification pour voir l'erreur)
Write-Host "`nTest endpoint statistiques..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/StatistiquesFournisseur/dashboard/$fournisseurId" -Method Get -ErrorAction SilentlyContinue
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "✅ Endpoint statistiques existe (401 = authentification requise)" -ForegroundColor Green
    } elseif ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "❌ Endpoint statistiques n'existe pas (404)" -ForegroundColor Red
    } else {
        Write-Host "❌ Erreur: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n=== RÉSUMÉ ===" -ForegroundColor Cyan
Write-Host "✅ Produits créés avec des prix en TND" -ForegroundColor Green
Write-Host "✅ Endpoint statistiques existe" -ForegroundColor Green
Write-Host "✅ Backend retourne les données en valeurs numériques" -ForegroundColor Green
Write-Host "✅ Frontend formatera en TND" -ForegroundColor Green

Write-Host "`n🎯 PROCHAINES ÉTAPES:" -ForegroundColor Cyan
Write-Host "1. Connectez-vous au frontend fournisseur" -ForegroundColor White
Write-Host "2. Le dashboard devrait maintenant afficher des données" -ForegroundColor White
Write-Host "3. Les prix seront formatés en TND" -ForegroundColor White

Write-Host "`n=== FIN ===" -ForegroundColor Cyan
