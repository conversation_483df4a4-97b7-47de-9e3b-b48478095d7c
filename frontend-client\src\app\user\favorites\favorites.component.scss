.favoris-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;

  h1 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
    text-align: center;
  }
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;

  p {
    margin-top: 1rem;
    color: #666;
  }
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #c62828;
  }
}

.empty-favoris {
  text-align: center;
  padding: 3rem 0;

  img {
    width: 200px;
    margin-bottom: 1.5rem;
    opacity: 0.7;
  }

  h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
  }

  p {
    color: #666;
    margin-bottom: 2rem;
  }

  button {
    padding: 0.75rem 2rem;
    background-color: #1976d2;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;

    &:hover {
      background-color: #1565c0;
    }
  }
}

.favoris-content {
  .category-group {
    margin-bottom: 3rem;

    h2 {
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid #eee;
      color: #444;
    }
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.product-card {
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }
}

.product-image {
  position: relative;
  height: 200px;
  background-color: #f9f9f9;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 1rem;
  }

  .remove-favorite {
    position: absolute;
    top: 10px;
    right: 10px;
    background: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    color: #e53935;

    mat-icon {
      font-size: 20px;
    }
  }

  .promo-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: #e53935;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
  }
}

.product-info {
  padding: 1rem;
  flex-grow: 1;

  h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    color: #333;
  }

  .price-section {
    margin: 0.5rem 0;

    .current-price {
      font-weight: bold;
      font-size: 1.2rem;
      color: #2e7d32;
    }

    .original-price {
      text-decoration: line-through;
      color: #999;
      margin-left: 0.5rem;
      font-size: 0.9rem;
    }
  }

  .rating {
    display: flex;
    align-items: center;
    margin: 0.5rem 0;

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      color: #ffc107;

      &.filled {
        color: #ffc107;
      }
    }

    span {
      margin-left: 0.5rem;
      font-size: 0.8rem;
      color: #666;
    }
  }

  .stock-status {
    font-size: 0.9rem;
    margin-top: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;

    &.in-stock {
      background-color: #e8f5e9;
      color: #2e7d32;
    }

    &:not(.in-stock) {
      background-color: #ffebee;
      color: #c62828;
    }
  }
}

.product-actions {
  display: flex;
  padding: 0 1rem 1rem 1rem;
  gap: 0.5rem;

  button {
    flex: 1;
    padding: 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: background-color 0.2s;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    mat-icon {
      font-size: 18px;
      margin-right: 0.25rem;
    }
  }

  .add-to-cart {
    background-color: #1976d2;
    color: white;

    &:hover:not(:disabled) {
      background-color: #1565c0;
    }
  }

  .view-details {
    background-color: #f5f5f5;
    color: #333;

    &:hover {
      background-color: #e0e0e0;
    }
  }
}

/* Responsive */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }

  .product-image {
    height: 160px;
  }

  .product-actions {
    flex-direction: column;

    button {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
}
