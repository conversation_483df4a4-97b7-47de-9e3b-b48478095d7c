import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { catchError, finalize, of, Subject, takeUntil } from 'rxjs';
import { FournisseurDto } from 'src/app/models/FournisseurDto';
import { FournisseurService } from 'src/app/services/fournisseur.service';
import { ImageUrlService } from 'src/app/services/image-url.service';

@Component({
  selector: 'app-fournisseurs-all',
  standalone: false,
  templateUrl: './fournisseurs-all.component.html',
  styleUrl: './fournisseurs-all.component.scss',
})
export class FournisseursAllComponent implements OnInit, OnDestroy {
  fournisseurs: FournisseurDto[] = [];
  loading = false;
  error: string | null = null;
  private destroy$ = new Subject<void>();
  constructor(
    private router: Router,
    private fournisseurService: FournisseurService,
    public imageUrlService: ImageUrlService
  ) {}

  ngOnInit(): void {
    this.loadAllFournisseurs();
  }

  private loadAllFournisseurs(): void {
    this.loading = true;
    this.error = null;

    this.fournisseurService
      .getAll()
      .pipe(
        takeUntil(this.destroy$),
        catchError((err) => {
          this.error = 'Erreur lors du chargement des fournisseurs';
          console.error(err);
          return of([]);
        }),
        finalize(() => (this.loading = false))
      )
      .subscribe((fournisseurs) => {
        this.fournisseurs = fournisseurs;
      });
  }
  navigateToFournisseur(fournisseurId: number): void {
    if (!fournisseurId) {
      console.error('Fournisseur ID is missing');
      return;
    }

    this.router.navigate(['/products/fournisseur', fournisseurId]);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
