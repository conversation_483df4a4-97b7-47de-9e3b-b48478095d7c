using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DiagnosticController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly ILogger<DiagnosticController> _logger;

        public DiagnosticController(AppDbContext context, ILogger<DiagnosticController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpGet("produits-sans-fournisseur")]
        public async Task<IActionResult> GetProduitsSansFournisseur()
        {
            try
            {
                var produitsSansFournisseur = await _context.Produits
                    .Where(p => p.FournisseurId <= 0)
                    .Select(p => new
                    {
                        Id = p.Id,
                        Nom = p.Nom,
                        FournisseurId = p.FournisseurId
                    })
                    .ToListAsync();

                var produitsAvecFournisseur = await _context.Produits
                    .Where(p => p.FournisseurId > 0)
                    .Include(p => p.Fournisseur)
                    .Select(p => new
                    {
                        Id = p.Id,
                        Nom = p.Nom,
                        FournisseurId = p.FournisseurId,
                        FournisseurNom = p.Fournisseur.RaisonSociale
                    })
                    .Take(10)
                    .ToListAsync();

                return Ok(new
                {
                    ProduitsSansFournisseur = produitsSansFournisseur,
                    ExempleProduitsAvecFournisseur = produitsAvecFournisseur,
                    TotalProduits = await _context.Produits.CountAsync(),
                    ProduitsAvecFournisseur = await _context.Produits.CountAsync(p => p.FournisseurId > 0),
                    ProduitsSansFournisseurCount = produitsSansFournisseur.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        [HttpGet("commandes-fournisseurs")]
        public async Task<IActionResult> GetCommandesFournisseurs()
        {
            try
            {
                var commandesFournisseurs = await _context.CommandesFournisseurs
                    .Include(cf => cf.Fournisseur)
                    .Include(cf => cf.LignesCommande)
                        .ThenInclude(l => l.Produit)
                    .Select(cf => new
                    {
                        Id = cf.Id,
                        CommandeClientId = cf.CommandeClientId,
                        FournisseurId = cf.FournisseurId,
                        FournisseurNom = cf.Fournisseur.RaisonSociale,
                        Reference = cf.Reference,
                        MontantTotal = cf.MontantTotal,
                        NombreLignes = cf.LignesCommande.Count(),
                        DateCreation = cf.DateCreation
                    })
                    .OrderByDescending(cf => cf.DateCreation)
                    .ToListAsync();

                return Ok(new
                {
                    CommandesFournisseurs = commandesFournisseurs,
                    Total = commandesFournisseurs.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        [HttpPost("fix-produits-fournisseurs")]
        public async Task<IActionResult> FixProduitsFournisseurs()
        {
            try
            {
                // Assigner un fournisseur par défaut aux produits sans fournisseur
                var premierFournisseur = await _context.Fournisseurs.FirstOrDefaultAsync();
                if (premierFournisseur == null)
                {
                    return BadRequest(new { Error = "Aucun fournisseur trouvé dans la base" });
                }

                var produitsSansFournisseur = await _context.Produits
                    .Where(p => p.FournisseurId <= 0)
                    .ToListAsync();

                foreach (var produit in produitsSansFournisseur)
                {
                    produit.FournisseurId = premierFournisseur.Id;
                }

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    Message = $"{produitsSansFournisseur.Count} produits mis à jour avec le fournisseur {premierFournisseur.RaisonSociale}",
                    FournisseurId = premierFournisseur.Id,
                    ProduitsModifies = produitsSansFournisseur.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message });
            }
        }
    }
}
