# Créer un nouveau fournisseur et tester les promotions
$baseUrl = "http://localhost:5014/api"

Write-Host "=== CRÉATION NOUVEAU FOURNISSEUR ET TEST PROMOTIONS ===" -ForegroundColor Cyan

# 1. Créer un nouveau fournisseur avec un mot de passe connu
Write-Host "`n1. Création d'un nouveau fournisseur..." -ForegroundColor Yellow

$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$email = "testpromo$<EMAIL>"

$fournisseurData = @{
    nom = "Test"
    prenom = "Promotion"
    email = $email
    password = "TestPromo123!"
    phoneNumber = "12345678"
    raisonSociale = "Test Promotion SARL"
    matriculeFiscale = "123456789$timestamp"
    rib = "12345678901234567890"
    codeBanque = "12345"
    commission = 5.0
    delaiPreparationJours = 2
    fraisLivraisonBase = 10.0
    description = "Fournisseur de test pour les promotions - $timestamp"
} | ConvertTo-Json

$headers = @{"Content-Type" = "application/json"}

try {
    $newFournisseur = Invoke-RestMethod -Uri "$baseUrl/Auth/register/fournisseur" -Method Post -Headers $headers -Body $fournisseurData
    Write-Host "✅ Fournisseur créé !" -ForegroundColor Green
    Write-Host "ID: $($newFournisseur.id) - Email: $email" -ForegroundColor White
    Write-Host "Nom: $($newFournisseur.nom) $($newFournisseur.prenom)" -ForegroundColor White
    
} catch {
    Write-Host "❌ Erreur création fournisseur: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
    exit 1
}

# 2. Connexion du nouveau fournisseur
Write-Host "`n2. Connexion du nouveau fournisseur..." -ForegroundColor Yellow

$loginData = @{
    email = $email
    motDePasse = "TestPromo123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
    Write-Host "✅ Connexion réussie !" -ForegroundColor Green
    Write-Host "Fournisseur: $($loginResponse.utilisateur.nom) $($loginResponse.utilisateur.prenom)" -ForegroundColor White
    Write-Host "Role: $($loginResponse.utilisateur.role)" -ForegroundColor White
    Write-Host "ID: $($loginResponse.utilisateur.id)" -ForegroundColor White
    
    # Ajouter le token aux headers
    $headers["Authorization"] = "Bearer $($loginResponse.token)"
    
} catch {
    Write-Host "❌ Erreur de connexion: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
    exit 1
}

# 3. Test de l'endpoint my-promotions (devrait être vide)
Write-Host "`n3. Test de l'endpoint my-promotions..." -ForegroundColor Yellow

try {
    $myPromotions = Invoke-RestMethod -Uri "$baseUrl/promotions/my-promotions" -Method Get -Headers $headers
    Write-Host "✅ My-promotions récupérées: $($myPromotions.Count)" -ForegroundColor Green
    
    if ($myPromotions.Count -gt 0) {
        foreach ($promo in $myPromotions) {
            $status = if ($promo.estValide) { "✅ Active" } else { "❌ Inactive" }
            Write-Host "  - $($promo.nomAffichage) ($($promo.type)) - $($promo.pourcentageRemise)% - $status" -ForegroundColor White
        }
    } else {
        Write-Host "Aucune promotion trouvée (normal pour un nouveau fournisseur)" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Erreur my-promotions: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 4. Créer plusieurs promotions de test
Write-Host "`n4. Création de promotions de test..." -ForegroundColor Yellow

$promotions = @(
    @{
        type = "CodePromo"
        pourcentageRemise = 20
        dateDebut = (Get-Date).ToString("yyyy-MM-dd")
        dateFin = (Get-Date).AddDays(30).ToString("yyyy-MM-dd")
        codePromo = "PROMO20"
        nomAffichage = "Promotion Code 20%"
        description = "Promotion avec code promo de 20%"
        appliquerSurHT = $false
    },
    @{
        type = "Automatique"
        pourcentageRemise = 15
        dateDebut = (Get-Date).ToString("yyyy-MM-dd")
        dateFin = (Get-Date).AddDays(15).ToString("yyyy-MM-dd")
        nomAffichage = "Promotion Automatique 15%"
        description = "Promotion automatique de 15%"
        appliquerSurHT = $true
    },
    @{
        type = "Outlet"
        pourcentageRemise = 30
        dateDebut = (Get-Date).ToString("yyyy-MM-dd")
        dateFin = (Get-Date).AddDays(7).ToString("yyyy-MM-dd")
        nomAffichage = "Outlet 30%"
        description = "Promotion outlet de 30%"
        appliquerSurHT = $false
    }
)

foreach ($promo in $promotions) {
    $promoJson = $promo | ConvertTo-Json
    
    try {
        $newPromo = Invoke-RestMethod -Uri "$baseUrl/promotions" -Method Post -Headers $headers -Body $promoJson
        Write-Host "✅ Promotion créée: $($newPromo.nomAffichage)" -ForegroundColor Green
        Write-Host "  ID: $($newPromo.id) | Type: $($newPromo.type) | Remise: $($newPromo.pourcentageRemise)%" -ForegroundColor White
        if ($newPromo.codePromo) {
            Write-Host "  Code: $($newPromo.codePromo)" -ForegroundColor Gray
        }
        
    } catch {
        Write-Host "❌ Erreur création promotion $($promo.nomAffichage): $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

# 5. Vérifier les promotions créées
Write-Host "`n5. Vérification finale des promotions..." -ForegroundColor Yellow

try {
    $finalPromotions = Invoke-RestMethod -Uri "$baseUrl/promotions/my-promotions" -Method Get -Headers $headers
    Write-Host "✅ Promotions finales: $($finalPromotions.Count)" -ForegroundColor Green
    
    foreach ($promo in $finalPromotions) {
        $status = if ($promo.estValide) { "✅ Active" } else { "❌ Inactive" }
        Write-Host "  - $($promo.nomAffichage) ($($promo.type)) - $($promo.pourcentageRemise)% - $status" -ForegroundColor White
        if ($promo.codePromo) {
            Write-Host "    Code: $($promo.codePromo)" -ForegroundColor Gray
        }
        Write-Host "    Du $($promo.dateDebut.Substring(0,10)) au $($promo.dateFin.Substring(0,10))" -ForegroundColor Gray
        Write-Host "    Utilisations: $($promo.nombreUtilisations)/$($promo.limitUtilisation)" -ForegroundColor Gray
    }
    
} catch {
    Write-Host "❌ Erreur vérification finale: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== RÉSUMÉ ===" -ForegroundColor Cyan
Write-Host "Email du fournisseur: $email" -ForegroundColor White
Write-Host "Mot de passe: TestPromo123!" -ForegroundColor White
Write-Host "Vous pouvez maintenant tester le frontend fournisseur avec ces identifiants." -ForegroundColor Green

Write-Host "`n=== FIN DU TEST ===" -ForegroundColor Cyan
