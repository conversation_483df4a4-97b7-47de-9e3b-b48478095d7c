import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import {
  Promotion,
  FiltrePromotion,
  StatistiquesPromotion,
  TypePromotion,
  StatutPromotion,
  TemplatePromotion
} from '../../../models/promotion.model';
import { PromotionService } from '../../../services/promotion.service';
import { AuthService } from '../../../services/auth.service';

@Component({
  selector: 'app-promotion-management',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './promotion-management.component.html',
  styleUrls: ['./promotion-management.component.css']
})
export class PromotionManagementComponent implements OnInit {
  // Données principales
  promotions: Promotion[] = [];
  statistiques: StatistiquesPromotion | null = null;
  templates: TemplatePromotion[] = [];

  // États de l'interface
  isLoading = false;
  error = '';
  viewMode: 'list' | 'grid' | 'stats' = 'list';
  
  // Filtres et recherche
  filtre: FiltrePromotion = {};
  searchQuery = '';
  selectedType: TypePromotion | 'all' = 'all';
  selectedStatut: StatutPromotion | 'all' = 'all';
  
  // Sélection et actions en lot
  selectedPromotions: Set<number> = new Set();
  showBulkActions = false;
  
  // Modal de confirmation
  showDeleteModal = false;
  promotionToDelete: Promotion | null = null;
  
  // Modal de templates
  showTemplateModal = false;
  
  // Tri
  sortField: keyof Promotion = 'nomPromotion';
  sortDirection: 'asc' | 'desc' = 'asc';

  // Types et statuts pour les filtres
  typesPromotion: { value: TypePromotion | 'all', label: string }[] = [
    { value: 'all', label: 'Tous les types' },
    { value: 'automatique', label: 'Automatiques' },
    { value: 'code_promo', label: 'Codes promo' },
    { value: 'outlet', label: 'Outlet' }
  ];

  statutsPromotion: { value: StatutPromotion | 'all', label: string }[] = [
    { value: 'all', label: 'Tous les statuts' },
    { value: 'active', label: 'Actives' },
    { value: 'inactive', label: 'Inactives' },
    { value: 'brouillon', label: 'Brouillons' },
    { value: 'expiree', label: 'Expirées' },
    { value: 'suspendue', label: 'Suspendues' }
  ];

  constructor(
    private promotionService: PromotionService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadData();
    this.loadTemplates();
  }

  /**
   * Charger les données depuis l'API
   */
  loadData(): void {
    this.isLoading = true;
    this.error = '';

    const fournisseurId = this.authService.getCurrentUserId();
    if (!fournisseurId) {
      this.error = 'Utilisateur non connecté';
      this.isLoading = false;
      return;
    }

    // Charger les promotions
    this.promotionService.getPromotionsByFournisseur(fournisseurId, this.getActiveFilters()).subscribe({
      next: (promotions) => {
        this.promotions = this.sortPromotions(promotions);
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Erreur lors du chargement des promotions';
        console.error('Error loading promotions:', error);
        this.isLoading = false;
      }
    });

    // Charger les statistiques
    this.promotionService.getStatistiques(fournisseurId).subscribe({
      next: (stats) => {
        this.statistiques = stats;
      },
      error: (error) => {
        console.error('Error loading statistics:', error);
      }
    });
  }

  /**
   * Charger les templates de promotion
   */
  loadTemplates(): void {
    this.promotionService.getTemplatesPromotion().subscribe({
      next: (templates) => {
        this.templates = templates;
      },
      error: (error) => {
        console.error('Error loading templates:', error);
      }
    });
  }

  /**
   * Obtenir les filtres actifs
   */
  getActiveFilters(): FiltrePromotion {
    const filtres: FiltrePromotion = {};
    
    if (this.searchQuery.trim()) {
      filtres.recherche = this.searchQuery.trim();
    }
    
    if (this.selectedType !== 'all') {
      filtres.type = this.selectedType;
    }
    
    if (this.selectedStatut !== 'all') {
      filtres.statut = this.selectedStatut;
    }
    
    return filtres;
  }

  /**
   * Appliquer les filtres
   */
  applyFilters(): void {
    this.loadData();
  }

  /**
   * Réinitialiser les filtres
   */
  resetFilters(): void {
    this.searchQuery = '';
    this.selectedType = 'all';
    this.selectedStatut = 'all';
    this.filtre = {};
    this.loadData();
  }

  /**
   * Trier les promotions
   */
  sortPromotions(promotions: Promotion[]): Promotion[] {
    return [...promotions].sort((a, b) => {
      const aValue = a[this.sortField];
      const bValue = b[this.sortField];

      let comparison = 0;
      if (aValue != null && bValue != null) {
        if (aValue < bValue) comparison = -1;
        if (aValue > bValue) comparison = 1;
      }

      return this.sortDirection === 'desc' ? -comparison : comparison;
    });
  }

  /**
   * Changer le tri
   */
  changeSortField(field: keyof Promotion): void {
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      this.sortDirection = 'desc';
    }
    this.promotions = this.sortPromotions(this.promotions);
  }

  /**
   * Obtenir l'icône de tri
   */
  getSortIcon(field: keyof Promotion): string {
    if (this.sortField !== field) return '↕️';
    return this.sortDirection === 'asc' ? '↑' : '↓';
  }

  // ==================== ACTIONS SUR LES PROMOTIONS ====================

  /**
   * Créer une nouvelle promotion
   */
  createPromotion(): void {
    this.router.navigate(['/dashboard/promotions/create']);
  }

  /**
   * Créer une promotion à partir d'un template
   */
  createFromTemplate(template: TemplatePromotion): void {
    this.router.navigate(['/dashboard/promotions/create'], {
      queryParams: { template: template.id }
    });
    this.showTemplateModal = false;
  }

  /**
   * Modifier une promotion
   */
  editPromotion(promotion: Promotion): void {
    this.router.navigate(['/dashboard/promotions/edit', promotion.id]);
  }

  /**
   * Voir les détails d'une promotion
   */
  viewPromotion(promotion: Promotion): void {
    this.router.navigate(['/dashboard/promotions/view', promotion.id]);
  }

  /**
   * Dupliquer une promotion
   */
  duplicatePromotion(promotion: Promotion): void {
    this.router.navigate(['/dashboard/promotions/create'], {
      queryParams: { duplicate: promotion.id }
    });
  }

  /**
   * Activer/Désactiver une promotion
   */
  togglePromotionStatus(promotion: Promotion): void {
    this.promotionService.togglePromotionStatus(promotion.id).subscribe({
      next: () => {
        this.loadData();
        this.showSuccessMessage(
          `Promotion ${promotion.statut === 'active' ? 'désactivée' : 'activée'} avec succès`
        );
      },
      error: (error) => {
        console.error('Error toggling promotion status:', error);
        this.showErrorMessage('Erreur lors du changement de statut');
      }
    });
  }

  /**
   * Confirmer la suppression d'une promotion
   */
  confirmDeletePromotion(promotion: Promotion): void {
    this.promotionToDelete = promotion;
    this.showDeleteModal = true;
  }

  /**
   * Supprimer une promotion
   */
  deletePromotion(): void {
    if (!this.promotionToDelete) return;

    this.promotionService.deletePromotion(this.promotionToDelete.id).subscribe({
      next: () => {
        this.loadData();
        this.showDeleteModal = false;
        this.promotionToDelete = null;
        this.showSuccessMessage('Promotion supprimée avec succès');
      },
      error: (error) => {
        console.error('Error deleting promotion:', error);
        this.showErrorMessage('Erreur lors de la suppression');
      }
    });
  }

  /**
   * Annuler la suppression
   */
  cancelDelete(): void {
    this.showDeleteModal = false;
    this.promotionToDelete = null;
  }

  // ==================== SÉLECTION ET ACTIONS EN LOT ====================

  /**
   * Sélectionner/Désélectionner une promotion
   */
  togglePromotionSelection(promotionId: number): void {
    if (this.selectedPromotions.has(promotionId)) {
      this.selectedPromotions.delete(promotionId);
    } else {
      this.selectedPromotions.add(promotionId);
    }
    this.showBulkActions = this.selectedPromotions.size > 0;
  }

  /**
   * Sélectionner toutes les promotions
   */
  selectAllPromotions(): void {
    if (this.selectedPromotions.size === this.promotions.length) {
      this.selectedPromotions.clear();
    } else {
      this.promotions.forEach(p => this.selectedPromotions.add(p.id));
    }
    this.showBulkActions = this.selectedPromotions.size > 0;
  }

  /**
   * Vérifier si une promotion est sélectionnée
   */
  isPromotionSelected(promotionId: number): boolean {
    return this.selectedPromotions.has(promotionId);
  }

  /**
   * Actions en lot - Activer
   */
  bulkActivatePromotions(): void {
    // Implémentation des actions en lot
    console.log('Activation en lot:', Array.from(this.selectedPromotions));
  }

  /**
   * Actions en lot - Désactiver
   */
  bulkDeactivatePromotions(): void {
    // Implémentation des actions en lot
    console.log('Désactivation en lot:', Array.from(this.selectedPromotions));
  }

  /**
   * Actions en lot - Supprimer
   */
  bulkDeletePromotions(): void {
    // Implémentation des actions en lot
    console.log('Suppression en lot:', Array.from(this.selectedPromotions));
  }

  // ==================== UTILITAIRES ====================

  /**
   * Obtenir la classe CSS pour le statut
   */
  getStatusClass(statut: StatutPromotion): string {
    const classes = {
      'active': 'status-active',
      'inactive': 'status-inactive',
      'brouillon': 'status-draft',
      'expiree': 'status-expired',
      'suspendue': 'status-suspended'
    };
    return classes[statut] || 'status-default';
  }

  /**
   * Obtenir l'icône pour le type de promotion
   */
  getTypeIcon(type: TypePromotion): string {
    const icons = {
      'automatique': '⚡',
      'code_promo': '🎫',
      'outlet': '🏷️'
    };
    return icons[type] || '📋';
  }

  /**
   * Formater le prix
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  }

  /**
   * Formater la date
   */
  formatDate(date: Date): string {
    return new Intl.DateTimeFormat('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(new Date(date));
  }

  /**
   * Vérifier si une promotion est expirée
   */
  isExpired(promotion: Promotion): boolean {
    return new Date() > new Date(promotion.dateFin);
  }

  /**
   * Vérifier si une promotion expire bientôt
   */
  isExpiringSoon(promotion: Promotion): boolean {
    const now = new Date();
    const dateFin = new Date(promotion.dateFin);
    const diffDays = Math.ceil((dateFin.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return diffDays <= 7 && diffDays > 0;
  }

  /**
   * Afficher un message de succès
   */
  private showSuccessMessage(message: string): void {
    // Pour l'instant, utiliser alert. Dans une vraie app, utiliser un service de notification
    alert(message);
  }

  /**
   * Afficher un message d'erreur
   */
  private showErrorMessage(message: string): void {
    // Pour l'instant, utiliser alert. Dans une vraie app, utiliser un service de notification
    alert(message);
  }

  /**
   * Obtenir le pourcentage d'utilisation d'une promotion
   */
  getUsagePercentage(promotion: Promotion): number {
    if (!promotion.utilisationMax) return 0;
    return Math.round((promotion.utilisationActuelle / promotion.utilisationMax) * 100);
  }

  /**
   * Obtenir le texte de la réduction
   */
  getReductionText(promotion: Promotion): string {
    switch (promotion.typeReduction) {
      case 'pourcentage':
        return `-${promotion.valeurReduction}%`;
      case 'montant_fixe':
        return `-${this.formatPrice(promotion.valeurReduction)}`;
      case 'prix_fixe':
        return `${this.formatPrice(promotion.valeurReduction)}`;
      default:
        return '';
    }
  }
}
