{"name": "@angular/common", "version": "19.2.14", "description": "Angular - commonly needed directives and services", "author": "angular", "license": "MIT", "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0"}, "locales": "locales", "dependencies": {"tslib": "^2.3.0"}, "exports": {"./locales/global/*": {"default": "./locales/global/*.js"}, "./locales/*": {"types": "./locales/*.d.ts", "default": "./locales/*.js"}, "./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/common.mjs"}, "./http": {"types": "./http/index.d.ts", "default": "./fesm2022/http.mjs"}, "./http/testing": {"types": "./http/testing/index.d.ts", "default": "./fesm2022/http/testing.mjs"}, "./testing": {"types": "./testing/index.d.ts", "default": "./fesm2022/testing.mjs"}, "./upgrade": {"types": "./upgrade/index.d.ts", "default": "./fesm2022/upgrade.mjs"}}, "peerDependencies": {"@angular/core": "19.2.14", "rxjs": "^6.5.3 || ^7.4.0"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/common"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": ["**/global/*.js", "**/closure-locale.*"], "module": "./fesm2022/common.mjs", "typings": "./index.d.ts", "type": "module"}