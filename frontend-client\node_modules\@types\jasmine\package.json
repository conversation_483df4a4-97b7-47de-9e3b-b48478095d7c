{"name": "@types/jasmine", "version": "5.1.8", "description": "TypeScript definitions for jasmine", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jasmine", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/theodorejb"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/david<PERSON>sson"}, {"name": "<PERSON><PERSON>", "githubUsername": "lukas-zech-software", "url": "https://github.com/lukas-zech-software"}, {"name": "<PERSON>", "githubUsername": "Engineer2B", "url": "https://github.com/Engineer2B"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/cyungmann"}, {"name": "Giles Roadnight", "githubUsername": "Roaders", "url": "https://github.com/Roaders"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "devoto13", "url": "https://github.com/devoto13"}, {"name": "<PERSON><PERSON>", "githubUsername": "fdim", "url": "https://github.com/fdim"}, {"name": "<PERSON><PERSON>", "githubUsername": "kolodny", "url": "https://github.com/kolodny"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/stephenfarrar"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/djungo<PERSON>"}, {"name": "Chives", "githubUsername": "chivesrs", "url": "https://github.com/chivesrs"}, {"name": "kirjs", "githubUsername": "kirjs", "url": "https://github.com/kirjs"}, {"name": "<PERSON>", "githubUsername": "Semigradsky", "url": "https://github.com/Semigradsky"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/jasmine"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "47cadde4181ae34efb40577227fe6c712e1dcd4573cfa488b455cdf513827291", "typeScriptVersion": "5.1"}