﻿using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.CreateDTOs
{
    public class AddItemPanierDto
    {
        [Required(ErrorMessage = "L'ID du produit est obligatoire")]
        public int ProduitId { get; set; }

        [Required(ErrorMessage = "La quantité est obligatoire")]
        [Range(1, int.MaxValue, ErrorMessage = "La quantité doit être au moins 1")]
        public int Quantite { get; set; }
    }
}
