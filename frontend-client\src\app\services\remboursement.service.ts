import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { RemboursementRequest } from '../models/RemboursementRequest';
import { BaseService } from './core/base.service';

@Injectable({ providedIn: 'root' })
export class RemboursementService extends BaseService<RemboursementRequest> {
  constructor(http: HttpClient) {
    super(http, 'remboursements');
  }

  requestRefund(paiementId: number, raison: string): Observable<RemboursementRequest> {
    return this.http.post<RemboursementRequest>(
      `${this.apiUrl}/${this.endpoint}/request`,
      { paiementId, raison }
    );
  }

  getByCommande(commandeId: number): Observable<RemboursementRequest[]> {
    return this.http.get<RemboursementRequest[]>(
      `${this.apiUrl}/${this.endpoint}/by-commande/${commandeId}`
    );
  }
}
