// Modèles pour la gestion des promotions

export type TypePromotion = 'outlet' | 'automatique' | 'code_promo';
export type TypeReduction = 'pourcentage' | 'montant_fixe' | 'prix_fixe';
export type StatutPromotion = 'brouillon' | 'active' | 'inactive' | 'expiree' | 'suspendue';
export type TypeCondition = 'montant_minimum' | 'quantite_minimum' | 'produit_specifique' | 'categorie' | 'premiere_commande' | 'client_fidele';

export interface Promotion {
  id: number;
  nomPromotion: string;
  description: string;
  type: TypePromotion;
  typeReduction: TypeReduction;
  valeurReduction: number;
  statut: StatutPromotion;
  
  // Dates de validité
  dateDebut: Date;
  dateFin: Date;
  
  // Code promo (pour type code_promo)
  codePromo?: string;
  utilisationMax?: number;
  utilisationActuelle: number;
  utilisationParClient?: number;
  
  // Conditions d'application
  conditions: ConditionPromotion[];
  
  // Produits concernés
  produitsConcernes: ProduitPromotion[];
  categoriesConcernees: number[];
  
  // Paramètres avancés
  cumulable: boolean;
  priorite: number;
  actifLundiVendredi?: boolean;
  actifWeekend?: boolean;
  heureDebut?: string;
  heureFin?: string;
  
  // Métadonnées
  fournisseurId: number;
  dateCreation: Date;
  dateModification: Date;
  creePar: string;
  modifiePar?: string;
  
  // Statistiques
  nombreUtilisations: number;
  montantEconomise: number;
  chiffreAffaireGenere: number;
}

export interface ConditionPromotion {
  id: number;
  promotionId: number;
  type: TypeCondition;
  valeur: number;
  valeurTexte?: string;
  operateur: 'egal' | 'superieur' | 'superieur_egal' | 'inferieur' | 'inferieur_egal' | 'contient';
  obligatoire: boolean;
}

export interface ProduitPromotion {
  id: number;
  promotionId: number;
  produitId: number;
  inclus: boolean; // true = inclus, false = exclu
  
  // Navigation
  produit?: {
    id: number;
    nom: string;
    referenceOriginal: string;
    prixVenteHT: number;
    imagePrincipaleUrl?: string;
  };
}

export interface UtilisationPromotion {
  id: number;
  promotionId: number;
  commandeId: number;
  clientId: number;
  montantOriginal: number;
  montantReduit: number;
  montantEconomise: number;
  dateUtilisation: Date;
  codeUtilise?: string;
}

// DTOs pour les opérations
export interface CreatePromotionDto {
  nomPromotion: string;
  description: string;
  type: TypePromotion;
  typeReduction: TypeReduction;
  valeurReduction: number;
  dateDebut: Date;
  dateFin: Date;
  codePromo?: string;
  utilisationMax?: number;
  utilisationParClient?: number;
  conditions: CreateConditionPromotionDto[];
  produitsConcernes: CreateProduitPromotionDto[];
  categoriesConcernees: number[];
  cumulable: boolean;
  priorite: number;
  actifLundiVendredi?: boolean;
  actifWeekend?: boolean;
  heureDebut?: string;
  heureFin?: string;
  fournisseurId: number;
}

export interface UpdatePromotionDto {
  nomPromotion?: string;
  description?: string;
  typeReduction?: TypeReduction;
  valeurReduction?: number;
  dateDebut?: Date;
  dateFin?: Date;
  codePromo?: string;
  utilisationMax?: number;
  utilisationParClient?: number;
  conditions?: CreateConditionPromotionDto[];
  produitsConcernes?: CreateProduitPromotionDto[];
  categoriesConcernees?: number[];
  cumulable?: boolean;
  priorite?: number;
  actifLundiVendredi?: boolean;
  actifWeekend?: boolean;
  heureDebut?: string;
  heureFin?: string;
  statut?: StatutPromotion;
}

export interface CreateConditionPromotionDto {
  type: TypeCondition;
  valeur: number;
  valeurTexte?: string;
  operateur: 'egal' | 'superieur' | 'superieur_egal' | 'inferieur' | 'inferieur_egal' | 'contient';
  obligatoire: boolean;
}

export interface CreateProduitPromotionDto {
  produitId: number;
  inclus: boolean;
}

// Interfaces pour les filtres et recherches
export interface FiltrePromotion {
  type?: TypePromotion;
  statut?: StatutPromotion;
  dateDebut?: Date;
  dateFin?: Date;
  recherche?: string;
  actives?: boolean;
  expirees?: boolean;
}

export interface StatistiquesPromotion {
  totalPromotions: number;
  promotionsActives: number;
  promotionsExpirees: number;
  totalUtilisations: number;
  montantTotalEconomise: number;
  chiffreAffaireTotalGenere: number;
  tauxUtilisationMoyen: number;
  promotionLaPlusUtilisee?: {
    nomPromotion: string;
    utilisations: number;
  };
}

// Types pour les règles de validation
export interface RegleValidationPromotion {
  champ: string;
  message: string;
  valide: boolean;
}

export interface ResultatValidationPromotion {
  valide: boolean;
  erreurs: RegleValidationPromotion[];
  avertissements: RegleValidationPromotion[];
}

// Interface pour la prévisualisation
export interface PreviewPromotion {
  prixOriginal: number;
  prixAvecPromo: number;
  montantEconomise: number;
  pourcentageEconomise: number;
  applicable: boolean;
  raisonNonApplicable?: string;
}

// Types pour les templates de promotion
export interface TemplatePromotion {
  id: number;
  nomPromotion: string;
  description: string;
  type: TypePromotion;
  typeReduction: TypeReduction;
  valeurReduction: number;
  conditions: CreateConditionPromotionDto[];
  populaire: boolean;
  categorie: 'soldes' | 'fidelite' | 'nouveaute' | 'clearance' | 'saisonnier';
}

// Interface pour l'historique des modifications
export interface HistoriquePromotion {
  id: number;
  promotionId: number;
  action: 'creation' | 'modification' | 'activation' | 'desactivation' | 'suppression';
  champModifie?: string;
  ancienneValeur?: string;
  nouvelleValeur?: string;
  utilisateur: string;
  dateAction: Date;
  commentaire?: string;
}

// Types pour les rapports
export interface RapportPromotion {
  promotion: Promotion;
  statistiques: {
    nombreUtilisations: number;
    montantEconomise: number;
    chiffreAffaireGenere: number;
    tauxConversion: number;
    clientsUniques: number;
    commandeMoyenne: number;
  };
  evolution: {
    date: Date;
    utilisations: number;
    montantEconomise: number;
  }[];
}

// Interface pour les notifications de promotion
export interface NotificationPromotion {
  id: number;
  promotionId: number;
  type: 'expiration_proche' | 'limite_atteinte' | 'performance_faible' | 'succes';
  message: string;
  dateNotification: Date;
  lue: boolean;
  priorite: 'basse' | 'normale' | 'haute' | 'critique';
}
