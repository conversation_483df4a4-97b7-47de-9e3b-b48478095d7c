import { FormeDto } from "./FormeDto";
import { FournisseurDto } from "./FournisseurDto";
import { ImageProduitDto } from "./ImageProduitDto";
import { MarqueDto } from "./MarqueDto";
import { PromotionDto } from "./PromotionDto";
import { SousCategorieDto } from "./SousCategorieDto";
import { TauxTVADto } from "./TauxTVADto";

export interface ProduitCard {
  id: number;
  nom: string;
  description?: string;

  prixHT: number;
  prixOriginalTTC: number;
  prixApresOutlet: number;
  prixApresPromo: number;
  prixFinalTTC: number;

  tauxRemiseOutlet: number;
  tauxRemiseTotale: number;

  estEnPromo: boolean;
  isInPromo?: boolean;


  noteMoyenne: number;
  nombreAvis: number;
  dateAjout: Date;
  totalVentes?: number;
  stock: number;
  isInStock: boolean;
  estNouveau: boolean;

  forme?: FormeDto;
  marque?: MarqueDto;
  sousCategorie?: SousCategorieDto;

  tauxTVA?: TauxTVADto;
  fournisseur?: FournisseurDto;

  imageUrl: string;
  images: ImageProduitDto[];

  promotions: PromotionDto[];
}
