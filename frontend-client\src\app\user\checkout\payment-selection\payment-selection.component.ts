import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';

export interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  icon: string;
  available: boolean;
}

@Component({
  selector: 'app-payment-selection',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './payment-selection.component.html',
  styleUrls: ['./payment-selection.component.scss']
})
export class PaymentSelectionComponent implements OnInit {
  paymentMethods: PaymentMethod[] = [
    {
      id: 'card',
      name: 'Carte bancaire',
      description: 'Visa, Mastercard, American Express',
      icon: '💳',
      available: false
    },
    {
      id: 'paypal',
      name: 'PayPal',
      description: 'Paiement sécurisé avec PayPal',
      icon: '🅿️',
      available: false
    },
    {
      id: 'cash',
      name: 'Paiement à la livraison',
      description: 'Payez en espèces lors de la réception',
      icon: '💵',
      available: true
    },
    {
      id: 'transfer',
      name: 'Virement vers notre compte bancaire',
      description: 'Virement vers notre compte bancaire',
      icon: '🏦',
      available: false
    }
  ];

  selectedPaymentMethod: string | null = null;
  loading = false;
  errorMessage = '';

  constructor(private router: Router) {}

  ngOnInit(): void {
    // Vérifier que l'adresse a été sélectionnée
    const selectedAddressId = sessionStorage.getItem('selectedAddressId');
    if (!selectedAddressId) {
      this.router.navigate(['/user/checkout/address']);
      return;
    }

    // Sélectionner par défaut le paiement à la livraison (seul disponible)
    this.selectedPaymentMethod = 'cash';
  }

  selectPaymentMethod(methodId: string): void {
    const method = this.paymentMethods.find(m => m.id === methodId);
    if (method && method.available) {
      this.selectedPaymentMethod = methodId;
    }
  }

  getSelectedMethod(): PaymentMethod | null {
    if (!this.selectedPaymentMethod) return null;
    return this.paymentMethods.find(m => m.id === this.selectedPaymentMethod) || null;
  }

  continuerVersConfirmation(): void {
    if (!this.selectedPaymentMethod) {
      this.errorMessage = 'Veuillez sélectionner un mode de paiement';
      return;
    }

    // Stocker le mode de paiement sélectionné
    sessionStorage.setItem('selectedPaymentMethod', this.selectedPaymentMethod);

    this.router.navigate(['/user/checkout/final']);
  }

  retourAdresse(): void {
    this.router.navigate(['/user/checkout/address']);
  }
}
