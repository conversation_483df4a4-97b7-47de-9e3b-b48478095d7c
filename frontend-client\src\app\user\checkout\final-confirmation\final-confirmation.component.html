<div class="checkout-container">
  <div class="checkout-header">
    <h1>Confirmation finale</h1>
    <div class="checkout-steps">
      <div class="step completed">
        <span class="step-number">✓</span>
        <span class="step-label">Confirmation</span>
      </div>
      <div class="step completed">
        <span class="step-number">✓</span>
        <span class="step-label">Adresse</span>
      </div>
      <div class="step completed">
        <span class="step-number">✓</span>
        <span class="step-label">Paiement</span>
      </div>
      <div class="step active">
        <span class="step-number">4</span>
        <span class="step-label">Confirmation</span>
      </div>
    </div>
  </div>

  <div *ngIf="loading" class="loading">
    <div class="spinner"></div>
    <p>Chargement...</p>
  </div>

  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <div *ngIf="!loading && cart && selectedAddress && selectedPaymentMethod" class="checkout-content">
    <!-- Récapitulatif complet -->
    <div class="final-summary">
      <h2>Récapitulatif de votre commande</h2>
      
      <!-- Articles -->
      <div class="summary-section">
        <h3>Articles commandés ({{ getTotalItems() }})</h3>
        <div class="cart-items">
          <div *ngFor="let item of cartItemsWithDetails" class="cart-item">
            <div class="item-image">
              <img [src]="getProductImage(item)"
                   [alt]="item.nomProduit"
                   (error)="onImageError($event)">
            </div>
            <div class="item-details">
              <h4>{{ item.nomProduit }}</h4>
              <p class="item-reference">Réf: {{ item.referenceProduit }}</p>
              <div class="item-quantity">Quantité: {{ item.quantite }}</div>
            </div>
            <div class="item-price">
              <span class="total-price">{{ (item.prixUnitaire * item.quantite).toFixed(2) }} DT</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Adresse de livraison -->
      <div class="summary-section">
        <div class="section-header">
          <h3>Adresse de livraison</h3>
          <button type="button" class="btn-link" (click)="modifierAdresse()">Modifier</button>
        </div>
        <div class="address-info">
          <div class="address-card">
            <h4>{{ selectedAddress.rue }}</h4>
            <p>{{ selectedAddress.ville }}, {{ selectedAddress.codePostal }}</p>
            <p *ngIf="selectedAddress.pays">{{ selectedAddress.pays }}</p>
            <span *ngIf="selectedAddress.estPrincipale" class="main-badge">Adresse principale</span>
          </div>
        </div>
      </div>

      <!-- Mode de paiement -->
      <div class="summary-section">
        <div class="section-header">
          <h3>Mode de paiement</h3>
          <button type="button" class="btn-link" (click)="modifierPaiement()">Modifier</button>
        </div>
        <div class="payment-info">
          <div class="payment-card">
            <div class="payment-icon">{{ selectedPaymentMethod.icon }}</div>
            <div class="payment-details">
              <h4>{{ selectedPaymentMethod.name }}</h4>
              <p>{{ selectedPaymentMethod.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Total -->
      <div class="summary-section total-section">
        <div class="total-breakdown">
          <div class="total-line">
            <span>Sous-total:</span>
            <span>{{ getSousTotal().toFixed(2) }} DT</span>
          </div>
          <!-- Frais de livraison détaillés -->
          <div class="delivery-section">
            <div class="total-line">
              <span>Frais de livraison:</span>
              <span *ngIf="!fraisLivraisonDetails">🚚 Calcul en cours...</span>
              <span *ngIf="fraisLivraisonDetails && fraisLivraisonDetails.fraisLivraisonTotal === 0">✅ Gratuit</span>
              <span *ngIf="fraisLivraisonDetails && fraisLivraisonDetails.fraisLivraisonTotal > 0">{{ fraisLivraisonDetails.fraisLivraisonTotal.toFixed(2) }} DT</span>
            </div>

            <!-- Détail par fournisseur (toujours affiché) -->
            <div *ngIf="fraisLivraisonDetails && fraisLivraisonDetails.fraisParFournisseur.length > 0" class="delivery-details">
              <div class="delivery-header">
                <small>Détail par boutique :</small>
              </div>
              <div *ngFor="let detail of fraisLivraisonDetails.fraisParFournisseur" class="delivery-detail-line">
                <span class="supplier-name">• {{ detail.nomFournisseur }}:</span>
                <span class="supplier-fee">
                  <span *ngIf="detail.fraisLivraison === 0">Gratuit</span>
                  <span *ngIf="detail.fraisLivraison > 0">{{ detail.fraisLivraison.toFixed(2) }} DT</span>
                </span>
              </div>
              <!-- Toujours afficher le total des frais de livraison -->
              <div class="delivery-total-line">
                <span class="total-label">Total frais de livraison:</span>
                <span class="total-amount">
                  <span *ngIf="fraisLivraisonDetails.fraisLivraisonTotal === 0">Gratuit</span>
                  <span *ngIf="fraisLivraisonDetails.fraisLivraisonTotal > 0">{{ fraisLivraisonDetails.fraisLivraisonTotal.toFixed(2) }} DT</span>
                </span>
              </div>
            </div>
          </div>
          <div class="total-line">
            <span>TVA:</span>
            <span>Incluse</span>
          </div>
          <div class="total-line final-total">
            <span>Total à payer:</span>
            <span>{{ getTotal().toFixed(2) }} DT</span>
          </div>
        </div>
      </div>

      <!-- Conditions -->
      <div class="summary-section">
        <div class="terms-section">
          <label class="checkbox-container">
            <input type="checkbox" required>
            <span class="checkmark"></span>
            J'accepte les <a href="/terms" target="_blank">conditions générales de vente</a>
          </label>
          <label class="checkbox-container">
            <input type="checkbox" required>
            <span class="checkmark"></span>
            J'accepte la <a href="/privacy" target="_blank">politique de confidentialité</a>
          </label>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="checkout-actions">
      <button type="button" class="btn btn-secondary" (click)="retourPaiement()">
        Retour
      </button>
      <button type="button" 
              class="btn btn-primary btn-confirm" 
              [disabled]="isProcessingOrder"
              (click)="confirmerCommande()">
        <span *ngIf="!isProcessingOrder">Confirmer la commande</span>
        <span *ngIf="isProcessingOrder">
          <div class="btn-spinner"></div>
          Traitement en cours...
        </span>
      </button>
    </div>
  </div>
</div>
