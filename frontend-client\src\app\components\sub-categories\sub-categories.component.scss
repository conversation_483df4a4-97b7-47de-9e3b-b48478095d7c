$mobile-breakpoint: 1400px;
$transition-speed: 0.3s;

/* Version Desktop (min-width: 1401px) */
@media (min-width: ($mobile-breakpoint + 1)) {
  .sub-categories-container {
    position: absolute;
    left: 0;
    width: 100%;
    top: calc(100% + 18px);
    z-index: 1000;
    animation: fadeIn $transition-speed ease-out;

    .sub-categories-hitbox {
      position: absolute;
      top: -18px;
      height: 12px;
      width: 100%;
      background: transparent;
      z-index: 5;
    }

    .sub-categories-visible {
      background: var(--card-background-color);
      box-shadow: var(--shadow-lg);
      padding: 24px;
      margin-top: -1px;
      border-radius: 0 0 12px 12px;
    }

    .sub-categories-content {
      display: grid;
      grid-template-columns: 25% 75%;
      align-items: start;
      gap: 24px;

      .sub-categories-section {
        .sub-categories-list {
          display: grid;
          grid-template-columns: 1fr;
          gap: 4px;
          list-style: none;
          padding-left: 0;
          margin: 0;
        }

        .sub-category-item {
          margin: 0;
          padding: 2px 0;
          line-height: 1.2;
        }

        .sub-category-link {
          display: block;
          padding: 2px 0;
          transition: transform $transition-speed;
          color: var(--text-color);
          font-size: 14px;
          font-weight: normal;
          text-decoration: none;
          white-space: normal;
          word-wrap: break-word;
          hyphens: auto;
          line-height: 1.4;
          background: var(--card-background-color);
          border: none;
          text-align: left;
          &:hover {
            transform: translateX(5px);
            color: var(--text-color-hover);
          }
        }
      }

      .formes-section {
        position: sticky;

        h2 {
          font-size: 1.1rem;
          margin-bottom: 1rem;
          color: var(--accent-color);
        }

        .formes-list {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
          gap: 8px;
          justify-content: space-between;
          text-align: center;
        }

        .forme-item {
          transition: transform $transition-speed;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;
          margin-bottom: 25px;

          &:hover {
            transform: translateY(-3px) scale(1.03);
          }

          .forme-nom {
            width: 100%;
            text-align: center;
            padding-top: 4px;
            margin: 0 auto;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .forme-image {
          width: 100%;
          height: auto;
          max-height: 35px;
          margin: 0 auto 0px;
          object-fit: contain;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
      }
    }
  }
}

/* Version Mobile (max-width: 1400px) */
@media (max-width: $mobile-breakpoint) {
  .mobile-categories {
    .mobile-subcategories-content {
      padding:  0 10px;
      background: transparent;
      
      .mobile-subcategories-list {
        display: grid;
        gap: 4px;
        padding: 0;
        margin: 0;
        
        .mobile-subcategory-item {
          list-style: none;
          
          .mobile-subcategory-link {
            display: block;
            padding: 4px 10px;
            background: transparent;
            border: none;
            text-align: left;
            transition: all $transition-speed;
            color: var(--text-color);
            white-space: normal;
            word-wrap: break-word;
            hyphens: auto;
            line-height: 1.4;
            &:hover {
              transform: scale(0.98);
              background: var(--card-background-color-hover);
              border-radius: 10px;

            }
          }
        }
      }
    }
  }
}

/* Animation commune */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
