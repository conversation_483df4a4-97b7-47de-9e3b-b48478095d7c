﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NotificationController : ControllerBase
    {
        private readonly INotificationService _service;
        private readonly ILogger<NotificationController> _logger;

        public NotificationController(INotificationService service, ILogger<NotificationController> logger)
        {
            _service = service;
            _logger = logger;
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<NotificationDto>> Get(int id)
        {
            try
            {
                return Ok(await _service.GetByIdAsync(id));
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération de la notification {id}");
                return StatusCode(500, "Erreur interne");
            }
        }

        [HttpGet("user/{userId}")]
        public async Task<ActionResult<List<NotificationDto>>> GetByUser(int userId)
        {
            try
            {
                return Ok(await _service.GetByUserAsync(userId));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération des notifications pour l'utilisateur {userId}");
                return StatusCode(500, "Erreur interne");
            }
        }

        [HttpGet("user/{userId}/unread")]
        public async Task<ActionResult<List<NotificationDto>>> GetUnreadByUser(int userId)
        {
            try
            {
                return Ok(await _service.GetUnreadByUserAsync(userId));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération des notifications non lues pour l'utilisateur {userId}");
                return StatusCode(500, "Erreur interne");
            }
        }

        [HttpPost]
        public async Task<ActionResult<NotificationDto>> Create([FromBody] CreateNotificationDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var notification = await _service.CreateAsync(dto);
                return CreatedAtAction(nameof(Get), new { id = notification.Id }, notification);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création de la notification");
                return StatusCode(500, "Erreur interne");
            }
        }

        [HttpPatch("{id}/read")]
        public async Task<IActionResult> MarkAsRead(int id)
        {
            try
            {
                await _service.MarkAsReadAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors du marquage comme lue de la notification {id}");
                return StatusCode(500, "Erreur interne");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                await _service.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la suppression de la notification {id}");
                return StatusCode(500, "Erreur interne");
            }
        }

        [HttpDelete("user/{userId}/all")]
        public async Task<IActionResult> DeleteAllUserNotifications(int userId)
        {
            try
            {
                await _service.DeleteAllUserNotificationsAsync(userId);
                return Ok(new { Message = "Toutes les notifications ont été supprimées" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la suppression des notifications de l'utilisateur {userId}");
                return StatusCode(500, "Erreur interne");
            }
        }

        [HttpGet("admin/all")]
        public async Task<ActionResult<List<NotificationDto>>> GetAllAdminNotifications()
        {
            try
            {
                // Récupérer toutes les notifications pour tous les admins
                var notifications = await _service.GetAllAdminNotificationsAsync();
                return Ok(notifications);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des notifications admin");
                return StatusCode(500, "Erreur interne");
            }
        }

        [HttpPost("notify-admins")]
        public async Task<IActionResult> NotifyAllAdmins([FromBody] NotifyAdminsRequest request)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                await _service.NotifierTousLesAdminsAsync(request.Contenu);
                return Ok(new { Message = "Notification envoyée à tous les admins" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'envoi de notification aux admins");
                return StatusCode(500, "Erreur interne");
            }
        }
    }

    public class NotifyAdminsRequest
    {
        public string Contenu { get; set; } = string.Empty;
    }
}
