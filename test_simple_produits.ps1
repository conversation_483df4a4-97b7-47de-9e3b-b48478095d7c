# Test simple des produits
$baseUrl = "http://localhost:5014/api"

Write-Host "=== TEST SIMPLE PRODUITS ===" -ForegroundColor Cyan

# Test direct des produits sans authentification
Write-Host "`n1. Test tous les produits..." -ForegroundColor Yellow
try {
    $produits = Invoke-RestMethod -Uri "$baseUrl/Produits" -Method Get
    Write-Host "✅ $($produits.Count) produits trouvés" -ForegroundColor Green
    
    if ($produits.Count -gt 0) {
        Write-Host "Premiers produits:" -ForegroundColor White
        $produits | Select-Object -First 5 | ForEach-Object {
            Write-Host "  - ID: $($_.id) | $($_.nom) | Fournisseur: $($_.fournisseurId) | Prix: $($_.prixVenteHT) TND" -ForegroundColor Gray
        }
        
        # Grouper par fournisseur
        Write-Host "`nProduits par fournisseur:" -ForegroundColor White
        $produits | Group-Object fournisseurId | Sort-Object Name | ForEach-Object {
            Write-Host "  - Fournisseur $($_.Name): $($_.Count) produits" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# Test produits d'un fournisseur spécifique
Write-Host "`n2. Test produits fournisseur 11..." -ForegroundColor Yellow
try {
    $produitsFournisseur = Invoke-RestMethod -Uri "$baseUrl/Fournisseurs/11/produits" -Method Get
    Write-Host "✅ $($produitsFournisseur.Count) produits pour fournisseur 11" -ForegroundColor Green
    
    if ($produitsFournisseur.Count -gt 0) {
        Write-Host "Produits du fournisseur 11:" -ForegroundColor White
        foreach ($p in $produitsFournisseur) {
            Write-Host "  - $($p.nom): $($p.prixVenteHT) TND (Stock: $($p.stock))" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# Test endpoint by-fournisseur
Write-Host "`n3. Test endpoint by-fournisseur..." -ForegroundColor Yellow
try {
    $produitsBy = Invoke-RestMethod -Uri "$baseUrl/Produits/by-fournisseur/11" -Method Get
    Write-Host "✅ $($produitsBy.Count) produits via by-fournisseur" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur by-fournisseur: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
}

Write-Host "`n=== DIAGNOSTIC ===" -ForegroundColor Cyan
Write-Host "Si les produits s'affichent ici, le problème est dans le frontend" -ForegroundColor White
Write-Host "Si pas de produits, le problème est dans le backend" -ForegroundColor White

Write-Host "`n=== FIN ===" -ForegroundColor Cyan
