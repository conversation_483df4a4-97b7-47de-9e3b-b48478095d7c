﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PanierController : ControllerBase
    {
        private readonly IPanierService _service;
        private readonly ILogger<PanierController> _logger;

        public PanierController(IPanierService service, ILogger<PanierController> logger)
        {
            _service = service;
            _logger = logger;
        }

        [HttpGet("client/{clientId:int}")]
        public async Task<ActionResult<PanierDto>> GetPanierActif(int clientId)
        {
            try
            {
                var panier = await _service.GetPanierActifAsync(clientId);
                return Ok(panier);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération du panier actif pour le client {clientId}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPost]
        public async Task<ActionResult<PanierDto>> Create([FromBody] CreatePanierDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var panier = await _service.CreateAsync(dto);
                return CreatedAtAction(nameof(GetPanierActif), new { clientId = panier.ClientId }, panier);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création du panier");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPut("{id:int}")]
        public async Task<IActionResult> Update(int id, [FromBody] UpdatePanierDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                await _service.UpdateAsync(id, dto);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la mise à jour du panier {id}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpDelete("{id:int}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                await _service.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la suppression du panier {id}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPost("{panierId:int}/items")]
        public async Task<ActionResult<ItemPanierDto>> AjouterItem(int panierId, [FromBody] AddItemPanierDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);
            try
            {
                var item = await _service.AjouterAuPanier(panierId, dto);
                var panier = await _service.GetPanierActifAsync(panierId);
                return CreatedAtAction(
                    nameof(GetPanierActif),
                    new { clientId = panier.ClientId },
                    item);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de l'ajout d'un item au panier {panierId}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPut("items/{itemId:int}")]
        public async Task<IActionResult> ModifierItem(int itemId, [FromBody] UpdateItemPanierDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                await _service.ModifierItemAsync(itemId, dto);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la modification de l'item {itemId}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpDelete("items/{itemId:int}")]
        public async Task<IActionResult> SupprimerItem(int itemId)
        {
            try
            {
                await _service.SupprimerItemAsync(itemId);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la suppression de l'item {itemId}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }
    }
}
