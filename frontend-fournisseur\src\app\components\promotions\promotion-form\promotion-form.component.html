<div class="promotion-form">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1>{{ isEditMode ? '✏️ Modifier' : '➕ Créer' }} une Promotion</h1>
      <p>{{ isEditMode ? 'Modifiez les paramètres de votre promotion' : 'Configurez votre nouvelle promotion étape par étape' }}</p>
    </div>
    
    <div class="header-actions">
      <button class="btn btn-secondary" (click)="cancel()">
        ❌ Annuler
      </button>
    </div>
  </div>

  <!-- Indicateur de progression -->
  <div class="progress-indicator">
    <div class="steps">
      <div 
        *ngFor="let step of [1,2,3,4]; let i = index" 
        class="step"
        [class.active]="currentStep === step"
        [class.completed]="currentStep > step"
        [class.valid]="isStepValid(step)"
        (click)="goToStep(step)"
      >
        <div class="step-number">{{ step }}</div>
        <div class="step-label">
          <span *ngIf="step === 1">Informations</span>
          <span *ngIf="step === 2">Réduction</span>
          <span *ngIf="step === 3">Conditions</span>
          <span *ngIf="step === 4">Révision</span>
        </div>
      </div>
    </div>
    <div class="progress-bar">
      <div class="progress-fill" [style.width.%]="(currentStep / totalSteps) * 100"></div>
    </div>
  </div>

  <!-- Message d'erreur global -->
  <div *ngIf="error" class="error-message">
    <div class="error-icon">❌</div>
    <p>{{ error }}</p>
  </div>

  <!-- Formulaire -->
  <div class="form-container" *ngIf="!isLoading">
    
    <!-- Étape 1: Informations générales -->
    <div class="form-step" *ngIf="currentStep === 1">
      <div class="step-header">
        <h2>📋 Informations générales</h2>
        <p>Définissez les informations de base de votre promotion</p>
      </div>
      
      <div class="form-content">
        <div class="form-group">
          <label for="nomPromotion">Nom de la promotion *</label>
          <input
            type="text"
            id="nomPromotion"
            [(ngModel)]="promotion.nomPromotion"
            class="form-control"
            placeholder="Ex: SUMMER, WINTER, BLACK_FRIDAY"
            required
          />
          <div class="form-help">Nom court pour identifier la promotion (ex: SUMMER, WINTER)</div>
        </div>

        <div class="form-group">
          <label for="description">Description *</label>
          <textarea
            id="description"
            [(ngModel)]="promotion.description"
            class="form-control"
            rows="3"
            placeholder="Décrivez votre promotion en détail..."
            required
          ></textarea>
          <div class="form-help">Expliquez les avantages et conditions de votre promotion</div>
        </div>

        <div class="form-group">
          <label>Type de promotion *</label>
          <div class="radio-group">
            <div 
              *ngFor="let type of typesPromotion" 
              class="radio-option"
              [class.selected]="promotion.type === type.value"
              (click)="promotion.type = type.value"
            >
              <div class="radio-header">
                <input 
                  type="radio" 
                  [value]="type.value" 
                  [(ngModel)]="promotion.type"
                  [id]="'type-' + type.value"
                />
                <span class="radio-icon">{{ getTypeIcon(type.value) }}</span>
                <label [for]="'type-' + type.value">{{ type.label }}</label>
              </div>
              <p class="radio-description">{{ type.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Étape 2: Configuration de la réduction -->
    <div class="form-step" *ngIf="currentStep === 2">
      <div class="step-header">
        <h2>💰 Configuration de la réduction</h2>
        <p>Définissez le type et la valeur de la réduction</p>
      </div>
      
      <div class="form-content">
        <div class="form-group">
          <label>Type de réduction *</label>
          <div class="radio-group">
            <div 
              *ngFor="let type of typesReduction" 
              class="radio-option"
              [class.selected]="promotion.typeReduction === type.value"
              (click)="promotion.typeReduction = type.value"
            >
              <div class="radio-header">
                <input 
                  type="radio" 
                  [value]="type.value" 
                  [(ngModel)]="promotion.typeReduction"
                  [id]="'reduction-' + type.value"
                />
                <label [for]="'reduction-' + type.value">{{ type.label }}</label>
              </div>
              <p class="radio-description">{{ type.description }}</p>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="valeurReduction">
            Valeur de la réduction *
            <span *ngIf="promotion.typeReduction === 'pourcentage'">(%)</span>
            <span *ngIf="promotion.typeReduction !== 'pourcentage'">(€)</span>
          </label>
          <div class="input-group">
            <input
              type="number"
              id="valeurReduction"
              [(ngModel)]="promotion.valeurReduction"
              class="form-control"
              [min]="0"
              [max]="promotion.typeReduction === 'pourcentage' ? 100 : 9999"
              step="0.01"
              required
            />
            <span class="input-suffix">
              {{ promotion.typeReduction === 'pourcentage' ? '%' : '€' }}
            </span>
          </div>
          <div class="reduction-preview" *ngIf="promotion.valeurReduction > 0">
            Aperçu: <strong>{{ getReductionPreview() }}</strong>
          </div>
        </div>

        <!-- Code promo (si type code_promo) -->
        <div class="form-group" *ngIf="promotion.type === 'code_promo'">
          <label for="codePromo">Code promo *</label>
          <input
            type="text"
            id="codePromo"
            [(ngModel)]="promotion.codePromo"
            class="form-control"
            placeholder="Ex: SUMMER2024"
            style="text-transform: uppercase"
            required
          />
          <div class="form-help">Le code que les clients devront saisir</div>
        </div>

        <!-- Limitations d'usage -->
        <div class="form-group" *ngIf="promotion.type === 'code_promo'">
          <label for="utilisationMax">Nombre d'utilisations maximum</label>
          <input
            type="number"
            id="utilisationMax"
            [(ngModel)]="promotion.utilisationMax"
            class="form-control"
            min="1"
            placeholder="Illimité si vide"
          />
        </div>

        <div class="form-group" *ngIf="promotion.type === 'code_promo'">
          <label for="utilisationParClient">Utilisations par client</label>
          <input
            type="number"
            id="utilisationParClient"
            [(ngModel)]="promotion.utilisationParClient"
            class="form-control"
            min="1"
            placeholder="Illimité si vide"
          />
        </div>

        <!-- Paramètres avancés -->
        <div class="form-group">
          <label for="priorite">Priorité</label>
          <select id="priorite" [(ngModel)]="promotion.priorite" class="form-control">
            <option value="1">1 - Très haute</option>
            <option value="2">2 - Haute</option>
            <option value="3">3 - Normale</option>
            <option value="4">4 - Basse</option>
            <option value="5">5 - Très basse</option>
          </select>
          <div class="form-help">En cas de promotions multiples, celle avec la priorité la plus haute s'applique</div>
        </div>

        <div class="form-group">
          <div class="checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" [(ngModel)]="promotion.cumulable" />
              <span>Cumulable avec d'autres promotions</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Étape 3: Conditions et validité -->
    <div class="form-step" *ngIf="currentStep === 3">
      <div class="step-header">
        <h2>⏰ Conditions et validité</h2>
        <p>Définissez quand et comment votre promotion s'applique</p>
      </div>
      
      <div class="form-content">
        <!-- Période de validité -->
        <div class="form-section">
          <h3>📅 Période de validité</h3>
          
          <div class="form-row">
            <div class="form-group">
              <label for="dateDebut">Date de début *</label>
              <input
                type="datetime-local"
                id="dateDebut"
                [(ngModel)]="promotion.dateDebut"
                class="form-control"
                required
              />
            </div>
            
            <div class="form-group">
              <label for="dateFin">Date de fin *</label>
              <input
                type="datetime-local"
                id="dateFin"
                [(ngModel)]="promotion.dateFin"
                class="form-control"
                required
              />
            </div>
          </div>

          <!-- Horaires spécifiques -->
          <div class="form-row">
            <div class="form-group">
              <label for="heureDebut">Heure de début (optionnel)</label>
              <input
                type="time"
                id="heureDebut"
                [(ngModel)]="promotion.heureDebut"
                class="form-control"
              />
            </div>
            
            <div class="form-group">
              <label for="heureFin">Heure de fin (optionnel)</label>
              <input
                type="time"
                id="heureFin"
                [(ngModel)]="promotion.heureFin"
                class="form-control"
              />
            </div>
          </div>

          <!-- Jours de la semaine -->
          <div class="form-group">
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" [(ngModel)]="promotion.actifLundiVendredi" />
                <span>Actif du lundi au vendredi</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" [(ngModel)]="promotion.actifWeekend" />
                <span>Actif le weekend</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Conditions d'application -->
        <div class="form-section">
          <h3>🎯 Conditions d'application</h3>
          
          <div class="conditions-list">
            <div 
              *ngFor="let condition of promotion.conditions; let i = index" 
              class="condition-item"
            >
              <div class="condition-header">
                <span class="condition-number">{{ i + 1 }}</span>
                <button 
                  type="button" 
                  class="btn btn-sm btn-danger"
                  (click)="removeCondition(i)"
                >
                  🗑️
                </button>
              </div>
              
              <div class="condition-content">
                <div class="form-row">
                  <div class="form-group">
                    <label>Type de condition</label>
                    <select [(ngModel)]="condition.type" class="form-control">
                      <option *ngFor="let type of typesCondition" [value]="type.value">
                        {{ type.label }}
                      </option>
                    </select>
                  </div>
                  
                  <div class="form-group">
                    <label>Opérateur</label>
                    <select [(ngModel)]="condition.operateur" class="form-control">
                      <option *ngFor="let op of operateurs" [value]="op.value">
                        {{ op.label }}
                      </option>
                    </select>
                  </div>
                </div>
                
                <div class="form-row">
                  <div class="form-group" *ngIf="condition.type !== 'premiere_commande' && condition.type !== 'client_fidele'">
                    <label>Valeur</label>
                    <input
                      type="number"
                      [(ngModel)]="condition.valeur"
                      class="form-control"
                      step="0.01"
                    />
                  </div>
                  
                  <div class="form-group" *ngIf="condition.type === 'produit_specifique' || condition.type === 'categorie'">
                    <label>Texte</label>
                    <input
                      type="text"
                      [(ngModel)]="condition.valeurTexte"
                      class="form-control"
                      placeholder="Nom du produit ou catégorie"
                    />
                  </div>
                </div>
                
                <div class="form-group">
                  <label class="checkbox-label">
                    <input type="checkbox" [(ngModel)]="condition.obligatoire" />
                    <span>Condition obligatoire</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <button 
            type="button" 
            class="btn btn-secondary"
            (click)="addCondition()"
          >
            ➕ Ajouter une condition
          </button>
        </div>

        <!-- Produits concernés -->
        <div class="form-section">
          <h3>📦 Produits concernés</h3>
          <p>Sélectionnez les produits auxquels cette promotion s'applique (laisser vide pour tous les produits)</p>
          
          <div class="products-grid" *ngIf="produits.length > 0">
            <div 
              *ngFor="let produit of produits" 
              class="product-item"
              [class.selected]="isProduitSelected(produit.id)"
              (click)="toggleProduitSelection(produit.id)"
            >
              <div class="product-checkbox">
                <input 
                  type="checkbox" 
                  [checked]="isProduitSelected(produit.id)"
                  (click)="$event.stopPropagation()"
                />
              </div>
              <div class="product-info">
                <h4>{{ produit.nom }}</h4>
                <p>{{ produit.referenceOriginal }}</p>
                <span class="product-price">{{ formatPrice(produit.prixVenteHT) }}</span>
              </div>
            </div>
          </div>
          
          <div class="selection-summary" *ngIf="selectedProduits.size > 0">
            {{ selectedProduits.size }} produit(s) sélectionné(s)
          </div>
        </div>
      </div>
    </div>

    <!-- Étape 4: Révision -->
    <div class="form-step" *ngIf="currentStep === 4">
      <div class="step-header">
        <h2>👁️ Révision et validation</h2>
        <p>Vérifiez tous les paramètres avant de {{ isEditMode ? 'modifier' : 'créer' }} votre promotion</p>
      </div>
      
      <div class="form-content">
        <div class="review-section">
          <h3>📋 Informations générales</h3>
          <div class="review-item">
            <strong>Nom de la promotion:</strong> {{ promotion.nomPromotion }}
          </div>
          <div class="review-item">
            <strong>Description:</strong> {{ promotion.description }}
          </div>
          <div class="review-item">
            <strong>Type:</strong> {{ promotion.type | titlecase }}
          </div>
        </div>

        <div class="review-section">
          <h3>💰 Réduction</h3>
          <div class="review-item">
            <strong>Type de réduction:</strong> {{ promotion.typeReduction | titlecase }}
          </div>
          <div class="review-item">
            <strong>Valeur:</strong> {{ getReductionPreview() }}
          </div>
          <div class="review-item" *ngIf="promotion.codePromo">
            <strong>Code promo:</strong> {{ promotion.codePromo }}
          </div>
        </div>

        <div class="review-section">
          <h3>⏰ Validité</h3>
          <div class="review-item">
            <strong>Du:</strong> {{ promotion.dateDebut | date:'dd/MM/yyyy HH:mm' }}
          </div>
          <div class="review-item">
            <strong>Au:</strong> {{ promotion.dateFin | date:'dd/MM/yyyy HH:mm' }}
          </div>
        </div>

        <div class="review-section" *ngIf="promotion.conditions.length > 0">
          <h3>🎯 Conditions</h3>
          <div *ngFor="let condition of promotion.conditions; let i = index" class="review-item">
            <strong>Condition {{ i + 1 }}:</strong> 
            {{ condition.type | titlecase }} {{ condition.operateur }} {{ condition.valeur }}
            <span *ngIf="condition.obligatoire" class="required-badge">Obligatoire</span>
          </div>
        </div>

        <div class="review-section" *ngIf="selectedProduits.size > 0">
          <h3>📦 Produits concernés</h3>
          <div class="review-item">
            <strong>Nombre de produits:</strong> {{ selectedProduits.size }}
          </div>
        </div>

        <!-- Validation -->
        <div class="validation-section" *ngIf="validationResult">
          <div class="validation-errors" *ngIf="validationResult.erreurs.length > 0">
            <h4>❌ Erreurs à corriger:</h4>
            <ul>
              <li *ngFor="let erreur of validationResult.erreurs">{{ erreur.message }}</li>
            </ul>
          </div>
          
          <div class="validation-warnings" *ngIf="validationResult.avertissements.length > 0">
            <h4>⚠️ Avertissements:</h4>
            <ul>
              <li *ngFor="let avertissement of validationResult.avertissements">{{ avertissement.message }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation -->
  <div class="form-navigation" *ngIf="!isLoading">
    <div class="nav-left">
      <button 
        *ngIf="currentStep > 1"
        type="button" 
        class="btn btn-secondary"
        (click)="previousStep()"
      >
        ← Précédent
      </button>
    </div>
    
    <div class="nav-center">
      <button 
        type="button" 
        class="btn btn-info"
        (click)="validatePromotion()"
      >
        🔍 Valider
      </button>
    </div>
    
    <div class="nav-right">
      <button 
        *ngIf="currentStep < totalSteps"
        type="button" 
        class="btn btn-primary"
        [disabled]="!isStepValid(currentStep)"
        (click)="nextStep()"
      >
        Suivant →
      </button>
      
      <button 
        *ngIf="currentStep === totalSteps"
        type="button" 
        class="btn btn-success"
        [disabled]="isSaving || (validationResult && !validationResult.valide)"
        (click)="savePromotion()"
      >
        <span *ngIf="!isSaving">{{ isEditMode ? '💾 Modifier' : '✅ Créer' }}</span>
        <span *ngIf="isSaving">⏳ Sauvegarde...</span>
      </button>
    </div>
  </div>

  <!-- Message de chargement -->
  <div *ngIf="isLoading" class="loading">
    <div class="loading-spinner"></div>
    <p>Chargement...</p>
  </div>
</div>
