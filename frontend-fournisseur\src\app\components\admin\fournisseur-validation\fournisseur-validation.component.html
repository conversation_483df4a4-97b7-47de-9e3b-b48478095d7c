<div class="fournisseur-validation-container">
  <div class="header">
    <h1>🔔 Validation des Fournisseurs</h1>
    <p><PERSON><PERSON><PERSON> les demandes d'inscription et validez les nouveaux fournisseurs</p>
  </div>

  <!-- Statistiques rapides -->
  <div class="stats-cards">
    <div class="stat-card pending">
      <div class="stat-icon">⏳</div>
      <div class="stat-content">
        <h3>{{ fournisseursEnAttente().length }}</h3>
        <p>En attente</p>
      </div>
    </div>
    <div class="stat-card notifications">
      <div class="stat-icon">🔔</div>
      <div class="stat-content">
        <h3>{{ notificationsNonLues().length }}</h3>
        <p>Notifications</p>
      </div>
    </div>
    <div class="stat-card total">
      <div class="stat-icon">👥</div>
      <div class="stat-content">
        <h3>{{ fournisseurs().length }}</h3>
        <p>Total</p>
      </div>
    </div>
  </div>

  <!-- Filtres et recherche -->
  <div class="filters-section">
    <div class="search-box">
      <input 
        type="text" 
        placeholder="Rechercher par nom, email, matricule..." 
        [(ngModel)]="searchTerm"
        (ngModelChange)="searchTerm.set($event)"
        class="search-input">
    </div>
    <div class="filter-box">
      <select 
        [(ngModel)]="selectedStatus"
        (ngModelChange)="selectedStatus.set($event)"
        class="filter-select">
        <option value="">Tous les statuts</option>
        <option value="en-attente">En attente</option>
        <option value="valide">Validé</option>
        <option value="rejete">Rejeté</option>
      </select>
    </div>
    <button (click)="loadData()" class="refresh-btn">
      🔄 Actualiser
    </button>
  </div>

  <!-- Section des notifications récentes -->
  <div class="notifications-section" *ngIf="notificationsNonLues().length > 0">
    <h2>📧 Notifications récentes</h2>
    <div class="notifications-list">
      <div 
        *ngFor="let notification of notificationsNonLues().slice(0, 3)" 
        class="notification-card"
        [class.unread]="!notification.estLue">
        
        <div class="notification-header">
          <span class="notification-date">{{ formatDate(notification.dateEnvoi) }}</span>
          <div class="notification-actions">
            <button 
              (click)="markNotificationAsRead(notification)"
              class="mark-read-btn"
              title="Marquer comme lu">
              ✓
            </button>
            <button 
              (click)="deleteNotification(notification.id)"
              class="delete-btn"
              title="Supprimer">
              🗑️
            </button>
          </div>
        </div>

        <div class="notification-content">
          <p>{{ notification.contenu }}</p>
        </div>

        <div class="notification-footer">
          <button 
            (click)="viewFournisseurFromNotification(notification)"
            class="view-details-btn">
            👁️ Voir les détails
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Liste des fournisseurs -->
  <div class="fournisseurs-section">
    <h2>👥 Fournisseurs</h2>
    
    <div *ngIf="isLoading()" class="loading">
      <div class="spinner"></div>
      <p>Chargement des fournisseurs...</p>
    </div>

    <div *ngIf="error()" class="error-message">
      <p>{{ error() }}</p>
      <button (click)="loadFournisseurs()" class="retry-btn">Réessayer</button>
    </div>

    <div *ngIf="!isLoading() && !error()" class="fournisseurs-grid">
      <div *ngIf="filteredFournisseurs().length === 0" class="no-results">
        <h3>Aucun fournisseur trouvé</h3>
        <p>Aucun fournisseur ne correspond aux critères de recherche</p>
      </div>

      <div 
        *ngFor="let fournisseur of filteredFournisseurs()" 
        class="fournisseur-card"
        [class]="getStatutValidationClass(fournisseur.statutValidation)">
        
        <div class="fournisseur-header">
          <div class="fournisseur-logo">
            <img 
              *ngIf="fournisseur.logoFile" 
              [src]="fournisseur.logoFile" 
              [alt]="fournisseur.raisonSociale">
            <div *ngIf="!fournisseur.logoFile" class="default-logo">
              🏪
            </div>
          </div>
          <div class="fournisseur-info">
            <h3>{{ fournisseur.raisonSociale }}</h3>
            <p>{{ fournisseur.nom }} {{ fournisseur.prenom }}</p>
            <span class="fournisseur-email">{{ fournisseur.email }}</span>
          </div>
          <div class="status-badge" [class]="getStatutValidationClass(fournisseur.statutValidation)">
            {{ getStatutValidationText(fournisseur.statutValidation) }}
          </div>
        </div>

        <div class="fournisseur-details">
          <div class="detail-item">
            <span class="label">Matricule fiscal:</span>
            <span class="value">{{ fournisseur.matriculeFiscale }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Date d'inscription:</span>
            <span class="value">{{ formatDate(fournisseur.dateInscription) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Commission:</span>
            <span class="value">{{ fournisseur.commission }}%</span>
          </div>
        </div>

        <div class="fournisseur-actions">
          <button 
            (click)="viewFournisseurDetails(fournisseur)"
            class="view-btn">
            👁️ Voir détails
          </button>
          <button 
            *ngIf="fournisseur.statutValidation === StatutValidationFournisseur.EnAttente"
            (click)="viewFournisseurDetails(fournisseur)"
            class="validate-btn">
            ✅ Valider
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal de détails du fournisseur -->
<div *ngIf="selectedFournisseur()" class="modal-overlay" (click)="closeDetails()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>📋 Détails du fournisseur</h2>
      <button (click)="closeDetails()" class="close-btn">✖️</button>
    </div>

    <div class="modal-body">
      <div class="supplier-full-details">
        <!-- Informations générales -->
        <div class="details-section">
          <h3>👤 Informations générales</h3>
          <div class="details-grid">
            <div class="detail-row">
              <span class="label">Raison sociale:</span>
              <span class="value">{{ selectedFournisseur()!.raisonSociale }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Contact:</span>
              <span class="value">{{ selectedFournisseur()!.nom }} {{ selectedFournisseur()!.prenom }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Email:</span>
              <span class="value">{{ selectedFournisseur()!.email }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Téléphone:</span>
              <span class="value">{{ selectedFournisseur()!.phoneNumber }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Statut:</span>
              <span class="value status-badge" [class]="getStatutValidationClass(selectedFournisseur()!.statutValidation)">
                {{ getStatutValidationText(selectedFournisseur()!.statutValidation) }}
              </span>
            </div>
          </div>
        </div>

        <!-- Informations commerciales -->
        <div class="details-section">
          <h3>💼 Informations commerciales</h3>
          <div class="details-grid">
            <div class="detail-row">
              <span class="label">Matricule fiscal:</span>
              <span class="value">{{ selectedFournisseur()!.matriculeFiscale }}</span>
            </div>
            <div class="detail-row">
              <span class="label">RIB:</span>
              <span class="value">{{ selectedFournisseur()!.rib }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Code banque:</span>
              <span class="value">{{ selectedFournisseur()!.codeBanque }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Commission:</span>
              <span class="value">{{ selectedFournisseur()!.commission }}%</span>
            </div>
            <div class="detail-row">
              <span class="label">Délai préparation:</span>
              <span class="value">{{ selectedFournisseur()!.delaiPreparationJours }} jours</span>
            </div>
            <div class="detail-row">
              <span class="label">Frais livraison:</span>
              <span class="value">{{ selectedFournisseur()!.fraisLivraisonBase }} TND</span>
            </div>
          </div>
        </div>

        <!-- Adresses -->
        <div class="details-section">
          <h3>📍 Adresses</h3>
          <div *ngFor="let adresse of selectedFournisseur()!.adresses" class="address-card">
            <div class="address-header">
              <span *ngIf="adresse.estPrincipale" class="principal-badge">Principale</span>
            </div>
            <p>{{ adresse.rue }}</p>
            <p>{{ adresse.codePostal }} {{ adresse.ville }}</p>
            <p>{{ adresse.pays }}</p>
          </div>
        </div>

        <!-- Description -->
        <div *ngIf="selectedFournisseur()!.description" class="details-section">
          <h3>📝 Description</h3>
          <p>{{ selectedFournisseur()!.description }}</p>
        </div>

        <!-- Historique de validation -->
        <div *ngIf="selectedFournisseur()!.dateValidation" class="details-section">
          <h3>📅 Historique de validation</h3>
          <div class="details-grid">
            <div class="detail-row">
              <span class="label">Date de validation:</span>
              <span class="value">{{ formatDate(selectedFournisseur()!.dateValidation!) }}</span>
            </div>
            <div *ngIf="selectedFournisseur()!.commentaireValidation" class="detail-row">
              <span class="label">Commentaire:</span>
              <span class="value">{{ selectedFournisseur()!.commentaireValidation }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button (click)="closeDetails()" class="cancel-btn">Fermer</button>
      <div *ngIf="selectedFournisseur()!.statutValidation === StatutValidationFournisseur.EnAttente" class="validation-buttons">
        <button (click)="openValidationModal('reject')" class="reject-btn">
          ❌ Rejeter
        </button>
        <button (click)="openValidationModal('accept')" class="approve-btn">
          ✅ Valider
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal de validation -->
<div *ngIf="showValidationModal()" class="modal-overlay" (click)="closeValidationModal()">
  <div class="modal-content validation-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>
        {{ validationAction() === 'accept' ? '✅ Valider' : '❌ Rejeter' }} le fournisseur
      </h2>
      <button (click)="closeValidationModal()" class="close-btn">✖️</button>
    </div>

    <div class="modal-body">
      <p>
        Êtes-vous sûr de vouloir 
        <strong>{{ validationAction() === 'accept' ? 'valider' : 'rejeter' }}</strong>
        le fournisseur <strong>{{ selectedFournisseur()?.raisonSociale }}</strong> ?
      </p>

      <div class="form-group">
        <label for="validationComment">
          Commentaire {{ validationAction() === 'reject' ? '(obligatoire)' : '(optionnel)' }}:
        </label>
        <textarea 
          id="validationComment"
          [(ngModel)]="validationComment"
          (ngModelChange)="validationComment.set($event)"
          placeholder="Ajoutez un commentaire..."
          rows="4">
        </textarea>
      </div>
    </div>

    <div class="modal-footer">
      <button (click)="closeValidationModal()" class="cancel-btn">
        Annuler
      </button>
      <button 
        (click)="confirmValidation()" 
        [class]="validationAction() === 'accept' ? 'approve-btn' : 'reject-btn'"
        [disabled]="validationAction() === 'reject' && !validationComment().trim()">
        {{ validationAction() === 'accept' ? '✅ Valider' : '❌ Rejeter' }}
      </button>
    </div>
  </div>
</div>
