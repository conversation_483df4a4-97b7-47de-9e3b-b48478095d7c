import { Component, OnInit, OnDestroy } from '@angular/core';
import { interval, Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';
import { LogoCarouselModule } from 'src/app/components/logo-carousel/logo-carousel.module';
import { NouveauxArrivagesComponent } from 'src/app/components/nouveaux-arrivages/nouveaux-arrivages.component';
import { MeilleuresVentesComponent } from 'src/app/components/meilleures-ventes/meilleures-ventes.component';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    LogoCarouselModule,
    NouveauxArrivagesComponent,
    MeilleuresVentesComponent,
  ],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
})
export class HomeComponent implements OnInit, OnDestroy {
  slides = [
    {
      image: 'assets/images/image1.jpg',
      category: 'HOMME',
      title: 'Collection Élégance',
      buttonText: 'Découvrir',
      link: '/femme',
    },
    {
      image: 'assets/images/image2.jpg',
      category: 'FEMME',
      title: 'Style Moderne',
      buttonText: 'Découvrir',
      link: '/homme',
    },
    {
      image: 'assets/images/image3.jpg',
      category: 'ENFANTS',
      title: 'Lunettes Fun',
      buttonText: 'Découvrir',
      link: '/enfants',
    },
  ];

  currentSlide: number = 0;
  private slideSubscription!: Subscription;
  private intervalId: any;

  ngOnInit() {
    this.startSlideShow();
  }
  startSlideShow() {
    this.slideSubscription = interval(5000).subscribe(() => {
      this.currentSlide = (this.currentSlide + 1) % this.slides.length;
    });
  }
  navigateTo(link: string): void {
    console.log('Navigation vers:', link);
  }

  ngOnDestroy() {
    if (this.slideSubscription) {
      this.slideSubscription.unsubscribe();
    }
    clearInterval(this.intervalId);
  }
}
