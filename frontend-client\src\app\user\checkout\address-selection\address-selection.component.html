<div class="checkout-container">
  <div class="checkout-header">
    <h1>Sélection de l'adresse de livraison</h1>
    <div class="checkout-steps">
      <div class="step completed">
        <span class="step-number">✓</span>
        <span class="step-label">Confirmation</span>
      </div>
      <div class="step active">
        <span class="step-number">2</span>
        <span class="step-label">Adresse</span>
      </div>
      <div class="step">
        <span class="step-number">3</span>
        <span class="step-label">Paiement</span>
      </div>
      <div class="step">
        <span class="step-number">4</span>
        <span class="step-label">Confirmation</span>
      </div>
    </div>
  </div>

  <div *ngIf="loading" class="loading">
    <div class="spinner"></div>
    <p>Chargement des adresses...</p>
  </div>

  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <div *ngIf="!loading" class="checkout-content">
    <!-- Liste des adresses -->
    <div class="addresses-section">
      <h2>Choisissez votre adresse de livraison</h2>
      
      <div *ngIf="addresses.length === 0" class="no-addresses">
        <div class="no-addresses-icon">📍</div>
        <h3>Aucune adresse enregistrée</h3>
        <p>Vous devez ajouter au moins une adresse pour continuer</p>
        <button type="button" class="btn btn-primary" (click)="ajouterNouvelleAdresse()">
          Ajouter une adresse
        </button>
      </div>

      <div *ngIf="addresses.length > 0" class="addresses-list">
        <div *ngFor="let address of addresses" 
             class="address-card" 
             [class.selected]="selectedAddressId === address.id"
             (click)="selectAddress(address.id)">
          
          <div class="address-radio">
            <input type="radio" 
                   [id]="'address-' + address.id"
                   name="selectedAddress"
                   [checked]="selectedAddressId === address.id"
                   (change)="selectAddress(address.id)">
          </div>
          
          <div class="address-content">
            <div class="address-header">
              <h3>{{ address.rue }}</h3>
              <span *ngIf="address.estPrincipale" class="main-badge">Principale</span>
            </div>
            
            <div class="address-details">
              <p>{{ address.ville }}, {{ address.codePostal }}</p>
              <p *ngIf="address.pays">{{ address.pays }}</p>
            </div>
          </div>
          
          <div class="address-actions">
            <span class="select-text" *ngIf="selectedAddressId === address.id">
              ✓ Sélectionnée
            </span>
          </div>
        </div>

        <!-- Bouton pour ajouter une nouvelle adresse -->
        <div class="add-address-card" (click)="ajouterNouvelleAdresse()">
          <div class="add-address-icon">+</div>
          <div class="add-address-text">
            <h3>Ajouter une nouvelle adresse</h3>
            <p>Livrer à une autre adresse</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="checkout-actions">
      <button type="button" class="btn btn-secondary" (click)="retourConfirmation()">
        Retour
      </button>
      <button type="button" 
              class="btn btn-primary" 
              [disabled]="!selectedAddressId"
              (click)="continuerVersPaiement()">
        Continuer vers le paiement
      </button>
    </div>
  </div>
</div>
