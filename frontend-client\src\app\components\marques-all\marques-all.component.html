<!-- Container principal -->
<div class="marques-wrapper">
  <!-- Version Desktop -->
  <div class="desktop-marques">
    <div class="marques-container">
      <!-- Contenu visible -->
      <div class="marques-visible" *ngIf="marques.length > 0">
        <div class="marques-content">
          <h2>Nos Marques</h2>
          <div *ngIf="marques.length > 0" class="marques-section">
            <ul class="marques-list">
              <li *ngFor="let marque of marques" class="marque-item">
                <div
                  (click)="navigateToMarque(marque.id)"
                  class="marque-link"
                  style="cursor: pointer"
                >
                  <div class="marque-image-container">
                    <img
                      [src]="marque.logo"
                      [alt]="marque.name + ' logo'"
                      class="marque-image"
                      loading="lazy"
                    />
                  </div>
                  <p class="marque-name">{{ marque.name }}</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>