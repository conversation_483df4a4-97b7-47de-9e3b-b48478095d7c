import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { PromotionDto } from '../models/PromotionDto';
import { BaseService } from './core/base.service';
import { environment } from 'src/environments/environment';

@Injectable({ providedIn: 'root' })
export class PromotionService {
  private readonly apiUrl = `${environment.apiUrl}/promotions`;
  constructor(private http: HttpClient) {}
  getAllPromotions(): Observable<PromotionDto[]> {
    return this.http.get<PromotionDto[]>(`${this.apiUrl}/active`);
  }
  getPromotionsForProduct(productId: number): Observable<PromotionDto[]> {
    return this.http.get<PromotionDto[]>(
      `${this.apiUrl}/by-product/${productId}`
    );
  }

  validateCodePromo(code: string): Observable<boolean> {
    return this.http.get<boolean>(`${this.apiUrl}/validate?code=${code}`);
  }

  calculateDiscount(price: number, promo: PromotionDto): number {
    return price * (1 - promo.pourcentageRemise / 100);
  }
  calculateFinalPrice(
    produitId: number,
    codePromo?: string
  ): Observable<number> {
    let url = `${this.apiUrl}/calculate-price?produitId=${produitId}`;
    if (codePromo) url += `&codePromo=${codePromo}`;
    return this.http.get<number>(url);
  }
}
