$mobile-breakpoint: 1400px;
$main-image-size: 500px;
$thumbnail-size: 125px;

.product-container {
  font-family: "Helvetica Neue", Arial, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  color: var(--text-color);
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  @media (max-width: $mobile-breakpoint) {
    padding: 10px;
  }
}

.product-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;

  @media (max-width: $mobile-breakpoint) {
    flex-direction: row;
    flex-wrap: wrap;
  }
}

.favorite-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255 255 255 / 0.7);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  padding: 0 px;
  border: none;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;

  mat-icon {
    margin: 0;
    padding: 0;
    font-size: 25px;
    color: var(--secondary-color);
    transition: color 0.3s;
    line-height: 1;
  }

  &:hover mat-icon,
  .favorite-active {
    color: var(--accent-color);
  }
}

.product-images-complex {
  position: relative;
  display: flex;
  gap: 15px;

  .thumbnails-vertical {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: calc($thumbnail-size * 4);
    overflow-y: auto;
  }

  .thumbnail {
    width: $thumbnail-size;
    height: $thumbnail-size;
    object-fit: cover;
    border: 2px solid var(--border-color);
    background-color: var(--card-background-color-hover);
    cursor: pointer;
    transition: border 0.3s;

    &.active-thumbnail {
      border-color: var(--primary-color);
    }

    &:hover {
      border-color: var(--primary-color);
    }
  }
}

.main-image-wrapper {
  flex-shrink: 0;
  position: relative;
  width: $main-image-size;
  height: $main-image-size;
  border: 2px solid var(--border-color);
  background-color: var(--card-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.product-details {
  flex: 1;
  width: 635px;
  height: $main-image-size;
  background-color: var(--card-background-color);
  padding: 20px;
  border-radius: 4px;
  box-shadow: var(--card-shadow);
  display: flex;
  flex-direction: column;

  .title-badges-wrapper {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-top: 10px;
  }

  .product-title {
    margin: 0;
    flex-shrink: 0;
    font-size: 28px;
    font-weight: 500;
  }

  .badges-wrapper-rate {
    justify-content: flex-end;
    flex-direction: column;
    align-items: flex-end;
    display: flex;
    gap: 12px;
  }

  .badges-wrapper {
    margin-right: 0px;
    display: flex;
    gap: 10px;
  }

  .product-promo-badge,
  .product-new-badge {
    color: #fff;
    padding: 4px 10px;
    font-size: 13px;
    font-weight: bold;
    border-radius: 10px;
    display: inline-flex;
    align-items: center;
  }

  .product-promo-badge {
    background-color: var(--secondary-color);
  }

  .product-new-badge {
    background-color: var(--primary-color);
  }

  .price-section {
    margin-bottom: 20px;
    text-align: left;

    .current-price {
      font-size: 26px;
      font-weight: bold;
      color: var(--primary-color);
    }

    .vat-label {
      font-size: 13px;
      color: var(--secondary-color);
    }

    .outlet-price,
    .original-price {
      margin-top: 5px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      color: var(--secondary-color);
    }

    .original-price {
      color: var(--error-color);
      font-size: 16px;
    }

    .price-strike {
      text-decoration: line-through;
      font-weight: normal;
      color: inherit;
    }

    .discount-badge {
      background-color: var(--error-color);
      color: #fff;
      padding: 2px 6px;
      border-radius: 3px;
      margin-left: 5px;
      font-weight: bold;
      font-size: 13px;
    }

    .tva-rate {
      margin-top: 5px;
      font-size: 13px;
      color: var(--secondary-color);
    }
  }

  .product-description {
    font-size: 16px;
    line-height: 1.4;
    max-height: 120px;
    overflow: auto;
    margin-bottom: 15px;
    color: var(--text-color);
  }

  .rating-section {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-right: 3px;

    mat-icon {
      font-size: 1.5rem;
      color: var(--secondary-color);

      &.filled {
        color: var(--primary-color);
      }
    }

    .review-count {
      font-size: 0.75rem;
      color: var(--text-color);
    }
  }

  .stock-info {
    margin-top: 15px;
    font-size: 14px;

    .stock-label {
      font-weight: bold;
    }

    .stock-quantity {
      color: var(--success-color);
    }
  }

  .product-attributes {
    margin: 20px 0;

    .attribute {
      display: flex;
      align-items: center;
      font-size: 14px;
      margin-bottom: 8px;

      .attribute-label {
        font-weight: bold;
        min-width: 100px;
      }

      .attribute-image {
        width: auto;
        height: 25px;
        object-fit: contain;
        margin-left: 5px;
        border-radius: 10px;
      }
    }
  }

  .add-to-cart-btn {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    background: var(--primary-color);
    color: #fff;
    border: none;
    padding: 12px 20px;
    font-size: 16px;
    width: 100%;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s;

    .icon {
      font-size: 20px;
    }

    &:hover {
      background: var(--primary-color-hover);
    }
  }
}

.delivery-info {
  background-color: var(--card-background-color-hover);
  padding: 10px;
  border-radius: 4px;
  margin: 15px 0;
  font-size: 14px;
  width: 1295px;
  justify-content: space-between;
  display: flex;

  .delivery-left {
    flex: 1 1 50%;
    display: flex;
    flex-direction: column;

    .delivery-text span,
    .free-return {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-bottom: 4px;
    }

    .delivery-by {
      font-weight: bold;
    }

    .delivery-date {
      color: var(--primary-color);
    }

    .delivery-fees {
      color: var(--error-color);
    }

    .free-return {
      color: var(--success-color);
    }

    .icon {
      font-size: 18px;
    }
  }

  .delivery-right {
    flex: 1 1 45%;
    display: flex;
    gap: 15px;
    align-items: flex-start;

    .fournisseur-logo {
      width: 80px;
      height: 80px;
      object-fit: contain;
      border-radius: 8px;
      border: 1px solid var(--border-color);
      background-color: white;
    }

    .fournisseur-info {
      display: flex;
      flex-direction: column;
      gap: 6px;

      .fournisseur-raison {
        font-weight: bold;
        font-size: 16px;
      }

      .fournisseur-email {
        font-style: italic;
        color: var(--secondary-color);
      }

      .fournisseur-adresse {
        font-size: 14px;
        color: var(--text-color);
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .address-icon {
          font-size: 1rem;
          height: 1rem;
          width: 1rem;
          color: var(--primary-color);
        }

        .no-address {
          color: #999;
          font-style: italic;
        }
      }

      .fournisseur-description {
        font-size: 13px;
        color: var(--secondary-color);
        margin-top: 6px;
      }
    }
  }
}

.avis-section {
  width: 1295px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background-color: var(--card-background-color-hover);
  padding: 16px;
  border-radius: 8px;
  margin: 20px 0;
  gap: 20px;

  .avis-left {
    flex: 1;
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    margin-left: 30px;

    .avis-title {
      font-weight: 600;
      font-size: 2rem;
      margin-bottom: 0.75rem;
      color: var(--text-color);
      text-align: left;
    }

    .avis-distribution {
      display: flex;
      flex-direction: column;
      gap: 1px;
      align-items: flex-start;
      margin-left: 30px;
      .avis-row {
        display: flex;
        align-items: center;
        gap: 8px;

        .star-label mat-icon {
          color: #ddd;
          font-size: 1.5rem;
          height: 1.5rem;
          width: 1.5rem;
          line-height: 1;

          &.filled {
            color: #ffc107;
          }
        }

        .avis-count {
          font-size: 0.9rem;
          color: var(--text-color);
        }
      }
    }

    .voir-commentaires {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      margin-top: 10px;
      font-size: 1.3rem;
      color: var(--text-color);
      margin-left: 30px;
      cursor: pointer;
      text-decoration: none;
      mat-icon {
        font-size: 1rem;
      }
    }
  }

  .avis-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
    margin-right: 30px;

    .moyenne-note {
      font-weight: 700;
      font-size: 1.5rem;
      color: var(--text-color);
    }

    .moyenne-stars mat-icon {
      color: var(--accent-color-hover);
      font-size: 2rem;
      line-height: 1;
      height: 2rem;
      width: 2rem;
    }
    .ajout-avis-form {
      margin-top: 16px;
      width: 100%;
      text-align: left;

      .ajout-avis-title {
        font-weight: 600;
        margin-bottom: 4px;
        color: var(--text-color);
      }

      .ajout-note {
        margin-bottom: 8px;
        font-size: 0.9rem;
        display: block;
        align-items: center !important;
        flex-direction: column;

        mat-icon {
          color: var(--accent-color-hover);
          font-size: 1.5rem;
        }
      }

      .ajout-commentaire {
        margin-bottom: 8px;
        display: block;
        align-items: center !important;
        flex-direction: column;

        textarea {
          width: 100%;
          border: 1px solid var(--border-color);
          border-radius: 4px;
          padding: 8px;
          font-size: 0.85rem;
          resize: vertical;
        }
      }

      .p-button {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
      }
    }
  }
}
.collection-fournisseur,
.collection-marque,
.collection-similaire {
  width: 1295px;
  padding: 0.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  background-color: var(--card-background-color);
  justify-content: space-between;
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;

  h2 {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    margin-top: 1.5rem;
  }

  p-carousel {
    width: 100%;

    .p-carousel-container {
      padding: 0 0.5rem;
    }

    .p-carousel-item {
      padding: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 520px;
    }
  }

  .card {
    position: relative;
    border-radius: var(--card-radius);
    background-color: var(--card-background-color);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: transform 0.3s ease, box-shadow 0.3s ease,
      background-color 0.3s ease;

    &:hover {
      background-color: var(--card-background-color-hover);
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }

    .card-image {
      width: 100%;
      position: relative;
      padding-top: 75%;
      overflow: hidden;
      background-color: white;
      height: 100%;

      img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
      }

      &:hover img {
        transform: scale(1.05);
      }

      .product-badges {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        display: flex;
        flex-direction: row;
        gap: 0.3rem;
        z-index: 5;

        .badge-new {
          background-color: var(--primary-color);
          color: white;
          padding: 0.25rem 0.6rem;
          border-radius: 8px;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .badge-discount {
          background-color: var(--secondary-color);
          color: white;
          padding: 0.25rem 0.6rem;
          border-radius: 8px;
          font-size: 0.75rem;
          font-weight: 600;
        }
      }
    }

    .card-content {
      flex-shrink: 0;
      height: 350px !important;
      padding: 16px;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      position: relative;

      h3 {
        margin: 0 0 8px;
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-color);
        cursor: pointer;

        &:hover {
          color: var(--primary-color);
          text-decoration: underline;
        }
      }

      .truncate {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        margin-bottom: 16px;
        color: var(--text-color);
      }

      .product-info-right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        text-align: right;

        .product-brand,
        .product-fournisseur,
        .rating-section {
          text-align: right;
          justify-content: flex-end;
        }
      }

      .product-brand {
        font-weight: 700;
        font-size: 0.75rem;
        color: var(--text-color);
        margin-bottom: 0.25rem;
        margin-right: 3px;
        display: flex;
        align-items: center;
        gap: 0.25rem;
      }

      .product-fournisseur {
        font-style: italic;
        font-size: 0.8rem;
        color: var(--secondary-color);
        margin-bottom: 0.5rem;
        margin-right: 3px;
        display: flex;
        align-items: center;
        gap: 0.25rem;
      }

      .rating-section {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 0.25rem;

        mat-icon {
          font-size: 1rem;
          color: var(--secondary-color);

          &.filled {
            color: var(--primary-color);
          }
        }

        .review-count {
          font-size: 0.75rem;
          color: var(--text-color);
        }
      }

      .price-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        .price-stack {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 0.25rem;

          .price-final {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--text-color);
          }

          .price-intermediate {
            font-size: 0.95rem;
            color: var(--text-color);

            .amount {
              text-decoration: line-through;
              font-weight: 600;
              margin-left: 0.3rem;
            }
          }

          .price-original {
            font-size: 0.85rem;
            color: var(--text-color);

            .amount {
              text-decoration: line-through;
              font-weight: 400;
              margin-left: 0.3rem;
            }
          }
        }
      }

      .button-container {
        margin-top: 25px;
        position: absolute;
        bottom: 20px;
        right: 20px;
        display: flex;
        gap: 0.75rem;
        justify-content: flex-end;

        .p-button {
          min-width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          transition: all 0.3s ease;
          cursor: pointer;

          &:hover {
            transform: translateY(-2px);

            .pi {
              transform: scale(1.1);
            }
          }

          &.p-button-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;

            &:hover {
              background-color: var(--primary-color-hover);
            }
          }

          &.p-button-secondary {
            background-color: transparent;
            border: 2px solid var(--secondary-color);
            color: var(--secondary-color);

            &:hover {
              background-color: var(--secondary-color);
              color: white;
            }
          }

          .pi {
            transition: transform 0.2s ease;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .card {
      height: 480px;

      .button-container {
        .p-button {
          min-width: 36px;
          height: 36px;
        }
      }
    }
  }
}

@media (max-width: $mobile-breakpoint) {
  .product-content {
    flex-direction: column;
    align-items: center;
  }

  .product-images-complex {
    flex-direction: row;
    align-items: flex-start;
    gap: 10px;

    .thumbnails-vertical {
      flex-direction: column;
      max-height: calc($thumbnail-size * 4);
      overflow-y: auto;

      .thumbnail {
        width: $thumbnail-size;
        height: $thumbnail-size;
      }
    }

    .main-image-wrapper {
      width: $main-image-size;
      height: $main-image-size;
    }
  }

  .product-details {
    max-width: 100%;
    padding: 15px;

    .product-description {
      max-height: 100px;
      font-size: 14px;
    }
  }

  .delivery-info {
    width: 635px !important;
    padding: 15px;
    flex-direction: column;

    .delivery-left,
    .delivery-right {
      flex: 1 1 100%;
      margin-bottom: 15px;
    }

    .delivery-right {
      flex-direction: row;
      justify-content: flex-start;
    }
  }
  .avis-section {
    width: 635px !important;
    padding: 15px;
    flex-direction: column;
    align-items: center;
    margin: 15px 0;
    gap: 15px;

    .avis-left {
      margin-left: 0;
      align-items: flex-start;

      .avis-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
      }

      .avis-distribution {
        margin-left: 0;

        .avis-row {
          gap: 6px;

          .star-label mat-icon {
            font-size: 1.2rem;
            height: 1.2rem;
            width: 1.2rem;
            color: #ddd;

            &.filled {
              color: #ffc107;
            }
          }

          .avis-count {
            font-size: 0.85rem;
          }
        }
      }

      .voir-commentaires {
        font-size: 1rem;
        margin-left: 0;
        mat-icon {
          font-size: 0.9rem;
        }
      }
    }

    .avis-right {
      margin-right: 0;
      align-items: flex-start;
      width: 100%;

      .moyenne-note {
        font-size: 1.3rem;
      }

      .moyenne-stars mat-icon {
        font-size: 1.5rem;
        height: 1.5rem;
        width: 1.5rem;
      }

      .ajout-avis-form {
        margin-top: 12px;
        width: 100%;
        text-align: left;
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .ajout-note {
          font-size: 0.85rem;
          margin-left: 0;
          margin-bottom: 0.5rem;
          mat-icon {
            font-size: 1.2rem;
          }
        }

        .ajout-commentaire {
          margin-left: 0;
          margin-bottom: 0.5rem;
          width: 100%;

          textarea {
            font-size: 0.8rem;
            width: 100%;
          }
        }

        .p-button {
          font-size: 0.75rem;
          padding: 0.35rem 0.6rem;
          margin-left: 0;
          margin-top: 0.5rem;
          align-self: flex-start;
        }
      }
    }

    .avis-score {
      margin-bottom: 10px;
    }

    // Styles mobile pour la section des avis inline
    .tous-les-avis-section {
      margin-top: 0.75rem;
      padding: 0.75rem;

      .avis-list-container {
        h3 {
          font-size: 1rem;
          margin-bottom: 0.75rem;
        }

        .avis-cards-grid {
          gap: 0.75rem;

          .avis-card-inline {
            padding: 0.75rem;

            .avis-card-header-inline {
              flex-direction: column;
              align-items: flex-start;
              gap: 0.5rem;

              .client-info-inline {
                gap: 0.4rem;

                .client-avatar-inline {
                  width: 28px;
                  height: 28px;
                  font-size: 0.8rem;
                }

                .client-details-inline {
                  .client-name-inline {
                    font-size: 0.85rem;
                  }

                  .avis-date-inline {
                    font-size: 0.7rem;
                  }
                }
              }

              .avis-rating-inline {
                .stars-container-inline {
                  .star-icon-inline {
                    font-size: 0.9rem;
                    height: 0.9rem;
                    width: 0.9rem;
                  }
                }

                .note-value-inline {
                  font-size: 0.7rem;
                }
              }
            }

            .avis-content-inline p {
              font-size: 0.8rem;
            }
          }
        }
      }
    }

    .avis-buttons {
      justify-content: center;
      width: 100%;
    }
  }
  .collection-fournisseur,
  .collection-marque,
  .collection-similaire {
    width: 635px !important;
    padding: 0.5rem;
    box-sizing: border-box;
    margin-bottom: 20px;
  }
  .collection-fournisseur,
  .collection-marque,
  .collection-similaire h2 {
    font-size: 1.2rem;
  }
  .card-content h3 {
    font-size: 1.05rem !important;
  }
  .p-carousel-item {
    height: auto !important;
    min-height: 480px;
  }
  .truncate {
    font-size: 0.85rem;
  }
  .card {
    height: auto !important;
    max-width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
    flex-direction: column;
  }
  .product-brand,
  .product-fournisseur,
  .rating-section {
    font-size: 0.75rem;
  }
  .price-final {
    font-size: 1.1rem !important;
  }

  .price-intermediate,
  .price-original {
    font-size: 0.75rem !important;
  }

  .p-button {
    font-size: 0.85rem !important;
    bottom: 10px !important;
    right: 10px !important;
  }
  .card-content {
    height: auto !important;
    padding: 12px 16px;
    overflow-wrap: break-word;
  }

  .card-image {
    padding-top: 70%;
    width: 100%;
    height: auto !important;
  }

  .button-container {
    bottom: 15px !important;
    right: 15px !important;
  }
}

// Modal styles améliorée
.avis-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.avis-modal-content {
  background: white;
  border-radius: 16px;
  width: 95%;
  max-width: 700px;
  max-height: 85vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

.avis-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);

  .modal-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;

    h2 {
      margin: 0;
      color: var(--primary-color);
      font-size: 1.5rem;
      font-weight: 600;
    }

    .avis-count-badge {
      background: var(--primary-color);
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.85rem;
      font-weight: 500;
    }
  }

  .modal-close-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #666;

    &:hover {
      background-color: #f5f5f5;
      color: var(--primary-color);
      transform: scale(1.1);
    }

    mat-icon {
      font-size: 1.5rem;
      height: 1.5rem;
      width: 1.5rem;
    }
  }
}

.avis-modal-body {
  max-height: calc(85vh - 100px);
  overflow-y: auto;
  padding: 0;

  .empty-avis-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #666;

    .empty-icon {
      font-size: 4rem;
      height: 4rem;
      width: 4rem;
      color: #ddd;
      margin-bottom: 1rem;
    }

    h3 {
      margin: 1rem 0 0.5rem 0;
      color: var(--primary-color);
      font-weight: 500;
    }

    p {
      margin: 0;
      font-size: 1rem;
      line-height: 1.5;
    }
  }
}

.avis-list {
  padding: 1rem 0;

  .avis-card {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #f5f5f5;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #fafafa;
    }

    &:last-child {
      border-bottom: none;
    }

    .avis-card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1rem;

      .client-info {
        display: flex;
        align-items: center;
        gap: 1rem;

        .client-avatar {
          width: 45px;
          height: 45px;
          border-radius: 50%;
          background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 1.1rem;
          flex-shrink: 0;
        }

        .client-details {
          .client-name {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 1rem;
            margin-bottom: 0.25rem;
          }

          .avis-date {
            color: #888;
            font-size: 0.9rem;
          }
        }
      }

      .avis-rating-display {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 0.25rem;

        .stars-container {
          display: flex;
          gap: 2px;

          .star-icon {
            color: #ddd;
            font-size: 1.3rem;
            height: 1.3rem;
            width: 1.3rem;

            &.filled {
              color: #ffc107;
            }
          }
        }

        .note-value {
          font-weight: 600;
          color: var(--primary-color);
          font-size: 0.9rem;
        }
      }
    }

    .avis-content {
      p {
        margin: 0;
        color: var(--text-color);
        line-height: 1.6;
        font-size: 1rem;
      }

      &.no-comment p {
        color: #999;
        font-style: italic;
      }
    }
  }
}

// Design simple pour l'affichage des avis inline
.tous-les-avis-section {
  margin-top: 1rem;
  padding: 1rem;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;

  .avis-list-container {
    h3 {
      color: var(--primary-color);
      font-size: 1.1rem;
      font-weight: 500;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid var(--accent-color);
    }

    .avis-cards-grid {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .avis-card-inline {
        padding: 1rem;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        background: #fafafa;

        .avis-card-header-inline {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.75rem;

          .client-info-inline {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .client-avatar-inline {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: var(--primary-color);
              color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: 600;
              font-size: 0.9rem;
            }

            .client-details-inline {
              .client-name-inline {
                font-weight: 600;
                color: var(--primary-color);
                font-size: 0.9rem;
                margin-bottom: 0.1rem;
              }

              .avis-date-inline {
                color: var(--secondary-color);
                font-size: 0.75rem;
              }
            }
          }

          .avis-rating-inline {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .stars-container-inline {
              display: flex;
              gap: 1px;

              .star-icon-inline {
                color: #ddd;
                font-size: 1rem;
                height: 1rem;
                width: 1rem;

                &.filled {
                  color: var(--accent-color);
                }
              }
            }

            .note-value-inline {
              font-weight: 600;
              color: var(--primary-color);
              font-size: 0.8rem;
            }
          }
        }

        .avis-content-inline {
          p {
            margin: 0;
            color: var(--text-color);
            line-height: 1.4;
            font-size: 0.9rem;
          }

          &.no-comment-inline p {
            color: var(--secondary-color);
            font-style: italic;
          }
        }
      }
    }
  }
}

// Styles pour les collections de produits avec taille fixe
.collection-fournisseur,
.collection-marque,
.collection-similaire {
  ::ng-deep .p-carousel {
    .p-carousel-content {
      .p-carousel-container {
        .p-carousel-items-content {
          .p-carousel-items-container {
            display: flex !important;

            .p-carousel-item {
              // Taille fixe pour desktop (5 produits par vue)
              flex: 0 0 calc(20% - 1rem) !important;
              max-width: calc(20% - 1rem) !important;
              min-width: calc(20% - 1rem) !important;
              margin: 0 0.5rem;

              @media (max-width: 768px) {
                // Taille fixe pour mobile (2 produits par vue)
                flex: 0 0 calc(50% - 1rem) !important;
                max-width: calc(50% - 1rem) !important;
                min-width: calc(50% - 1rem) !important;
              }

              .product-card {
                width: 100%;
                height: auto;
                display: flex;
                flex-direction: column;

                .product-image {
                  width: 100%;
                  height: 200px;
                  object-fit: cover;
                  flex-shrink: 0;

                  @media (max-width: 768px) {
                    height: 150px;
                  }
                }

                .product-info {
                  flex: 1;
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  padding: 0.5rem;
                }
              }
            }
          }
        }
      }
    }
  }
}

// Styles additionnels pour forcer la taille fixe des produits
::ng-deep {
  .collection-fournisseur,
  .collection-marque,
  .collection-similaire {
    .p-carousel-item {
      // Force la taille fixe même avec moins de produits
      width: 20% !important;
      min-width: 20% !important;
      max-width: 20% !important;

      @media (max-width: 768px) {
        width: 50% !important;
        min-width: 50% !important;
        max-width: 50% !important;
      }
    }

    .product-card {
      height: 350px !important;

      @media (max-width: 768px) {
        height: 280px !important;
      }

      img {
        height: 200px !important;
        object-fit: cover !important;

        @media (max-width: 768px) {
          height: 150px !important;
        }
      }
    }
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.avis-rating {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-bottom: 10px;

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
    color: #ddd;

    &.filled {
      color: #ffc107;
    }
  }

  .note-text {
    margin-left: 8px;
    font-weight: 600;
    color: #333;
  }
}

.avis-comment {
  color: #555;
  line-height: 1.5;

  &.no-comment {
    color: #999;
    font-style: italic;
  }
}
