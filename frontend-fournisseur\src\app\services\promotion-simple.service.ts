import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { PromotionSimpleCreateDto, PromotionSimpleDto } from '../models/promotion-simple.model';

@Injectable({
  providedIn: 'root'
})
export class PromotionSimpleService {
  private apiUrl = `${environment.apiUrl}/Promotions`;

  constructor(private http: HttpClient) { }

  /**
   * Headers avec token d'authentification
   */
  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('auth_token'); // Utiliser la même clé que AuthService
    return new HttpHeaders({
      'Authorization': token ? `Bearer ${token}` : '',
      'Content-Type': 'application/json'
    });
  }

  // Récupérer toutes les promotions du fournisseur connecté
  getMyPromotions(): Observable<PromotionSimpleDto[]> {
    console.log('🔄 PromotionSimpleService: Récupération des promotions...');

    return this.http.get<PromotionSimpleDto[]>(`${this.apiUrl}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      map(promotions => {
        console.log(`✅ PromotionSimpleService: ${promotions.length} promotions récupérées:`, promotions);
        return promotions || [];
      }),
      catchError(error => {
        console.error('❌ PromotionSimpleService: Erreur lors de la récupération des promotions:', error);
        // Retourner un tableau vide en cas d'erreur
        return of([]);
      })
    );
  }

  // Créer une nouvelle promotion
  create(promotion: PromotionSimpleCreateDto): Observable<PromotionSimpleDto> {
    console.log('🔄 PromotionSimpleService: Création d\'une nouvelle promotion', promotion);

    return this.http.post<PromotionSimpleDto>(this.apiUrl, promotion, {
      headers: this.getAuthHeaders()
    }).pipe(
      map(newPromotion => {
        console.log('✅ PromotionSimpleService: Promotion créée:', newPromotion);
        return newPromotion;
      }),
      catchError(error => {
        console.error('❌ PromotionSimpleService: Erreur lors de la création:', error);
        throw error;
      })
    );
  }

  // Activer/désactiver une promotion
  toggle(id: number): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/toggle`, {}, {
      headers: this.getAuthHeaders()
    });
  }

  // Supprimer une promotion
  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`, {
      headers: this.getAuthHeaders()
    });
  }

  // Modifier une promotion
  update(id: number, promotion: Partial<PromotionSimpleCreateDto>): Observable<PromotionSimpleDto> {
    const url = `${this.apiUrl}/${id}`;
    console.log('🔄 PromotionSimpleService: Modification de la promotion');
    console.log('🎯 ID:', id);
    console.log('📝 Données:', promotion);
    console.log('🌐 URL:', url);
    console.log('🔑 Headers:', this.getAuthHeaders());

    return this.http.put<PromotionSimpleDto>(url, promotion, {
      headers: this.getAuthHeaders()
    }).pipe(
      map(updatedPromotion => {
        console.log('✅ PromotionSimpleService: Promotion modifiée avec succès:', updatedPromotion);
        return updatedPromotion;
      }),
      catchError(error => {
        console.error('❌ PromotionSimpleService: Erreur lors de la modification');
        console.error('❌ URL appelée:', url);
        console.error('❌ Données envoyées:', promotion);
        console.error('❌ Erreur complète:', error);
        console.error('❌ Statut:', error.status);
        console.error('❌ Message:', error.message);
        console.error('❌ Corps de l\'erreur:', error.error);
        throw error;
      })
    );
  }

  // Récupérer une promotion par ID
  getById(id: number): Observable<PromotionSimpleDto> {
    console.log('🔄 PromotionSimpleService: Récupération de la promotion', id);

    return this.http.get<PromotionSimpleDto>(`${this.apiUrl}/${id}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      map(promotion => {
        console.log('✅ PromotionSimpleService: Promotion récupérée:', promotion);
        return promotion;
      }),
      catchError(error => {
        console.error('❌ PromotionSimpleService: Erreur lors de la récupération:', error);
        throw error;
      })
    );
  }

  // Récupérer les statistiques d'une promotion
  getStats(id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/${id}/stats`);
  }
}
