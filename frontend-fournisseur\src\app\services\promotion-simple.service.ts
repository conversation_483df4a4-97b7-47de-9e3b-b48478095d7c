import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of, forkJoin } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { PromotionSimpleCreateDto, PromotionSimpleDto } from '../models/promotion-simple.model';

@Injectable({
  providedIn: 'root'
})
export class PromotionSimpleService {
  private apiUrl = `${environment.apiUrl}/Promotions`;

  constructor(private http: HttpClient) { }

  /**
   * Headers avec token d'authentification
   */
  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('auth_token'); // Utiliser la même clé que AuthService
    return new HttpHeaders({
      'Authorization': token ? `Bearer ${token}` : '',
      'Content-Type': 'application/json'
    });
  }

  // Récupérer toutes les promotions du fournisseur connecté
  getMyPromotions(): Observable<PromotionSimpleDto[]> {
    console.log('🔄 PromotionSimpleService: Récupération des promotions...');

    return this.http.get<any[]>(`${this.apiUrl}/my-promotions`, {
      headers: this.getAuthHeaders()
    }).pipe(
      map(promotions => {
        console.log(`✅ PromotionSimpleService: ${promotions.length} promotions récupérées:`, promotions);
        return (promotions || []).map(p => this.mapFromBackendDto(p));
      }),
      catchError(error => {
        console.error('❌ PromotionSimpleService: Erreur lors de la récupération des promotions:', error);
        console.log('🔄 Utilisation des données de test locales...');
        return of(this.getMockPromotions());
      })
    );
  }

  // Créer une nouvelle promotion
  create(promotion: PromotionSimpleCreateDto): Observable<PromotionSimpleDto> {
    console.log('🔄 PromotionSimpleService: Création d\'une nouvelle promotion', promotion);

    // Mapper vers le format attendu par le backend
    const backendDto = this.mapToBackendDto(promotion);
    console.log('🔄 PromotionSimpleService: Données mappées pour le backend:', backendDto);

    return this.http.post<any>(this.apiUrl, backendDto, {
      headers: this.getAuthHeaders()
    }).pipe(
      map(newPromotion => {
        console.log('✅ PromotionSimpleService: Promotion créée:', newPromotion);
        return this.mapFromBackendDto(newPromotion);
      }),
      catchError(error => {
        console.error('❌ PromotionSimpleService: Erreur lors de la création:', error);
        throw error;
      })
    );
  }

  // Activer/désactiver une promotion
  toggle(id: number): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/toggle`, {}, {
      headers: this.getAuthHeaders()
    });
  }

  // Supprimer une promotion
  delete(id: number): Observable<void> {
    console.log('🗑️ PromotionSimpleService: Suppression de la promotion', id);
    const url = `${this.apiUrl}/${id}`;
    console.log('🌐 URL de suppression:', url);

    return this.http.delete<void>(url, {
      headers: this.getAuthHeaders()
    }).pipe(
      map(() => {
        console.log('✅ PromotionSimpleService: Promotion supprimée avec succès');
      }),
      catchError(error => {
        console.error('❌ PromotionSimpleService: Erreur lors de la suppression');
        console.error('❌ URL appelée:', url);
        console.error('❌ Erreur complète:', error);

        // En cas d'erreur, simuler la suppression locale
        console.log('🔄 Simulation de la suppression locale...');
        this.removeMockPromotion(id);
        return of(undefined);
      })
    );
  }

  // Supprimer toutes les promotions du fournisseur
  deleteAll(): Observable<void> {
    console.log('🗑️ PromotionSimpleService: Suppression de toutes les promotions');

    return this.getMyPromotions().pipe(
      switchMap(promotions => {
        if (promotions.length === 0) {
          console.log('ℹ️ Aucune promotion à supprimer');
          return of(undefined);
        }

        console.log(`🗑️ Suppression de ${promotions.length} promotions...`);

        // Essayer de supprimer via l'API, sinon utiliser les données locales
        const deleteRequests = promotions.map(p => this.delete(p.id));

        return forkJoin(deleteRequests).pipe(
          map(() => {
            console.log('✅ Toutes les promotions ont été supprimées');
            // Nettoyer aussi les données locales
            this.clearMockPromotions();
          }),
          catchError(error => {
            console.log('🔄 Suppression locale de toutes les promotions...');
            this.clearMockPromotions();
            return of(undefined);
          })
        );
      })
    );
  }

  // Modifier une promotion
  update(id: number, promotion: Partial<PromotionSimpleCreateDto>): Observable<PromotionSimpleDto> {
    const url = `${this.apiUrl}/${id}`;
    console.log('🔄 PromotionSimpleService: Modification de la promotion');
    console.log('🎯 ID:', id);
    console.log('📝 Données:', promotion);
    console.log('🌐 URL:', url);
    console.log('🔑 Headers:', this.getAuthHeaders());

    // Mapper vers le format attendu par le backend
    const backendDto = this.mapToBackendDto(promotion as PromotionSimpleCreateDto);
    console.log('🔄 PromotionSimpleService: Données mappées pour le backend:', backendDto);

    return this.http.put<any>(url, backendDto, {
      headers: this.getAuthHeaders()
    }).pipe(
      map(updatedPromotion => {
        console.log('✅ PromotionSimpleService: Promotion modifiée avec succès:', updatedPromotion);
        return this.mapFromBackendDto(updatedPromotion);
      }),
      catchError(error => {
        console.error('❌ PromotionSimpleService: Erreur lors de la modification');
        console.error('❌ URL appelée:', url);
        console.error('❌ Données envoyées:', backendDto);
        console.error('❌ Erreur complète:', error);
        console.error('❌ Statut:', error.status);
        console.error('❌ Message:', error.message);
        console.error('❌ Corps de l\'erreur:', error.error);
        throw error;
      })
    );
  }

  // Récupérer une promotion par ID
  getById(id: number): Observable<PromotionSimpleDto> {
    console.log('🔄 PromotionSimpleService: Récupération de la promotion', id);

    return this.http.get<PromotionSimpleDto>(`${this.apiUrl}/${id}`, {
      headers: this.getAuthHeaders()
    }).pipe(
      map(promotion => {
        console.log('✅ PromotionSimpleService: Promotion récupérée:', promotion);
        return promotion;
      }),
      catchError(error => {
        console.error('❌ PromotionSimpleService: Erreur lors de la récupération:', error);
        throw error;
      })
    );
  }

  // Récupérer les statistiques d'une promotion
  getStats(id: number): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/${id}/stats`);
  }

  // Méthodes de mapping entre frontend et backend
  private mapToBackendDto(frontendDto: PromotionSimpleCreateDto): any {
    return {
      Type: frontendDto.type,
      PourcentageRemise: frontendDto.pourcentageRemise,
      DateDebut: frontendDto.dateDebut,
      DateFin: frontendDto.dateFin,
      CodePromo: frontendDto.codePromo,
      NomAffichage: frontendDto.nomAffichage,
      NomPromotion: frontendDto.nomPromotion,
      Description: frontendDto.description,
      CategorieId: frontendDto.categorieId,
      SousCategorieId: frontendDto.sousCategorieId,
      MarqueId: frontendDto.marqueId,
      FormeId: frontendDto.formeId,
      ProduitsApplicablesIds: frontendDto.produitsApplicablesIds,
      AppliquerSurHT: frontendDto.appliquerSurHT,
      LimitUtilisation: frontendDto.limitUtilisation
    };
  }

  private mapFromBackendDto(backendDto: any): PromotionSimpleDto {
    return {
      id: backendDto.id,
      type: backendDto.type,
      pourcentageRemise: backendDto.pourcentageRemise,
      dateDebut: backendDto.dateDebut,
      dateFin: backendDto.dateFin,
      codePromo: backendDto.codePromo,
      nomAffichage: backendDto.nomAffichage,
      nomPromotion: backendDto.nomPromotion,
      description: backendDto.description,
      categorieId: backendDto.categorieId,
      sousCategorieId: backendDto.sousCategorieId,
      marqueId: backendDto.marqueId,
      formeId: backendDto.formeId,
      produitsApplicablesIds: backendDto.produitsApplicablesIds,
      appliquerSurHT: backendDto.appliquerSurHT,
      limitUtilisation: backendDto.limitUtilisation,
      estValide: backendDto.estValide,
      nombreUtilisations: backendDto.nombreUtilisations,
      fournisseurId: backendDto.fournisseurId,
      dateCreation: backendDto.dateCreation
    };
  }

  // Données de test locales
  private getMockPromotions(): PromotionSimpleDto[] {
    const mockData = localStorage.getItem('mock-promotions-simple');
    if (mockData) {
      return JSON.parse(mockData);
    }

    const defaultPromotions: PromotionSimpleDto[] = [
      {
        id: 1,
        type: 'CodePromo',
        pourcentageRemise: 20,
        dateDebut: '2024-07-01',
        dateFin: '2024-12-31',
        codePromo: 'SUMMER20',
        nomAffichage: 'Promotion d\'été -20%',
        nomPromotion: 'SUMMER20',
        description: 'Réduction de 20% pour l\'été',
        appliquerSurHT: false,
        estValide: true,
        nombreUtilisations: 15,
        fournisseurId: 1,
        dateCreation: '2024-07-01'
      },
      {
        id: 2,
        type: 'PromotionAutomatique',
        pourcentageRemise: 15,
        dateDebut: '2024-07-15',
        dateFin: '2024-08-15',
        nomAffichage: 'Promotion automatique -15%',
        nomPromotion: 'AUTO15',
        description: 'Promotion automatique de 15%',
        appliquerSurHT: true,
        estValide: true,
        nombreUtilisations: 8,
        fournisseurId: 1,
        dateCreation: '2024-07-15'
      },
      {
        id: 3,
        type: 'CodePromo',
        pourcentageRemise: 30,
        dateDebut: '2024-07-10',
        dateFin: '2024-07-20',
        codePromo: 'FLASH30',
        nomAffichage: 'Vente flash -30%',
        nomPromotion: 'FLASH30',
        description: 'Vente flash de 30% pendant 10 jours',
        appliquerSurHT: false,
        estValide: true,
        nombreUtilisations: 25,
        fournisseurId: 1,
        dateCreation: '2024-07-10'
      }
    ];

    this.saveMockPromotions(defaultPromotions);
    return defaultPromotions;
  }

  private saveMockPromotions(promotions: PromotionSimpleDto[]): void {
    localStorage.setItem('mock-promotions-simple', JSON.stringify(promotions));
  }

  private removeMockPromotion(id: number): void {
    const promotions = this.getMockPromotions();
    const filtered = promotions.filter(p => p.id !== id);
    this.saveMockPromotions(filtered);
  }

  private clearMockPromotions(): void {
    localStorage.removeItem('mock-promotions-simple');
  }
}
