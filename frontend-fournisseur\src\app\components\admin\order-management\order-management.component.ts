import { Component, OnInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AdminService } from '../../../services/admin.service';

interface CommandeAdmin {
  id: number;
  numeroCommande: string;
  clientNom: string;
  clientEmail: string;
  fournisseurNom: string;
  montantTotal: number;
  statut: string;
  dateCommande: string;
  nombreArticles: number;
}

interface CommandeAvecFournisseur {
  id: number;
  numeroCommande: string;
  clientNom: string;
  clientEmail: string;
  montantTotal: number;
  statut: string;
  dateCommande: string;
  nombreArticles: number;
  commandesFournisseur: CommandeFournisseurDetail[];
}

interface CommandeFournisseurDetail {
  id: number;
  reference: string;
  fournisseurId: number;
  fournisseurNom: string;
  fournisseurEmail: string;
  montantTotal: number;
  statut: string;
  dateCreation: string;
  dateLivraison?: string;
  numeroBonLivraison?: string;
  fraisLivraison: number;
  lignes: LigneCommandeFournisseurDetail[];
}

interface LigneCommandeFournisseurDetail {
  id: number;
  produitId: number;
  produitNom: string;
  referenceProduit: string;
  quantite: number;
  prixUnitaire: number;
  montantLigne: number;
}

@Component({
  selector: 'app-order-management',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './order-management.component.html',
  styleUrls: ['./order-management.component.css']
})
export class OrderManagementComponent implements OnInit {
  // Angular 19: Signals
  commandes = signal<CommandeAdmin[]>([]);
  commandesAvecFournisseur = signal<CommandeAvecFournisseur[]>([]);
  isLoading = signal(false);
  error = signal('');
  searchTerm = signal('');
  selectedStatus = signal('');
  dateDebut = signal('');
  dateFin = signal('');
  currentPage = signal(1);
  pageSize = signal(10);
  totalItems = signal(0);
  showDetailedView = signal(false); // Commencer par la vue simple
  expandedOrderId = signal<number | null>(null); // Pour gérer l'expansion des détails

  // Computed signals pour la vue simple
  filteredCommandes = computed(() => {
    const orders = this.commandes();
    const search = this.searchTerm().toLowerCase();
    const status = this.selectedStatus();
    const dateDebut = this.dateDebut();
    const dateFin = this.dateFin();

    return orders.filter(order => {
      const matchesSearch = !search ||
        order.numeroCommande.toLowerCase().includes(search) ||
        order.clientNom.toLowerCase().includes(search) ||
        order.clientEmail.toLowerCase().includes(search);

      const matchesStatus = !status || order.statut === status;

      let matchesDate = true;
      if (dateDebut || dateFin) {
        const orderDate = new Date(order.dateCommande);
        if (dateDebut) {
          const startDate = new Date(dateDebut);
          matchesDate = matchesDate && orderDate >= startDate;
        }
        if (dateFin) {
          const endDate = new Date(dateFin);
          endDate.setHours(23, 59, 59, 999); // Fin de journée
          matchesDate = matchesDate && orderDate <= endDate;
        }
      }

      return matchesSearch && matchesStatus && matchesDate;
    });
  });

  // Computed signals pour la vue détaillée
  filteredCommandesAvecFournisseur = computed(() => {
    const orders = this.commandesAvecFournisseur();
    const search = this.searchTerm().toLowerCase();
    const status = this.selectedStatus();
    const dateDebut = this.dateDebut();
    const dateFin = this.dateFin();

    return orders.filter(order => {
      const matchesSearch = !search ||
        order.numeroCommande.toLowerCase().includes(search) ||
        order.clientNom.toLowerCase().includes(search) ||
        order.clientEmail.toLowerCase().includes(search);

      const matchesStatus = !status || order.statut === status;

      let matchesDate = true;
      if (dateDebut || dateFin) {
        const orderDate = new Date(order.dateCommande);
        if (dateDebut) {
          const startDate = new Date(dateDebut);
          matchesDate = matchesDate && orderDate >= startDate;
        }
        if (dateFin) {
          const endDate = new Date(dateFin);
          endDate.setHours(23, 59, 59, 999); // Fin de journée
          matchesDate = matchesDate && orderDate <= endDate;
        }
      }

      return matchesSearch && matchesStatus && matchesDate;
    });
  });

  totalPages = computed(() => Math.ceil(this.totalItems() / this.pageSize()));

  // Statuts disponibles
  statuts = [
    'EnAttente',
    'Confirmee',
    'EnPreparation',
    'Expediee',
    'Livree',
    'Annulee'
  ];

  // Expose Math to template
  Math = Math;

  constructor(private adminService: AdminService) {}

  ngOnInit(): void {
    this.loadCommandes();
  }

  loadCommandes(): void {
    this.isLoading.set(true);
    this.error.set('');

    if (this.showDetailedView()) {
      // Charger les commandes avec détails fournisseur
      this.adminService.getCommandesAvecFournisseur().subscribe({
        next: (response) => {
          this.commandesAvecFournisseur.set(response.data || []);
          this.totalItems.set(response.total || 0);
          this.isLoading.set(false);
        },
        error: (error) => {
          console.error('Erreur lors du chargement des commandes avec fournisseur:', error);
          this.error.set('Erreur lors du chargement des commandes avec fournisseur');
          this.isLoading.set(false);
        }
      });
    } else {
      // Charger les commandes simples
      const params: any = {
        page: this.currentPage(),
        pageSize: this.pageSize()
      };

      if (this.selectedStatus()) {
        params.statut = this.selectedStatus();
      }

      if (this.dateDebut()) {
        params.dateDebut = new Date(this.dateDebut());
      }

      if (this.dateFin()) {
        params.dateFin = new Date(this.dateFin());
      }

      this.adminService.getCommandes(params).subscribe({
        next: (response) => {
          this.commandes.set(response.data || []);
          this.totalItems.set(response.total || 0);
          this.isLoading.set(false);
        },
        error: (error) => {
          console.error('Erreur lors du chargement des commandes:', error);
          this.error.set('Erreur lors du chargement des commandes');
          this.isLoading.set(false);
        }
      });
    }
  }

  annulerCommande(commande: CommandeAdmin): void {
    const motif = prompt(`Motif d'annulation de la commande ${commande.numeroCommande}:`);
    
    if (motif) {
      this.adminService.annulerCommande(commande.id, motif).subscribe({
        next: () => {
          // Mettre à jour localement
          const orders = this.commandes();
          const index = orders.findIndex(c => c.id === commande.id);
          if (index !== -1) {
            orders[index] = { ...orders[index], statut: 'Annulee' };
            this.commandes.set([...orders]);
          }
          alert('Commande annulée avec succès');
        },
        error: (error) => {
          console.error('Erreur lors de l\'annulation:', error);
          alert('Erreur lors de l\'annulation de la commande');
        }
      });
    }
  }

  onSearch(): void {
    this.currentPage.set(1);
    this.loadCommandes();
  }

  onFilterChange(): void {
    this.currentPage.set(1);
    this.loadCommandes();
  }

  onDateFilterChange(): void {
    this.currentPage.set(1);
    this.loadCommandes();
  }

  onPageChange(page: number): void {
    this.currentPage.set(page);
    this.loadCommandes();
  }

  getStatusClass(statut: string): string {
    switch (statut) {
      case 'EnAttente': return 'status-pending';
      case 'Confirmee': return 'status-confirmed';
      case 'EnPreparation': return 'status-preparing';
      case 'Expediee': return 'status-shipped';
      case 'Livree': return 'status-delivered';
      case 'Annulee': return 'status-cancelled';
      default: return 'status-default';
    }
  }

  getStatusText(statut: string): string {
    switch (statut) {
      case 'EnAttente': return 'En attente';
      case 'Confirmee': return 'Confirmée';
      case 'EnPreparation': return 'En préparation';
      case 'Expediee': return 'Expédiée';
      case 'Livree': return 'Livrée';
      case 'Annulee': return 'Annulée';
      default: return statut;
    }
  }

  canCancelOrder(statut: string): boolean {
    return ['EnAttente', 'Confirmee', 'EnPreparation'].includes(statut);
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  onSearch(): void {
    // Le signal se met à jour automatiquement
  }

  onFilterChange(): void {
    // Le signal se met à jour automatiquement
  }

  onDateFilterChange(): void {
    // Le signal se met à jour automatiquement
  }

  refresh(): void {
    this.loadCommandes();
  }

  exportOrders(): void {
    // TODO: Implémenter l'export des commandes
    alert('Fonctionnalité d\'export en cours de développement');
  }

  clearFilters(): void {
    this.searchTerm.set('');
    this.selectedStatus.set('');
    this.dateDebut.set('');
    this.dateFin.set('');
    this.currentPage.set(1);
    this.loadCommandes();
  }

  viewOrderDetails(commande: CommandeAdmin | CommandeAvecFournisseur): void {
    if (this.showDetailedView()) {
      // En vue détaillée, toggle l'expansion
      const currentExpanded = this.expandedOrderId();
      this.expandedOrderId.set(currentExpanded === commande.id ? null : commande.id);
    } else {
      // En vue simple, passer en vue détaillée et charger les données
      this.showDetailedView.set(true);
      this.loadCommandes();
    }
  }

  isOrderExpanded(orderId: number): boolean {
    return this.expandedOrderId() === orderId;
  }

  // Statistiques basées sur la vue actuelle
  getTotalCommandes(): number {
    return this.showDetailedView()
      ? this.filteredCommandesAvecFournisseur().length
      : this.filteredCommandes().length;
  }

  getTotalAmount(): string {
    const total = this.showDetailedView()
      ? this.filteredCommandesAvecFournisseur().reduce((sum, c) => sum + c.montantTotal, 0)
      : this.filteredCommandes().reduce((sum, c) => sum + c.montantTotal, 0);
    return this.formatPrice(total);
  }

  toggleView(): void {
    this.showDetailedView.set(!this.showDetailedView());
    this.loadCommandes();
  }

  getCommandeFournisseurStatusClass(statut: string): string {
    switch (statut) {
      case 'EnAttente': return 'status-pending';
      case 'Confirmee': return 'status-confirmed';
      case 'EnPreparation': return 'status-preparing';
      case 'Expediee': return 'status-shipped';
      case 'Livree': return 'status-delivered';
      case 'Annulee': return 'status-cancelled';
      default: return 'status-default';
    }
  }

  getCommandeFournisseurStatusText(statut: string): string {
    switch (statut) {
      case 'EnAttente': return 'En attente';
      case 'Confirmee': return 'Confirmée';
      case 'EnPreparation': return 'En préparation';
      case 'Expediee': return 'Expédiée';
      case 'Livree': return 'Livrée';
      case 'Annulee': return 'Annulée';
      default: return statut;
    }
  }

  canCancelOrder(statut: string): boolean {
    return ['EnAttente', 'Confirmee'].includes(statut);
  }

  annulerCommande(commande: CommandeAdmin): void {
    // TODO: Implémenter l'annulation de commande
    console.log('Annuler commande:', commande.numeroCommande);
  }
}
