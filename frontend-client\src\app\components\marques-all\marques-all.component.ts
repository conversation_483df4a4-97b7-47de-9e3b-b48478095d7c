import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { catchError, finalize, of, Subject, takeUntil } from 'rxjs';
import { MarqueDto } from 'src/app/models/MarqueDto';
import { MarqueService } from 'src/app/services/marque.service';

@Component({
  selector: 'app-marques-all',
  standalone: false,
  templateUrl: './marques-all.component.html',
  styleUrl: './marques-all.component.scss',
})
export class MarquesAllComponent implements OnInit, OnDestroy {
  marques: MarqueDto[] = [];
  loading = false;
  error: string | null = null;
  private destroy$ = new Subject<void>();

  constructor(private router: Router, private marqueService: MarqueService) {}

  ngOnInit(): void {
    this.loadAllMarques();
  }

  private loadAllMarques(): void {
    this.loading = true;
    this.error = null;

    this.marqueService
      .getAll()
      .pipe(
        takeUntil(this.destroy$),
        catchError((err) => {
          this.error = 'Erreur lors du chargement des marques';
          console.error(err);
          return of([]);
        }),
        finalize(() => (this.loading = false))
      )
      .subscribe((marques) => {
        this.marques = marques;
      });
  }

  navigateToMarque(marqueId: number): void {
    if (!marqueId) {
      console.error('Marque ID is missing');
      return;
    }

    this.router.navigate(['/products/marque', marqueId]);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
