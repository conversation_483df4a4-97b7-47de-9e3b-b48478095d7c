{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-toggleswitch.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, forwardRef, EventEmitter, inject, booleanAttribute, numberAttribute, ContentChildren, ContentChild, ViewChild, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { SharedModule, PrimeTemplate } from 'primeng/api';\nimport { AutoFocus } from 'primeng/autofocus';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"handle\"];\nconst _c1 = [\"input\"];\nconst _c2 = a0 => ({\n  checked: a0\n});\nfunction ToggleSwitch_Conditional_5_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ToggleSwitch_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ToggleSwitch_Conditional_5_ng_container_0_Template, 1, 0, \"ng-container\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.handleTemplate || ctx_r1._handleTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c2, ctx_r1.checked()));\n  }\n}\nconst theme = ({\n  dt\n}) => `\n.p-toggleswitch {\n    display: inline-block;\n    width: ${dt('toggleswitch.width')};\n    height: ${dt('toggleswitch.height')};\n}\n\n.p-toggleswitch-input {\n    cursor: pointer;\n    appearance: none;\n    position: absolute;\n    top: 0;\n    inset-inline-start: 0;\n    width: 100%;\n    height: 100%;\n    padding: 0;\n    margin: 0;\n    opacity: 0;\n    z-index: 1;\n    outline: 0 none;\n    border-radius: ${dt('toggleswitch.border.radius')};\n}\n\n.p-toggleswitch-slider {\n    display: inline-block;\n    cursor: pointer;\n    width: 100%;\n    height: 100%;\n    border-width: ${dt('toggleswitch.border.width')};\n    border-style: solid;\n    border-color: ${dt('toggleswitch.border.color')};\n    background: ${dt('toggleswitch.background')};\n    transition: background ${dt('toggleswitch.transition.duration')}, color ${dt('toggleswitch.transition.duration')}, border-color ${dt('toggleswitch.transition.duration')}, outline-color ${dt('toggleswitch.transition.duration')}, box-shadow ${dt('toggleswitch.transition.duration')};\n    border-radius: ${dt('toggleswitch.border.radius')};\n    outline-color: transparent;\n    box-shadow: ${dt('toggleswitch.shadow')};\n}\n\n.p-toggleswitch-handle {\n    position: absolute;\n    top: 50%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    background: ${dt('toggleswitch.handle.background')};\n    color: ${dt('toggleswitch.handle.color')};\n    width: ${dt('toggleswitch.handle.size')};\n    height: ${dt('toggleswitch.handle.size')};\n    inset-inline-start: ${dt('toggleswitch.gap')};\n    margin-block-start: calc(-1 * calc(${dt('toggleswitch.handle.size')} / 2));\n    border-radius: ${dt('toggleswitch.handle.border.radius')};\n    transition: background ${dt('toggleswitch.transition.duration')}, color ${dt('toggleswitch.transition.duration')}, inset-inline-start ${dt('toggleswitch.slide.duration')}, box-shadow ${dt('toggleswitch.slide.duration')};\n}\n\n.p-toggleswitch.p-toggleswitch-checked .p-toggleswitch-slider {\n    background: ${dt('toggleswitch.checked.background')};\n    border-color: ${dt('toggleswitch.checked.border.color')};\n}\n\n.p-toggleswitch.p-toggleswitch-checked .p-toggleswitch-handle {\n    background: ${dt('toggleswitch.handle.checked.background')};\n    color: ${dt('toggleswitch.handle.checked.color')};\n    inset-inline-start: calc(${dt('toggleswitch.width')} - calc(${dt('toggleswitch.handle.size')} + ${dt('toggleswitch.gap')}));\n}\n\n.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover) .p-toggleswitch-slider {\n    background: ${dt('toggleswitch.hover.background')};\n    border-color: ${dt('toggleswitch.hover.border.color')};\n}\n\n.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover) .p-toggleswitch-handle {\n    background: ${dt('toggleswitch.handle.hover.background')};\n    color: ${dt('toggleswitch.handle.hover.color')};\n}\n\n.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover).p-toggleswitch-checked .p-toggleswitch-slider {\n    background: ${dt('toggleswitch.checked.hover.background')};\n    border-color: ${dt('toggleswitch.checked.hover.border.color')};\n}\n\n.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:hover).p-toggleswitch-checked .p-toggleswitch-handle {\n    background: ${dt('toggleswitch.handle.checked.hover.background')};\n    color: ${dt('toggleswitch.handle.checked.hover.color')};\n}\n\n.p-toggleswitch:not(.p-disabled):has(.p-toggleswitch-input:focus-visible) .p-toggleswitch-slider {\n    box-shadow: ${dt('toggleswitch.focus.ring.shadow')};\n    outline: ${dt('toggleswitch.focus.ring.width')} ${dt('toggleswitch.focus.ring.style')} ${dt('toggleswitch.focus.ring.color')};\n    outline-offset: ${dt('toggleswitch.focus.ring.offset')};\n}\n\n.p-toggleswitch.p-invalid > .p-toggleswitch-slider {\n    border-color: ${dt('toggleswitch.invalid.border.color')};\n}\n\n.p-toggleswitch.p-disabled {\n    opacity: 1;\n}\n\n.p-toggleswitch.p-disabled .p-toggleswitch-slider {\n    background: ${dt('toggleswitch.disabled.background')};\n}\n\n.p-toggleswitch.p-disabled .p-toggleswitch-handle {\n    background: ${dt('toggleswitch.handle.disabled.background')};\n}\n\n/* For PrimeNG */\n\np-toggleSwitch.ng-invalid.ng-dirty > .p-toggleswitch > .p-toggleswitch-slider,\np-toggle-switch.ng-invalid.ng-dirty > .p-toggleswitch > .p-toggleswitch-slider,\np-toggleswitch.ng-invalid.ng-dirty > .p-toggleswitch > .p-toggleswitch-slider {\n    border-color: ${dt('toggleswitch.invalid.border.color')};\n}`;\nconst inlineStyles = {\n  root: {\n    position: 'relative'\n  }\n};\nconst classes = {\n  root: ({\n    instance\n  }) => ({\n    'p-toggleswitch p-component': true,\n    'p-toggleswitch-checked': instance.checked(),\n    'p-disabled': instance.disabled,\n    'p-invalid': instance.invalid\n  }),\n  input: 'p-toggleswitch-input',\n  slider: 'p-toggleswitch-slider',\n  handle: 'p-toggleswitch-handle'\n};\nclass ToggleSwitchStyle extends BaseStyle {\n  name = 'toggleswitch';\n  theme = theme;\n  classes = classes;\n  inlineStyles = inlineStyles;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵToggleSwitchStyle_BaseFactory;\n    return function ToggleSwitchStyle_Factory(__ngFactoryType__) {\n      return (ɵToggleSwitchStyle_BaseFactory || (ɵToggleSwitchStyle_BaseFactory = i0.ɵɵgetInheritedFactory(ToggleSwitchStyle)))(__ngFactoryType__ || ToggleSwitchStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ToggleSwitchStyle,\n    factory: ToggleSwitchStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToggleSwitchStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * ToggleSwitch is used to select a boolean value.\n *\n * [Live Demo](https://www.primeng.org/toggleswitch/)\n *\n * @module toggleswitchstyle\n *\n */\nvar ToggleSwitchClasses;\n(function (ToggleSwitchClasses) {\n  /**\n   * Class name of the root element\n   */\n  ToggleSwitchClasses[\"root\"] = \"p-toggleswitch\";\n  /**\n   * Class name of the input element\n   */\n  ToggleSwitchClasses[\"input\"] = \"p-toggleswitch-input\";\n  /**\n   * Class name of the slider element\n   */\n  ToggleSwitchClasses[\"slider\"] = \"p-toggleswitch-slider\";\n})(ToggleSwitchClasses || (ToggleSwitchClasses = {}));\nconst TOGGLESWITCH_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => ToggleSwitch),\n  multi: true\n};\n/**\n * ToggleSwitch is used to select a boolean value.\n * @group Components\n */\nclass ToggleSwitch extends BaseComponent {\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  tabindex;\n  /**\n   * Identifier of the input element.\n   * @group Props\n   */\n  inputId;\n  /**\n   * Name of the input element.\n   * @group Props\n   */\n  name;\n  /**\n   * When present, it specifies that the element should be disabled.\n   * @group Props\n   */\n  disabled;\n  /**\n   * When present, it specifies that the component cannot be edited.\n   * @group Props\n   */\n  readonly;\n  /**\n   * Value in checked state.\n   * @group Props\n   */\n  trueValue = true;\n  /**\n   * Value in unchecked state.\n   * @group Props\n   */\n  falseValue = false;\n  /**\n   * Used to define a string that autocomplete attribute the current element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @group Props\n   */\n  autofocus;\n  /**\n   * Callback to invoke when the on value change.\n   * @param {ToggleSwitchChangeEvent} event - Custom change event.\n   * @group Emits\n   */\n  onChange = new EventEmitter();\n  input;\n  /**\n   * Callback to invoke when the on value change.\n   * @type {TemplateRef<ToggleSwitchHandleTemplateContext>} context - Context of the template\n   * @example\n   * ```html\n   * <ng-template #handle let-checked=\"checked\"> </ng-template>\n   * ```\n   * @see {@link ToggleSwitchHandleTemplateContext}\n   * @group Templates\n   */\n  handleTemplate;\n  _handleTemplate;\n  modelValue = false;\n  focused = false;\n  onModelChange = () => {};\n  onModelTouched = () => {};\n  _componentStyle = inject(ToggleSwitchStyle);\n  templates;\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'handle':\n          this._handleTemplate = item.template;\n          break;\n        default:\n          this._handleTemplate = item.template;\n          break;\n      }\n    });\n  }\n  onClick(event) {\n    if (!this.disabled && !this.readonly) {\n      this.modelValue = this.checked() ? this.falseValue : this.trueValue;\n      this.onModelChange(this.modelValue);\n      this.onChange.emit({\n        originalEvent: event,\n        checked: this.modelValue\n      });\n      this.input.nativeElement.focus();\n    }\n  }\n  onFocus() {\n    this.focused = true;\n  }\n  onBlur() {\n    this.focused = false;\n    this.onModelTouched();\n  }\n  writeValue(value) {\n    this.modelValue = value;\n    this.cd.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  setDisabledState(val) {\n    this.disabled = val;\n    this.cd.markForCheck();\n  }\n  checked() {\n    return this.modelValue === this.trueValue;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵToggleSwitch_BaseFactory;\n    return function ToggleSwitch_Factory(__ngFactoryType__) {\n      return (ɵToggleSwitch_BaseFactory || (ɵToggleSwitch_BaseFactory = i0.ɵɵgetInheritedFactory(ToggleSwitch)))(__ngFactoryType__ || ToggleSwitch);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: ToggleSwitch,\n    selectors: [[\"p-toggleswitch\"], [\"p-toggleSwitch\"], [\"p-toggle-switch\"]],\n    contentQueries: function ToggleSwitch_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, _c0, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.handleTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function ToggleSwitch_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n      }\n    },\n    inputs: {\n      style: \"style\",\n      styleClass: \"styleClass\",\n      tabindex: [2, \"tabindex\", \"tabindex\", numberAttribute],\n      inputId: \"inputId\",\n      name: \"name\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      readonly: [2, \"readonly\", \"readonly\", booleanAttribute],\n      trueValue: \"trueValue\",\n      falseValue: \"falseValue\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      autofocus: [2, \"autofocus\", \"autofocus\", booleanAttribute]\n    },\n    outputs: {\n      onChange: \"onChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([TOGGLESWITCH_VALUE_ACCESSOR, ToggleSwitchStyle]), i0.ɵɵInheritDefinitionFeature],\n    decls: 6,\n    vars: 23,\n    consts: [[\"input\", \"\"], [3, \"click\", \"ngClass\", \"ngStyle\"], [\"type\", \"checkbox\", \"role\", \"switch\", 3, \"focus\", \"blur\", \"ngClass\", \"checked\", \"disabled\", \"pAutoFocus\"], [3, \"ngClass\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n    template: function ToggleSwitch_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵlistener(\"click\", function ToggleSwitch_Template_div_click_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onClick($event));\n        });\n        i0.ɵɵelementStart(1, \"input\", 2, 0);\n        i0.ɵɵlistener(\"focus\", function ToggleSwitch_Template_input_focus_1_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onFocus());\n        })(\"blur\", function ToggleSwitch_Template_input_blur_1_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onBlur());\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"span\", 3)(4, \"div\", 3);\n        i0.ɵɵtemplate(5, ToggleSwitch_Conditional_5_Template, 1, 4, \"ng-container\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleMap(ctx.sx(\"root\"));\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", ctx.cx(\"root\"))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-name\", \"toggleswitch\")(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", ctx.cx(\"input\"))(\"checked\", ctx.checked())(\"disabled\", ctx.disabled)(\"pAutoFocus\", ctx.autofocus);\n        i0.ɵɵattribute(\"id\", ctx.inputId)(\"aria-checked\", ctx.checked())(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel)(\"name\", ctx.name)(\"tabindex\", ctx.tabindex)(\"data-pc-section\", \"hiddenInput\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngClass\", ctx.cx(\"slider\"));\n        i0.ɵɵattribute(\"data-pc-section\", \"slider\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", ctx.cx(\"handle\"));\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.handleTemplate || ctx._handleTemplate ? 5 : -1);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgTemplateOutlet, i1.NgStyle, AutoFocus, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToggleSwitch, [{\n    type: Component,\n    args: [{\n      selector: 'p-toggleswitch, p-toggleSwitch, p-toggle-switch',\n      standalone: true,\n      imports: [CommonModule, AutoFocus, SharedModule],\n      template: `\n        <div [ngClass]=\"cx('root')\" [style]=\"sx('root')\" [ngStyle]=\"style\" [class]=\"styleClass\" (click)=\"onClick($event)\" [attr.data-pc-name]=\"'toggleswitch'\" [attr.data-pc-section]=\"'root'\">\n            <input\n                #input\n                [attr.id]=\"inputId\"\n                type=\"checkbox\"\n                role=\"switch\"\n                [ngClass]=\"cx('input')\"\n                [checked]=\"checked()\"\n                [disabled]=\"disabled\"\n                [attr.aria-checked]=\"checked()\"\n                [attr.aria-labelledby]=\"ariaLabelledBy\"\n                [attr.aria-label]=\"ariaLabel\"\n                [attr.name]=\"name\"\n                [attr.tabindex]=\"tabindex\"\n                (focus)=\"onFocus()\"\n                (blur)=\"onBlur()\"\n                [attr.data-pc-section]=\"'hiddenInput'\"\n                [pAutoFocus]=\"autofocus\"\n            />\n            <span [ngClass]=\"cx('slider')\" [attr.data-pc-section]=\"'slider'\">\n                <div [ngClass]=\"cx('handle')\">\n                    @if (handleTemplate || _handleTemplate) {\n                        <ng-container *ngTemplateOutlet=\"handleTemplate || _handleTemplate; context: { checked: checked() }\" />\n                    }\n                </div>\n            </span>\n        </div>\n    `,\n      providers: [TOGGLESWITCH_VALUE_ACCESSOR, ToggleSwitchStyle],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], null, {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    inputId: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    readonly: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    trueValue: [{\n      type: Input\n    }],\n    falseValue: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    autofocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    onChange: [{\n      type: Output\n    }],\n    input: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    handleTemplate: [{\n      type: ContentChild,\n      args: ['handle', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass ToggleSwitchModule {\n  static ɵfac = function ToggleSwitchModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ToggleSwitchModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ToggleSwitchModule,\n    imports: [ToggleSwitch, SharedModule],\n    exports: [ToggleSwitch, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ToggleSwitch, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ToggleSwitchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [ToggleSwitch, SharedModule],\n      exports: [ToggleSwitch, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TOGGLESWITCH_VALUE_ACCESSOR, ToggleSwitch, ToggleSwitchClasses, ToggleSwitchModule, ToggleSwitchStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,OAAO;AACpB,IAAM,MAAM,SAAO;AAAA,EACjB,SAAS;AACX;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,oCAAoC,IAAI,KAAK;AACpD,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,gBAAgB,CAAC;AAAA,EAC9F;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,kBAAkB,OAAO,eAAe,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,QAAQ,CAAC,CAAC;AAAA,EAC5J;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA,aAGO,GAAG,oBAAoB,CAAC;AAAA,cACvB,GAAG,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAgBlB,GAAG,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAQjC,GAAG,2BAA2B,CAAC;AAAA;AAAA,oBAE/B,GAAG,2BAA2B,CAAC;AAAA,kBACjC,GAAG,yBAAyB,CAAC;AAAA,6BAClB,GAAG,kCAAkC,CAAC,WAAW,GAAG,kCAAkC,CAAC,kBAAkB,GAAG,kCAAkC,CAAC,mBAAmB,GAAG,kCAAkC,CAAC,gBAAgB,GAAG,kCAAkC,CAAC;AAAA,qBACtQ,GAAG,4BAA4B,CAAC;AAAA;AAAA,kBAEnC,GAAG,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBASzB,GAAG,gCAAgC,CAAC;AAAA,aACzC,GAAG,2BAA2B,CAAC;AAAA,aAC/B,GAAG,0BAA0B,CAAC;AAAA,cAC7B,GAAG,0BAA0B,CAAC;AAAA,0BAClB,GAAG,kBAAkB,CAAC;AAAA,yCACP,GAAG,0BAA0B,CAAC;AAAA,qBAClD,GAAG,mCAAmC,CAAC;AAAA,6BAC/B,GAAG,kCAAkC,CAAC,WAAW,GAAG,kCAAkC,CAAC,wBAAwB,GAAG,6BAA6B,CAAC,gBAAgB,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI5M,GAAG,iCAAiC,CAAC;AAAA,oBACnC,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIzC,GAAG,wCAAwC,CAAC;AAAA,aACjD,GAAG,mCAAmC,CAAC;AAAA,+BACrB,GAAG,oBAAoB,CAAC,WAAW,GAAG,0BAA0B,CAAC,MAAM,GAAG,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI1G,GAAG,+BAA+B,CAAC;AAAA,oBACjC,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIvC,GAAG,sCAAsC,CAAC;AAAA,aAC/C,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIhC,GAAG,uCAAuC,CAAC;AAAA,oBACzC,GAAG,yCAAyC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI/C,GAAG,8CAA8C,CAAC;AAAA,aACvD,GAAG,yCAAyC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIxC,GAAG,gCAAgC,CAAC;AAAA,eACvC,GAAG,+BAA+B,CAAC,IAAI,GAAG,+BAA+B,CAAC,IAAI,GAAG,+BAA+B,CAAC;AAAA,sBAC1G,GAAG,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,oBAItC,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAQzC,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAItC,GAAG,yCAAyC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAQ3C,GAAG,mCAAmC,CAAC;AAAA;AAE3D,IAAM,eAAe;AAAA,EACnB,MAAM;AAAA,IACJ,UAAU;AAAA,EACZ;AACF;AACA,IAAM,UAAU;AAAA,EACd,MAAM,CAAC;AAAA,IACL;AAAA,EACF,OAAO;AAAA,IACL,8BAA8B;AAAA,IAC9B,0BAA0B,SAAS,QAAQ;AAAA,IAC3C,cAAc,SAAS;AAAA,IACvB,aAAa,SAAS;AAAA,EACxB;AAAA,EACA,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AACV;AACA,IAAM,oBAAN,MAAM,2BAA0B,UAAU;AAAA,EACxC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,eAAe;AAAA,EACf,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,0BAA0B,mBAAmB;AAC3D,cAAQ,mCAAmC,iCAAoC,sBAAsB,kBAAiB,IAAI,qBAAqB,kBAAiB;AAAA,IAClK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,mBAAkB;AAAA,EAC7B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,sBAAqB;AAI9B,EAAAA,qBAAoB,MAAM,IAAI;AAI9B,EAAAA,qBAAoB,OAAO,IAAI;AAI/B,EAAAA,qBAAoB,QAAQ,IAAI;AAClC,GAAG,wBAAwB,sBAAsB,CAAC,EAAE;AACpD,IAAM,8BAA8B;AAAA,EAClC,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,YAAY;AAAA,EAC1C,OAAO;AACT;AAKA,IAAM,eAAN,MAAM,sBAAqB,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,IAAI,aAAa;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb,UAAU;AAAA,EACV,gBAAgB,MAAM;AAAA,EAAC;AAAA,EACvB,iBAAiB,MAAM;AAAA,EAAC;AAAA,EACxB,kBAAkB,OAAO,iBAAiB;AAAA,EAC1C;AAAA,EACA,qBAAqB;AACnB,SAAK,UAAU,QAAQ,UAAQ;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,kBAAkB,KAAK;AAC5B;AAAA,QACF;AACE,eAAK,kBAAkB,KAAK;AAC5B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,OAAO;AACb,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,UAAU;AACpC,WAAK,aAAa,KAAK,QAAQ,IAAI,KAAK,aAAa,KAAK;AAC1D,WAAK,cAAc,KAAK,UAAU;AAClC,WAAK,SAAS,KAAK;AAAA,QACjB,eAAe;AAAA,QACf,SAAS,KAAK;AAAA,MAChB,CAAC;AACD,WAAK,MAAM,cAAc,MAAM;AAAA,IACjC;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,SAAS;AACP,SAAK,UAAU;AACf,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,aAAa;AAClB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,iBAAiB,KAAK;AACpB,SAAK,WAAW;AAChB,SAAK,GAAG,aAAa;AAAA,EACvB;AAAA,EACA,UAAU;AACR,WAAO,KAAK,eAAe,KAAK;AAAA,EAClC;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,GAAG,CAAC,gBAAgB,GAAG,CAAC,iBAAiB,CAAC;AAAA,IACvE,gBAAgB,SAAS,4BAA4B,IAAI,KAAK,UAAU;AACtE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,SAAS,mBAAmB,IAAI,KAAK;AAC9C,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,KAAK,CAAC;AAAA,MACvB;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAAA,MAC9D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,UAAU,CAAC,GAAG,YAAY,YAAY,eAAe;AAAA,MACrD,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACtD,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,WAAW,CAAC,GAAG,aAAa,aAAa,gBAAgB;AAAA,IAC3D;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,6BAA6B,iBAAiB,CAAC,GAAM,0BAA0B;AAAA,IACjH,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,SAAS,WAAW,SAAS,GAAG,CAAC,QAAQ,YAAY,QAAQ,UAAU,GAAG,SAAS,QAAQ,WAAW,WAAW,YAAY,YAAY,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,CAAC;AAAA,IAC1O,UAAU,SAAS,sBAAsB,IAAI,KAAK;AAChD,UAAI,KAAK,GAAG;AACV,cAAM,MAAS,iBAAiB;AAChC,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,SAAS,SAAS,2CAA2C,QAAQ;AACjF,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,QAAQ,MAAM,CAAC;AAAA,QAC3C,CAAC;AACD,QAAG,eAAe,GAAG,SAAS,GAAG,CAAC;AAClC,QAAG,WAAW,SAAS,SAAS,+CAA+C;AAC7E,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,QAAQ,CAAC;AAAA,QACrC,CAAC,EAAE,QAAQ,SAAS,8CAA8C;AAChE,UAAG,cAAc,GAAG;AACpB,iBAAU,YAAY,IAAI,OAAO,CAAC;AAAA,QACpC,CAAC;AACD,QAAG,aAAa;AAChB,QAAG,eAAe,GAAG,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC;AAC3C,QAAG,WAAW,GAAG,qCAAqC,GAAG,GAAG,cAAc;AAC1E,QAAG,aAAa,EAAE,EAAE;AAAA,MACtB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,GAAG,MAAM,CAAC;AAC5B,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,IAAI,GAAG,MAAM,CAAC,EAAE,WAAW,IAAI,KAAK;AAC7D,QAAG,YAAY,gBAAgB,cAAc,EAAE,mBAAmB,MAAM;AACxE,QAAG,UAAU;AACb,QAAG,WAAW,WAAW,IAAI,GAAG,OAAO,CAAC,EAAE,WAAW,IAAI,QAAQ,CAAC,EAAE,YAAY,IAAI,QAAQ,EAAE,cAAc,IAAI,SAAS;AACzH,QAAG,YAAY,MAAM,IAAI,OAAO,EAAE,gBAAgB,IAAI,QAAQ,CAAC,EAAE,mBAAmB,IAAI,cAAc,EAAE,cAAc,IAAI,SAAS,EAAE,QAAQ,IAAI,IAAI,EAAE,YAAY,IAAI,QAAQ,EAAE,mBAAmB,aAAa;AACjN,QAAG,UAAU,CAAC;AACd,QAAG,WAAW,WAAW,IAAI,GAAG,QAAQ,CAAC;AACzC,QAAG,YAAY,mBAAmB,QAAQ;AAC1C,QAAG,UAAU;AACb,QAAG,WAAW,WAAW,IAAI,GAAG,QAAQ,CAAC;AACzC,QAAG,UAAU;AACb,QAAG,cAAc,IAAI,kBAAkB,IAAI,kBAAkB,IAAI,EAAE;AAAA,MACrE;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,kBAAqB,SAAS,WAAW,YAAY;AAAA,IACjG,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,WAAW,YAAY;AAAA,MAC/C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA6BV,WAAW,CAAC,6BAA6B,iBAAiB;AAAA,MAC1D,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,cAAc,YAAY;AAAA,IACpC,SAAS,CAAC,cAAc,YAAY;AAAA,EACtC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,cAAc,cAAc,YAAY;AAAA,EACpD,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,SAAS,CAAC,cAAc,YAAY;AAAA,IACtC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["ToggleSwitchClasses"]}