﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LivraisonController : ControllerBase
    {
        private readonly ILivraisonService _service;
        private readonly ILogger<LivraisonController> _logger;

        public LivraisonController(ILivraisonService service, ILogger<LivraisonController> logger)
        {
            _service = service;
            _logger = logger;
        }

        [HttpGet("{id:int}")]
        public async Task<ActionResult<LivraisonDto>> Get(int id)
        {
            try
            {
                var livraison = await _service.GetByIdAsync(id);
                return Ok(livraison);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération de la livraison {id}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpGet("commande/{commandeId:int}")]
        public async Task<ActionResult<List<LivraisonDto>>> GetByCommande(int commandeId)
        {
            try
            {
                var livraisons = await _service.GetByCommandeAsync(commandeId);
                return Ok(livraisons);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération des livraisons pour la commande {commandeId}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPost]
        public async Task<ActionResult<LivraisonDto>> Create([FromBody] CreateLivraisonDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var livraison = await _service.CreateAsync(dto);
                return CreatedAtAction(nameof(Get), new { id = livraison.Id }, livraison);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création de la livraison");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPut("{id:int}")]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateLivraisonDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                await _service.UpdateAsync(id, dto);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la mise à jour de la livraison {id}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPost("{livraisonId:int}/statut")]
        public async Task<IActionResult> ChangeStatut(int livraisonId, [FromBody] ChangeStatutLivraisonDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                await _service.ChangeStatutAsync(livraisonId, dto);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors du changement de statut pour la livraison {livraisonId}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpGet("{livraisonId:int}/statuts-disponibles")]
        public async Task<ActionResult<List<StatutLivraisonDto>>> GetStatutsDisponibles(int livraisonId)
        {
            try
            {
                var statuts = await _service.GetStatutsDisponiblesAsync(livraisonId);
                return Ok(statuts);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération des statuts disponibles pour la livraison {livraisonId}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpGet("{livraisonId:int}/etiquette")]
        public async Task<ActionResult<string>> GenererEtiquette(int livraisonId)
        {
            try
            {
                var etiquette = await _service.GenererEtiquetteLivraisonAsync(livraisonId);
                return Ok(etiquette);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la génération de l'étiquette pour la livraison {livraisonId}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }
    }
}
