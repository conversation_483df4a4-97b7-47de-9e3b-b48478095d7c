<!-- home.component.html -->
<div class="home">
  <!-- contenu du page -->
  <div class="contenu">
    <!-- Grand rectangle (slider) -->
    <div class="big-rectangle">
      <div class="slider">
        <div
          *ngFor="let slide of slides; let i = index"
          class="slide"
          [class.active]="currentSlide === i"
        >
          <img [src]="slide.image" [alt]="slide.title" />
          <div class="slide-content">
            <h3>{{ slide.category }}</h3>
            <h2>{{ slide.title }}</h2>
            <button (click)="navigateTo(slide.link)">
              {{ slide.buttonText }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Petits rectangles -->
    <div class="small-rectangles">
      <div class="small-rectangle1">
        <img src="assets/images/image4.jpg" />
        <div class="small-content">
          <h3>Lunett<PERSON></h3>
          <h2>Optique</h2>
          <button (click)="navigateTo('/optique')">Découvrir</button>
        </div>
      </div>

      <div class="small-rectangle2">
        <img src="assets/images/image5.jpg" />
        <div class="small-content">
          <h3>Lunettes</h3>
          <h2>Soleil</h2>
          <button (click)="navigateTo('/soleil')">Découvrir</button>
        </div>
      </div>
    </div>
  </div>
  <!-- Carousel des marques -->
  <demo-carousel-multilist-from-index></demo-carousel-multilist-from-index> 
  <app-nouveaux-arrivages></app-nouveaux-arrivages>
  <app-meilleures-ventes></app-meilleures-ventes>
</div>
