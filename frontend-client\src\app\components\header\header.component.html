<header>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <mat-toolbar
    color="primary"
    class="main-navbar"
    style="background-color: var(--background-color)"
  >
    <div class="container">
      <a routerLink="/" class="brand">
        <span>EasyLunettes</span>
      </a>

      <!-- Desktop Version -->
      <div class="desktop-nav" *ngIf="isDesktop">
        <div class="container d-flex align-items-center">
          <div class="nav-bar">
            <nav class="categories">
              <button
                *ngFor="let category of categories"
                (mouseenter)="onCategoryMouseEnter(category)"
                (mouseleave)="onCategoryMouseLeave()"
                (click)="navigateToCategory(category)"
              >
                {{ category.nom }}
              </button>
              <app-sub-categories
                *ngIf="hoveredCategory"
                [categoryId]="hoveredCategory.id"
                (mouseenter)="onSubCategoriesEnter()"
                (mouseleave)="onSubCategoriesLeave()"
              >
              </app-sub-categories>
            </nav>
            <div class="marque-container">
              <button
                (mouseenter)="onMarqueButtonMouseEnter()"
                (mouseleave)="onMarqueButtonMouseLeave()"
              >
                Marques
              </button>
              <div
                class="marques-list-container"
                *ngIf="hoveredMarqueMenu"
                (mouseenter)="onMarquesListEnter()"
                (mouseleave)="onMarquesListLeave()"
              >
                <app-marques-list
                  *ngFor="let marque of marques"
                  (click)="navigateToMarque(marque)"
                >
                </app-marques-list>
              </div>
            </div>
            <div class="fournisseur-container">
              <button
                (mouseenter)="onFournisseurButtonMouseEnter()"
                (mouseleave)="onFournisseurButtonMouseLeave()"
              >
                Fournisseurs
              </button>
              <div
                class="fournisseurs-list-container"
                *ngIf="hoveredFournisseurMenu"
                (mouseenter)="onFournisseursListEnter()"
                (mouseleave)="onFournisseursListLeave()"
              >
                <app-fournisseurs-list
                  *ngFor="let fournisseur of fournisseurs"
                  (click)="navigateToFournisseur(fournisseur)"
                >
                </app-fournisseurs-list>
              </div>
            </div>
          </div>
          <!-- Barre de recherche -->
          <div class="search-container mx-4 flex-grow-1">
            <form (submit)="onSearch($event)" class="input-group">
              <input
                type="search"
                class="form-control"
                placeholder="Recherchez plus de 80 000 produits"
                [(ngModel)]="searchQuery"
                name="searchQuery"
              />
              <button type="submit" class="btn btn-outline-secondary">
                <i class="bi bi-search"></i>
              </button>
            </form>
          </div>
          <!-- Actions utilisateur -->
          <div class="user-actions d-flex gap-2">
            <button mat-icon-button (click)="goToProfile()">
              <i class="bi bi-person fs-5"></i>
            </button>
            <button mat-icon-button (click)="goToFavorites()">
              <i class="bi bi-heart fs-5" ></i>
            </button>
            <app-notification-icon></app-notification-icon>
            <button mat-icon-button (click)="goToCart()">
              <i class="bi bi-cart3 fs-5" ></i>
            </button>
            <p-toggleswitch
              [(ngModel)]="isDarkMode"
              (onChange)="toggleTheme()"
              class="toggleswitch custom-toggle"
              [ngStyle]="{ color: 'var(--toggle-background-color)!important' }"
            >
              <ng-template #handle let-checked="checked">
                <i
                  [ngClass]="[
                    '!text-xs',
                    'fas',
                    checked ? 'fa-moon' : 'fa-sun'
                  ]"
                  style="color: var(--toggle-icon-color)"
                ></i>
              </ng-template>
            </p-toggleswitch>
          </div>
        </div>
      </div>

      <!-- Mobile Version Fermée -->
      <div class="mobile-nav" *ngIf="isMobile && !isMenuOpen">
        <div
          class="mobile-header d-flex align-items-center justify-content-end"
        >
          <!-- Actions à gauche -->
          <div class="mobile-actions">
            <div class="user-actions d-flex gap-2">
              <button mat-icon-button (click)="goToProfile()">
                <i class="bi bi-person fs-5"></i>
              </button>
              <button mat-icon-button (click)="goToFavorites()">
                <i class="bi bi-heart fs-5"></i>
              </button>
              <app-notification-icon></app-notification-icon>
              <button mat-icon-button (click)="goToCart()">
                <i class="bi bi-cart3 fs-5"></i>
              </button>
              <p-toggleswitch
                [(ngModel)]="isDarkMode"
                (onChange)="toggleTheme()"
                class="toggleswitch custom-toggle"
              >
                <ng-template #handle let-checked="checked">
                  <i
                    [ngClass]="[
                      '!text-xs',
                      'fas',
                      checked ? 'fa-moon' : 'fa-sun'
                    ]"
                    style="color: var(--toggle-icon-color)"
                  ></i>
                </ng-template>
              </p-toggleswitch>

              <button
                mat-icon-button
                (click)="toggleMenu()"
                class="menu-button"
              >
                <i class="bi bi-list fs-5"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- Mobile Version Ouverte -->
      <div class="mobile-menu-container" *ngIf="isMobile && isMenuOpen">
        <div class="mobile-menu-drawer">
          <div class="mobile-menu-header">
            <button mat-icon-button (click)="toggleMenu()" class="close-btn">
              <i class="bi bi-x-lg"></i>
            </button>
            <span class="title">Menu</span>
          </div>
          <div classe="nav-bar-mobile">
            <nav class="mobile-categories">
              <div
                *ngFor="let category of categories"
                class="mobile-category-item"
              >
                <button
                  (mouseenter)="onCategoryMouseEnter(category)"
                  (mouseleave)="onCategoryMouseLeave()"
                  (click)="navigateToCategory(category)"
                >
                  {{ category.nom }}
                </button>
                <app-sub-categories
                  *ngIf="hoveredCategory && hoveredCategory.id === category.id"
                  [categoryId]="hoveredCategory.id"
                  (closeMenuEvent)="isMenuOpen = false"
                  (mouseenter)="onSubCategoriesEnter()"
                  (mouseleave)="onSubCategoriesLeave()"
                >
                </app-sub-categories>
              </div>
            </nav>
            <!-- Pour les marques -->
            <div class="mobile-marque-container">
              <button
                (mouseenter)="onMarqueButtonMouseEnter()"
                (mouseleave)="onMarqueButtonMouseLeave()"
              >
                Marques
              </button>
              <nav
                class="mobile-marques"
                *ngIf="hoveredMarqueMenu"
                (mouseenter)="onMarquesListEnter()"
                (mouseleave)="onMarquesListLeave()"
              >
                  <app-marques-list></app-marques-list>
              </nav>
            </div>

            <div class="mobile-fournisseur-container">
              <button
                (mouseenter)="onFournisseurButtonMouseEnter()"
                (mouseleave)="onFournisseurButtonMouseLeave()"
              >
                Fournisseurs
              </button>
              <nav
                class="mobile-fournisseurs"
                *ngIf="hoveredFournisseurMenu"
                (mouseenter)="onFournisseursListEnter()"
                (mouseleave)="onFournisseursListLeave()"
              >
                <app-fournisseurs-list></app-fournisseurs-list>
              </nav>
            </div>
          </div>
          <div class="mobile-menu-backdrop" (click)="toggleMenu()"></div>
        </div>
        <div class="mobile-header">
          <div class="mobile-actions">
            <div class="user-actions d-flex gap-2">
              <button mat-icon-button (click)="goToProfile()">
                <i class="bi bi-person fs-5"></i>
              </button>
              <button mat-icon-button (click)="goToFavorites()">
                <i class="bi bi-heart fs-5"></i>
              </button>
              <app-notification-icon></app-notification-icon>
              <button mat-icon-button (click)="goToCart()">
                <i class="bi bi-cart3 fs-5"></i>
              </button>
              <p-toggleswitch
                [(ngModel)]="isDarkMode"
                (onChange)="toggleTheme()"
                class="toggleswitch custom-toggle"
              >
                <ng-template #handle let-checked="checked">
                  <i
                    [ngClass]="[
                      '!text-xs',
                      'fas',
                      checked ? 'fa-moon' : 'fa-sun'
                    ]"
                    style="color: var(--toggle-icon-color)"
                  ></i>
                </ng-template>
              </p-toggleswitch>
            </div>
          </div>
        </div>
      </div>
    </div>
  </mat-toolbar>
</header>