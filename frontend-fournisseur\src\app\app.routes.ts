import { Routes } from '@angular/router';
import { AuthGuard } from './guards/auth.guard';
import { AdminAuthGuard } from './guards/admin-auth.guard';
import { FournisseurGuard } from './guards/fournisseur.guard';

export const routes: Routes = [
  {
    path: 'login',
    loadComponent: () => import('./components/auth/login/login.component').then(m => m.LoginComponent)
  },
  {
    path: 'register',
    loadComponent: () => import('./components/auth/register/register.component').then(m => m.RegisterComponent)
  },
  {
    path: 'adminOptiLet',
    loadComponent: () => import('./components/admin/auth/admin-login/admin-login.component').then(m => m.AdminLoginComponent),
    title: 'Connexion Administrateur - OptiLet'
  },
  {
    path: 'admin',
    loadChildren: () => import('./routes/admin.routes').then(m => m.adminRoutes)
  },
  {
    path: 'admin-simple',
    loadComponent: () => import('./components/admin/admin-dashboard/admin-dashboard.component').then(m => m.AdminDashboardComponent),
    canActivate: [AdminAuthGuard],
    title: 'Administration OptiLet'
  },

  // Composants Angular 19 - Temporairement commentés pour résoudre les erreurs
  // {
  //   path: 'dashboard-ng19',
  //   loadComponent: () => import('./components/dashboard/dashboard-ng19.component').then(m => m.DashboardNg19Component),
  //   title: 'Dashboard Angular 19',
  //   canActivate: [AuthGuard]
  // },
  // {
  //   path: 'login-ng19',
  //   loadComponent: () => import('./components/auth/login-ng19/login-ng19.component').then(m => m.LoginNg19Component),
  //   title: 'Connexion Angular 19'
  // },
  // {
  //   path: 'angular19-nav',
  //   loadComponent: () => import('./components/navigation/angular19-nav.component').then(m => m.Angular19NavComponent),
  //   title: 'Navigation Angular 19'
  // },
  // Dashboard du fournisseur avec layout et sidebar
  {
    path: 'dashboard',
    loadComponent: () => import('./components/layout/dashboard-layout/dashboard-layout.component').then(m => m.DashboardLayoutComponent),
    canActivate: [FournisseurGuard],
    children: [
      {
        path: '',
        redirectTo: 'overview',
        pathMatch: 'full'
      },
      {
        path: 'overview',
        loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent)
      },
      {
        path: 'advanced',
        loadComponent: () => import('./components/dashboard/dashboard-advanced.component').then(m => m.DashboardAdvancedComponent),
        title: 'Tableau de Bord Avancé'
      },
      {
        path: 'products',
        loadComponent: () => import('./components/products/products.component').then(m => m.ProductsComponent),
        title: 'Gestion des Produits'
      },
      {
        path: 'stock',
        loadComponent: () => import('./components/products/stock-management/stock-management.component').then(m => m.StockManagementComponent),
        title: 'Gestion du Stock'
      },
      {
        path: 'stock-depot',
        loadComponent: () => import('./components/products/stock-depot-management/stock-depot-management.component').then(m => m.StockDepotManagementComponent),
        title: 'Gestion du Stock par Dépôt'
      },
      {
        path: 'promotions',
        loadComponent: () => import('./components/promotion-management/promotion-management.component').then(m => m.PromotionManagementComponent),
        title: 'Gestion des Promotions'
      },
      {
        path: 'test-auth-promotions',
        loadComponent: () => import('./components/test-auth-promotions/test-auth-promotions.component').then(m => m.TestAuthPromotionsComponent),
        title: 'Test Auth & Promotions'
      },
      {
        path: 'referentiels',
        loadComponent: () => import('./components/referentiels/referentiels.component').then(m => m.ReferentielsComponent),
        title: 'Gestion des Référentiels'
      },
      {
        path: 'profil',
        loadComponent: () => import('./components/profile/profile.component').then(m => m.ProfileComponent),
        title: 'Mon Profil'
      },
      {
        path: 'notifications',
        loadComponent: () => import('./notifications/notifications.component').then(m => m.NotificationsComponent),
        title: 'Mes Notifications'
      }
    ]
  },
  {
    path: '',
    redirectTo: '/login',
    pathMatch: 'full'
  },
  {
    path: 'home',
    loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent),
    title: 'Accueil - Plateforme Fournisseur Angular 19'
  },
  {
    path: 'test-validation',
    loadComponent: () => import('./components/test-validation/test-validation.component').then(m => m.TestValidationComponent),
    title: 'Test de Validation des Fournisseurs'
  },
  {
    path: 'test-debug',
    loadComponent: () => import('./components/test-auth-promotions/test-auth-promotions.component').then(m => m.TestAuthPromotionsComponent),
    title: 'Test Debug - Auth & Promotions'
  },
  {
    path: '**',
    redirectTo: '/login'
  }
];
