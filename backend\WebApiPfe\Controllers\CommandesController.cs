﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Models;
using WebApiPfe.Models.DTOs;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CommandesController : ControllerBase
    {
        private readonly ICommandeService _commandeService;
        private readonly AppDbContext _context;
        private readonly ILogger<CommandesController> _logger;

        public CommandesController(
            ICommandeService commandeService,
            AppDbContext context,
            ILogger<CommandesController> logger)
        {
            _commandeService = commandeService;
            _context = context;
            _logger = logger;
        }

        // POST: api/commandes
        [HttpPost]
        public async Task<ActionResult<CommandeDto>> CreerCommande([FromBody] CreateCommandeDto dto)
        {
            try
            {
                _logger.LogInformation($"🛒 Début création commande - ClientId: {dto?.ClientId}, Produits: {dto?.DetailsCommandes?.Count ?? 0}");

                // Validation des données d'entrée
                if (dto == null)
                {
                    _logger.LogWarning("❌ DTO de commande est null");
                    return BadRequest(new { Error = "Données de commande manquantes" });
                }

                if (dto.DetailsCommandes == null || !dto.DetailsCommandes.Any())
                {
                    _logger.LogWarning($"❌ Détails commandes invalides - Count: {dto.DetailsCommandes?.Count ?? 0}");
                    return BadRequest(new { Error = "Données de commande invalides ou panier vide" });
                }

                // Vérifier que toutes les quantités sont positives
                if (dto.DetailsCommandes.Any(d => d.Quantite <= 0))
                {
                    _logger.LogWarning("❌ Quantités invalides détectées");
                    return BadRequest(new { Error = "Les quantités doivent être positives" });
                }

                _logger.LogInformation($"🛒 Appel service création commande - ClientId: {dto.ClientId}");

                // Convertir les DTOs simples en DTOs complets
                var detailsCommandes = dto.DetailsCommandes.Select(d => new DetailsCommandeDto
                {
                    ProduitId = d.ProduitId,
                    Quantite = d.Quantite
                }).ToList();

                // VERSION SIMPLIFIÉE - Créer directement dans le contrôleur
                var commande = new Commande
                {
                    ClientId = dto.ClientId,
                    DateCreation = DateTime.UtcNow,
                    Statut = StatutCommande.Brouillon,
                    MontantTotal = 0,
                    FraisLivraison = 0
                };

                _context.Commandes.Add(commande);
                await _context.SaveChangesAsync();
                _logger.LogInformation($"✅ Commande base créée: {commande.Id}");

                // Ajouter détails
                decimal total = 0;
                foreach (var detail in detailsCommandes)
                {
                    var produit = await _context.Produits
                        .Include(p => p.TauxTVA)
                        .FirstOrDefaultAsync(p => p.Id == detail.ProduitId);

                    if (produit != null)
                    {
                        var detailCommande = new DetailsCommande
                        {
                            CommandeId = commande.Id,
                            ProduitId = detail.ProduitId,
                            Quantite = detail.Quantite,
                            PrixUnitaireHT = produit.PrixVenteHT,
                            TauxTVAValue = produit.TauxTVA?.Taux ?? 0m
                        };

                        _context.DetailsCommandes.Add(detailCommande);
                        total += (detailCommande.PrixUnitaireHT * (1 + detailCommande.TauxTVAValue / 100)) * detailCommande.Quantite;
                    }
                }

                commande.MontantTotal = total;
                await _context.SaveChangesAsync();

                _logger.LogInformation($"✅ Commande {commande.Id} créée - Début division par fournisseur");

                // DIVISION AUTOMATIQUE PAR FOURNISSEUR
                await DiviserCommandeParFournisseur(commande.Id);

                _logger.LogInformation($"✅ SUCCES COMPLET - Commande {commande.Id} créée et divisée");

                return Ok(new {
                    id = commande.Id,
                    montantTotal = commande.MontantTotal,
                    statut = commande.Statut.ToString()
                });
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Erreur de validation lors de la création de la commande");
                return BadRequest(new { Error = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erreur critique lors de la création de la commande");

                // Capturer l'inner exception pour plus de détails
                var innerMessage = ex.InnerException?.Message ?? "Pas d'inner exception";
                var fullMessage = $"{ex.Message} | Inner: {innerMessage}";

                return StatusCode(500, new { Error = "Une erreur interne s'est produite", Details = fullMessage });
            }
        }

        // Méthode pour diviser une commande par fournisseur
        private async Task DiviserCommandeParFournisseur(int commandeId)
        {
            try
            {
                _logger.LogInformation($"🏭 DEBUT division commande {commandeId}");

                // Récupérer la commande avec ses détails et produits
                var commande = await _context.Commandes
                    .Include(c => c.DetailsCommandes)
                        .ThenInclude(d => d.Produit)
                            .ThenInclude(p => p.Fournisseur)
                    .FirstOrDefaultAsync(c => c.Id == commandeId);

                if (commande == null)
                {
                    _logger.LogWarning($"❌ Commande {commandeId} introuvable");
                    return;
                }

                // Debug: Vérifier les détails
                _logger.LogInformation($"🔍 Commande {commandeId} a {commande.DetailsCommandes?.Count ?? 0} détails");
                foreach (var detail in commande.DetailsCommandes ?? new List<DetailsCommande>())
                {
                    _logger.LogInformation($"🔍 Détail: ProduitId={detail.ProduitId}, Produit={detail.Produit?.Nom ?? "NULL"}, FournisseurId={detail.Produit?.FournisseurId ?? 0}");
                }

                // CORRECTION: Assigner un fournisseur par défaut si manquant
                var fournisseurParDefaut = await _context.Fournisseurs.FirstOrDefaultAsync();
                if (fournisseurParDefaut == null)
                {
                    _logger.LogError("❌ Aucun fournisseur trouvé dans la base de données");
                    return;
                }

                // Grouper les détails par fournisseur (avec fournisseur par défaut si nécessaire)
                var detailsParFournisseur = commande.DetailsCommandes
                    .Where(d => d.Produit != null)
                    .GroupBy(d => d.Produit.FournisseurId > 0 ? d.Produit.FournisseurId : fournisseurParDefaut.Id)
                    .ToList();

                _logger.LogInformation($"🏭 {detailsParFournisseur.Count} fournisseurs trouvés après filtrage");

                if (detailsParFournisseur.Count == 0)
                {
                    _logger.LogWarning($"⚠️ Aucun fournisseur trouvé pour la commande {commandeId}");

                    // Diagnostic détaillé
                    foreach (var detail in commande.DetailsCommandes ?? new List<DetailsCommande>())
                    {
                        if (detail.Produit == null)
                        {
                            _logger.LogWarning($"❌ Produit NULL pour détail {detail.Id} (ProduitId: {detail.ProduitId})");
                        }
                        else if (detail.Produit.FournisseurId <= 0)
                        {
                            _logger.LogWarning($"❌ FournisseurId invalide ({detail.Produit.FournisseurId}) pour produit {detail.Produit.Nom}");
                        }
                    }
                    return;
                }

                foreach (var groupe in detailsParFournisseur)
                {
                    var fournisseurId = groupe.Key;
                    var detailsFournisseur = groupe.ToList();

                    _logger.LogInformation($"🏭 Création commande fournisseur {fournisseurId} avec {detailsFournisseur.Count} produits");

                    try
                    {
                        // Récupérer les frais de livraison du fournisseur
                        var fraisLivraison = await _context.Fournisseurs
                            .Where(f => f.Id == fournisseurId)
                            .Select(f => f.FraisLivraisonBase)
                            .FirstOrDefaultAsync();

                        // Créer la commande fournisseur
                        var commandeFournisseur = new CommandeFournisseur
                        {
                            CommandeClientId = commandeId,
                            FournisseurId = fournisseurId,
                            Reference = $"F{fournisseurId}-{DateTime.Now:yyMMdd-HHmm}",
                            DateCreation = DateTime.UtcNow,
                            Statut = Models.Enum.StatutCommandeFournisseur.EnAttente,
                            FraisLivraison = fraisLivraison,
                            MontantTotal = 0 // Sera calculé après
                        };

                        _context.CommandesFournisseurs.Add(commandeFournisseur);
                        await _context.SaveChangesAsync(); // Sauvegarder pour obtenir l'ID

                        _logger.LogInformation($"✅ Commande fournisseur {fournisseurId} créée avec ID: {commandeFournisseur.Id}");

                        // Créer les lignes de commande fournisseur
                        decimal montantTotal = 0;
                        foreach (var detail in detailsFournisseur)
                        {
                            var ligneCommande = new LigneCommandeFournisseur
                            {
                                CommandeId = commandeFournisseur.Id,
                                ProduitId = detail.ProduitId,
                                Quantite = detail.Quantite,
                                PrixUnitaire = detail.PrixUnitaireHT,
                                MontantLigne = detail.PrixUnitaireHT * detail.Quantite
                            };

                            _context.LignesCommandeFournisseur.Add(ligneCommande);
                            montantTotal += ligneCommande.MontantLigne;

                            _logger.LogInformation($"✅ Ligne ajoutée: {detail.Quantite}x {detail.Produit?.Nom} = {ligneCommande.MontantLigne:C}");
                        }

                        // Mettre à jour le montant total (produits + frais de livraison)
                        commandeFournisseur.MontantTotal = montantTotal + fraisLivraison;
                        await _context.SaveChangesAsync();

                        _logger.LogInformation($"✅ Commande fournisseur {fournisseurId} TERMINÉE: {commandeFournisseur.MontantTotal:C}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"❌ Erreur création commande fournisseur {fournisseurId}");
                    }
                }

                _logger.LogInformation($"✅ Division terminée - {detailsParFournisseur.Count} commandes fournisseurs créées");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Erreur lors de la division de la commande {commandeId}");
            }
        }

        // GET: api/commandes
        [HttpGet]
        public async Task<ActionResult<IEnumerable<CommandeDto>>> GetAllCommandes()
        {
            try
            {
                var commandes = await _context.Commandes
                    .Include(c => c.DetailsCommandes)
                        .ThenInclude(d => d.Produit)
                    .Include(c => c.PromotionsUtilisees)
                    .Include(c => c.CommandesFournisseurs)
                        .ThenInclude(cf => cf.Fournisseur)
                    .Include(c => c.CommandesFournisseurs)
                        .ThenInclude(cf => cf.LignesCommande)
                            .ThenInclude(lc => lc.Produit)
                    .OrderByDescending(c => c.DateCreation)
                    .ToListAsync();

                var response = new List<CommandeDto>();
                foreach (var commande in commandes)
                {
                    response.Add(MapToResponseDto(commande));
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de toutes les commandes");
                return StatusCode(500, new { Error = "Erreur interne", Details = ex.Message });
            }
        }

        // GET: api/commandes/5
        [HttpGet("{id}")]
        public async Task<ActionResult<CommandeDto>> GetCommande(int id)
        {
            try
            {
                var commande = await _context.Commandes
                    .Include(c => c.DetailsCommandes)
                        .ThenInclude(d => d.Produit)
                    .Include(c => c.PromotionsUtilisees)
                    .Include(c => c.CommandesFournisseurs)
                        .ThenInclude(cf => cf.Fournisseur)
                    .Include(c => c.CommandesFournisseurs)
                        .ThenInclude(cf => cf.LignesCommande)
                            .ThenInclude(lc => lc.Produit)
                                .ThenInclude(p => p.Images)
                    .FirstOrDefaultAsync(c => c.Id == id);

                _logger.LogInformation($"Commande {id} trouvée avec {commande?.CommandesFournisseurs?.Count ?? 0} commandes fournisseurs");

                if (commande == null)
                {
                    return NotFound(new { Message = $"Commande {id} introuvable" });
                }

                return Ok(MapToResponseDto(commande));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération de la commande {id}");
                return StatusCode(500, new { Error = "Erreur interne", Details = ex.Message });
            }
        }

        // GET: api/commandes/by-client/5
        [HttpGet("by-client/{clientId}")]
        public async Task<ActionResult<IEnumerable<CommandeDto>>> GetCommandesByClient(int clientId)
        {
            try
            {
                var commandes = await _context.Commandes
                    .Include(c => c.DetailsCommandes)
                        .ThenInclude(d => d.Produit)
                    .Include(c => c.PromotionsUtilisees)
                    .Include(c => c.CommandesFournisseurs)
                        .ThenInclude(cf => cf.Fournisseur)
                    .Include(c => c.CommandesFournisseurs)
                        .ThenInclude(cf => cf.LignesCommande)
                            .ThenInclude(lc => lc.Produit)
                                .ThenInclude(p => p.Images)
                    .Where(c => c.ClientId == clientId)
                    .OrderByDescending(c => c.DateCreation)
                    .ToListAsync();

                var response = new List<CommandeDto>();
                foreach (var commande in commandes)
                {
                    response.Add(MapToResponseDto(commande));
                }

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération des commandes du client {clientId}");
                return StatusCode(500, new { Error = "Erreur interne", Details = ex.Message });
            }
        }

        // GET: api/commandes/{id}/commandes-fournisseurs - Vérifier les commandes fournisseurs créées
        [HttpGet("{id}/commandes-fournisseurs")]
        public async Task<IActionResult> GetCommandesFournisseurs(int id)
        {
            try
            {
                var commandesFournisseurs = await _context.CommandesFournisseurs
                    .Include(cf => cf.Fournisseur)
                    .Include(cf => cf.LignesCommande)
                        .ThenInclude(lc => lc.Produit)
                    .Where(cf => cf.CommandeClientId == id)
                    .Select(cf => new {
                        cf.Id,
                        cf.CommandeClientId,
                        cf.FournisseurId,
                        FournisseurNom = $"{cf.Fournisseur.Nom} {cf.Fournisseur.Prenom}",
                        cf.DateCreation,
                        cf.Statut,
                        cf.MontantTotal,
                        cf.Reference,
                        NombreLignes = cf.LignesCommande.Count,
                        Produits = cf.LignesCommande.Select(lc => new {
                            lc.ProduitId,
                            ProduitNom = lc.Produit.Nom,
                            lc.Quantite,
                            lc.PrixUnitaire,
                            SousTotal = lc.MontantLigne
                        }).ToList()
                    })
                    .ToListAsync();

                return Ok(new {
                    CommandeClientId = id,
                    NombreCommandesFournisseurs = commandesFournisseurs.Count,
                    CommandesFournisseurs = commandesFournisseurs
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des commandes fournisseurs");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        // POST: api/commandes/{id}/force-division - Forcer la division d'une commande
        [HttpPost("{id}/force-division")]
        public async Task<IActionResult> ForcerDivision(int id)
        {
            try
            {
                _logger.LogInformation($"🔧 DÉBUT - Forçage division commande {id}");
                Console.WriteLine($"🔧 DÉBUT - Forçage division commande {id}");

                // Vérifier que la commande existe
                var commande = await _context.Commandes
                    .Include(c => c.DetailsCommandes)
                        .ThenInclude(d => d.Produit)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (commande == null)
                {
                    Console.WriteLine($"❌ Commande {id} introuvable");
                    return NotFound(new { Error = $"Commande {id} introuvable" });
                }

                Console.WriteLine($"✅ Commande {id} trouvée avec {commande.DetailsCommandes.Count} détails");

                // Supprimer les anciennes commandes fournisseurs
                var anciennes = await _context.CommandesFournisseurs
                    .Where(cf => cf.CommandeClientId == id)
                    .ToListAsync();

                if (anciennes.Any())
                {
                    Console.WriteLine($"🗑️ Suppression de {anciennes.Count} anciennes commandes fournisseurs");
                    _context.CommandesFournisseurs.RemoveRange(anciennes);
                    await _context.SaveChangesAsync();
                }

                // Grouper les détails par fournisseur
                var detailsParFournisseur = commande.DetailsCommandes
                    .Where(d => d.Produit != null && d.Produit.FournisseurId > 0)
                    .GroupBy(d => d.Produit.FournisseurId)
                    .ToList();

                Console.WriteLine($"🏭 {detailsParFournisseur.Count} fournisseurs différents trouvés");

                var commandesFournisseursCreees = 0;

                // Créer une commande fournisseur pour chaque groupe
                foreach (var groupe in detailsParFournisseur)
                {
                    var fournisseurId = groupe.Key;
                    var detailsFournisseur = groupe.ToList();

                    Console.WriteLine($"🏭 Création commande fournisseur pour fournisseur {fournisseurId} avec {detailsFournisseur.Count} produits");

                    // Calculer le montant total pour ce fournisseur
                    var montantProduits = detailsFournisseur.Sum(d => d.PrixUnitaireHT * d.Quantite);
                    var fraisLivraison = 7.99m; // Frais fixes pour simplifier
                    var montantTotal = montantProduits + fraisLivraison;

                    var commandeFournisseur = new CommandeFournisseur
                    {
                        CommandeClientId = id,
                        FournisseurId = fournisseurId,
                        Reference = $"F{fournisseurId}-{DateTime.Now:MMddHHmm}",
                        DateCreation = DateTime.UtcNow,
                        Statut = StatutCommandeFournisseur.EnAttente,
                        FraisLivraison = fraisLivraison,
                        MontantTotal = montantTotal
                    };

                    _context.CommandesFournisseurs.Add(commandeFournisseur);
                    await _context.SaveChangesAsync(); // Sauvegarder pour obtenir l'ID

                    Console.WriteLine($"✅ Commande fournisseur créée avec ID: {commandeFournisseur.Id}");

                    // Créer les lignes de commande pour ce fournisseur
                    foreach (var detail in detailsFournisseur)
                    {
                        var ligne = new LigneCommandeFournisseur
                        {
                            CommandeId = commandeFournisseur.Id,
                            ProduitId = detail.ProduitId,
                            Quantite = detail.Quantite,
                            PrixUnitaire = detail.PrixUnitaireHT,
                            MontantLigne = detail.PrixUnitaireHT * detail.Quantite
                        };
                        _context.LignesCommandeFournisseur.Add(ligne);
                        Console.WriteLine($"   📦 Ligne ajoutée: Produit {detail.ProduitId} - Qté: {detail.Quantite} - Prix: {detail.PrixUnitaireHT:C}");
                    }

                    await _context.SaveChangesAsync();
                    commandesFournisseursCreees++;
                    Console.WriteLine($"✅ Lignes de commande créées pour fournisseur {fournisseurId}");
                }

                Console.WriteLine($"✅ FIN - Division forcée pour commande {id} - {commandesFournisseursCreees} commandes fournisseurs créées");

                return Ok(new {
                    Message = $"Division forcée pour commande {id}",
                    Success = true,
                    CommandesFournisseursCreees = commandesFournisseursCreees,
                    FournisseursDistincts = detailsParFournisseur.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors du forçage de division");
                Console.WriteLine($"❌ ERREUR - Division forcée: {ex.Message}");
                Console.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        // DELETE: api/commandes/{id} - Supprimer une commande
        [HttpDelete("{id}")]
        public async Task<IActionResult> SupprimerCommande(int id)
        {
            try
            {
                _logger.LogInformation($"🗑️ DÉBUT - Suppression commande {id}");
                Console.WriteLine($"🗑️ DÉBUT - Suppression commande {id}");

                var success = await _commandeService.SupprimerCommande(id);

                if (!success)
                {
                    Console.WriteLine($"❌ Échec de la suppression de la commande {id}");
                    return BadRequest(new {
                        Error = "Impossible de supprimer la commande",
                        Details = "La commande n'existe pas ou ne peut pas être supprimée (statut expédiée/livrée)"
                    });
                }

                Console.WriteLine($"✅ FIN - Commande {id} supprimée avec succès");

                return Ok(new {
                    Message = $"Commande {id} supprimée avec succès",
                    Success = true
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la suppression de commande");
                Console.WriteLine($"❌ ERREUR - Suppression commande: {ex.Message}");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        // POST: api/commandes/5/finaliser
        [HttpPost("{id}/finaliser")]
        public async Task<IActionResult> FinaliserCommande(int id, [FromBody] string tokenPaiement)
        {
            try
            {
                var success = await _commandeService.FinaliserCommande(id, tokenPaiement);
                if (!success)
                {
                    return BadRequest(new { Error = "Échec du paiement ou commande invalide" });
                }
                return Ok(new { Message = "Commande finalisée avec succès" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la finalisation de la commande {id}");
                return BadRequest(new { Error = ex.Message });
            }
        }

        // POST: api/commandes/calculer-frais-livraison
        [HttpPost("calculer-frais-livraison")]
        public async Task<ActionResult<FraisLivraisonResponseDto>> CalculerFraisLivraison([FromBody] CalculerFraisLivraisonRequest request)
        {
            try
            {
                if (request == null || request.ProduitIds == null || !request.ProduitIds.Any())
                {
                    return BadRequest(new FraisLivraisonResponseDto());
                }

                // Récupérer les produits avec les informations des fournisseurs
                var produits = await _context.Produits
                    .Include(p => p.Fournisseur)
                    .Where(p => request.ProduitIds.Contains(p.Id))
                    .Select(p => new { p.Id, p.FournisseurId, p.Fournisseur.RaisonSociale })
                    .ToListAsync();

                if (!produits.Any())
                {
                    return BadRequest(new FraisLivraisonResponseDto());
                }

                // Grouper par fournisseur et calculer les frais
                var fraisParFournisseur = new List<FraisLivraisonDetailDto>();
                decimal totalFraisLivraison = 0;

                var groupesParFournisseur = produits.GroupBy(p => p.FournisseurId);

                foreach (var groupe in groupesParFournisseur)
                {
                    var fournisseurId = groupe.Key;
                    var nomFournisseur = groupe.First().RaisonSociale;
                    var produitIds = groupe.Select(p => p.Id).ToList();

                    var fraisFournisseur = await _commandeService.CalculerFraisLivraison(fournisseurId);
                    totalFraisLivraison += fraisFournisseur;

                    fraisParFournisseur.Add(new FraisLivraisonDetailDto
                    {
                        FournisseurId = fournisseurId,
                        NomFournisseur = nomFournisseur,
                        FraisLivraison = fraisFournisseur,
                        ProduitIds = produitIds
                    });
                }

                var response = new FraisLivraisonResponseDto
                {
                    FraisParFournisseur = fraisParFournisseur,
                    FraisLivraisonTotal = totalFraisLivraison
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors du calcul des frais de livraison");
                return StatusCode(500, new FraisLivraisonResponseDto());
            }
        }

        // Méthode de mapping privée
        private CommandeDto MapToResponseDto(Commande commande)
        {
            try
            {
                var dto = new CommandeDto
                {
                    Id = commande.Id,
                    ClientId = commande.ClientId,
                    DateCreation = commande.DateCreation,
                    Statut = commande.Statut.ToString(),
                    MontantTotal = commande.MontantTotal,
                    Details = new List<DetailsCommandeDto>(),
                    PromotionsUtilisees = new List<PromotionUtiliseeDto>(),
                    CommandesFournisseurs = new List<CommandeFournisseurDto>()
                };

                // Calcul des montants après avoir ajouté les détails
                decimal montantHT = 0;
                decimal montantTVA = 0;

                // Mapping des détails avec gestion d'erreurs
                if (commande.DetailsCommandes != null)
                {
                    foreach (var d in commande.DetailsCommandes)
                    {
                        try
                        {
                            var ligneHT = d.PrixUnitaireHT * d.Quantite;
                            var ligneTVA = (d.PrixUnitaireHT * d.TauxTVAValue / 100) * d.Quantite;

                            montantHT += ligneHT;
                            montantTVA += ligneTVA;

                            dto.Details.Add(new DetailsCommandeDto
                            {
                                Id = d.Id,
                                CommandeId = d.CommandeId,
                                ProduitId = d.ProduitId,
                                ProduitNom = d.Produit?.Nom ?? "Produit supprimé",
                                PrixUnitaireHT = d.PrixUnitaireHT,
                                TauxTVA = d.TauxTVAValue,
                                PrixUnitaireTTC = d.PrixUnitaireHT * (1 + d.TauxTVAValue / 100),
                                TotalLigneTTC = (d.PrixUnitaireHT * (1 + d.TauxTVAValue / 100)) * d.Quantite,
                                MontantTVA = ligneTVA,
                                Quantite = d.Quantite
                            });
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"Erreur lors du mapping du détail commande {d.Id}");
                        }
                    }
                }

                // Mapping des promotions avec gestion d'erreurs
                _logger.LogInformation($"Commande {commande.Id}: {commande.PromotionsUtilisees?.Count ?? 0} promotions trouvées");
                if (commande.PromotionsUtilisees != null)
                {
                    foreach (var p in commande.PromotionsUtilisees)
                    {
                        try
                        {
                            dto.PromotionsUtilisees.Add(new PromotionUtiliseeDto
                            {
                                Id = p.Id,
                                PromotionId = p.PromotionId,
                                CommandeId = p.CommandeId,
                                DateUtilisation = p.DateUtilisation,
                                CodePromoUtilise = p.CodePromoUtilise,
                                MontantEconomise = p.MontantEconomise
                            });
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"Erreur lors du mapping de la promotion {p.Id}");
                        }
                    }
                }

                // Mapping des commandes fournisseurs avec gestion d'erreurs
                _logger.LogInformation($"Commande {commande.Id}: {commande.CommandesFournisseurs?.Count ?? 0} commandes fournisseurs trouvées");
                if (commande.CommandesFournisseurs != null)
                {
                    foreach (var cf in commande.CommandesFournisseurs)
                    {
                        try
                        {
                            var commandeFournisseurDto = new CommandeFournisseurDto
                            {
                                Id = cf.Id,
                                Reference = cf.Reference,
                                FournisseurId = cf.FournisseurId,
                                NomFournisseur = cf.Fournisseur?.RaisonSociale ?? "Fournisseur inconnu",
                                MatriculeFiscale = cf.Fournisseur?.MatriculeFiscale ?? "",
                                DateCreation = cf.DateCreation,
                                DateLivraison = cf.DateLivraison,
                                FraisLivraison = cf.FraisLivraison,
                                Statut = cf.Statut.ToString(),
                                NumeroBonLivraison = cf.NumeroBonLivraison ?? "",
                                MontantTotal = cf.MontantTotal,
                                LignesCommande = new List<LigneCommandeFournisseurDto>()
                            };

                            // Ajouter les lignes de commande fournisseur
                            if (cf.LignesCommande != null)
                            {
                                foreach (var ligne in cf.LignesCommande)
                                {
                                    ((List<LigneCommandeFournisseurDto>)commandeFournisseurDto.LignesCommande).Add(new LigneCommandeFournisseurDto
                                    {
                                        Id = ligne.Id,
                                        CommandeId = ligne.CommandeId,
                                        ProduitId = ligne.ProduitId,
                                        NomProduit = ligne.Produit?.Nom ?? "Produit inconnu",
                                        ReferenceProduit = ligne.Produit?.ReferenceOriginal ?? "",
                                        Quantite = ligne.Quantite,
                                        PrixUnitaire = ligne.PrixUnitaire,
                                        TotalLigne = ligne.MontantLigne,
                                        ImagePrincipale = ligne.Produit?.Images?.FirstOrDefault(img => img.IsMain)?.ImageUrl
                                    });
                                }
                            }

                            dto.CommandesFournisseurs.Add(commandeFournisseurDto);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"Erreur lors du mapping de la commande fournisseur {cf.Id}");
                        }
                    }
                }

                // Mise à jour des montants calculés
                dto.MontantHT = montantHT;
                dto.MontantTVA = montantTVA;
                dto.FraisLivraison = commande.FraisLivraison;

                return dto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors du mapping de la commande {commande.Id}");
                throw;
            }
        }
    }
}
