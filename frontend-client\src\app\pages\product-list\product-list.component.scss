$mobile-breakpoint: 1400px;
$drawer-width: 400px;
$header-height: 70px;

.product-list-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 16px;
}

.filter-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;

  &:hover {
    background-color: var(--primary-color-hover);
    transform: translateY(-2px);
  }

  mat-icon {
    font-size: 1rem;
  }
}

.filter-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.4);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 999;

  &.show {
    opacity: 1;
    pointer-events: auto;
  }
}

.filter-drawer {
  position: fixed;
  top: 0;
  left: -400px;
  width: $drawer-width;
  max-width: 90vw;
  height: 100vh;
  background-color: var(--card-background-color);
  box-shadow: 4px 0 12px rgba(0, 0, 0, 0.1);
  border-right: 1px solid var(--border-color);
  transition: left 0.3s ease;
  z-index: 1000;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  &.open {
    left: 0;
  }

  .filter-drawer-header {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--card-background-color);

    h2 {
      margin: 0;
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--text-color);
    }

    .close-button {
      background: transparent;
      border: none;
      cursor: pointer;

      mat-icon {
        font-size: 1.5rem;
        color: var(--text-color);
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.2);
          color: var(--primary-color);
        }
      }
    }
  }
  app-filter {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
  }
}

/* Grille */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--gap);
  padding: 16px;

  @media (max-width: 600px) {
    grid-template-columns: 1fr;
  }
}

.card {
  border-radius: var(--card-radius);
  background: var(--card-background-color);
  box-shadow: var(--shadow);
  overflow: hidden;
  border: 1px solid var(--border-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    background-color: var(--card-background-color-hover);
  }
}

.card-image {
  position: relative;
  aspect-ratio: 2 / 1;
  background-color: white;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.product-badges {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  flex-direction: row;
  gap: 0.3rem;
  z-index: 5;

  .badge-new {
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.6rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
  }

  .badge-discount {
    background-color: var(--secondary-color);
    color: white;
    padding: 0.25rem 0.6rem;
    border-radius: 8px;
    font-size: 0.75rem;
    font-weight: 600;
  }
}

.card-content {
  padding: 16px;

  h3 {
    margin: 0 0 8px;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
    cursor: pointer;
    &:hover {
      color: var(--primary-color);
      text-decoration: underline;
    }
  }

  .truncate {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 16px;
    color: var(--text-color);
  }
}

.product-info-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;

  .product-brand,
  .product-fournisseur,
  .rating-section {
    justify-content: flex-end;
    text-align: right;
  }
}

.product-brand {
  color: var(--text-color);
  font-weight: 700;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
  margin-right: 3px;

  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.product-fournisseur {
  color: var(--secondary-color);
  font-style: italic;
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
  margin-right: 3px;

  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.rating-section {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 3px;

  mat-icon {
    font-size: 1rem;
    color: var(--secondary-color);

    &.filled {
      color: var(--primary-color);
    }
  }

  .review-count {
    font-size: 0.75rem;
    color: var(--text-color);
  }
}

.price-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .price-stack {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;

    .price-final {
      font-size: 1.3rem;
      font-weight: 700;
      color: var(--text-color);
    }

    .price-intermediate {
      font-size: 0.95rem;
      color: var(--text-color);

      .amount {
        font-weight: 600;
        text-decoration: line-through;
        margin-left: 0.3rem;
      }
    }

    .price-original {
      font-size: 0.85rem;
      color: var(--text-color);

      .amount {
        text-decoration: line-through;
        font-weight: 400;
        margin-left: 0.3rem;
      }
    }
  }
}

.button-container {
  margin-top: 25px;
  position: absolute;
  bottom: 25px;
  right: 25px;
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;

  .p-button {
    min-width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);

      .pi {
        transform: scale(1.1);
      }
    }

    &.p-button-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      color: white;

      &:hover {
        background-color: var(--primary-color-hover);
      }
    }

    &.p-button-secondary {
      background-color: transparent;
      border: 2px solid var(--secondary-color);
      color: var(--secondary-color);

      &:hover {
        background-color: var(--secondary-color);
        color: white;
      }
    }

    .pi {
      transition: transform 0.2s ease;
    }
  }
}

.pagination-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 16px;
  margin-top: 2rem;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 2rem 0 3rem;
  z-index: 10;
  padding: 0 16px;

  .pagination-inner {
    background: var(--card-background-color);
    border-radius: 12px;
    box-shadow: var(--shadow);
    padding: 8px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  ::ng-deep .p-paginator {
    background: transparent;
    border: none;

    .p-paginator-pages .p-paginator-page {
      min-width: 40px;
      height: 40px;
      margin: 0 4px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &.p-highlight {
        background-color: var(--primary-color);
        color: var(--background-color);
        transform: scale(1.05);
      }

      &:not(.p-highlight):hover {
        background-color: var(--card-background-color-hover);
        transform: translateY(-2px);
      }
    }

    .p-paginator-first,
    .p-paginator-prev,
    .p-paginator-next,
    .p-paginator-last {
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--card-background-color-hover);
        transform: translateY(-2px);
      }
    }
  }
}
