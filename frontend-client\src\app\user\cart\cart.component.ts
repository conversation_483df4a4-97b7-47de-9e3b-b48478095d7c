import { Component, OnInit } from '@angular/core';
import { CartService } from '../../services/cart.service';
import { AuthService } from '../../auth/auth.service';
import { PanierDto } from 'src/app/models/PanierDto';
import { ItemPanierDto } from 'src/app/models/ItemPanierDto';
import { ProduitService } from 'src/app/services/produit.service';
import { ImageUrlService } from 'src/app/services/image-url.service';
import { ProduitCard } from 'src/app/models/ProduitCard';
import { Router } from '@angular/router';
import { forkJoin, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { FavoritesService } from 'src/app/services/favorites.service';

@Component({
  selector: 'app-cart',
  templateUrl: './cart.component.html',
  styleUrls: ['./cart.component.scss'],
  standalone:false
})
export class CartComponent implements OnInit {
  cart: PanierDto | null = null;
  cartItemsWithDetails: Array<ItemPanierDto & { produitDetails?: ProduitCard }> = [];
  loading = false;
  promoCode = '';
  errorMessage = '';
  productImageCache: Map<number, string> = new Map();

  constructor(
    private cartService: CartService,
    private authService: AuthService,
    private produitService: ProduitService,
    private favoritesService: FavoritesService,
    public imageUrlService: ImageUrlService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadCart();
  }

  loadCart(): void {
    this.loading = true;
    const clientId = this.authService.getCurrentUser()?.id;
    if (!clientId) {
      this.loading = false;
      return;
    }

    this.cartService.getOrCreateCart(clientId).subscribe({
      next: (cart) => {
        this.cart = cart;
        if (cart && cart.items && cart.items.length > 0) {
          this.loadCartItemsWithDetails();
        } else {
          this.cartItemsWithDetails = [];
          this.loading = false;
        }
      },
      error: (err) => {
        console.error('Erreur lors du chargement du panier:', err);
        this.errorMessage = 'Erreur lors du chargement du panier';
        this.loading = false;
      }
    });
  }

  private loadCartItemsWithDetails(): void {
    if (!this.cart || this.cart.items.length === 0) {
      this.cartItemsWithDetails = [];
      this.loading = false;
      return;
    }

    // Récupérer les détails de chaque produit
    const produitRequests = this.cart.items.map(item =>
      this.produitService.getById(item.produitId).pipe(
        map(produit => ({ ...item, produitDetails: produit })),
        catchError(error => {
          console.error(`Erreur lors de la récupération du produit ${item.produitId}:`, error);
          return of({ ...item, produitDetails: undefined });
        })
      )
    );

    forkJoin(produitRequests).subscribe({
      next: (itemsWithDetails) => {
        this.cartItemsWithDetails = itemsWithDetails;
        this.loading = false;
      },
      error: (err) => {
        console.error('Erreur lors du chargement des détails des produits:', err);
        // En cas d'erreur, utiliser les items du panier sans les détails des produits
        this.cartItemsWithDetails = this.cart!.items.map(item => ({ ...item, produitDetails: undefined }));
        this.loading = false;
      }
    });
  }

  updateQuantity(item: ItemPanierDto, newQuantity: number): void {
    if (newQuantity < 1) return;

    this.cartService.updateItem(item.id, { quantite: newQuantity }).subscribe({
      next: () => this.loadCart(),
      error: () => this.errorMessage = 'Erreur lors de la mise à jour'
    });
  }

  removeItem(itemId: number): void {
    this.cartService.removeItem(itemId).subscribe({
      next: () => this.loadCart(),
      error: () => this.errorMessage = 'Erreur lors de la suppression'
    });
  }

  moveToFavorites(item: ItemPanierDto & { produitDetails?: ProduitCard }): void {
    const clientId = this.getClientId();
    if (!clientId) {
      this.errorMessage = 'Veuillez vous connecter pour gérer vos favoris.';
      return;
    }

    // D'abord ajouter aux favoris
    this.favoritesService.ajouterFavori(clientId, item.produitId).subscribe({
      next: () => {
        // Ensuite supprimer du panier
        this.cartService.removeItem(item.id).subscribe({
          next: () => {
            this.loadCart();
            // Message de succès discret
            this.errorMessage = '';
            // Vous pouvez ajouter une notification toast ici si vous en avez
            console.log('Produit déplacé vers les favoris avec succès');
          },
          error: () => {
            this.errorMessage = 'Erreur lors de la suppression du panier';
          }
        });
      },
      error: (err) => {
        if (err.error?.includes && err.error.includes('déjà en favori')) {
          // Si le produit est déjà en favoris, on le supprime juste du panier
          this.cartService.removeItem(item.id).subscribe({
            next: () => {
              this.loadCart();
              this.errorMessage = '';
              console.log('Produit supprimé du panier (déjà en favoris)');
            },
            error: () => {
              this.errorMessage = 'Erreur lors de la suppression du panier';
            }
          });
        } else {
          this.errorMessage = 'Erreur lors de l\'ajout aux favoris';
        }
      }
    });
  }

  private getClientId(): number | null {
    const user = this.authService.getCurrentUser();
    return user ? user.id : null;
  }

  applyPromoCode(): void {
    if (!this.cart || !this.promoCode) return;

    this.cartService.applyPromoCode(this.cart.id, this.promoCode).subscribe({
      next: () => {
        this.loadCart(); // Recharger le panier pour voir les changements
        this.promoCode = '';
        this.errorMessage = '';
      },
      error: (err) => {
        this.errorMessage = err.error?.message || 'Code promo invalide';
      }
    });
  }

  clearCart(): void {
    if (!this.cart) return;

    if (confirm('Vider complètement votre panier ?')) {
      this.cartService.clearCart(this.cart.id).subscribe({
        next: () => this.loadCart(),
        error: () => this.errorMessage = 'Erreur lors de la vidange'
      });
    }
  }

  getTotalItems(): number {
    return this.cartItemsWithDetails.reduce((sum, item) => sum + item.quantite, 0) || 0;
  }

  // Calculer le sous-total d'un item
  getItemSubtotal(item: ItemPanierDto & { produitDetails?: ProduitCard }): number {
    // Utiliser sousTotal s'il existe et est valide, sinon calculer
    if (item.sousTotal && item.sousTotal > 0) {
      return item.sousTotal;
    }

    // Calculer basé sur le prix après promotion ou prix unitaire
    const prix = item.prixApresPromotion || item.prixUnitaire || 0;
    const quantite = item.quantite || 0;
    return prix * quantite;
  }

  // Obtenir le prix unitaire effectif (avec promotion si applicable)
  getEffectivePrice(item: ItemPanierDto): number {
    return item.prixApresPromotion || item.prixUnitaire || 0;
  }

  // Formater le prix en DT
  formatPrice(price: number | null | undefined): string {
    const validPrice = price || 0;
    return `${validPrice.toFixed(2)} DT`;
  }

  // Calculer le total du panier
  getCartTotal(): number {
    if (!this.cartItemsWithDetails || this.cartItemsWithDetails.length === 0) {
      return 0;
    }
    return this.cartItemsWithDetails.reduce((sum, item) => {
      return sum + this.getItemSubtotal(item);
    }, 0);
  }

  // Calculer le total avant promotions
  getSubtotalBeforePromo(): number {
    if (!this.cartItemsWithDetails || this.cartItemsWithDetails.length === 0) {
      return 0;
    }
    return this.cartItemsWithDetails.reduce((sum, item) => {
      const prix = item.prixUnitaire || 0;
      const quantite = item.quantite || 0;
      return sum + (prix * quantite);
    }, 0);
  }

  // Calculer les économies totales
  getTotalSavings(): number {
    if (!this.cartItemsWithDetails || this.cartItemsWithDetails.length === 0) {
      return 0;
    }
    return this.cartItemsWithDetails.reduce((sum, item) => {
      if (item.prixApresPromotion && item.prixApresPromotion < item.prixUnitaire) {
        const prixOriginal = item.prixUnitaire || 0;
        const prixPromo = item.prixApresPromotion || 0;
        const quantite = item.quantite || 0;
        const savings = (prixOriginal - prixPromo) * quantite;
        return sum + savings;
      }
      return sum;
    }, 0);
  }

  getProductImage(item: ItemPanierDto & { produitDetails?: ProduitCard }): string {
    // Vérifier le cache d'abord
    if (this.productImageCache.has(item.produitId)) {
      return this.productImageCache.get(item.produitId)!;
    }

    let finalImageUrl = this.imageUrlService.getPlaceholderUrl();

    // Essayer d'abord avec les détails du produit
    if (item.produitDetails?.images && item.produitDetails.images.length > 0) {
      const mainImage = item.produitDetails.images.find(img => img.isMain);
      const imageUrl = mainImage?.imageUrl || item.produitDetails.images[0]?.imageUrl;
      if (imageUrl) {
        finalImageUrl = this.imageUrlService.getProduitImageUrl(imageUrl);
      }
    }
    // Fallback: essayer avec imageUrl du produit de base si disponible
    else if (item.produitDetails?.imageUrl) {
      finalImageUrl = this.imageUrlService.getProduitImageUrl(item.produitDetails.imageUrl);
    }
    // Si pas de détails, charger asynchrone
    else if (!item.produitDetails) {
      this.loadProductImageAsync(item.produitId);
      finalImageUrl = this.imageUrlService.getPlaceholderUrl();
    }

    // Mettre en cache
    this.productImageCache.set(item.produitId, finalImageUrl);
    return finalImageUrl;
  }

  getProductPrice(item: ItemPanierDto & { produitDetails?: ProduitCard }): number {
    // Utiliser le prix après promotion s'il existe, sinon le prix unitaire du panier
    return item.prixApresPromotion || item.prixUnitaire;
  }

  hasPromotion(item: ItemPanierDto & { produitDetails?: ProduitCard }): boolean {
    return !!item.prixApresPromotion && item.prixApresPromotion < item.prixUnitaire;
  }

  getPromotionPercentage(item: ItemPanierDto & { produitDetails?: ProduitCard }): number {
    if (!this.hasPromotion(item)) return 0;
    const reduction = item.prixUnitaire - item.prixApresPromotion!;
    return Math.round((reduction / item.prixUnitaire) * 100);
  }

  // Méthode alternative pour charger les images de manière asynchrone
  loadProductImageAsync(produitId: number): void {
    if (this.productImageCache.has(produitId)) {
      return; // Déjà en cache
    }

    this.produitService.getById(produitId).subscribe({
      next: (produit) => {
        let imageUrl = this.imageUrlService.getPlaceholderUrl();

        if (produit.images?.length > 0) {
          const mainImage = produit.images.find(img => img.isMain);
          const rawImageUrl = mainImage?.imageUrl || produit.images[0].imageUrl;
          if (rawImageUrl) {
            imageUrl = this.imageUrlService.getProduitImageUrl(rawImageUrl);
          }
        } else if (produit.imageUrl) {
          imageUrl = this.imageUrlService.getProduitImageUrl(produit.imageUrl);
        }

        this.productImageCache.set(produitId, imageUrl);
      },
      error: (err) => {
        console.error(`Erreur lors du chargement de l'image pour le produit ${produitId}:`, err);
        this.productImageCache.set(produitId, this.imageUrlService.getPlaceholderUrl());
      }
    });
  }

  // Méthode simple pour obtenir l'image d'un produit
  getSimpleProductImage(item: ItemPanierDto & { produitDetails?: ProduitCard }): string {
    // Si on a les détails du produit avec des images
    if (item.produitDetails?.images && item.produitDetails.images.length > 0) {
      const mainImage = item.produitDetails.images.find(img => img.isMain);
      const imageUrl = mainImage?.imageUrl || item.produitDetails.images[0]?.imageUrl;
      if (imageUrl) {
        return this.imageUrlService.getProduitImageUrl(imageUrl);
      }
    }

    // Si on a l'imageUrl de base du produit
    if (item.produitDetails?.imageUrl) {
      return this.imageUrlService.getProduitImageUrl(item.produitDetails.imageUrl);
    }

    return this.imageUrlService.getPlaceholderUrl();
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = this.imageUrlService.getPlaceholderUrl();
    }
  }

  // Vérifier si des produits sont en rupture de stock
  hasOutOfStockItems(): boolean {
    return this.cartItemsWithDetails.some(item =>
      item.produitDetails && item.produitDetails.stock < item.quantite
    );
  }

  // Obtenir les produits en rupture de stock
  getOutOfStockItems(): Array<ItemPanierDto & { produitDetails?: ProduitCard }> {
    return this.cartItemsWithDetails.filter(item =>
      item.produitDetails && item.produitDetails.stock < item.quantite
    );
  }

  // Méthode pour passer la commande - Redirection vers le processus de checkout
  passerCommande(): void {
    const clientId = this.authService.getCurrentUser()?.id;

    if (!clientId) {
      alert('Veuillez vous connecter pour passer une commande.');
      this.router.navigate(['/auth/login']);
      return;
    }

    if (!this.cart || this.cart.items.length === 0) {
      alert('Votre panier est vide.');
      return;
    }

    // Vérifier le stock avant de permettre le checkout
    if (this.hasOutOfStockItems()) {
      const outOfStockItems = this.getOutOfStockItems();
      let message = 'Impossible de passer la commande. Les produits suivants ne sont plus disponibles en quantité suffisante :\n\n';

      outOfStockItems.forEach(item => {
        message += `• ${item.nomProduit} - Demandé: ${item.quantite}, Disponible: ${item.produitDetails?.stock || 0}\n`;
      });

      message += '\nVeuillez ajuster les quantités avant de continuer.';
      alert(message);
      return;
    }

    // Rediriger vers le processus de checkout en plusieurs étapes
    this.router.navigate(['/user/checkout']);
  }
}
