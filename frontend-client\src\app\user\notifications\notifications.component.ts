import { Compo<PERSON>, OnIni<PERSON>, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subscription } from 'rxjs';
import { NotificationService, NotificationDto } from '../../services/notification.service';
import { AuthService } from '../../auth/auth.service';

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    MatChipsModule,
    MatTooltipModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="notifications-container">
      <div class="notifications-header">
        <h2>Mes Notifications</h2>
        <div class="header-actions">
          <button 
            mat-raised-button 
            color="primary"
            *ngIf="unreadCount > 0"
            (click)="markAllAsRead()">
            <mat-icon>done_all</mat-icon>
            Tout marquer comme lu ({{ unreadCount }})
          </button>
        </div>
      </div>

      <div class="notifications-stats" *ngIf="notifications.length > 0">
        <mat-chip-set>
          <mat-chip>
            <mat-icon>notifications</mat-icon>
            {{ notifications.length }} notification(s)
          </mat-chip>
          <mat-chip *ngIf="unreadCount > 0" color="warn">
            <mat-icon>circle</mat-icon>
            {{ unreadCount }} non lue(s)
          </mat-chip>
        </mat-chip-set>
      </div>

      <div class="notifications-content">
        <div *ngIf="loading" class="loading-container">
          <mat-spinner></mat-spinner>
          <p>Chargement des notifications...</p>
        </div>

        <div *ngIf="!loading && notifications.length === 0" class="empty-state">
          <mat-icon class="empty-icon">notifications_off</mat-icon>
          <h3>Aucune notification</h3>
          <p>Vous n'avez aucune notification pour le moment.</p>
        </div>

        <div *ngIf="!loading && notifications.length > 0" class="notifications-list">
          <mat-card 
            *ngFor="let notification of notifications; trackBy: trackByNotificationId"
            class="notification-card"
            [class.unread]="!notification.estLue">
            
            <mat-card-header>
              <div class="notification-status">
                <mat-icon 
                  *ngIf="!notification.estLue" 
                  class="unread-indicator"
                  matTooltip="Non lu">
                  circle
                </mat-icon>
              </div>
              
              <mat-card-title class="notification-content">
                {{ notification.contenu }}
              </mat-card-title>
              
              <div class="notification-actions">
                <button 
                  mat-icon-button
                  *ngIf="!notification.estLue"
                  (click)="markAsRead(notification)"
                  matTooltip="Marquer comme lu">
                  <mat-icon>done</mat-icon>
                </button>
                
                <button 
                  mat-icon-button
                  (click)="deleteNotification(notification.id)"
                  matTooltip="Supprimer"
                  class="delete-btn">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </mat-card-header>
            
            <mat-card-content>
              <div class="notification-meta">
                <span class="notification-date">
                  <mat-icon>schedule</mat-icon>
                  {{ formatDate(notification.dateEnvoi) }}
                </span>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .notifications-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 24px;
    }

    .notifications-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .notifications-header h2 {
      margin: 0;
      color: var(--primary-color);
    }

    .notifications-stats {
      margin-bottom: 24px;
    }

    .loading-container {
      text-align: center;
      padding: 48px;
    }

    .loading-container mat-spinner {
      margin: 0 auto 16px;
    }

    .empty-state {
      text-align: center;
      padding: 48px;
      color: var(--text-muted);
    }

    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
    }

    .notifications-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .notification-card {
      transition: all 0.3s ease;
      border-left: 4px solid transparent;
    }

    .notification-card.unread {
      border-left-color: var(--primary-color);
      background-color: var(--unread-bg);
    }

    .notification-card:hover {
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      transform: translateY(-2px);
    }

    .notification-card mat-card-header {
      display: flex;
      align-items: flex-start;
      gap: 12px;
    }

    .notification-status {
      flex-shrink: 0;
      padding-top: 4px;
    }

    .unread-indicator {
      color: var(--primary-color);
      font-size: 12px;
      width: 12px;
      height: 12px;
    }

    .notification-content {
      flex: 1;
      font-size: 16px;
      line-height: 1.5;
      margin: 0;
    }

    .notification-actions {
      display: flex;
      gap: 4px;
      flex-shrink: 0;
    }

    .delete-btn {
      color: var(--error-color);
    }

    .notification-meta {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-top: 8px;
    }

    .notification-date {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 14px;
      color: var(--text-muted);
    }

    .notification-date mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    @media (max-width: 768px) {
      .notifications-container {
        padding: 16px;
      }

      .notifications-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
      }

      .notification-card mat-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .notification-actions {
        align-self: flex-end;
      }
    }

    :host {
      --primary-color: #1976d2;
      --text-muted: #666;
      --unread-bg: #e3f2fd;
      --error-color: #f44336;
    }

    [data-theme="dark"] :host {
      --text-muted: #aaa;
      --unread-bg: #1a237e;
    }
  `]
})
export class NotificationsComponent implements OnInit, OnDestroy {
  notifications: NotificationDto[] = [];
  unreadCount = 0;
  loading = true;
  private subscriptions = new Subscription();

  constructor(
    private notificationService: NotificationService,
    private authService: AuthService
  ) {}

  ngOnInit() {
    this.subscriptions.add(
      this.notificationService.notifications$.subscribe(notifications => {
        this.notifications = notifications;
        this.loading = false;
      })
    );

    this.subscriptions.add(
      this.notificationService.unreadCount$.subscribe(count => {
        this.unreadCount = count;
      })
    );

    this.loadNotifications();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  loadNotifications() {
    const currentUser = this.authService.getCurrentUser();
    if (currentUser?.id) {
      this.notificationService.getUserNotifications(currentUser.id).subscribe({
        error: () => this.loading = false
      });
    } else {
      this.loading = false;
    }
  }

  markAsRead(notification: NotificationDto) {
    this.notificationService.markAsRead(notification.id).subscribe();
  }

  markAllAsRead() {
    const unreadNotifications = this.notifications.filter(n => !n.estLue);
    unreadNotifications.forEach(notification => {
      this.notificationService.markAsRead(notification.id).subscribe();
    });
  }

  deleteNotification(notificationId: number) {
    this.notificationService.deleteNotification(notificationId).subscribe();
  }

  trackByNotificationId(index: number, notification: NotificationDto): number {
    return notification.id;
  }

  formatDate(date: Date): string {
    const now = new Date();
    const notifDate = new Date(date);
    const diffInMinutes = Math.floor((now.getTime() - notifDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''}`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `Il y a ${diffInHours} heure${diffInHours > 1 ? 's' : ''}`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;
    
    if (diffInDays < 30) {
      const diffInWeeks = Math.floor(diffInDays / 7);
      return `Il y a ${diffInWeeks} semaine${diffInWeeks > 1 ? 's' : ''}`;
    }
    
    return notifDate.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  }
}
