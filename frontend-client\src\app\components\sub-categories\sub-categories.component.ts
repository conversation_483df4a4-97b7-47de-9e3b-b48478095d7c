import {
  Component,
  Input,
  OnChang<PERSON>,
  OnDestroy,
  SimpleChanges,
  Output,
  EventEmitter,
} from '@angular/core';
import { Router } from '@angular/router';
import { catchError, finalize, forkJoin, of } from 'rxjs';
import { FormeDto } from 'src/app/models/FormeDto';
import { ProduitCard } from 'src/app/models/ProduitCard';
import { SousCategorieDto } from 'src/app/models/SousCategorieDto';
import { FormeService } from 'src/app/services/forme.service';
import { SousCategorieService } from 'src/app/services/sous-categorie.service';

@Component({
  standalone: false,
  providers:[FormeService,SousCategorieService],
  selector: 'app-sub-categories',
  templateUrl: './sub-categories.component.html',
  styleUrls: ['./sub-categories.component.scss'],
})
export class SubCategoriesComponent implements OnChanges, OnDestroy {
  @Input() categoryId: number | null = null;
  @Output() mouseLeave = new EventEmitter<void>();
  @Input() isMenuOpen: boolean = false;
  @Output() closeMenuEvent = new EventEmitter<void>();

  isActive = false;
  subCategories: SousCategorieDto[] = [];
  formes: FormeDto[] = [];
  produitsNouveautes: ProduitCard[] = [];
  loading = false;
  error: string | null = null;

  private closeTimer: number | null = null;
  private previousCategoryId: number | null = null;
  
  constructor(
    private sousCategorieService: SousCategorieService,
    private formeService: FormeService,
    private router: Router
  ) {}
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['categoryId']) {
      this.clearTimer();
      if (this.categoryId !== this.previousCategoryId) {
        this.previousCategoryId = this.categoryId;
        this.loadData();
        this.isActive = true;
      } else {
        this.closeMenu();
      }
    }
  }
  private loadData(): void {
    if (!this.categoryId) return;
    this.loading = true;
    this.error = null;

    forkJoin([
      this.sousCategorieService.getByCategorie(this.categoryId).pipe(
        catchError(() => of([] as SousCategorieDto[]))
      ),
      this.formeService.getByCategorie(this.categoryId).pipe(
        catchError(() => of([] as FormeDto[]))
      )
    ]).pipe(
      finalize(() => this.loading = false)
    ).subscribe({
      next: ([subCategories, formes]) => {
        this.subCategories = subCategories;
        this.formes = formes;        
      },
      error: (err) => {
        console.error(err);
        this.error = 'Erreur lors du chargement des données';
      }
    });
  }

  closeMenu(): void {
    this.isActive = false;
    this.mouseLeave.emit();
  }

  navigateToForme(formeId: number): void {
    if (!formeId) {
      console.error('Forme ID is missing');
      return;
    }
    
    this.router.navigate(['/products/form', formeId]);
    this.closeMenu();
    this.closeMenuEvent.emit(); 
  }

  navigateToSubCategory(subCategoryId?: number): void {
    if (!subCategoryId) return;
    this.router.navigate(['/products/subcategory', subCategoryId]);
    this.closeMenu();
    this.closeMenuEvent.emit(); 
  }

  handleMouseLeave(): void {
    this.mouseLeave.emit();
  }

  delayClose(): void {
    this.clearTimer();
  }

  private clearTimer(): void {
    if (this.closeTimer) {
      clearTimeout(this.closeTimer);
      this.closeTimer = null;
    }
  }

  ngOnDestroy(): void {
    this.clearTimer();
  }
}
