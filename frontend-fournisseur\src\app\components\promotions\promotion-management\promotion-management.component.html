<div class="promotion-management">
  <!-- En-tête avec statistiques -->
  <div class="page-header">
    <div class="header-content">
      <h1>🎯 Gestion des Promotions</h1>
      <p>Créez et gérez vos promotions : outlet, automatiques et codes promo</p>
    </div>
    
    <div class="header-actions">
      <button class="btn btn-secondary" (click)="showTemplateModal = true">
        📋 Templates
      </button>
      <button class="btn btn-primary" (click)="createPromotion()">
        ➕ Nouvelle Promotion
      </button>
    </div>
  </div>

  <!-- Statistiques rapides -->
  <div class="stats-section" *ngIf="statistiques">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <div class="stat-value">{{ statistiques.totalPromotions }}</div>
          <div class="stat-label">Total promotions</div>
        </div>
      </div>
      
      <div class="stat-card active">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <div class="stat-value">{{ statistiques.promotionsActives }}</div>
          <div class="stat-label">Actives</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">📈</div>
        <div class="stat-content">
          <div class="stat-value">{{ statistiques.totalUtilisations }}</div>
          <div class="stat-label">Utilisations</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">💰</div>
        <div class="stat-content">
          <div class="stat-value">{{ formatPrice(statistiques.montantTotalEconomise) }}</div>
          <div class="stat-label">Économies générées</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Barre d'outils et filtres -->
  <div class="toolbar">
    <div class="search-filters">
      <!-- Recherche -->
      <div class="search-box">
        <input
          type="text"
          [(ngModel)]="searchQuery"
          (input)="applyFilters()"
          placeholder="Rechercher une promotion..."
          class="form-control"
        />
        <span class="search-icon">🔍</span>
      </div>
      
      <!-- Filtres -->
      <div class="filters">
        <select [(ngModel)]="selectedType" (ngModelChange)="applyFilters()" class="form-control">
          <option *ngFor="let type of typesPromotion" [value]="type.value">
            {{ type.label }}
          </option>
        </select>
        
        <select [(ngModel)]="selectedStatut" (ngModelChange)="applyFilters()" class="form-control">
          <option *ngFor="let statut of statutsPromotion" [value]="statut.value">
            {{ statut.label }}
          </option>
        </select>
        
        <button class="btn btn-secondary" (click)="resetFilters()">
          🔄 Réinitialiser
        </button>
      </div>
    </div>
    
    <!-- Actions de vue -->
    <div class="view-actions">
      <div class="view-toggle">
        <button 
          class="btn btn-sm"
          [class.active]="viewMode === 'list'"
          (click)="viewMode = 'list'"
        >
          📋
        </button>
        <button 
          class="btn btn-sm"
          [class.active]="viewMode === 'grid'"
          (click)="viewMode = 'grid'"
        >
          ⊞
        </button>
        <button 
          class="btn btn-sm"
          [class.active]="viewMode === 'stats'"
          (click)="viewMode = 'stats'"
        >
          📊
        </button>
      </div>
    </div>
  </div>

  <!-- Actions en lot -->
  <div class="bulk-actions" *ngIf="showBulkActions">
    <div class="bulk-info">
      <span>{{ selectedPromotions.size }} promotion(s) sélectionnée(s)</span>
    </div>
    <div class="bulk-buttons">
      <button class="btn btn-sm btn-success" (click)="bulkActivatePromotions()">
        ✅ Activer
      </button>
      <button class="btn btn-sm btn-warning" (click)="bulkDeactivatePromotions()">
        ⏸️ Désactiver
      </button>
      <button class="btn btn-sm btn-danger" (click)="bulkDeletePromotions()">
        🗑️ Supprimer
      </button>
    </div>
  </div>

  <!-- Message de chargement -->
  <div *ngIf="isLoading" class="loading">
    <div class="loading-spinner"></div>
    <p>Chargement des promotions...</p>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="error-message">
    <div class="error-icon">❌</div>
    <p>{{ error }}</p>
  </div>

  <!-- Vue Liste -->
  <div class="promotions-table" *ngIf="!isLoading && !error && viewMode === 'list'">
    <div class="table-header">
      <div class="col-select">
        <input 
          type="checkbox" 
          [checked]="selectedPromotions.size === promotions.length && promotions.length > 0"
          (change)="selectAllPromotions()"
        />
      </div>
      <div class="col-name" (click)="changeSortField('nomPromotion')">
        Nom de promotion {{ getSortIcon('nomPromotion') }}
      </div>
      <div class="col-type">Type</div>
      <div class="col-reduction">Réduction</div>
      <div class="col-dates" (click)="changeSortField('dateFin')">
        Validité {{ getSortIcon('dateFin') }}
      </div>
      <div class="col-usage">Utilisation</div>
      <div class="col-status">Statut</div>
      <div class="col-actions">Actions</div>
    </div>
    
    <div *ngFor="let promotion of promotions" class="table-row" [class.selected]="isPromotionSelected(promotion.id)">
      <!-- Sélection -->
      <div class="col-select">
        <input 
          type="checkbox" 
          [checked]="isPromotionSelected(promotion.id)"
          (change)="togglePromotionSelection(promotion.id)"
        />
      </div>
      
      <!-- Nom et description -->
      <div class="col-name">
        <div class="promotion-info">
          <h3>{{ promotion.nomPromotion }}</h3>
          <p>{{ promotion.description }}</p>
          <div class="promotion-meta">
            <span class="code-promo" *ngIf="promotion.codePromo">
              Code: {{ promotion.codePromo }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- Type -->
      <div class="col-type">
        <div class="type-badge" [class]="'type-' + promotion.type">
          <span class="type-icon">{{ getTypeIcon(promotion.type) }}</span>
          <span class="type-text">{{ promotion.type | titlecase }}</span>
        </div>
      </div>
      
      <!-- Réduction -->
      <div class="col-reduction">
        <div class="reduction-value">
          {{ getReductionText(promotion) }}
        </div>
        <div class="reduction-type">
          {{ promotion.typeReduction | titlecase }}
        </div>
      </div>
      
      <!-- Dates -->
      <div class="col-dates">
        <div class="date-range">
          <div class="date-start">{{ formatDate(promotion.dateDebut) }}</div>
          <div class="date-separator">→</div>
          <div class="date-end" [class.expired]="isExpired(promotion)" [class.expiring]="isExpiringSoon(promotion)">
            {{ formatDate(promotion.dateFin) }}
          </div>
        </div>
      </div>
      
      <!-- Utilisation -->
      <div class="col-usage">
        <div class="usage-info" *ngIf="promotion.utilisationMax">
          <div class="usage-text">
            {{ promotion.utilisationActuelle }} / {{ promotion.utilisationMax }}
          </div>
          <div class="usage-bar">
            <div class="usage-progress" [style.width.%]="getUsagePercentage(promotion)"></div>
          </div>
        </div>
        <div class="usage-unlimited" *ngIf="!promotion.utilisationMax">
          {{ promotion.utilisationActuelle }} utilisations
        </div>
      </div>
      
      <!-- Statut -->
      <div class="col-status">
        <span class="status-badge" [class]="getStatusClass(promotion.statut)">
          {{ promotion.statut | titlecase }}
        </span>
      </div>
      
      <!-- Actions -->
      <div class="col-actions">
        <div class="action-buttons">
          <button 
            class="btn btn-sm btn-primary"
            (click)="viewPromotion(promotion)"
            title="Voir les détails"
          >
            👁️
          </button>
          <button 
            class="btn btn-sm btn-secondary"
            (click)="editPromotion(promotion)"
            title="Modifier"
          >
            ✏️
          </button>
          <button 
            class="btn btn-sm"
            [class.btn-success]="promotion.statut !== 'active'"
            [class.btn-warning]="promotion.statut === 'active'"
            (click)="togglePromotionStatus(promotion)"
            [title]="promotion.statut === 'active' ? 'Désactiver' : 'Activer'"
          >
            {{ promotion.statut === 'active' ? '⏸️' : '▶️' }}
          </button>
          <button 
            class="btn btn-sm btn-info"
            (click)="duplicatePromotion(promotion)"
            title="Dupliquer"
          >
            📋
          </button>
          <button 
            class="btn btn-sm btn-danger"
            (click)="confirmDeletePromotion(promotion)"
            title="Supprimer"
          >
            🗑️
          </button>
        </div>
      </div>
    </div>

    <!-- Message si aucune promotion -->
    <div *ngIf="promotions.length === 0" class="no-promotions">
      <div class="no-promotions-icon">🎯</div>
      <h3>Aucune promotion trouvée</h3>
      <p>Créez votre première promotion pour commencer à attirer vos clients.</p>
      <button class="btn btn-primary" (click)="createPromotion()">
        ➕ Créer une promotion
      </button>
    </div>
  </div>

  <!-- Vue Grille -->
  <div class="promotions-grid" *ngIf="!isLoading && !error && viewMode === 'grid'">
    <div *ngFor="let promotion of promotions" class="promotion-card" [class.selected]="isPromotionSelected(promotion.id)">
      <div class="card-header">
        <div class="card-select">
          <input 
            type="checkbox" 
            [checked]="isPromotionSelected(promotion.id)"
            (change)="togglePromotionSelection(promotion.id)"
          />
        </div>
        <div class="card-type">
          <span class="type-icon">{{ getTypeIcon(promotion.type) }}</span>
        </div>
        <div class="card-status">
          <span class="status-badge" [class]="getStatusClass(promotion.statut)">
            {{ promotion.statut }}
          </span>
        </div>
      </div>
      
      <div class="card-content">
        <h3 class="card-title">{{ promotion.nomPromotion }}</h3>
        <p class="card-description">{{ promotion.description }}</p>
        
        <div class="card-reduction">
          <span class="reduction-value">{{ getReductionText(promotion) }}</span>
        </div>
        
        <div class="card-meta">
          <div class="meta-item" *ngIf="promotion.codePromo">
            <span class="meta-label">Code:</span>
            <span class="meta-value">{{ promotion.codePromo }}</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">Expire le:</span>
            <span class="meta-value" [class.expired]="isExpired(promotion)">
              {{ formatDate(promotion.dateFin) }}
            </span>
          </div>
          <div class="meta-item" *ngIf="promotion.utilisationMax">
            <span class="meta-label">Utilisations:</span>
            <span class="meta-value">
              {{ promotion.utilisationActuelle }} / {{ promotion.utilisationMax }}
            </span>
          </div>
        </div>
      </div>
      
      <div class="card-actions">
        <button class="btn btn-sm btn-primary" (click)="viewPromotion(promotion)">
          Voir
        </button>
        <button class="btn btn-sm btn-secondary" (click)="editPromotion(promotion)">
          Modifier
        </button>
        <button 
          class="btn btn-sm"
          [class.btn-success]="promotion.statut !== 'active'"
          [class.btn-warning]="promotion.statut === 'active'"
          (click)="togglePromotionStatus(promotion)"
        >
          {{ promotion.statut === 'active' ? 'Désactiver' : 'Activer' }}
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal de suppression -->
<div class="modal-overlay" *ngIf="showDeleteModal" (click)="cancelDelete()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>Confirmer la suppression</h3>
      <button class="modal-close" (click)="cancelDelete()">✕</button>
    </div>
    <div class="modal-body">
      <p>Êtes-vous sûr de vouloir supprimer la promotion <strong>{{ promotionToDelete?.nomPromotion }}</strong> ?</p>
      <p class="warning">Cette action est irréversible.</p>
    </div>
    <div class="modal-footer">
      <button class="btn btn-secondary" (click)="cancelDelete()">Annuler</button>
      <button class="btn btn-danger" (click)="deletePromotion()">Supprimer</button>
    </div>
  </div>
</div>

<!-- Modal des templates -->
<div class="modal-overlay" *ngIf="showTemplateModal" (click)="showTemplateModal = false">
  <div class="modal-content large" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>Templates de Promotion</h3>
      <button class="modal-close" (click)="showTemplateModal = false">✕</button>
    </div>
    <div class="modal-body">
      <div class="templates-grid">
        <div *ngFor="let template of templates" class="template-card" (click)="createFromTemplate(template)">
          <div class="template-header">
            <span class="template-icon">{{ getTypeIcon(template.type) }}</span>
            <span class="template-badge" *ngIf="template.populaire">Populaire</span>
          </div>
          <h4>{{ template.nomPromotion }}</h4>
          <p>{{ template.description }}</p>
          <div class="template-reduction">
            {{ template.typeReduction === 'pourcentage' ? '-' + template.valeurReduction + '%' : formatPrice(template.valeurReduction) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
