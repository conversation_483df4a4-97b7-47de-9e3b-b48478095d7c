<div class="orders-container">
  <h2>Historique de vos commandes</h2>

  <!-- Message d'erreur -->
  <div *ngIf="errorMessage" class="error-message">
    <p>{{ errorMessage }}</p>
    <button (click)="loadOrders()" class="retry-btn">Réessayer</button>
  </div>

  <!-- Filtre par statut -->
  <div class="filter-section" *ngIf="!errorMessage">
    <select #statusFilter (change)="filterOrders(statusFilter.value)">
      <option value="all">Tous les statuts</option>
      <option value="Brouillon">Brouillon</option>
      <option value="EnAttente">En attente</option>
      <option value="Validee">Validée</option>
      <option value="EnPreparation">En préparation</option>
      <option value="Expediee">Expédiée</option>
      <option value="Livree">Livrée</option>
      <option value="Annulee">Annulée</option>
    </select>
  </div>

  <!-- Liste des commandes -->
  <div *ngIf="filteredOrders.length > 0; else noOrders" class="order-list">
    <div *ngFor="let order of filteredOrders" class="order-card" [ngClass]="'status-' + order.statut">

      <!-- En-tête de la commande -->
      <div class="order-header">
        <div class="order-main-info">
          <h3 class="order-id">Commande #{{ order.id }}</h3>
          <div class="order-meta">
            <span class="order-date">
              <i class="fas fa-calendar"></i>
              {{ order.dateCreation | date:'dd/MM/yyyy à HH:mm' }}
            </span>
            <span class="order-status" [ngClass]="'status-' + order.statut">
              <i class="fas fa-info-circle"></i>
              {{ getStatusLabel(order.statut) }}
            </span>
            <span class="order-items-count">
              <i class="fas fa-shopping-bag"></i>
              {{ getTotalItemsCount(order) }} article(s)
            </span>
          </div>
        </div>
        <div class="order-total">
          <span class="total-label">Total commande</span>
          <span class="total-amount">{{ order.montantTotal | currency:'DT':'symbol':'1.2-2' }}</span>
        </div>
      </div>

      <!-- Commandes fournisseurs -->
      <div class="supplier-orders-section">
        <div *ngIf="order.commandesFournisseurs && order.commandesFournisseurs.length > 0; else noSupplierOrders">
          <h4 class="supplier-orders-title">
            <i class="fas fa-truck"></i>
            Commande divisé en  ({{ order.commandesFournisseurs.length }})
          </h4>

          <div *ngFor="let cmdFournisseur of order.commandesFournisseurs; let i = index" class="supplier-order-card">
            <!-- En-tête fournisseur -->
            <div class="supplier-header">
              <div class="supplier-info">
                <h5 class="supplier-name">{{ cmdFournisseur.nomFournisseur }}</h5>
                <span class="supplier-reference">Réf: {{ cmdFournisseur.reference }}</span>
              </div>
              <div class="supplier-status-info">
                <span class="supplier-status" [ngClass]="'status-' + cmdFournisseur.statut">
                  {{ getSupplierStatusLabel(cmdFournisseur.statut) }}
                </span>
                <span class="supplier-total">{{ cmdFournisseur.montantTotal | currency:'EUR':'symbol':'1.2-2' }}</span>
              </div>
            </div>

            <!-- Détails fournisseur -->
            <div class="supplier-details">
              <div class="supplier-meta">
                <span class="supplier-shipping">
                  <i class="fas fa-shipping-fast"></i>
                  Frais de livraison: {{ cmdFournisseur.fraisLivraison | currency:'EUR':'symbol':'1.2-2' }}
                </span>
                <span class="supplier-fiscal" *ngIf="cmdFournisseur.matriculeFiscale">
                  <i class="fas fa-file-invoice"></i>
                  Matricule: {{ cmdFournisseur.matriculeFiscale }}
                </span>
              </div>
            </div>

            <!-- Articles du fournisseur -->
            <div class="supplier-items">
              <h6 class="supplier-items-title">Articles ({{ cmdFournisseur.lignesCommande.length || 0 }})</h6>
              <div class="supplier-items-list">
                <div *ngFor="let ligne of cmdFournisseur.lignesCommande" class="supplier-item">
                  <div class="item-image">
                    <img
                      [src]="ligne.imagePrincipale || 'assets/images/placeholder.png'"
                      [alt]="ligne.nomProduit"
                      class="product-image"
                      (error)="onImageError($event)"
                    />
                  </div>
                  <div class="item-info">
                    <span class="item-name">{{ ligne.nomProduit }}</span>
                    <span class="item-reference">Réf: {{ ligne.referenceProduit }}</span>
                  </div>
                  <div class="item-pricing">
                    <span class="item-quantity">Qté: {{ ligne.quantite }}</span>
                    <span class="item-unit-price">{{ ligne.prixUnitaire | currency:'EUR':'symbol':'1.2-2' }}/unité</span>
                    <span class="item-total">{{ ligne.totalLigne | currency:'EUR':'symbol':'1.2-2' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <ng-template #noSupplierOrders>
          <div class="no-supplier-orders">
            <i class="fas fa-exclamation-triangle"></i>
            <p>Aucune commande fournisseur générée pour cette commande</p>
          </div>
        </ng-template>
      </div>

      <!-- Actions -->
      <div class="order-actions">
        <button *ngIf="order.statut === 'EnAttente'" (click)="cancelOrder(order.id)" class="btn-cancel">
          <i class="fas fa-times"></i>
          Annuler la commande
        </button>
        <button class="btn-details" (click)="toggleOrderDetails(order.id)">
          <i class="fas fa-eye"></i>
          {{ isOrderExpanded(order.id) ? 'Masquer' : 'Voir' }} les détails
        </button>
      </div>
    </div>
  </div>

  <ng-template #noOrders>
    <div class="no-orders" *ngIf="!isLoading">
      <p>Aucune commande trouvée.</p>
      <p>Commencez vos achats dès maintenant !</p>
      <a routerLink="/products" class="shop-now-btn">Voir les produits</a>
    </div>
  </ng-template>

  <!-- Loading spinner -->
  <div *ngIf="isLoading" class="loading-spinner">
    <p>Chargement de vos commandes...</p>
  </div>
</div>
