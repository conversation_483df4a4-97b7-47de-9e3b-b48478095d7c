/* ===== HISTORIQUE DES COMMANDES - STYLE PROFESSIONNEL ===== */
.orders-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 2rem;
  background: var(--background-color);
  min-height: 100vh;

  h2 {
    color: var(--text-color);
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 2rem;
    text-align: center;
  }

  .filter-section {
    margin-bottom: 2rem;
    display: flex;
    justify-content: center;

    select {
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      border: 1px solid var(--border-color);
      background: var(--card-background-color);
      color: var(--text-color);
      font-size: 1rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 200px;

      &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
      }

      &:hover {
        border-color: var(--primary-color);
      }
    }
  }

  .order-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .order-card {
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    background: var(--card-background-color);
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    overflow: hidden;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    // Couleurs par statut - bordure gauche simple
    &.status-Brouillon { border-left: 4px solid var(--secondary-color); }
    &.status-EnAttente { border-left: 4px solid var(--accent-color-hover); }
    &.status-Validee { border-left: 4px solid var(--primary-color); }
    &.status-EnPreparation { border-left: 4px solid var(--accent-color); }
    &.status-Expediee { border-left: 4px solid var(--accent-color); }
    &.status-Livree { border-left: 4px solid var(--success-color); }
    &.status-Annulee { border-left: 4px solid var(--error-color); }

    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 1.5rem;
      border-bottom: 1px solid var(--border-color);

      .order-main-info {
        flex: 1;

        .order-id {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--primary-color);
          margin: 0 0 0.75rem 0;
        }

        .order-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 1rem;
          align-items: center;

          span {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: var(--text-color-secondary);

            i {
              color: var(--primary-color);
            }
          }

          .order-status {
            padding: 0.4rem 0.8rem;
            border-radius: 1rem;
            font-weight: 600;
            font-size: 0.8rem;
            text-transform: uppercase;

            &.status-Brouillon { background: var(--secondary-color); color: white; }
            &.status-EnAttente { background: var(--accent-color-hover); color: white; }
            &.status-Validee { background: var(--primary-color); color: white; }
            &.status-EnPreparation { background: var(--accent-color); color: white; }
            &.status-Expediee { background: var(--accent-color); color: white; }
            &.status-Livree { background: var(--success-color); color: white; }
            &.status-Annulee { background: var(--error-color); color: white; }
          }
        }
      }

      .order-total {
        text-align: right;

        .total-label {
          display: block;
          font-size: 0.8rem;
          color: var(--text-color-secondary);
          margin-bottom: 0.25rem;
        }

        .total-amount {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--primary-color);
        }
      }
    }

    .supplier-orders-section {
      padding: 1.5rem;

      .supplier-orders-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 1rem;

        i {
          color: var(--primary-color);
        }
      }

      .supplier-order-card {
        background: var(--card-background-color);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        overflow: hidden;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .supplier-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem;
          background: rgba(52, 152, 219, 0.05);
          border-bottom: 1px solid var(--border-color);

          .supplier-info {
            flex: 1;

            .supplier-name {
              font-size: 1.1rem;
              font-weight: 600;
              color: var(--text-color);
              margin: 0 0 0.5rem 0;
            }

            .supplier-reference {
              font-size: 0.8rem;
              color: var(--text-color-secondary);
              font-family: monospace;
              background: rgba(52, 152, 219, 0.1);
              padding: 0.25rem 0.5rem;
              border-radius: 0.25rem;
              font-weight: 500;
              display: inline-block;
            }
          }

          .supplier-status-info {
            text-align: right;

            .supplier-status {
              display: block;
              padding: 0.4rem 0.8rem;
              border-radius: 1rem;
              font-weight: 600;
              font-size: 0.8rem;
              text-transform: uppercase;
              margin-bottom: 0.5rem;

              &.status-EnAttente { background: var(--accent-color-hover); color: white; }
              &.status-Confirmee { background: var(--primary-color); color: white; }
              &.status-EnPreparation { background: var(--accent-color); color: white; }
              &.status-Expediee { background: var(--accent-color); color: white; }
              &.status-Livree { background: var(--success-color); color: white; }
              &.status-Annulee { background: var(--error-color); color: white; }
            }

            .supplier-total {
              font-size: 1.2rem;
              font-weight: 700;
              color: var(--primary-color);
            }
          }
        }

        .supplier-details {
          padding: 1rem;
          border-bottom: 1px solid var(--border-color);
          background: rgba(52, 152, 219, 0.02);

          .supplier-meta {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;

            span {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              font-size: 0.9rem;
              color: var(--text-color-secondary);

              i {
                color: var(--primary-color);
              }
            }
          }
        }

        .supplier-items {
          padding: 1rem;

          .supplier-items-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-color);
            margin: 0 0 0.75rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;

            &::before {
              content: '📦';
              font-size: 0.9rem;
            }
          }

          .supplier-items-list {
            .supplier-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 0.75rem;
              margin-bottom: 0.5rem;
              background: rgba(255, 255, 255, 0.5);
              border-radius: 0.5rem;
              border: 1px solid rgba(52, 152, 219, 0.1);
              transition: all 0.3s ease;

              &:hover {
                background: rgba(52, 152, 219, 0.05);
                transform: translateX(5px);
              }

              .item-image {
                margin-right: 1rem;
                flex-shrink: 0;

                .product-image {
                  width: 50px;
                  height: 50px;
                  object-fit: cover;
                  border-radius: 0.375rem;
                  border: 1px solid rgba(52, 152, 219, 0.2);
                }
              }

              .item-info {
                flex: 1;

                .item-name {
                  font-weight: 600;
                  color: var(--text-color);
                  margin-bottom: 0.25rem;
                }

                .item-reference {
                  font-size: 0.8rem;
                  color: var(--text-color-secondary);
                  font-family: monospace;
                }
              }

              .item-pricing {
                display: flex;
                align-items: center;
                gap: 1rem;
                text-align: right;

                .item-quantity {
                  font-weight: 600;
                  color: var(--primary-color);
                  background: rgba(52, 152, 219, 0.1);
                  padding: 0.25rem 0.5rem;
                  border-radius: 0.25rem;
                  font-size: 0.9rem;
                }

                .item-unit-price {
                  font-size: 0.9rem;
                  color: var(--text-color-secondary);
                }

                .item-total {
                  font-weight: 700;
                  color: var(--primary-color);
                  font-size: 1.1rem;
                }
              }
            }
          }
        }
      }

      .no-supplier-orders {
        text-align: center;
        padding: 2rem;
        color: var(--text-color-secondary);

        i {
          font-size: 2rem;
          color: #f39c12;
          margin-bottom: 1rem;
        }

        p {
          margin: 0;
          font-style: italic;
        }
      }
    }

    .order-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1.5rem;
      background: rgba(52, 152, 219, 0.02);
      border-top: 1px solid var(--border-color);

      button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        border: none;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;

        &.btn-cancel {
          background: var(--error-color);
          color: white;

          &:hover {
            background: #c0392b;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
          }
        }

        &.btn-details {
          background: var(--primary-color);
          color: white;

          &:hover {
            background: var(--primary-color-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
          }
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  .no-orders {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--card-background-color);
    border-radius: 1rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--card-shadow);

    p {
      color: var(--text-color-secondary);
      margin-bottom: 1rem;
      font-size: 1rem;
      line-height: 1.6;
    }

    .shop-now-btn {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 2rem;
      background: var(--primary-color);
      color: white;
      text-decoration: none;
      border-radius: 0.5rem;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
        text-decoration: none;
        color: white;
      }
    }
  }

  .error-message {
    background: rgba(231, 76, 60, 0.1);
    color: var(--error-color);
    border: 1px solid var(--error-color);
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;

    .retry-btn {
      margin-top: 1rem;
      padding: 0.75rem 1.5rem;
      background: var(--error-color);
      color: white;
      border: none;
      border-radius: 0.5rem;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        background: #c0392b;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
      }
    }
  }

  .loading-spinner {
    text-align: center;
    padding: 4rem;
    color: var(--text-color-secondary);

    .spinner {
      width: 40px;
      height: 40px;
      border: 3px solid var(--border-color);
      border-top: 3px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    p {
      margin: 0;
      font-size: 1rem;
      font-weight: 500;
    }
  }
  /* === RESPONSIVE === */
  @media (max-width: 768px) {
    .orders-container {
      padding: 1rem;
      margin: 1rem auto;

      h2 {
        font-size: 1.5rem;
      }

      .filter-section select {
        width: 100%;
        min-width: auto;
      }

      .order-card {
        .order-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 1rem;
          padding: 1rem;

          .order-total {
            align-self: stretch;
            text-align: left;
          }
        }

        .supplier-orders-section {
          padding: 1rem;

          .supplier-order-card {
            .supplier-header {
              flex-direction: column;
              align-items: flex-start;
              gap: 1rem;

              .supplier-status-info {
                align-self: stretch;
                text-align: left;
              }
            }

            .supplier-items {
              .supplier-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;

                .item-image {
                  margin-right: 0;
                  margin-bottom: 0.5rem;
                  align-self: center;

                  .product-image {
                    width: 60px;
                    height: 60px;
                  }
                }

                .item-pricing {
                  align-self: stretch;
                  justify-content: space-between;
                }
              }
            }
          }
        }

        .order-actions {
          flex-direction: column;
          gap: 1rem;

          button {
            width: 100%;
          }
        }
      }
    }
  }
}