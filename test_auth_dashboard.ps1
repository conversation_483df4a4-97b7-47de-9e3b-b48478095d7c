# Test authentification et dashboard
$baseUrl = "http://localhost:5014/api"

Write-Host "=== TEST AUTHENTIFICATION ET DASHBOARD ===" -ForegroundColor Cyan

# Créer un nouveau fournisseur pour être sûr
Write-Host "`n1. Création d'un nouveau fournisseur..." -ForegroundColor Yellow

$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$email = "dashtest$<EMAIL>"
$password = "DashTest123!"

$fournisseurData = @{
    nom = "Dashboard"
    prenom = "Test"
    email = $email
    password = $password
    phoneNumber = "12345678"
    raisonSociale = "Dashboard Test SARL"
    matriculeFiscale = "DASH$timestamp"
    rib = "12345678901234567890"
    codeBanque = "12345"
    commission = 5.0
    delaiPreparationJours = 2
    fraisLivraisonBase = 10.0
    description = "Test dashboard"
} | ConvertTo-Json

$headers = @{"Content-Type" = "application/json"}

try {
    $newFournisseur = Invoke-RestMethod -Uri "$baseUrl/Auth/register/fournisseur" -Method Post -Headers $headers -Body $fournisseurData
    Write-Host "✅ Fournisseur créé - ID: $($newFournisseur.id)" -ForegroundColor Green
    $fournisseurId = $newFournisseur.id
} catch {
    Write-Host "❌ Erreur création: $($_.Exception.Message)" -ForegroundColor Red
    # Utiliser un fournisseur existant
    $fournisseurId = 11
    $email = "<EMAIL>"
    $password = "123456"
    Write-Host "Utilisation du fournisseur existant ID: $fournisseurId" -ForegroundColor Yellow
}

# 2. Connexion
Write-Host "`n2. Connexion..." -ForegroundColor Yellow

$loginData = @{
    email = $email
    motDePasse = $password
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
    Write-Host "✅ Connecté - ID: $($loginResponse.utilisateur.id)" -ForegroundColor Green
    
    $headers["Authorization"] = "Bearer $($loginResponse.token)"
    $fournisseurId = $loginResponse.utilisateur.id
    
} catch {
    Write-Host "❌ Erreur connexion: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Essai avec différents mots de passe..." -ForegroundColor Yellow
    
    # Essayer différents mots de passe courants
    $passwords = @("123456", "password", "admin", "test123")
    $connected = $false
    
    foreach ($pwd in $passwords) {
        $loginData = @{
            email = "<EMAIL>"
            motDePasse = $pwd
        } | ConvertTo-Json
        
        try {
            $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers @{"Content-Type" = "application/json"} -Body $loginData
            Write-Host "✅ Connecté avec mot de passe: $pwd" -ForegroundColor Green
            $headers["Authorization"] = "Bearer $($loginResponse.token)"
            $fournisseurId = $loginResponse.utilisateur.id
            $connected = $true
            break
        } catch {
            # Continue
        }
    }
    
    if (-not $connected) {
        Write-Host "❌ Impossible de se connecter" -ForegroundColor Red
        exit 1
    }
}

# 3. Test des statistiques
Write-Host "`n3. Test des statistiques..." -ForegroundColor Yellow

try {
    $stats = Invoke-RestMethod -Uri "$baseUrl/StatistiquesFournisseur/dashboard/$fournisseurId" -Method Get -Headers $headers
    Write-Host "✅ STATISTIQUES RÉCUPÉRÉES !" -ForegroundColor Green
    
    Write-Host "`n📊 DONNÉES DU DASHBOARD:" -ForegroundColor Cyan
    Write-Host "  - Total produits: $($stats.totalProduits)" -ForegroundColor White
    Write-Host "  - Commandes actives: $($stats.commandesActives)" -ForegroundColor White
    Write-Host "  - Livraisons en cours: $($stats.livraisonsEnCours)" -ForegroundColor White
    Write-Host "  - CA mensuel: $([math]::Round($stats.chiffreAffaireMensuel, 2)) TND" -ForegroundColor Green
    
    if ($stats.topProduits -and $stats.topProduits.Count -gt 0) {
        Write-Host "`n🏆 TOP PRODUITS:" -ForegroundColor Cyan
        foreach ($produit in $stats.topProduits) {
            Write-Host "  - $($produit.nom): $([math]::Round($produit.prix, 2)) TND (Stock: $($produit.stock))" -ForegroundColor White
        }
    }
    
} catch {
    Write-Host "❌ Erreur statistiques: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
}

Write-Host "`n=== CONCLUSION ===" -ForegroundColor Cyan
Write-Host "Si les statistiques s'affichent ici, le problème est dans le frontend" -ForegroundColor White
Write-Host "Si pas de statistiques, le problème est dans le backend" -ForegroundColor White
