import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { CartService } from '../../services/cart.service';
import { AuthService } from '../../auth/auth.service';
import { ProduitService } from '../../services/produit.service';
import { CommandeService } from '../../services/commande.service';
import { ImageUrlService } from '../../services/image-url.service';
import { PanierDto } from '../../models/PanierDto';
import { ItemPanierDto } from '../../models/ItemPanierDto';
import { ProduitCard } from '../../models/ProduitCard';
import { FraisLivraisonResponseDto, FraisLivraisonDetailDto } from '../../models/FraisLivraisonDto';
import { forkJoin, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

@Component({
  selector: 'app-checkout',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './checkout.component.html',
  styleUrls: ['./checkout.component.scss']
})
export class CheckoutComponent implements OnInit {
  cart: PanierDto | null = null;
  cartItemsWithDetails: Array<ItemPanierDto & { produitDetails?: ProduitCard }> = [];
  fraisLivraisonDetails: FraisLivraisonResponseDto | null = null;
  loading = false;
  errorMessage = '';

  constructor(
    private cartService: CartService,
    private authService: AuthService,
    private produitService: ProduitService,
    private commandeService: CommandeService,
    public imageUrlService: ImageUrlService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadCart();
  }

  loadCart(): void {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      this.router.navigate(['/auth/login']);
      return;
    }

    this.loading = true;
    this.cartService.getCart(currentUser.id).subscribe({
      next: (cart) => {
        this.cart = cart;

        // Si le panier est vide, rediriger vers le panier
        if (!cart || cart.items.length === 0) {
          this.router.navigate(['/user/cart']);
          return;
        }

        // Charger les détails des produits
        this.loadProductDetails();

        // Calculer les frais de livraison (avec une adresse par défaut pour l'estimation)
        this.calculerFraisLivraisonEstimation();
      },
      error: (err) => {
        console.error('Erreur lors du chargement du panier:', err);
        this.errorMessage = 'Erreur lors du chargement du panier';
        this.loading = false;
        this.router.navigate(['/user/cart']);
      }
    });
  }

  loadProductDetails(): void {
    if (!this.cart) return;

    // Récupérer les détails de chaque produit
    const produitRequests = this.cart.items.map(item =>
      this.produitService.getById(item.produitId).pipe(
        map(produit => ({ ...item, produitDetails: produit })),
        catchError(error => {
          console.error(`Erreur lors de la récupération du produit ${item.produitId}:`, error);
          return of({ ...item, produitDetails: undefined });
        })
      )
    );

    forkJoin(produitRequests).subscribe({
      next: (itemsWithDetails) => {
        this.cartItemsWithDetails = itemsWithDetails;
        this.loading = false;
      },
      error: (err) => {
        console.error('Erreur lors du chargement des détails des produits:', err);
        this.cartItemsWithDetails = this.cart!.items.map(item => ({ ...item, produitDetails: undefined }));
        this.loading = false;
      }
    });
  }

  // Obtenir l'image d'un produit
  getProductImage(item: ItemPanierDto & { produitDetails?: ProduitCard }): string {
    // Si on a les détails du produit avec des images
    if (item.produitDetails?.images && item.produitDetails.images.length > 0) {
      const mainImage = item.produitDetails.images.find(img => img.isMain);
      const imageUrl = mainImage?.imageUrl || item.produitDetails.images[0]?.imageUrl;
      if (imageUrl) {
        return this.imageUrlService.getProduitImageUrl(imageUrl);
      }
    }

    // Si on a l'imageUrl de base du produit
    if (item.produitDetails?.imageUrl) {
      return this.imageUrlService.getProduitImageUrl(item.produitDetails.imageUrl);
    }

    return this.imageUrlService.getPlaceholderUrl();
  }

  // Calculer le total
  getTotal(): number {
    if (!this.cartItemsWithDetails.length) return 0;
    return this.cartItemsWithDetails.reduce((total, item) => total + (item.prixUnitaire * item.quantite), 0);
  }

  // Calculer le nombre total d'articles
  getTotalItems(): number {
    if (!this.cartItemsWithDetails.length) return 0;
    return this.cartItemsWithDetails.reduce((total, item) => total + item.quantite, 0);
  }

  // Gestion d'erreur d'image
  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = this.imageUrlService.getPlaceholderUrl();
    }
  }

  // Calculer les frais de livraison pour estimation
  calculerFraisLivraisonEstimation(): void {
    if (!this.cart || !this.cart.items.length) {
      return;
    }

    const produitIds = this.cart.items.map(item => item.produitId);

    // Obtenir l'adresse principale du client connecté
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser?.adresses?.length) {
      console.log('Aucune adresse trouvée pour le client');
      this.fraisLivraisonDetails = null;
      return;
    }

    // Utiliser l'adresse principale ou la première adresse disponible
    const adressePrincipale = currentUser.adresses.find(addr => addr.estPrincipale) || currentUser.adresses[0];

    console.log('🚚 Calcul des frais de livraison avec adresse:', adressePrincipale.id);
    this.commandeService.calculerFraisLivraison(adressePrincipale.id, produitIds).subscribe({
      next: (response) => {
        console.log('✅ Frais de livraison calculés:', response);
        this.fraisLivraisonDetails = response;
      },
      error: (err) => {
        console.error('❌ Erreur lors du calcul des frais de livraison:', err);
        this.fraisLivraisonDetails = null;
      }
    });
  }

  // Obtenir le total avec frais de livraison
  getTotalAvecLivraison(): number {
    const sousTotal = this.getTotal();
    const fraisLivraison = this.fraisLivraisonDetails?.fraisLivraisonTotal || 0;
    return sousTotal + fraisLivraison;
  }

  // Continuer vers la sélection d'adresse
  continuerVersAdresse(): void {
    this.router.navigate(['/user/checkout/address']);
  }

  // Retourner au panier
  retourPanier(): void {
    this.router.navigate(['/user/cart']);
  }
}
