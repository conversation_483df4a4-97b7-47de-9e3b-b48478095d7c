import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { DetailsCommandeDto } from '../models/DetailsCommandeDto';
import { BaseService } from './core/base.service';

@Injectable({ providedIn: 'root' })
export class DetailsCommandeService extends BaseService<DetailsCommandeDto> {
  constructor(http: HttpClient) {
    super(http, 'detailscommande');
  }

  // Récupérer tous les détails d'une commande
  getByCommandeId(commandeId: number): Observable<DetailsCommandeDto[]> {
    return this.http.get<DetailsCommandeDto[]>(
      `${this.apiUrl}/${this.endpoint}/commande/${commandeId}`
    );
  }

  // Ajouter un détail à une commande
  ajouterDetail(detail: Partial<DetailsCommandeDto>): Observable<DetailsCommandeDto> {
    return this.http.post<DetailsCommandeDto>(`${this.apiUrl}/${this.endpoint}`, detail);
  }

  // Modifier la quantité d'un détail
  modifierQuantite(detailId: number, nouvelleQuantite: number): Observable<DetailsCommandeDto> {
    return this.http.put<DetailsCommandeDto>(
      `${this.apiUrl}/${this.endpoint}/${detailId}`,
      { quantite: nouvelleQuantite }
    );
  }

  // Supprimer un détail de commande
  supprimerDetail(detailId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${this.endpoint}/${detailId}`);
  }

  // Calculer le total d'une ligne
  calculerTotalLigne(detail: DetailsCommandeDto): number {
    return detail.quantite * detail.prixUnitaireTTC;
  }

  // Calculer le total HT d'une ligne
  calculerTotalLigneHT(detail: DetailsCommandeDto): number {
    return detail.quantite * detail.prixUnitaireHT;
  }

  // Calculer la TVA d'une ligne
  calculerTVALigne(detail: DetailsCommandeDto): number {
    return detail.quantite * detail.montantTVA;
  }

  // Méthodes héritées de BaseService disponibles :
  // - getAll(): Observable<DetailsCommandeDto[]>
  // - getById(id: number): Observable<DetailsCommandeDto>
  // - create(item: DetailsCommandeDto): Observable<DetailsCommandeDto>
  // - update(id: number, item: DetailsCommandeDto): Observable<DetailsCommandeDto>
  // - delete(id: number): Observable<void>
}
