# Debug simple du dashboard
$baseUrl = "http://localhost:5014/api"

Write-Host "=== DEBUG DASHBOARD SIMPLE ===" -ForegroundColor Red

# 1. Vérifier les produits
Write-Host "`n1. Vérification des produits..." -ForegroundColor Yellow
try {
    $produits = Invoke-RestMethod -Uri "$baseUrl/Produits" -Method Get
    Write-Host "✅ $($produits.Count) produits trouvés" -ForegroundColor Green
    
    if ($produits.Count -gt 0) {
        Write-Host "Premier produit:" -ForegroundColor White
        $premier = $produits[0]
        Write-Host "  - ID: $($premier.id)" -ForegroundColor Gray
        Write-Host "  - Nom: $($premier.nom)" -ForegroundColor Gray
        Write-Host "  - Fournisseur: $($premier.fournisseurId)" -ForegroundColor Gray
        Write-Host "  - Prix: $($premier.prixVenteHT) TND" -ForegroundColor Gray
        Write-Host "  - Stock: $($premier.stock)" -ForegroundColor Gray
    } else {
        Write-Host "❌ AUCUN PRODUIT !" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Erreur produits: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Vérifier les commandes
Write-Host "`n2. Vérification des commandes..." -ForegroundColor Yellow
try {
    $commandes = Invoke-RestMethod -Uri "$baseUrl/Commandes" -Method Get
    Write-Host "✅ $($commandes.Count) commandes trouvées" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur commandes: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Test endpoint statistiques
Write-Host "`n3. Test endpoint statistiques..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/StatistiquesFournisseur/dashboard/11" -Method Get -ErrorAction SilentlyContinue
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "✅ Endpoint existe (401 = authentification requise)" -ForegroundColor Green
    } elseif ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "❌ ENDPOINT N'EXISTE PAS (404)" -ForegroundColor Red
    } else {
        Write-Host "❌ Erreur: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

# 4. Vérifier produits d'un fournisseur spécifique
Write-Host "`n4. Produits du fournisseur 11..." -ForegroundColor Yellow
try {
    $produitsFournisseur = Invoke-RestMethod -Uri "$baseUrl/Fournisseurs/11/produits" -Method Get
    Write-Host "✅ $($produitsFournisseur.Count) produits pour fournisseur 11" -ForegroundColor Green
    
    if ($produitsFournisseur.Count -gt 0) {
        Write-Host "Produits du fournisseur 11:" -ForegroundColor White
        foreach ($p in $produitsFournisseur) {
            Write-Host "  - $($p.nom): $($p.prixVenteHT) TND" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== RÉSUMÉ ===" -ForegroundColor Red
Write-Host "Vérifiez les résultats ci-dessus pour identifier le problème" -ForegroundColor White
