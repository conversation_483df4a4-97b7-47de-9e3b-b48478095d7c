﻿using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.ReadDTOs
{
    public class SousCategorieDto
    {
        public int Id { get; set; }
        [Required(ErrorMessage = "Le nom est obligatoire")]
        [StringLength(100, ErrorMessage = "Max 100 caractères")]
        public required string Nom { get; set; }
        [Required(ErrorMessage = "La catégorie est obligatoire")]
        [Range(1, int.MaxValue, ErrorMessage = "ID de catégorie invalide")]
        public int CategorieId { get; set; }
        public string? Description { get; set; }
        public bool EstValidee { get; set; } = false;
        // Variantes pour création/mise à jour
        [Display(Name = "SousCategorieCreate")]
        public class Create : SousCategorieDto { }
        [Display(Name = "SousCategorieUpdate")]
        public class Update : SousCategorieDto { public new int Id { get; set; } }
    }
}
