import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppComponent } from 'src/app/app.component';
import { HomeComponent } from './home.component';
import { LogoCarouselModule } from 'src/app/components/logo-carousel/logo-carousel.module';
import { MeilleuresVentesComponent } from 'src/app/components/meilleures-ventes/meilleures-ventes.component';
import { NouveauxArrivagesComponent } from 'src/app/components/nouveaux-arrivages/nouveaux-arrivages.component';


@NgModule({
  declarations: [
  ],
  imports: [
    HomeComponent,
    CommonModule,
    LogoCarouselModule,
    NouveauxArrivagesComponent,
    MeilleuresVentesComponent,   ],
  providers: [],
  bootstrap: [AppComponent]
})
export class HomeModule { }
