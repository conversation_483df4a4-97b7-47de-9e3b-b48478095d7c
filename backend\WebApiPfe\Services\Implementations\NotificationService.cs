﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Services.Interfaces;
using WebApiPfe.Models.Entity;
namespace WebApiPfe.Services.Implementations
{
    public class NotificationService : INotificationService
    {
        private readonly AppDbContext _context;
        private readonly IMapper _mapper;
        private readonly ILogger<NotificationService> _logger;

        public NotificationService(AppDbContext context, IMapper mapper, ILogger<NotificationService> logger)
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<NotificationDto> GetByIdAsync(int id)
        {
            var notification = await _context.Notifications
                .Include(n => n.Utilisateur)
                .FirstOrDefaultAsync(n => n.Id == id);

            if (notification == null)
                throw new KeyNotFoundException("Notification introuvable");

            return _mapper.Map<NotificationDto>(notification);
        }

        public async Task<List<NotificationDto>> GetByUserAsync(int userId)
        {
            var notifications = await _context.Notifications
                .Where(n => n.UtilisateurId == userId)
                .OrderByDescending(n => n.DateEnvoi)
                .ToListAsync();

            return _mapper.Map<List<NotificationDto>>(notifications);
        }

        public async Task<List<NotificationDto>> GetUnreadByUserAsync(int userId)
        {
            var notifications = await _context.Notifications
                .Where(n => n.UtilisateurId == userId && !n.EstLue)
                .OrderByDescending(n => n.DateEnvoi)
                .ToListAsync();

            return _mapper.Map<List<NotificationDto>>(notifications);
        }

        public async Task<NotificationDto> CreateAsync(CreateNotificationDto dto)
        {
            var userExists = await _context.Utilisateurs.AnyAsync(u => u.Id == dto.UtilisateurId);
            if (!userExists)
                throw new KeyNotFoundException("Utilisateur introuvable");

            var notification = new Notification
            {
                Contenu = dto.Contenu,
                DateEnvoi = DateTime.UtcNow,
                EstLue = false,
                UtilisateurId = dto.UtilisateurId
            };

            _context.Notifications.Add(notification);
            await _context.SaveChangesAsync();

            return await GetByIdAsync(notification.Id);
        }

        public async Task MarkAsReadAsync(int id)
        {
            var notification = await _context.Notifications.FindAsync(id);
            if (notification == null)
                throw new KeyNotFoundException("Notification introuvable");

            notification.EstLue = true;
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var notification = await _context.Notifications.FindAsync(id);
            if (notification == null)
                throw new KeyNotFoundException("Notification introuvable");

            _context.Notifications.Remove(notification);
            await _context.SaveChangesAsync();
        }

        public async Task NotifierTousLesAdminsAsync(string contenu)
        {
            // Récupérer tous les utilisateurs avec le rôle Admin
            var admins = await _context.Users
                .OfType<Admin>()
                .Where(u => u.EstActif)
                .ToListAsync();

            // Créer une notification pour chaque admin
            var notifications = admins.Select(admin => new Notification
            {
                Contenu = contenu,
                DateEnvoi = DateTime.UtcNow,
                EstLue = false,
                UtilisateurId = admin.Id
            }).ToList();

            _context.Notifications.AddRange(notifications);
            await _context.SaveChangesAsync();
        }
    }
}
