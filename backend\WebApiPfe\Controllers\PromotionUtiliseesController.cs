﻿using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Services.Implementations;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PromotionsUtiliseesController : ControllerBase
    {
        private readonly PromotionUtiliseeService _service;

        public PromotionsUtiliseesController(PromotionUtiliseeService service)
        {
            _service = service;
        }

        [HttpPost("apply")]
        public async Task<ActionResult<PromotionUtiliseeDto>> ApplyPromotion([FromBody] ApplyPromotionDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var result = await _service.ApplyPromotionAsync(dto);
                return Ok(result);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("commande/{commandeId}")]
        public async Task<ActionResult<IEnumerable<PromotionUtiliseeDto>>> GetByCommande(int commandeId)
        {
            var result = await _service.GetByCommandeAsync(commandeId);
            return Ok(result);
        }

        [HttpGet("promotion/{promotionId}")]
        public async Task<ActionResult<IEnumerable<PromotionUtiliseeDto>>> GetByPromotion(int promotionId)
        {
            var result = await _service.GetByPromotionAsync(promotionId);
            return Ok(result);
        }

        [HttpGet("economies")]
        public async Task<ActionResult<decimal>> GetTotalSavings(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            var savings = await _service.GetTotalSavingsAsync(startDate, endDate);
            return Ok(savings);
        }
    }
}
