import { Injectable } from '@angular/core';
import { ProduitCard } from '../models/ProduitCard';
import { FavoriClientDto } from '../models/FavoriClientDto';
import { FavoriResponseDto } from '../models/FavoriResponseDto';
import { FavoriCreateDto } from '../models/FavoriCreateDto';
import { Observable, map, forkJoin, of, switchMap, catchError } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { ProduitService } from './produit.service';

@Injectable({
  providedIn: 'root',
})
export class FavoritesService {
  private apiUrl = `${environment.apiUrl}/favoris`;

  constructor(
    private http: HttpClient,
    private produitService: ProduitService
  ) {}

  getFavoris(clientId: number): Observable<FavoriClientDto> {
    // Récupérer la liste des favoris depuis l'API backend
    return this.http.get<FavoriResponseDto[]>(`${this.apiUrl}/client/${clientId}`).pipe(
      switchMap(favoris => {
        if (!favoris || favoris.length === 0) {
          return of({
            produits: [],
            derniersAjouts: []
          });
        }

        // Pour chaque favori, récupérer les détails du produit
        const produitRequests = favoris.map(favori =>
          this.produitService.getById(favori.produitId).pipe(
            catchError(error => {
              console.error(`Erreur lors de la récupération du produit ${favori.produitId}:`, error);
              return of(null); // Retourner null si le produit n'est pas trouvé
            })
          )
        );

        // Attendre que tous les produits soient récupérés
        return forkJoin(produitRequests).pipe(
          map(produits => ({
            produits: produits.filter(p => p !== null) as ProduitCard[],
            derniersAjouts: favoris.map(() => new Date()) // Placeholder pour les dates
          }))
        );
      }),
      catchError(error => {
        console.error('Erreur lors de la récupération des favoris:', error);
        // Si l'API retourne une erreur (ex: 404), retourner une liste vide
        return of({
          produits: [],
          derniersAjouts: []
        });
      })
    );
  }

  // Méthode alternative plus simple qui récupère directement les favoris
  getFavorisSimple(clientId: number): Observable<FavoriResponseDto[]> {
    return this.http.get<FavoriResponseDto[]>(`${this.apiUrl}/client/${clientId}`);
  }

  ajouterFavori(clientId: number, produitId: number): Observable<FavoriResponseDto> {
    const favoriDto: FavoriCreateDto = {
      clientId,
      produitId,
      stockStatus: true,
    };
    return this.http.post<FavoriResponseDto>(this.apiUrl, favoriDto);
  }

  supprimerFavoriParProduitId(clientId: number, produitId: number): Observable<void> {
    // D'abord, trouver l'ID du favori
    return this.getFavorisSimple(clientId).pipe(
      map(favoris => favoris.find(f => f.produitId === produitId)),
      switchMap(favori => {
        if (favori) {
          return this.http.delete<void>(`${this.apiUrl}/${favori.id}`);
        } else {
          throw new Error('Favori non trouvé');
        }
      })
    );
  }

  verifierFavori(clientId: number, produitId: number): Observable<boolean> {
    return this.http.get<boolean>(
      `${this.apiUrl}/verifier?clientId=${clientId}&produitId=${produitId}`
    );
  }
}
