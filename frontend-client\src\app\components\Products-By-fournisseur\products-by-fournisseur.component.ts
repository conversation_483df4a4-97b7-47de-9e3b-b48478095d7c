import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { catchError, finalize, forkJoin, of } from 'rxjs';
import { FournisseurDto } from 'src/app/models/FournisseurDto';
import { ProduitCard } from 'src/app/models/ProduitCard';
import { ProductListComponent } from 'src/app/pages/product-list/product-list.component';
import { FournisseurService } from 'src/app/services/fournisseur.service';
import { ProduitService } from 'src/app/services/produit.service';

@Component({
  selector: 'app-products-by-fournisseur',
  imports: [ProductListComponent],
  providers: [ProduitService, FournisseurService],
  template: `
    <div class="nom-header">
      <h1>{{ fournisseur?.nom }} {{ fournisseur?.prenom }}</h1>
    </div>
    <app-product-list [produits]="produits"></app-product-list>
  `,
  styleUrl: './products-by-fournisseur.component.scss',
})
export class ProductsByFournisseurComponent implements OnInit {
  fournisseurId!: number;
  produits: ProduitCard[] = [];
  fournisseur?: FournisseurDto;
  loading = true;
  error: string | null = null;
  constructor(
    private route: ActivatedRoute,
    private produitService: ProduitService,
    private fournisseurService: FournisseurService
  ) {}
  ngOnInit() {
    this.route.params.subscribe((params) => {
      this.fournisseurId = +params['id'];
      this.chargerDonnees();
    });
  }
  chargerDonnees(): void {
    this.loading = true;
    this.error = null;

    forkJoin([
      this.produitService.getByFournisseur(this.fournisseurId).pipe(
        catchError((err) => {
          console.error('Erreur produits:', err);
          return of([] as ProduitCard[]);
        })
      ),
      this.fournisseurService.getById(this.fournisseurId).pipe(
        catchError((err) => {
          console.error('Erreur marque:', err);
          return of({
            id: this.fournisseurId,
            nom: 'Fournisseur inconnue',
            prenom: 'Fournisseur inconnue',
            logoFile: '',
          } as FournisseurDto);
        })
      ),
    ])
      .pipe(finalize(() => (this.loading = false)))
      .subscribe({
        next: ([produits, fournisseur]) => {
          this.produits = produits;
          this.fournisseur = fournisseur;
        },
        error: (err) => {
          console.error(err);
          this.error = 'Erreur lors du chargement des données';
        },
      });
  }
}
