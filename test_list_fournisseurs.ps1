# Test pour lister les fournisseurs existants
$baseUrl = "http://localhost:5014/api"

Write-Host "=== LISTE DES FOURNISSEURS EXISTANTS ===" -ForegroundColor Cyan

# 1. Récupérer la liste des fournisseurs
Write-Host "`n1. Récupération de la liste des fournisseurs..." -ForegroundColor Yellow

try {
    $fournisseurs = Invoke-RestMethod -Uri "$baseUrl/Fournisseurs" -Method Get
    Write-Host "✅ $($fournisseurs.Count) fournisseurs trouvés !" -ForegroundColor Green
    
    foreach ($fournisseur in $fournisseurs) {
        Write-Host "  - ID: $($fournisseur.id) | Email: $($fournisseur.email) | Nom: $($fournisseur.nom) $($fournisseur.prenom)" -ForegroundColor White
        Write-Host "    Raison sociale: $($fournisseur.raisonSociale)" -ForegroundColor Gray
    }
    
} catch {
    Write-Host "❌ Erreur lors de la récupération des fournisseurs: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 2. Test de connexion avec le premier fournisseur trouvé
if ($fournisseurs -and $fournisseurs.Count -gt 0) {
    $premierFournisseur = $fournisseurs[0]
    Write-Host "`n2. Test de connexion avec le premier fournisseur..." -ForegroundColor Yellow
    Write-Host "Email: $($premierFournisseur.email)" -ForegroundColor White
    
    # Essayer plusieurs mots de passe possibles
    $motsDePasse = @("123456", "password", "Password123", "Test123!", "admin", "fournisseur")
    
    foreach ($mdp in $motsDePasse) {
        Write-Host "`nTest avec mot de passe: $mdp" -ForegroundColor Cyan
        
        $loginData = @{
            email = $premierFournisseur.email
            motDePasse = $mdp
        } | ConvertTo-Json
        
        $headers = @{"Content-Type" = "application/json"}
        
        try {
            $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
            Write-Host "✅ CONNEXION RÉUSSIE avec le mot de passe: $mdp" -ForegroundColor Green
            Write-Host "Token reçu: $($loginResponse.token.Substring(0, 50))..." -ForegroundColor White
            Write-Host "Utilisateur: $($loginResponse.utilisateur.nom) $($loginResponse.utilisateur.prenom)" -ForegroundColor White
            Write-Host "Role: $($loginResponse.utilisateur.role)" -ForegroundColor White
            
            # Test de l'endpoint my-promotions avec ce token
            Write-Host "`n3. Test de l'endpoint my-promotions..." -ForegroundColor Yellow
            $headers["Authorization"] = "Bearer $($loginResponse.token)"
            
            try {
                $myPromotions = Invoke-RestMethod -Uri "$baseUrl/promotions/my-promotions" -Method Get -Headers $headers
                Write-Host "✅ My-promotions récupérées: $($myPromotions.Count)" -ForegroundColor Green
                
                if ($myPromotions.Count -gt 0) {
                    foreach ($promo in $myPromotions) {
                        $status = if ($promo.estValide) { "✅ Active" } else { "❌ Inactive" }
                        Write-Host "  - $($promo.nomAffichage) ($($promo.type)) - $($promo.pourcentageRemise)% - $status" -ForegroundColor White
                    }
                } else {
                    Write-Host "Aucune promotion trouvée pour ce fournisseur" -ForegroundColor Yellow
                }
                
            } catch {
                Write-Host "❌ Erreur my-promotions: $($_.Exception.Message)" -ForegroundColor Red
            }
            
            break # Sortir de la boucle si connexion réussie
            
        } catch {
            if ($_.Exception.Response.StatusCode -eq 401) {
                Write-Host "❌ Mot de passe incorrect" -ForegroundColor Red
            } else {
                Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

Write-Host "`n=== FIN DU TEST ===" -ForegroundColor Cyan
