/* ===== DASHBOARD MODERNE - DESIGN SYSTEM ===== */

.dashboard-container {
  padding: var(--spacing-8);
  max-width: 1400px;
  margin: 0 auto;
  background: transparent;
  min-height: 100vh;
}

/* === HEADER MODERNE === */
.dashboard-header {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-2xl);
  padding: var(--spacing-8);
  margin-bottom: var(--spacing-8);
  box-shadow: var(--shadow-blue-lg);
  position: relative;
  overflow: hidden;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.header-text {
  color: var(--white);
}

.dashboard-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  margin: 0 0 var(--spacing-2) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.title-icon {
  font-size: var(--font-size-3xl);
  animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(20deg); }
  75% { transform: rotate(-10deg); }
}

.dashboard-subtitle {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin: 0;
  font-weight: var(--font-weight-normal);
}

.header-actions {
  display: flex;
  gap: var(--spacing-4);
}

/* === SECTION STATISTIQUES === */
.stats-section {
  margin-bottom: var(--spacing-8);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
}

.stat-card {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-base);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all var(--transition-base);
}

.stat-card.stat-primary::before { background: var(--gradient-primary); }
.stat-card.stat-success::before { background: linear-gradient(90deg, var(--success-500), var(--success-400)); }
.stat-card.stat-warning::before { background: linear-gradient(90deg, var(--warning-500), var(--warning-400)); }
.stat-card.stat-info::before { background: var(--gradient-primary); }

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.stat-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
}

.stat-primary .stat-icon-wrapper { background: var(--primary-100); }
.stat-success .stat-icon-wrapper { background: var(--success-100); }
.stat-warning .stat-icon-wrapper { background: var(--warning-100); }
.stat-info .stat-icon-wrapper { background: var(--primary-100); }

.stat-trend {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-full);
}

.stat-trend.positive {
  background: var(--success-100);
  color: var(--success-700);
}

.stat-trend.neutral {
  background: var(--gray-100);
  color: var(--gray-600);
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
  line-height: 1;
}

.stat-number.loading {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.stat-label {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  margin-bottom: var(--spacing-1);
}

.stat-description {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}

/* === GRILLE PRINCIPALE === */
.dashboard-main {
  margin-bottom: var(--spacing-8);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
}

/* === CARTES DASHBOARD === */
.dashboard-card {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  transition: all var(--transition-base);
  border: 1px solid var(--gray-200);
}

.dashboard-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  border-color: var(--primary-300);
}

/* === CARTES SPÉCIALES === */
.chart-card {
  background: linear-gradient(135deg, var(--white) 0%, #f8faff 100%);
  border: 1px solid #e1e7ff;
}

.orders-card {
  background: linear-gradient(135deg, var(--white) 0%, #f0f4ff 100%);
  border: 1px solid #d1d9ff;
}

.activity-card {
  background: linear-gradient(135deg, var(--white) 0%, #f0fff4 100%);
  border: 1px solid #d1fae5;
}

.actions-card {
  background: linear-gradient(135deg, var(--white) 0%, #fffbf0 100%);
  border: 1px solid #fed7aa;
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.card-icon {
  font-size: var(--font-size-xl);
}

.card-link {
  color: var(--primary-600);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: var(--transition-fast);
}

.card-link:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

.card-body {
  padding: var(--spacing-6);
  background: var(--white);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
}

.period-selector {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-sm);
  background: var(--white);
  color: var(--gray-700);
}

/* === ÉTATS DE CHARGEMENT ET VIDES === */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
  gap: var(--spacing-3);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
  text-align: center;
}

.empty-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-3);
}

.empty-state h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  margin: 0 0 var(--spacing-2) 0;
}

.empty-state p {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
  margin: 0;
}

/* === GRAPHIQUES === */
.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8faff 0%, var(--white) 100%);
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-2);
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  padding: var(--spacing-4);
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-3);
  height: 200px;
  margin-bottom: var(--spacing-4);
}

.chart-bar {
  width: 40px;
  background: var(--gradient-primary);
  border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
  transition: all var(--transition-base);
  position: relative;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.chart-bar:hover {
  background: linear-gradient(180deg, #5a67d8 0%, #667eea 100%);
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.bar-tooltip {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--white);
  color: var(--gray-800);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-xs);
  opacity: 0;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  font-weight: var(--font-weight-semibold);
}

.chart-bar:hover .bar-tooltip {
  opacity: 1;
}

.chart-labels {
  display: flex;
  gap: var(--spacing-3);
}

.chart-labels span {
  width: 40px;
  text-align: center;
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}

/* === LISTES === */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  background: var(--gray-50);
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-fast);
  border-left: 4px solid var(--primary-500);
}

.order-item:hover {
  background: var(--primary-50);
  transform: translateX(4px);
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.order-id {
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  font-size: var(--font-size-sm);
}

.order-customer {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

.order-date {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
}

.order-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-2);
}

.order-amount {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  color: var(--primary-600);
}

/* === ACTIVITÉ === */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-fast);
}

.activity-item:hover {
  background: var(--gray-50);
}

.activity-icon {
  font-size: var(--font-size-xl);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-100);
  border-radius: var(--border-radius-full);
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: var(--font-weight-medium);
  color: var(--gray-900);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-1);
}

.activity-time {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
}

/* === ACTIONS RAPIDES === */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.action-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius-xl);
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
  color: inherit;
}

.action-button:hover {
  border-color: var(--primary-500);
  background: var(--primary-50);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-icon {
  font-size: var(--font-size-2xl);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-100);
  border-radius: var(--border-radius-xl);
  transition: var(--transition-base);
}

.action-button:hover .action-icon {
  background: var(--primary-200);
  transform: scale(1.1);
}

.action-content {
  flex: 1;
}

.action-title {
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-1);
}

.action-subtitle {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .dashboard-container {
    padding: var(--spacing-4);
  }

  .header-content {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }

  .dashboard-title {
    font-size: var(--font-size-2xl);
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .chart-bars {
    gap: var(--spacing-2);
  }

  .chart-bar {
    width: 30px;
  }

  .chart-labels span {
    width: 30px;
  }
}

/* Très petits écrans (< 480px) */
@media (max-width: 480px) {
  .dashboard-container {
    padding: var(--spacing-3);
  }

  .dashboard-title {
    font-size: var(--font-size-xl);
  }

  .welcome-message {
    font-size: var(--font-size-sm);
  }

  .stat-card {
    padding: var(--spacing-4);
  }

  .stat-value {
    font-size: var(--font-size-xl);
  }

  .stat-label {
    font-size: var(--font-size-xs);
  }

  .action-card {
    padding: var(--spacing-4);
  }

  .action-title {
    font-size: var(--font-size-base);
  }

  .action-description {
    font-size: var(--font-size-xs);
  }
}
