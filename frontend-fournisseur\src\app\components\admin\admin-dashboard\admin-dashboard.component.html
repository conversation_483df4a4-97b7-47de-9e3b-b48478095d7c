<div class="admin-dashboard-container">
  <!-- Sidebar Navigation -->
  <div class="admin-sidebar">
    <div class="sidebar-header">
      <h2 class="sidebar-title">
        <i class="icon-shield"></i>
        Administration
      </h2>
    </div>

    <nav class="sidebar-nav">
      <ul class="nav-list">
        <li *ngFor="let item of menuItems">
          <button 
            [class]="getMenuItemClass(item.id)"
            (click)="setActiveSection(item.id)">
            <i [class]="item.icon"></i>
            <div class="menu-content">
              <span class="menu-label">{{ item.label }}</span>
              <span class="menu-description">{{ item.description }}</span>
            </div>
          </button>
        </li>
      </ul>
    </nav>

    <div class="sidebar-footer">
      <button class="btn btn-secondary btn-full" (click)="refresh()">
        <i class="icon-refresh"></i>
        Actualiser
      </button>
      <button class="btn btn-success btn-full" (click)="testBackendConnection()" style="margin-top: 10px;">
        <i class="icon-test"></i>
        Test Backend
      </button>
    </div>
  </div>

  <!-- Main Content -->
  <div class="admin-content">
    <!-- Dashboard Overview -->
    <div *ngIf="activeSection() === 'dashboard'" class="dashboard-section">
      <div class="section-header">
        <h1 class="section-title">Tableau de bord</h1>
        <p class="section-subtitle">Vue d'ensemble de la plateforme</p>
      </div>

      <!-- Loading -->
      <div *ngIf="isLoading()" class="loading-container">
        <div class="loading-spinner"></div>
        <p>Chargement des statistiques...</p>
      </div>

      <!-- Error -->
      <div *ngIf="error()" class="error-message">
        <i class="icon-alert"></i>
        {{ error() }}
        <button class="btn btn-sm btn-primary" (click)="refresh()">
          Réessayer
        </button>
      </div>

      <!-- Statistics Cards -->
      <div *ngIf="!isLoading() && !error()" class="stats-grid">
        <div 
          *ngFor="let card of dashboardCards()" 
          [class]="getCardClass(card.color)">
          <div class="card-icon">
            <i [class]="card.icon"></i>
          </div>
          <div class="card-content">
            <div class="card-value">{{ card.value }}</div>
            <div class="card-title">{{ card.title }}</div>
            <div class="card-description">{{ card.description }}</div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div *ngIf="!isLoading() && !error()" class="quick-actions">
        <h3 class="actions-title">Actions rapides</h3>
        <div class="actions-grid">
          <button 
            class="action-card"
            (click)="setActiveSection('users')">
            <i class="icon-users"></i>
            <span>Gérer les utilisateurs</span>
          </button>
          
          <button 
            class="action-card"
            (click)="setActiveSection('categories')">
            <i class="icon-folder"></i>
            <span>Gérer les catégories</span>
          </button>
          
          <button 
            class="action-card"
            (click)="setActiveSection('orders')">
            <i class="icon-shopping-cart"></i>
            <span>Voir les commandes</span>
          </button>
          
          <button 
            class="action-card"
            (click)="setActiveSection('products')">
            <i class="icon-package"></i>
            <span>Valider les produits</span>
          </button>
        </div>
      </div>
    </div>

    <!-- User Management -->
    <div *ngIf="activeSection() === 'users'" class="management-section">
      <app-user-management></app-user-management>
    </div>

    <!-- Category Management -->
    <div *ngIf="activeSection() === 'categories'" class="management-section">
      <app-category-management></app-category-management>
    </div>

    <!-- Order Management -->
    <div *ngIf="activeSection() === 'orders'" class="management-section">
      <app-order-management></app-order-management>
    </div>

    <!-- Product Management -->
    <div *ngIf="activeSection() === 'products'" class="management-section">
      <app-admin-products></app-admin-products>
    </div>
  </div>
</div>
