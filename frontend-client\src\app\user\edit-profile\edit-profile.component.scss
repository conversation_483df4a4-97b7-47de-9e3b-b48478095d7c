.edit-profile-container {
  background-color: var(--card-background-color-hover);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  margin: 20px auto;
  max-width: 500px;
  transition: box-shadow 0.3s;
  color: var(--text-color);

  h2 {
    margin-bottom: 2rem;
    text-align: center;
    font-size: 2rem;
    color: var(--text-color);
  }
  button.mat-icon-button.edit-primary {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    min-width: 40px;
    height: 40px;

    &:hover {
      background-color: var(--primary-color-hover) !important;
      transform: translateY(-2px);
    }

    mat-icon {
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }

  button.mat-icon-button.edit-secondary {
    background-color: transparent !important;
    border: 2px solid var(--secondary-color) !important;
    color: var(--secondary-color) !important;
    border-radius: 8px;
    min-width: 40px;
    height: 40px;
    transition: all 0.3s ease;

    &:hover {
      background-color: var(--secondary-color) !important;
      color: white !important;
      transform: translateY(-2px);
    }

    mat-icon {
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }
  form {
    .form-section {
      margin-bottom: 2.5rem;

      h3 {
        font-size: 1.4rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 0.5rem;
        color: var(--text-color-light);

        a {
          text-decoration: none;
          color: inherit;
          display: flex;
          align-items: center;
          cursor: pointer;

          mat-icon {
            margin-left: 0.5rem;
          }
        }
      }

      .form-group {
        margin-bottom: 1.5rem;

        label {
          display: block;
          margin-bottom: 0.3rem;
          font-weight: 600;
          color: var(--text-color-light);
        }

        .editable-field {
          display: flex;
          align-items: center;
          justify-content: space-between;

          span {
            flex: 1;
            font-size: 1rem;
            color: var(--text-color);
          }

          input {
            flex: 1;
            margin-right: 0.5rem;
          }

          button {
            margin-left: 0.5rem;
          }
        }

        .adresse-livraison-display {
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: var(--card-background-color);
          padding: 0.7rem;
          border-radius: 0.5rem;

          button {
            margin-left: 0.5rem;
          }
        }

        .adresse-livraison-edit {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;

          input {
            flex: 1 1 30%;
            min-width: 150px;
          }

          button {
            margin-left: auto;
          }
        }

        .checkbox-group {
          display: flex;
          align-items: center;

          input[type="checkbox"] {
            margin-right: 0.5rem;
          }
        }
      }
    }

    .address-card {
      background-color: var(--card-background-color);
      padding: 1rem;
      border: 1px solid var(--border-color);
      border-radius: 0.7rem;
      margin-bottom: 1rem;

      .address-actions {
        display: flex;
        justify-content: flex-end;

        button {
          margin-left: auto;
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 1rem;

      .btn {
        &.btn-cancel {
          background-color: var(--border-color);
          color: var(--text-color);
          padding: 0.5rem 1rem;
          border-radius: 0.5rem;
          border: none;
          cursor: pointer;
          transition: background-color 0.3s ease;

          &:hover {
            background-color: #c7c7c7;
          }
        }

        &.btn-save {
          background-color: var(--primary-color);
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 0.5rem;
          border: none;
          cursor: pointer;
          transition: background-color 0.3s ease;

          &:hover {
            background-color: var(--primary-color-hover);
          }

          &:disabled {
            background-color: var(--disabled-color);
            cursor: not-allowed;
          }
        }

        &.btn-add {
          background: var(--gradient-primary);
          color: white;
          padding: 12px 20px;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          margin-bottom: 1.5rem;
          font-weight: 500;
          font-size: 14px;
          transition: all 0.3s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          display: inline-flex;
          align-items: center;
          gap: 8px;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          &::before {
            content: '+';
            font-size: 16px;
            font-weight: bold;
          }
        }
      }
    }
  }
}

.address-card {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: var(--card-background-color);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--card-shadow);
    transform: translateY(-2px);
  }

  .address-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    min-height: 50px;

    .address-info {
      flex: 1;
      color: var(--text-color);
      font-size: 14px;
      line-height: 1.4;
      padding-right: 15px;

      .no-address {
        color: var(--secondary-color);
        font-style: italic;
      }
    }

    .address-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-shrink: 0;
    }
  }

  .address-edit {
    .form-group {
      margin-bottom: 15px;

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: var(--text-color);
        font-size: 13px;
      }

      .form-control {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        background-color: var(--card-background-color);
        color: var(--text-color);
        transition: all 0.3s ease;
        font-size: 14px;

        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
          transform: translateY(-1px);
        }

        &::placeholder {
          color: var(--secondary-color);
          opacity: 0.7;
        }
      }
    }

    .address-actions {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 12px;
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px solid var(--border-color);
    }
  }

  .edit-btn, .save-btn, .delete-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      line-height: 1;
    }
  }

  .edit-btn {
    background-color: var(--primary-color) !important;
    color: white !important;

    &:hover {
      background-color: var(--primary-color-hover) !important;
    }
  }

  .save-btn {
    background-color: var(--success-color) !important;
    color: white !important;

    &:hover {
      background-color: #229954 !important;
    }
  }

  .delete-btn {
    background-color: var(--error-color) !important;
    color: white !important;

    &:hover {
      background-color: #c0392b !important;
    }
  }


}

.checkbox-group {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);

  .custom-checkbox {
    display: none !important; 
  }

  .custom-checkbox-label {
    display: flex !important;
    align-items: center;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-color);
    transition: color 0.3s ease;

    &:hover {
      color: var(--primary-color);
    }

    .checkmark {
      width: 22px !important;
      height: 22px !important;
      border: 2px solid var(--border-color);
      border-radius: 50% !important;
      margin-right: 12px;
      position: relative;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background-color: var(--card-background-color);
      flex-shrink: 0;

      &::after {
        content: '';
        position: absolute;
        left: 7px;
        top: 3px;
        width: 6px;
        height: 10px;
        border: solid white;
        border-width: 0 2.5px 2.5px 0;
        transform: rotate(45deg) scale(0);
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
      }
    }
  }

  .custom-checkbox:checked + .custom-checkbox-label {
    color: var(--primary-color);

    .checkmark {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-color-hover)) !important;
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2) !important;

      &::after {
        opacity: 1;
        transform: rotate(45deg) scale(1);
      }
    }
  }

  .custom-checkbox:hover + .custom-checkbox-label {
    .checkmark {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15) !important;
      transform: scale(1.05);
    }
  }

  .custom-checkbox:focus + .custom-checkbox-label {
    .checkmark {
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3) !important;
    }
  }
}

.edit-btn, .save-btn, .delete-btn {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 8px !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;

  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  }

  &:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  mat-icon {
    font-size: 20px !important;
    width: 20px !important;
    height: 20px !important;
    line-height: 1 !important;
  }
}

.edit-btn {
  background-color: var(--primary-color) !important;
  color: white !important;

  &:hover {
    background-color: var(--primary-color-hover) !important;
  }
}

.save-btn {
  background-color: var(--success-color) !important;
  color: white !important;

  &:hover {
    background-color: #229954 !important;
  }
}

.delete-btn {
  background-color: var(--error-color) !important;
  color: white !important;

  &:hover {
    background-color: #c0392b !important;
  }
}

// Styles globaux pour les boutons edit-primary
button.mat-icon-button.edit-primary,
.edit-primary {
  background-color: var(--primary-color) !important;
  color: white !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  min-width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;

  &:hover {
    background-color: var(--primary-color-hover) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
  }

  &:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  mat-icon {
    transition: transform 0.2s ease !important;
    font-size: 20px !important;
    width: 20px !important;
    height: 20px !important;
    line-height: 1 !important;

    &:hover {
      transform: scale(1.1) !important;
    }
  }
}

@media (max-width: 600px) {
  .edit-profile-container {
    padding: 1rem;

    form {
      .form-section {
        .adresse-livraison-edit input {
          flex: 1 1 100%;
        }
      }
    }
  }

  .address-card {
    .address-display {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;

      .address-info {
        padding-right: 0;
      }

      .address-actions {
        align-self: flex-end;
      }
    }

    .address-edit {
      .address-actions {
        flex-direction: column;
        gap: 8px;

        .edit-btn, .save-btn, .delete-btn {
          width: 100%;
          height: 44px;
          border-radius: 6px;
        }
      }
    }

    .checkbox-group {
      .custom-checkbox-label {
        font-size: 14px;

        .checkmark {
          width: 18px !important;
          height: 18px !important;
        }
      }
    }
  }
}
