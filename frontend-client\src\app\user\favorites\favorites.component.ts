import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/auth/auth.service';
import { ProduitCard } from 'src/app/models/ProduitCard';
import { CartService } from 'src/app/services/cart.service';
import { ImageUrlService } from 'src/app/services/image-url.service';
import { FavoritesService } from 'src/app/services/favorites.service';

@Component({
  selector: 'app-favorites',
  templateUrl: './favorites.component.html',
styleUrls: ['./favorites.component.scss'],
  standalone: false,
})
export class FavoritesComponent {
  produits: ProduitCard[] = [];
  loading = true;
  objectKeys = Object.keys;
  errorMessage = '';
  groupedProducts: { [key: string]: ProduitCard[] } = {};
  productImageCache: Map<number, string> = new Map();

  constructor(
    private favoritesService: FavoritesService,
    private authService: AuthService,
    private cartService: CartService,
    private router: Router,
    public imageUrlService: ImageUrlService
  ) {}
ngOnInit(): void {
    this.loadFavoris();
  }

  loadFavoris(): void {
    this.loading = true;
    const clientId = this.authService.getCurrentUser()?.id;
    if (!clientId) {
      this.router.navigate(['/login']);
      return;
    }

    this.favoritesService.getFavoris(clientId).subscribe({
      next: (response) => {
        this.produits = response.produits || [];
        this.groupProductsByCategory();
        this.loading = false;
      },
      error: (err) => {
        console.error('Erreur lors du chargement des favoris:', err);
        this.errorMessage = 'Erreur lors du chargement des favoris';
        this.loading = false;
      }
    });
  }

  groupProductsByCategory(): void {
    this.groupedProducts = this.produits.reduce((acc, product) => {
      const category = product.sousCategorie?.nom || 'Autres';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(product);
      return acc;
    }, {} as {[key: string]: ProduitCard[]});
  }

  removeFromFavorites(produitId: number): void {
    const clientId = this.authService.getCurrentUser()?.id;
    if (!clientId) return;

    this.favoritesService.supprimerFavoriParProduitId(clientId, produitId).subscribe({
      next: () => {
        this.produits = this.produits.filter(p => p.id !== produitId);
        this.groupProductsByCategory();
      },
      error: () => this.errorMessage = 'Erreur lors de la suppression'
    });
  }

  addToCart(product: ProduitCard): void {
    const clientId = this.authService.getCurrentUser()?.id;
    if (!clientId) {
      this.router.navigate(['/login']);
      return;
    }

    this.cartService.addItemByClientId(clientId, {
      produitId: product.id,
      quantite: 1
    }).subscribe({
      next: (result) => {
        if (result.quantite > 1) {
          alert(`Quantité mise à jour ! Vous avez maintenant ${result.quantite} de ce produit dans votre panier.`);
        } else {
          alert('Produit ajouté au panier !');
        }
        this.errorMessage = ''; // Clear any previous error
      },
      error: (err) => {
        console.error('Erreur lors de l\'ajout au panier:', err);
        this.errorMessage = 'Erreur lors de l\'ajout au panier';
        if (err.error?.message || typeof err.error === 'string') {
          this.errorMessage += ': ' + (err.error.message || err.error);
        }
      }
    });
  }

  trackByProductId(index: number, product: ProduitCard): number {
    return product.id;
  }

  getProductImage(product: ProduitCard): string {
    console.log('❤️ getProductImage pour:', product.nom);
    console.log('❤️ product:', product);
    console.log('❤️ images:', product.images);
    console.log('❤️ imageUrl:', product.imageUrl);

    // Vérifier le cache d'abord
    if (this.productImageCache.has(product.id)) {
      const cachedUrl = this.productImageCache.get(product.id)!;
      console.log('❤️ URL depuis le cache:', cachedUrl);
      return cachedUrl;
    }

    let finalImageUrl = this.imageUrlService.getPlaceholderUrl();

    if (product.images?.length > 0) {
      const mainImage = product.images.find(img => img.isMain);
      const imageUrl = mainImage?.imageUrl || product.images[0].imageUrl;
      finalImageUrl = this.imageUrlService.getProduitImageUrl(imageUrl);
      console.log('❤️ URL depuis images array:', finalImageUrl);
    } else if (product.imageUrl) {
      finalImageUrl = this.imageUrlService.getProduitImageUrl(product.imageUrl);
      console.log('❤️ URL depuis imageUrl de base:', finalImageUrl);
    } else {
      console.log('❤️ Aucune image trouvée, utilisation du placeholder');
    }

    // Mettre en cache
    this.productImageCache.set(product.id, finalImageUrl);
    console.log('❤️ URL finale mise en cache:', finalImageUrl);
    return finalImageUrl;
  }

  // Méthode simple pour obtenir l'image d'un produit
  getSimpleProductImage(product: ProduitCard): string {
    // Si on a des images dans le tableau
    if (product.images?.length > 0) {
      const mainImage = product.images.find(img => img.isMain);
      const imageUrl = mainImage?.imageUrl || product.images[0].imageUrl;
      if (imageUrl) {
        return this.imageUrlService.getProduitImageUrl(imageUrl);
      }
    }

    // Si on a l'imageUrl de base
    if (product.imageUrl) {
      return this.imageUrlService.getProduitImageUrl(product.imageUrl);
    }

    return this.imageUrlService.getPlaceholderUrl();
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = this.imageUrlService.getPlaceholderUrl();
    }
  }
}