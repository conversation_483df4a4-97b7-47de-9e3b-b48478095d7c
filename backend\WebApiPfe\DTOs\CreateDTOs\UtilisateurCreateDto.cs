﻿using System.ComponentModel.DataAnnotations;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Models.Enum;

namespace WebApiPfe.DTOs.CreateDTOs
{
    public abstract class UtilisateurCreateDto
    {
        [Required]
        [EmailAddress]
        public required string Email { get; set; }

        [Required, StringLength(50)]
        public required string Nom { get; set; }

        [Required, StringLength(50)]
        public required string Prenom { get; set; }

        [Required]
        [Phone(ErrorMessage = "Numéro de téléphone invalide")]
        public string? PhoneNumber { get; set; }
        public DateTime DateNaissance { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 8)]
        public required string Password { get; set; }
        [Required]
        public bool EstActif { get; set; } = false;
    }
}
