.meilleures-ventes {
  max-width: 95vw;
  overflow: hidden;
  margin: 0 auto;
  padding: 0.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  h2 {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 2rem 0 1.5rem;
    color: var(--text-color);
  }

  .p-carousel {
    width: 100%;

    .p-carousel-container {
      padding: 0 0.5rem;
    }

    .p-carousel-item {
      padding: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 520px; // hauteur uniforme
    }
  }

  .card {
    height: 520px; // hauteur uniforme
    border-radius: var(--card-radius);
    background-color: var(--card-background-color);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &:hover {
      background-color: var(--card-background-color-hover);
      transform: translateY(-5px);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .card-image {
      position: relative;
      aspect-ratio: 2/1;
      overflow: hidden;
      background-color: white;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
      }

      &:hover img {
        transform: scale(1.05);
      }

      .product-badges {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        display: flex;
        flex-direction: row;
        gap: 0.3rem;
        z-index: 5;

        .badge-new {
          background-color: var(--primary-color);
          color: white;
          padding: 0.25rem 0.6rem;
          border-radius: 8px;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .badge-discount {
          background-color: var(--secondary-color);
          color: white;
          padding: 0.25rem 0.6rem;
          border-radius: 8px;
          font-size: 0.75rem;
          font-weight: 600;
        }
      }
    }

    .card-content {
      padding: 16px;
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;

      h3 {
        margin: 0 0 8px;
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--text-color);
      }

      .truncate {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2; // standard
        -webkit-box-orient: vertical;
        overflow: hidden;
        margin-bottom: 16px;
        color: var(--text-color);
      }

      .product-info-right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        text-align: right;

        .product-brand,
        .product-fournisseur,
        .rating-section {
          text-align: right;
        }
      }

      .product-brand {
        font-weight: 700;
        font-size: 0.75rem;
        color: var(--text-color);
        margin-bottom: 0.25rem;
      }

      .product-fournisseur {
        font-style: italic;
        font-size: 0.8rem;
        color: var(--secondary-color);
        margin-bottom: 0.5rem;
      }

      .rating-section {
        display: flex;
        align-items: center;
        gap: 0.25rem;

        mat-icon {
          font-size: 1rem;
          color: var(--secondary-color);

          &.filled {
            color: var(--primary-color);
          }
        }

        .review-count {
          font-size: 0.75rem;
          color: var(--text-color);
        }
      }

      .price-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-top: auto; // colle le prix en bas

        .price-stack {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 0.25rem;

          .price-final {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--text-color);
          }

          .price-intermediate {
            font-size: 0.95rem;
            color: var(--text-color);

            .amount {
              text-decoration: line-through;
              font-weight: 600;
              margin-left: 0.3rem;
            }
          }

          .price-original {
            font-size: 0.85rem;
            color: var(--text-color);

            .amount {
              text-decoration: line-through;
              font-weight: 400;
              margin-left: 0.3rem;
            }
          }
        }
      }

      .button-container {
        margin-top: 1rem;
        display: flex;
        gap: 0.75rem;
        justify-content: flex-end;

        .p-button {
          min-width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          transition: all 0.3s ease;
          cursor: pointer;

          &:hover {
            transform: translateY(-2px);
          }

          &.p-button-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;

            &:hover {
              background-color: var(--primary-color-hover);
            }
          }

          &.p-button-secondary {
            background-color: transparent;
            border: 2px solid var(--secondary-color);
            color: var(--secondary-color);

            &:hover {
              background-color: var(--secondary-color);
              color: white;
            }
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .card {
      height: 480px;

      .button-container {
        .p-button {
          min-width: 36px;
          height: 36px;
        }
      }
    }
  }
}
