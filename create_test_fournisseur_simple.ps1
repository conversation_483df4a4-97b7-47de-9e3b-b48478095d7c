# Script simple pour créer un fournisseur de test
$baseUrl = "http://localhost:5014/api"

Write-Host "=== CRÉATION FOURNISSEUR DE TEST ===" -ForegroundColor Cyan

$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$email = "testdebug$<EMAIL>"

Write-Host "Email: $email" -ForegroundColor Yellow
Write-Host "Mot de passe: TestDebug123!" -ForegroundColor Yellow

$fournisseurData = @{
    nom = "Debug"
    prenom = "Test"
    email = $email
    password = "TestDebug123!"
    phoneNumber = "12345678"
    raisonSociale = "Debug Test SARL"
    matriculeFiscale = "DEBUG$timestamp"
    rib = "12345678901234567890"
    codeBanque = "12345"
    commission = 5.0
    delaiPreparationJours = 2
    fraisLivraisonBase = 10.0
    description = "Fournisseur de test pour debug - $timestamp"
} | ConvertTo-Json

$headers = @{"Content-Type" = "application/json"}

try {
    $newFournisseur = Invoke-RestMethod -Uri "$baseUrl/Auth/register/fournisseur" -Method Post -Headers $headers -Body $fournisseurData
    Write-Host "✅ Fournisseur créé !" -ForegroundColor Green
    Write-Host "ID: $($newFournisseur.id)" -ForegroundColor White
    Write-Host "Email: $email" -ForegroundColor White
    Write-Host "Mot de passe: TestDebug123!" -ForegroundColor White
    
} catch {
    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "⚠️ Le fournisseur existe peut-être déjà" -ForegroundColor Yellow
    }
}

Write-Host "`n=== INSTRUCTIONS ===" -ForegroundColor Cyan
Write-Host "1. Ouvrez http://localhost:55520/test-debug" -ForegroundColor White
Write-Host "2. Utilisez ces identifiants:" -ForegroundColor White
Write-Host "   Email: $email" -ForegroundColor Green
Write-Host "   Mot de passe: TestDebug123!" -ForegroundColor Green
