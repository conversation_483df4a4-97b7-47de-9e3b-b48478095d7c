.filter-container {
  width: 100%;
  max-width: 300px;
  background-color: var(--card-background-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: var(--shadow);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  overflow-y: auto;
  font-size: 1rem;
  line-height: 1.4;

  section {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    h3 {
      font-size: 1.2rem;
      font-weight: 650;
      margin-bottom: 0.5rem;
      color: var(--text-color);
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 0.25rem;
      position: sticky;
      top: 0;
      background-color: var(--card-background-color);
      z-index: 2;
    }

    .radio-group,
    .checkbox-group,
    .dropdown-list {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0.75rem;

      label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.95rem;
        color: var(--text-color);
        cursor: pointer;

        input[type="radio"],
        input[type="checkbox"] {
          width: 18px;
          height: 18px;
          accent-color: var(--primary-color);
          cursor: pointer;
          border-radius: 4px;
          transition: box-shadow 0.2s ease;

          &:hover {
            box-shadow: 0 0 0 2px var(--primary-color-hover);
          }
        }
        img {
          width: 40px;
          height: 20px;
          border-radius: 8px;
          object-fit: cover;
          box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.1);
          }
        }
      }
    }

    input[type="text"] {
      border: 1px solid var(--border-color);
      border-radius: 8px;
      padding: 0.5rem;
      font-size: 0.9rem;
      outline: none;
      transition: border-color 0.3s ease;

      &:focus {
        border-color: var(--primary-color);
      }
    }

    .prix-inputs {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;

      input[type="number"] {
        flex: 1 1 45%;
        min-width: 45%;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 0.5rem;
        font-size: 0.9rem;

        &:focus {
          border-color: var(--primary-color);
        }
      }
    }

    input[type="range"] {
      width: 100%;
      margin-top: 0.5rem;
      accent-color: var(--primary-color);
    }
  }

  .buttons {
    display: flex;
    justify-content: space-between;
    gap: 0.5rem;
    margin-top: auto;

    button {
      flex: 1;
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s ease, transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }

      &:first-child {
        background-color: var(--secondary-color);
        color: var(--text-color);

        &:hover {
          background-color: var(--secondary-color-hover);
        }
      }

      &:last-child {
        background-color: var(--primary-color);
        color: var(--text-color);

        &:hover {
          background-color: var(--primary-color-hover);
        }
      }
    }
  }

  @media (max-width: 768px) {
    max-width: 100%;
    border-radius: 0;
    box-shadow: none;
  }
}
