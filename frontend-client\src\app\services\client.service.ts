import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ClientDto } from '../models/ClientDto';
import { AdresseDto } from '../models/AdresseDto';
import { CommandeDto } from '../models/CommandeDto';

@Injectable({
  providedIn: 'root',
})
export class ClientService {
  private apiUrl = `${environment.apiUrl}/clients`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<ClientDto[]> {
    return this.http.get<ClientDto[]>(this.apiUrl);
  }

  getById(id: number): Observable<ClientDto> {
    return this.http.get<ClientDto>(`${this.apiUrl}/${id}`);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  updateAdresses(id: number, adresses: AdresseDto[]): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}/adresses`, adresses);
  }

  updateClient(id: number, client: any): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/${id}`, client);
  }

  updateProfile(id: number, profileData: any): Observable<any> {
    return this.http.put<any>(`${this.apiUrl}/${id}/profile`, profileData);
  }
  getCommandesByClient(id: number): Observable<CommandeDto[]> {
    return this.http.get<CommandeDto[]>(`${this.apiUrl}/${id}/commandes`);
  }
}
