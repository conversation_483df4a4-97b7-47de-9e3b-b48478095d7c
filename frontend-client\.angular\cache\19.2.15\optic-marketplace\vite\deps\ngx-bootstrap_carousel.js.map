{"version": 3, "sources": ["../../../../../../node_modules/ngx-bootstrap/utils/fesm2022/ngx-bootstrap-utils.mjs", "../../../../../../node_modules/ngx-bootstrap/carousel/fesm2022/ngx-bootstrap-carousel.mjs"], "sourcesContent": ["import { isDevMode } from '@angular/core';\n\n/**\n * @copyright Valor Software\n * @copyright Angular ng-bootstrap team\n */\nclass Trigger {\n  constructor(open, close) {\n    this.open = open;\n    this.close = close || open;\n  }\n  isManual() {\n    return this.open === 'manual' || this.close === 'manual';\n  }\n}\nconst DEFAULT_ALIASES = {\n  hover: ['mouseover', 'mouseout'],\n  focus: ['focusin', 'focusout']\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction parseTriggers(triggers, aliases = DEFAULT_ALIASES) {\n  const trimmedTriggers = (triggers || '').trim();\n  if (trimmedTriggers.length === 0) {\n    return [];\n  }\n  const parsedTriggers = trimmedTriggers.split(/\\s+/).map(trigger => trigger.split(':')).map(triggerPair => {\n    const alias = aliases[triggerPair[0]] || triggerPair;\n    return new Trigger(alias[0], alias[1]);\n  });\n  const manualTriggers = parsedTriggers.filter(triggerPair => triggerPair.isManual());\n  if (manualTriggers.length > 1) {\n    throw new Error('Triggers parse error: only one manual trigger is allowed');\n  }\n  if (manualTriggers.length === 1 && parsedTriggers.length > 1) {\n    throw new Error('Triggers parse error: manual trigger can\\'t be mixed with other triggers');\n  }\n  return parsedTriggers;\n}\nfunction listenToTriggers(renderer,\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntarget, triggers, showFn, hideFn, toggleFn) {\n  const parsedTriggers = parseTriggers(triggers);\n  const listeners = [];\n  if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n    return Function.prototype;\n  }\n  parsedTriggers.forEach(trigger => {\n    if (trigger.open === trigger.close) {\n      listeners.push(renderer.listen(target, trigger.open, toggleFn));\n      return;\n    }\n    listeners.push(renderer.listen(target, trigger.open, showFn));\n    if (trigger.close) {\n      listeners.push(renderer.listen(target, trigger.close, hideFn));\n    }\n  });\n  return () => {\n    listeners.forEach(unsubscribeFn => unsubscribeFn());\n  };\n}\nfunction listenToTriggersV2(renderer, options) {\n  const parsedTriggers = parseTriggers(options.triggers);\n  const target = options.target;\n  // do nothing\n  if (parsedTriggers.length === 1 && parsedTriggers[0].isManual()) {\n    return Function.prototype;\n  }\n  // all listeners\n  const listeners = [];\n  // lazy listeners registration\n  const _registerHide = [];\n  const registerHide = () => {\n    // add hide listeners to unregister array\n    _registerHide.forEach(fn => listeners.push(fn()));\n    // register hide events only once\n    _registerHide.length = 0;\n  };\n  // register open\\close\\toggle listeners\n  parsedTriggers.forEach(trigger => {\n    const useToggle = trigger.open === trigger.close;\n    const showFn = useToggle ? options.toggle : options.show;\n    if (!useToggle && trigger.close && options.hide) {\n      const triggerClose = trigger.close;\n      const optionsHide = options.hide;\n      const _hide = () => renderer.listen(target, triggerClose, optionsHide);\n      _registerHide.push(_hide);\n    }\n    if (showFn) {\n      listeners.push(renderer.listen(target, trigger.open, () => showFn(registerHide)));\n    }\n  });\n  return () => {\n    listeners.forEach(unsubscribeFn => unsubscribeFn());\n  };\n}\nfunction registerOutsideClick(renderer, options) {\n  if (!options.outsideClick) {\n    return Function.prototype;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return renderer.listen('document', 'click', event => {\n    if (options.target && options.target.contains(event.target)) {\n      return;\n    }\n    if (options.targets && options.targets.some(target => target.contains(event.target))) {\n      return;\n    }\n    if (options.hide) {\n      options.hide();\n    }\n  });\n}\nfunction registerEscClick(renderer, options) {\n  if (!options.outsideEsc) {\n    return Function.prototype;\n  }\n  return renderer.listen('document', 'keyup.esc', event => {\n    if (options.target && options.target.contains(event.target)) {\n      return;\n    }\n    if (options.targets && options.targets.some(target => target.contains(event.target))) {\n      return;\n    }\n    if (options.hide) {\n      options.hide();\n    }\n  });\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * JS version of browser APIs. This library can only run in the browser.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst win = typeof window !== 'undefined' && window || {};\nconst document = win.document;\nconst location = win.location;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst gc = win.gc ? () => win.gc() : () => null;\nconst performance = win.performance ? win.performance : null;\nconst Event = win.Event;\nconst MouseEvent = win.MouseEvent;\nconst KeyboardEvent = win.KeyboardEvent;\nconst EventTarget = win.EventTarget;\nconst History = win.History;\nconst Location = win.Location;\nconst EventListener = win.EventListener;\nvar BsVerions;\n(function (BsVerions) {\n  BsVerions[\"isBs4\"] = \"bs4\";\n  BsVerions[\"isBs5\"] = \"bs5\";\n})(BsVerions || (BsVerions = {}));\nlet guessedVersion;\nfunction _guessBsVersion() {\n  const spanEl = win.document.createElement('span');\n  spanEl.innerText = 'testing bs version';\n  spanEl.classList.add('d-none');\n  spanEl.classList.add('pl-1');\n  win.document.head.appendChild(spanEl);\n  const checkPadding = win.getComputedStyle(spanEl).paddingLeft;\n  if (checkPadding && parseFloat(checkPadding)) {\n    win.document.head.removeChild(spanEl);\n    return 'bs4';\n  }\n  win.document.head.removeChild(spanEl);\n  return 'bs5';\n}\nfunction setTheme(theme) {\n  guessedVersion = theme;\n}\nfunction isBs4() {\n  if (guessedVersion) return guessedVersion === 'bs4';\n  guessedVersion = _guessBsVersion();\n  return guessedVersion === 'bs4';\n}\nfunction isBs5() {\n  if (guessedVersion) return guessedVersion === 'bs5';\n  guessedVersion = _guessBsVersion();\n  return guessedVersion === 'bs5';\n}\nfunction getBsVer() {\n  return {\n    isBs4: isBs4(),\n    isBs5: isBs5()\n  };\n}\nfunction currentBsVersion() {\n  const bsVer = getBsVer();\n  const resVersion = Object.keys(bsVer).find(key => bsVer[key]);\n  return BsVerions[resVersion];\n}\nclass LinkedList {\n  constructor() {\n    this.length = 0;\n    this.asArray = [];\n    // Array methods overriding END\n  }\n  get(position) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      return void 0;\n    }\n    let current = this.head;\n    for (let index = 0; index < position; index++) {\n      current = current?.next;\n    }\n    return current?.value;\n  }\n  add(value, position = this.length) {\n    if (position < 0 || position > this.length) {\n      throw new Error('Position is out of the list');\n    }\n    const node = {\n      value,\n      next: undefined,\n      previous: undefined\n    };\n    if (this.length === 0) {\n      this.head = node;\n      this.tail = node;\n      this.current = node;\n    } else {\n      if (position === 0 && this.head) {\n        // first node\n        node.next = this.head;\n        this.head.previous = node;\n        this.head = node;\n      } else if (position === this.length && this.tail) {\n        // last node\n        this.tail.next = node;\n        node.previous = this.tail;\n        this.tail = node;\n      } else {\n        // node in middle\n        const currentPreviousNode = this.getNode(position - 1);\n        const currentNextNode = currentPreviousNode?.next;\n        if (currentPreviousNode && currentNextNode) {\n          currentPreviousNode.next = node;\n          currentNextNode.previous = node;\n          node.previous = currentPreviousNode;\n          node.next = currentNextNode;\n        }\n      }\n    }\n    this.length++;\n    this.createInternalArrayRepresentation();\n  }\n  remove(position = 0) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      throw new Error('Position is out of the list');\n    }\n    if (position === 0 && this.head) {\n      // first node\n      this.head = this.head.next;\n      if (this.head) {\n        // there is no second node\n        this.head.previous = undefined;\n      } else {\n        // there is no second node\n        this.tail = undefined;\n      }\n    } else if (position === this.length - 1 && this.tail?.previous) {\n      // last node\n      this.tail = this.tail.previous;\n      this.tail.next = undefined;\n    } else {\n      // middle node\n      const removedNode = this.getNode(position);\n      if (removedNode?.next && removedNode.previous) {\n        removedNode.next.previous = removedNode.previous;\n        removedNode.previous.next = removedNode.next;\n      }\n    }\n    this.length--;\n    this.createInternalArrayRepresentation();\n  }\n  set(position, value) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      throw new Error('Position is out of the list');\n    }\n    const node = this.getNode(position);\n    if (node) {\n      node.value = value;\n      this.createInternalArrayRepresentation();\n    }\n  }\n  toArray() {\n    return this.asArray;\n  }\n  findAll(fn) {\n    let current = this.head;\n    const result = [];\n    if (!current) {\n      return result;\n    }\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return result;\n      }\n      if (fn(current.value, index)) {\n        result.push({\n          index,\n          value: current.value\n        });\n      }\n      current = current.next;\n    }\n    return result;\n  }\n  // Array methods overriding start\n  push(...args) {\n    args.forEach(arg => {\n      this.add(arg);\n    });\n    return this.length;\n  }\n  pop() {\n    if (this.length === 0) {\n      return undefined;\n    }\n    const last = this.tail;\n    this.remove(this.length - 1);\n    return last?.value;\n  }\n  unshift(...args) {\n    args.reverse();\n    args.forEach(arg => {\n      this.add(arg, 0);\n    });\n    return this.length;\n  }\n  shift() {\n    if (this.length === 0) {\n      return undefined;\n    }\n    const lastItem = this.head?.value;\n    this.remove();\n    return lastItem;\n  }\n  forEach(fn) {\n    let current = this.head;\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return;\n      }\n      fn(current.value, index);\n      current = current.next;\n    }\n  }\n  indexOf(value) {\n    let current = this.head;\n    let position = -1;\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return position;\n      }\n      if (current.value === value) {\n        position = index;\n        break;\n      }\n      current = current.next;\n    }\n    return position;\n  }\n  some(fn) {\n    let current = this.head;\n    let result = false;\n    while (current && !result) {\n      if (fn(current.value)) {\n        result = true;\n        break;\n      }\n      current = current.next;\n    }\n    return result;\n  }\n  every(fn) {\n    let current = this.head;\n    let result = true;\n    while (current && result) {\n      if (!fn(current.value)) {\n        result = false;\n      }\n      current = current.next;\n    }\n    return result;\n  }\n  toString() {\n    return '[Linked List]';\n  }\n  find(fn) {\n    let current = this.head;\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return;\n      }\n      if (fn(current.value, index)) {\n        return current.value;\n      }\n      current = current.next;\n    }\n  }\n  findIndex(fn) {\n    let current = this.head;\n    for (let index = 0; index < this.length; index++) {\n      if (!current) {\n        return -1;\n      }\n      if (fn(current.value, index)) {\n        return index;\n      }\n      current = current.next;\n    }\n    return -1;\n  }\n  getNode(position) {\n    if (this.length === 0 || position < 0 || position >= this.length) {\n      throw new Error('Position is out of the list');\n    }\n    let current = this.head;\n    for (let index = 0; index < position; index++) {\n      current = current?.next;\n    }\n    return current;\n  }\n  createInternalArrayRepresentation() {\n    const outArray = [];\n    let current = this.head;\n    while (current) {\n      outArray.push(current.value);\n      current = current.next;\n    }\n    this.asArray = outArray;\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction OnChange() {\n  const sufix = 'Change';\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return function OnChangeHandler(target, propertyKey) {\n    const _key = ` __${propertyKey}Value`;\n    Object.defineProperty(target, propertyKey, {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      get() {\n        return this[_key];\n      },\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      set(value) {\n        const prevValue = this[_key];\n        this[_key] = value;\n        if (prevValue !== value && this[propertyKey + sufix]) {\n          this[propertyKey + sufix].emit(value);\n        }\n      }\n    });\n  };\n}\nclass Utils {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  static reflow(element) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    (bs => bs)(element.offsetHeight);\n  }\n  // source: https://github.com/jquery/jquery/blob/master/src/css/var/getStyles.js\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  static getStyles(elem) {\n    // Support: IE <=11 only, Firefox <=30 (#15098, #14150)\n    // IE throws on elements created in popups\n    // FF meanwhile throws on frame elements through \"defaultView.getComputedStyle\"\n    let view = elem.ownerDocument.defaultView;\n    if (!view || !view.opener) {\n      view = win;\n    }\n    return view.getComputedStyle(elem);\n  }\n  static stackOverflowConfig() {\n    const bsVer = currentBsVersion();\n    return {\n      crossorigin: \"anonymous\",\n      integrity: bsVer === 'bs5' ? 'sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65' : 'sha384-TX8t27EcRE3e/ihU7zmQxVncDAy5uIKz4rEkgIXeMed4M0jlfIDPvg6uqKI2xXr2',\n      cdnLink: bsVer === 'bs5' ? 'https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css' : 'https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/css/bootstrap.min.css'\n    };\n  }\n}\nconst _messagesHash = {};\nconst _hideMsg = typeof console === 'undefined' || !('warn' in console);\nfunction warnOnce(msg) {\n  if (!isDevMode() || _hideMsg || msg in _messagesHash) {\n    return;\n  }\n  _messagesHash[msg] = true;\n  console.warn(msg);\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BsVerions, LinkedList, OnChange, Trigger, Utils, currentBsVersion, document, getBsVer, listenToTriggers, listenToTriggersV2, parseTriggers, registerEscClick, registerOutsideClick, setTheme, warnOnce, win as window };\n", "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, PLATFORM_ID, Component, Inject, Input, Output, HostBinding, NgModule } from '@angular/core';\nimport { is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';\nimport { getBsVer, LinkedList } from 'ngx-bootstrap/utils';\nconst _c0 = [\"*\"];\nconst _c1 = a0 => ({\n  \"display\": a0\n});\nfunction CarouselComponent_ng_container_1_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 7);\n    i0.ɵɵlistener(\"click\", function CarouselComponent_ng_container_1_li_2_Template_li_click_0_listener() {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectSlide(i_r2));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const slide_r4 = ctx.$implicit;\n    i0.ɵɵclassProp(\"active\", slide_r4.active === true);\n  }\n}\nfunction CarouselComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"ol\", 5);\n    i0.ɵɵtemplate(2, CarouselComponent_ng_container_1_li_2_Template, 1, 2, \"li\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.indicatorsSlides());\n  }\n}\nfunction CarouselComponent_ng_container_2_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function CarouselComponent_ng_container_2_button_2_Template_button_click_0_listener() {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectSlide(i_r6));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const slide_r7 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", slide_r7.active === true);\n    i0.ɵɵattribute(\"data-bs-target\", \"#carousel\" + ctx_r2.currentId)(\"data-bs-slide-to\", i_r6);\n  }\n}\nfunction CarouselComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5);\n    i0.ɵɵtemplate(2, CarouselComponent_ng_container_2_button_2_Template, 1, 4, \"button\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.indicatorsSlides());\n  }\n}\nfunction CarouselComponent_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 10);\n    i0.ɵɵlistener(\"click\", function CarouselComponent_a_5_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.previousSlide());\n    });\n    i0.ɵɵelement(1, \"span\", 11);\n    i0.ɵɵelementStart(2, \"span\", 12);\n    i0.ɵɵtext(3, \"Previous\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"disabled\", ctx_r2.checkDisabledClass(\"prev\"));\n    i0.ɵɵattribute(\"data-bs-target\", \"#carousel\" + ctx_r2.currentId);\n  }\n}\nfunction CarouselComponent_a_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 13);\n    i0.ɵɵlistener(\"click\", function CarouselComponent_a_6_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.nextSlide());\n    });\n    i0.ɵɵelement(1, \"span\", 14);\n    i0.ɵɵelementStart(2, \"span\", 12);\n    i0.ɵɵtext(3, \"Next\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"disabled\", ctx_r2.checkDisabledClass(\"next\"));\n    i0.ɵɵattribute(\"data-bs-target\", \"#carousel\" + ctx_r2.currentId);\n  }\n}\nclass CarouselConfig {\n  constructor() {\n    /* Default interval of auto changing of slides */\n    this.interval = 5000;\n    /* Is loop of auto changing of slides can be paused */\n    this.noPause = false;\n    /* Is slides can wrap from the last to the first slide */\n    this.noWrap = false;\n    /* Show carousel-indicators */\n    this.showIndicators = true;\n    /* Slides can be paused on focus */\n    this.pauseOnFocus = false;\n    /* If `true` - carousel indicators indicate slides chunks works ONLY if singleSlideOffset = FALSE */\n    this.indicatorsByChunk = false;\n    /* If value more then 1 — carousel works in multilist mode */\n    this.itemsPerSlide = 1;\n    /* If `true` — carousel shifts by one element. By default carousel shifts by number\n      of visible elements (itemsPerSlide field) */\n    this.singleSlideOffset = false;\n  }\n  static {\n    this.ɵfac = function CarouselConfig_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CarouselConfig)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: CarouselConfig,\n      factory: CarouselConfig.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CarouselConfig, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * Returns the index of the last element in the array where predicate is true, and -1\n * otherwise.\n * @param array The source array to search in\n * @param predicate find calls predicate once for each element of the array, in descending\n * order, until it finds one where predicate returns true. If such an element is found,\n * findLastIndex immediately returns that element index. Otherwise, findLastIndex returns -1.\n */\nfunction findLastIndex(array, predicate) {\n  let l = array.length;\n  while (l--) {\n    if (predicate(array[l], l, array)) {\n      return l;\n    }\n  }\n  return -1;\n}\nfunction chunkByNumber(array, size) {\n  const out = [];\n  const n = Math.ceil(array.length / size);\n  let i = 0;\n  while (i < n) {\n    const chunk = array.splice(0, i === n - 1 && size < array.length ? array.length : size);\n    out.push(chunk);\n    i++;\n  }\n  return out;\n}\nfunction isNumber(value) {\n  return typeof value === 'number' || Object.prototype.toString.call(value) === '[object Number]';\n}\n\n/***\n * pause (not yet supported) (?string='hover') - event group name which pauses\n * the cycling of the carousel, if hover pauses on mouseenter and resumes on\n * mouseleave keyboard (not yet supported) (?boolean=true) - if false\n * carousel will not react to keyboard events\n * note: swiping not yet supported\n */\n/****\n * Problems:\n * 1) if we set an active slide via model changes, .active class remains on a\n * current slide.\n * 2) if we have only one slide, we shouldn't show prev/next nav buttons\n * 3) if first or last slide is active and noWrap is true, there should be\n * \"disabled\" class on the nav buttons.\n * 4) default interval should be equal 5000\n */\nvar Direction;\n(function (Direction) {\n  Direction[Direction[\"UNKNOWN\"] = 0] = \"UNKNOWN\";\n  Direction[Direction[\"NEXT\"] = 1] = \"NEXT\";\n  Direction[Direction[\"PREV\"] = 2] = \"PREV\";\n})(Direction || (Direction = {}));\nlet _currentId = 1;\n/**\n * Base element to create carousel\n */\nclass CarouselComponent {\n  /** Index of currently displayed slide(started for 0) */\n  set activeSlide(index) {\n    if (this.multilist) {\n      return;\n    }\n    if (isNumber(index)) {\n      this.customActiveSlide = index;\n    }\n    if (this._slides.length && index !== this._currentActiveSlide) {\n      this._select(index);\n    }\n  }\n  get activeSlide() {\n    return this._currentActiveSlide || 0;\n  }\n  /**\n   * Delay of item cycling in milliseconds. If false, carousel won't cycle\n   * automatically.\n   */\n  get interval() {\n    return this._interval;\n  }\n  set interval(value) {\n    this._interval = value;\n    this.restartTimer();\n  }\n  get slides() {\n    return this._slides.toArray();\n  }\n  get isFirstSlideVisible() {\n    const indexes = this.getVisibleIndexes();\n    if (!indexes || indexes instanceof Array && !indexes.length) {\n      return false;\n    }\n    return indexes.includes(0);\n  }\n  get isLastSlideVisible() {\n    const indexes = this.getVisibleIndexes();\n    if (!indexes || indexes instanceof Array && !indexes.length) {\n      return false;\n    }\n    return indexes.includes(this._slides.length - 1);\n  }\n  get _bsVer() {\n    return getBsVer();\n  }\n  constructor(config, ngZone, platformId) {\n    this.ngZone = ngZone;\n    this.platformId = platformId;\n    /* If `true` — carousel will not cycle continuously and will have hard stops (prevent looping) */\n    this.noWrap = false;\n    /*  If `true` — will disable pausing on carousel mouse hover */\n    this.noPause = false;\n    /*  If `true` — carousel-indicators are visible  */\n    this.showIndicators = true;\n    /*  If `true` - autoplay will be stopped on focus */\n    this.pauseOnFocus = false;\n    /* If `true` - carousel indicators indicate slides chunks\n       works ONLY if singleSlideOffset = FALSE */\n    this.indicatorsByChunk = false;\n    /* If value more then 1 — carousel works in multilist mode */\n    this.itemsPerSlide = 1;\n    /* If `true` — carousel shifts by one element. By default carousel shifts by number\n       of visible elements (itemsPerSlide field) */\n    this.singleSlideOffset = false;\n    /** Turn on/off animation. Animation doesn't work for multilist carousel */\n    this.isAnimated = false;\n    /** Will be emitted when active slide has been changed. Part of two-way-bindable [(activeSlide)] property */\n    this.activeSlideChange = new EventEmitter(false);\n    /** Will be emitted when active slides has been changed in multilist mode */\n    this.slideRangeChange = new EventEmitter();\n    /* Index to start display slides from it */\n    this.startFromIndex = 0;\n    this._interval = 5000;\n    this._slides = new LinkedList();\n    this._currentVisibleSlidesIndex = 0;\n    this.isPlaying = false;\n    this.destroyed = false;\n    this.currentId = 0;\n    this.getActive = slide => slide.active;\n    this.makeSlidesConsistent = slides => {\n      slides.forEach((slide, index) => slide.item.order = index);\n    };\n    Object.assign(this, config);\n    this.currentId = _currentId++;\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      if (this.singleSlideOffset) {\n        this.indicatorsByChunk = false;\n      }\n      if (this.multilist) {\n        this._chunkedSlides = chunkByNumber(this.mapSlidesAndIndexes(), this.itemsPerSlide);\n        this.selectInitialSlides();\n      }\n      if (this.customActiveSlide && !this.multilist) {\n        this._select(this.customActiveSlide);\n      }\n    }, 0);\n  }\n  ngOnDestroy() {\n    this.destroyed = true;\n  }\n  /**\n   * Adds new slide. If this slide is first in collection - set it as active\n   * and starts auto changing\n   * @param slide\n   */\n  addSlide(slide) {\n    this._slides.add(slide);\n    if (this.multilist && this._slides.length <= this.itemsPerSlide) {\n      slide.active = true;\n    }\n    if (!this.multilist && this.isAnimated) {\n      slide.isAnimated = true;\n    }\n    if (!this.multilist && this._slides.length === 1) {\n      this._currentActiveSlide = undefined;\n      if (!this.customActiveSlide) {\n        this.activeSlide = 0;\n      }\n      this.play();\n    }\n    if (this.multilist && this._slides.length > this.itemsPerSlide) {\n      this.play();\n    }\n  }\n  /**\n   * Removes specified slide. If this slide is active - will roll to another\n   * slide\n   * @param slide\n   */\n  removeSlide(slide) {\n    const remIndex = this._slides.indexOf(slide);\n    if (this._currentActiveSlide === remIndex) {\n      // removing of active slide\n      let nextSlideIndex;\n      if (this._slides.length > 1) {\n        // if this slide last - will roll to first slide, if noWrap flag is\n        // FALSE or to previous, if noWrap is TRUE in case, if this slide in\n        // middle of collection, index of next slide is same to removed\n        nextSlideIndex = !this.isLast(remIndex) ? remIndex : this.noWrap ? remIndex - 1 : 0;\n      }\n      this._slides.remove(remIndex);\n      // prevents exception with changing some value after checking\n      setTimeout(() => {\n        this._select(nextSlideIndex);\n      }, 0);\n    } else {\n      this._slides.remove(remIndex);\n      const currentSlideIndex = this.getCurrentSlideIndex();\n      setTimeout(() => {\n        // after removing, need to actualize index of current active slide\n        this._currentActiveSlide = currentSlideIndex;\n        this.activeSlideChange.emit(this._currentActiveSlide);\n      }, 0);\n    }\n  }\n  nextSlideFromInterval(force = false) {\n    this.move(Direction.NEXT, force);\n  }\n  /**\n   * Rolling to next slide\n   * @param force: {boolean} if true - will ignore noWrap flag\n   */\n  nextSlide(force = false) {\n    if (this.isPlaying) {\n      this.restartTimer();\n    }\n    this.move(Direction.NEXT, force);\n  }\n  /**\n   * Rolling to previous slide\n   * @param force: {boolean} if true - will ignore noWrap flag\n   */\n  previousSlide(force = false) {\n    if (this.isPlaying) {\n      this.restartTimer();\n    }\n    this.move(Direction.PREV, force);\n  }\n  getFirstVisibleIndex() {\n    return this.slides.findIndex(this.getActive);\n  }\n  getLastVisibleIndex() {\n    return findLastIndex(this.slides, this.getActive);\n  }\n  move(direction, force = false) {\n    const firstVisibleIndex = this.getFirstVisibleIndex();\n    const lastVisibleIndex = this.getLastVisibleIndex();\n    if (this.noWrap) {\n      if (direction === Direction.NEXT && this.isLast(lastVisibleIndex) || direction === Direction.PREV && firstVisibleIndex === 0) {\n        return;\n      }\n    }\n    if (!this.multilist) {\n      this.activeSlide = this.findNextSlideIndex(direction, force) || 0;\n    } else {\n      this.moveMultilist(direction);\n    }\n  }\n  /**\n   * Swith slides by enter, space and arrows keys\n   * @internal\n   */\n  keydownPress(event) {\n    if (event.keyCode === 13 || event.key === 'Enter' || event.keyCode === 32 || event.key === 'Space') {\n      this.nextSlide();\n      event.preventDefault();\n      return;\n    }\n    if (event.keyCode === 37 || event.key === 'LeftArrow') {\n      this.previousSlide();\n      return;\n    }\n    if (event.keyCode === 39 || event.key === 'RightArrow') {\n      this.nextSlide();\n      return;\n    }\n  }\n  /**\n   * Play on mouse leave\n   * @internal\n   */\n  onMouseLeave() {\n    if (!this.pauseOnFocus) {\n      this.play();\n    }\n  }\n  /**\n   * Play on mouse up\n   * @internal\n   */\n  onMouseUp() {\n    if (!this.pauseOnFocus) {\n      this.play();\n    }\n  }\n  /**\n   * When slides on focus autoplay is stopped(optional)\n   * @internal\n   */\n  pauseFocusIn() {\n    if (this.pauseOnFocus) {\n      this.isPlaying = false;\n      this.resetTimer();\n    }\n  }\n  /**\n   * When slides out of focus autoplay is started\n   * @internal\n   */\n  pauseFocusOut() {\n    this.play();\n  }\n  /**\n   * Rolling to specified slide\n   * @param index: {number} index of slide, which must be shown\n   */\n  selectSlide(index) {\n    if (this.isPlaying) {\n      this.restartTimer();\n    }\n    if (!this.multilist) {\n      this.activeSlide = this.indicatorsByChunk ? index * this.itemsPerSlide : index;\n    } else {\n      this.selectSlideRange(this.indicatorsByChunk ? index * this.itemsPerSlide : index);\n    }\n  }\n  /**\n   * Starts a auto changing of slides\n   */\n  play() {\n    if (!this.isPlaying) {\n      this.isPlaying = true;\n      this.restartTimer();\n    }\n  }\n  /**\n   * Stops a auto changing of slides\n   */\n  pause() {\n    if (!this.noPause) {\n      this.isPlaying = false;\n      this.resetTimer();\n    }\n  }\n  /**\n   * Finds and returns index of currently displayed slide\n   */\n  getCurrentSlideIndex() {\n    return this._slides.findIndex(this.getActive);\n  }\n  /**\n   * Defines, whether the specified index is last in collection\n   * @param index\n   */\n  isLast(index) {\n    return index + 1 >= this._slides.length;\n  }\n  /**\n   * Defines, whether the specified index is first in collection\n   * @param index\n   */\n  isFirst(index) {\n    return index === 0;\n  }\n  indicatorsSlides() {\n    return this.slides.filter((slide, index) => !this.indicatorsByChunk || index % this.itemsPerSlide === 0);\n  }\n  selectInitialSlides() {\n    const startIndex = this.startFromIndex <= this._slides.length ? this.startFromIndex : 0;\n    this.hideSlides();\n    if (this.singleSlideOffset) {\n      this._slidesWithIndexes = this.mapSlidesAndIndexes();\n      if (this._slides.length - startIndex < this.itemsPerSlide) {\n        const slidesToAppend = this._slidesWithIndexes.slice(0, startIndex);\n        this._slidesWithIndexes = [...this._slidesWithIndexes, ...slidesToAppend].slice(slidesToAppend.length).slice(0, this.itemsPerSlide);\n      } else {\n        this._slidesWithIndexes = this._slidesWithIndexes.slice(startIndex, startIndex + this.itemsPerSlide);\n      }\n      this._slidesWithIndexes.forEach(slide => slide.item.active = true);\n      this.makeSlidesConsistent(this._slidesWithIndexes);\n    } else {\n      this.selectRangeByNestedIndex(startIndex);\n    }\n    this.slideRangeChange.emit(this.getVisibleIndexes());\n  }\n  /**\n   * Defines next slide index, depending of direction\n   * @param direction: Direction(UNKNOWN|PREV|NEXT)\n   * @param force: {boolean} if TRUE - will ignore noWrap flag, else will\n   *   return undefined if next slide require wrapping\n   */\n  findNextSlideIndex(direction, force) {\n    let nextSlideIndex = 0;\n    if (!force && this.isLast(this.activeSlide) && direction !== Direction.PREV && this.noWrap) {\n      return;\n    }\n    switch (direction) {\n      case Direction.NEXT:\n        // if this is last slide, not force, looping is disabled\n        // and need to going forward - select current slide, as a next\n        if (typeof this._currentActiveSlide === 'undefined') {\n          nextSlideIndex = 0;\n          break;\n        }\n        if (!this.isLast(this._currentActiveSlide)) {\n          nextSlideIndex = this._currentActiveSlide + 1;\n          break;\n        }\n        nextSlideIndex = !force && this.noWrap ? this._currentActiveSlide : 0;\n        break;\n      case Direction.PREV:\n        // if this is first slide, not force, looping is disabled\n        // and need to going backward - select current slide, as a next\n        if (typeof this._currentActiveSlide === 'undefined') {\n          nextSlideIndex = 0;\n          break;\n        }\n        if (this._currentActiveSlide > 0) {\n          nextSlideIndex = this._currentActiveSlide - 1;\n          break;\n        }\n        if (!force && this.noWrap) {\n          nextSlideIndex = this._currentActiveSlide;\n          break;\n        }\n        nextSlideIndex = this._slides.length - 1;\n        break;\n      default:\n        throw new Error('Unknown direction');\n    }\n    return nextSlideIndex;\n  }\n  mapSlidesAndIndexes() {\n    return this.slides.slice().map((slide, index) => {\n      return {\n        index,\n        item: slide\n      };\n    });\n  }\n  selectSlideRange(index) {\n    if (this.isIndexInRange(index)) {\n      return;\n    }\n    this.hideSlides();\n    if (!this.singleSlideOffset) {\n      this.selectRangeByNestedIndex(index);\n    } else {\n      const startIndex = this.isIndexOnTheEdges(index) ? index : index - this.itemsPerSlide + 1;\n      const endIndex = this.isIndexOnTheEdges(index) ? index + this.itemsPerSlide : index + 1;\n      this._slidesWithIndexes = this.mapSlidesAndIndexes().slice(startIndex, endIndex);\n      this.makeSlidesConsistent(this._slidesWithIndexes);\n      this._slidesWithIndexes.forEach(slide => slide.item.active = true);\n    }\n    this.slideRangeChange.emit(this.getVisibleIndexes());\n  }\n  selectRangeByNestedIndex(index) {\n    if (!this._chunkedSlides) {\n      return;\n    }\n    const selectedRange = this._chunkedSlides.map((slidesList, i) => {\n      return {\n        index: i,\n        list: slidesList\n      };\n    }).find(slidesList => {\n      return slidesList.list.find(slide => slide.index === index) !== undefined;\n    });\n    if (!selectedRange) {\n      return;\n    }\n    this._currentVisibleSlidesIndex = selectedRange.index;\n    this._chunkedSlides[selectedRange.index].forEach(slide => {\n      slide.item.active = true;\n    });\n  }\n  isIndexOnTheEdges(index) {\n    return index + 1 - this.itemsPerSlide <= 0 || index + this.itemsPerSlide <= this._slides.length;\n  }\n  isIndexInRange(index) {\n    if (this.singleSlideOffset && this._slidesWithIndexes) {\n      const visibleIndexes = this._slidesWithIndexes.map(slide => slide.index);\n      return visibleIndexes.indexOf(index) >= 0;\n    }\n    return index <= this.getLastVisibleIndex() && index >= this.getFirstVisibleIndex();\n  }\n  hideSlides() {\n    this.slides.forEach(slide => slide.active = false);\n  }\n  isVisibleSlideListLast() {\n    if (!this._chunkedSlides) {\n      return false;\n    }\n    return this._currentVisibleSlidesIndex === this._chunkedSlides.length - 1;\n  }\n  isVisibleSlideListFirst() {\n    return this._currentVisibleSlidesIndex === 0;\n  }\n  moveSliderByOneItem(direction) {\n    let firstVisibleIndex;\n    let lastVisibleIndex;\n    let indexToHide;\n    let indexToShow;\n    if (this.noWrap) {\n      firstVisibleIndex = this.getFirstVisibleIndex();\n      lastVisibleIndex = this.getLastVisibleIndex();\n      indexToHide = direction === Direction.NEXT ? firstVisibleIndex : lastVisibleIndex;\n      indexToShow = direction !== Direction.NEXT ? firstVisibleIndex - 1 : !this.isLast(lastVisibleIndex) ? lastVisibleIndex + 1 : 0;\n      const slideToHide = this._slides.get(indexToHide);\n      if (slideToHide) {\n        slideToHide.active = false;\n      }\n      const slideToShow = this._slides.get(indexToShow);\n      if (slideToShow) {\n        slideToShow.active = true;\n      }\n      const slidesToReorder = this.mapSlidesAndIndexes().filter(slide => slide.item.active);\n      this.makeSlidesConsistent(slidesToReorder);\n      if (this.singleSlideOffset) {\n        this._slidesWithIndexes = slidesToReorder;\n      }\n      this.slideRangeChange.emit(this.getVisibleIndexes());\n      return;\n    }\n    if (!this._slidesWithIndexes || !this._slidesWithIndexes[0]) {\n      return;\n    }\n    let index;\n    firstVisibleIndex = this._slidesWithIndexes[0].index;\n    lastVisibleIndex = this._slidesWithIndexes[this._slidesWithIndexes.length - 1].index;\n    if (direction === Direction.NEXT) {\n      this._slidesWithIndexes.shift();\n      index = this.isLast(lastVisibleIndex) ? 0 : lastVisibleIndex + 1;\n      const item = this._slides.get(index);\n      if (item) {\n        this._slidesWithIndexes.push({\n          index,\n          item\n        });\n      }\n    } else {\n      this._slidesWithIndexes.pop();\n      index = this.isFirst(firstVisibleIndex) ? this._slides.length - 1 : firstVisibleIndex - 1;\n      const item = this._slides.get(index);\n      if (item) {\n        this._slidesWithIndexes = [{\n          index,\n          item\n        }, ...this._slidesWithIndexes];\n      }\n    }\n    this.hideSlides();\n    this._slidesWithIndexes.forEach(slide => slide.item.active = true);\n    this.makeSlidesConsistent(this._slidesWithIndexes);\n    this.slideRangeChange.emit(this._slidesWithIndexes.map(slide => slide.index));\n  }\n  moveMultilist(direction) {\n    if (this.singleSlideOffset) {\n      this.moveSliderByOneItem(direction);\n    } else {\n      this.hideSlides();\n      if (this.noWrap) {\n        this._currentVisibleSlidesIndex = direction === Direction.NEXT ? this._currentVisibleSlidesIndex + 1 : this._currentVisibleSlidesIndex - 1;\n      } else if (direction === Direction.NEXT) {\n        this._currentVisibleSlidesIndex = this.isVisibleSlideListLast() ? 0 : this._currentVisibleSlidesIndex + 1;\n      } else {\n        if (this.isVisibleSlideListFirst()) {\n          this._currentVisibleSlidesIndex = this._chunkedSlides ? this._chunkedSlides.length - 1 : 0;\n        } else {\n          this._currentVisibleSlidesIndex = this._currentVisibleSlidesIndex - 1;\n        }\n      }\n      if (this._chunkedSlides) {\n        this._chunkedSlides[this._currentVisibleSlidesIndex].forEach(slide => slide.item.active = true);\n      }\n      this.slideRangeChange.emit(this.getVisibleIndexes());\n    }\n  }\n  getVisibleIndexes() {\n    if (!this.singleSlideOffset && this._chunkedSlides) {\n      return this._chunkedSlides[this._currentVisibleSlidesIndex].map(slide => slide.index);\n    }\n    if (this._slidesWithIndexes) {\n      return this._slidesWithIndexes.map(slide => slide.index);\n    }\n  }\n  /**\n   * Sets a slide, which specified through index, as active\n   * @param index\n   */\n  _select(index) {\n    if (isNaN(index)) {\n      this.pause();\n      return;\n    }\n    if (!this.multilist && typeof this._currentActiveSlide !== 'undefined') {\n      const currentSlide = this._slides.get(this._currentActiveSlide);\n      if (typeof currentSlide !== 'undefined') {\n        currentSlide.active = false;\n      }\n    }\n    const nextSlide = this._slides.get(index);\n    if (typeof nextSlide !== 'undefined') {\n      this._currentActiveSlide = index;\n      nextSlide.active = true;\n      this.activeSlide = index;\n      this.activeSlideChange.emit(index);\n    }\n  }\n  /**\n   * Starts loop of auto changing of slides\n   */\n  restartTimer() {\n    this.resetTimer();\n    const interval = +this.interval;\n    if (!isNaN(interval) && interval > 0 && isPlatformBrowser(this.platformId)) {\n      this.currentInterval = this.ngZone.runOutsideAngular(() => {\n        return window.setInterval(() => {\n          const nInterval = +this.interval;\n          this.ngZone.run(() => {\n            if (this.isPlaying && !isNaN(this.interval) && nInterval > 0 && this.slides.length) {\n              this.nextSlideFromInterval();\n            } else {\n              this.pause();\n            }\n          });\n        }, interval);\n      });\n    }\n  }\n  get multilist() {\n    return this.itemsPerSlide > 1;\n  }\n  /**\n   * Stops loop of auto changing of slides\n   */\n  resetTimer() {\n    if (this.currentInterval) {\n      clearInterval(this.currentInterval);\n      this.currentInterval = void 0;\n    }\n  }\n  checkDisabledClass(buttonType) {\n    if (buttonType === 'prev') {\n      return this.activeSlide === 0 && this.noWrap && !this.multilist || this.isFirstSlideVisible && this.noWrap && this.multilist;\n    }\n    return this.isLast(this.activeSlide) && this.noWrap && !this.multilist || this.isLastSlideVisible && this.noWrap && this.multilist;\n  }\n  static {\n    this.ɵfac = function CarouselComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CarouselComponent)(i0.ɵɵdirectiveInject(CarouselConfig), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(PLATFORM_ID));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CarouselComponent,\n      selectors: [[\"carousel\"]],\n      inputs: {\n        noWrap: \"noWrap\",\n        noPause: \"noPause\",\n        showIndicators: \"showIndicators\",\n        pauseOnFocus: \"pauseOnFocus\",\n        indicatorsByChunk: \"indicatorsByChunk\",\n        itemsPerSlide: \"itemsPerSlide\",\n        singleSlideOffset: \"singleSlideOffset\",\n        isAnimated: \"isAnimated\",\n        activeSlide: \"activeSlide\",\n        startFromIndex: \"startFromIndex\",\n        interval: \"interval\"\n      },\n      outputs: {\n        activeSlideChange: \"activeSlideChange\",\n        slideRangeChange: \"slideRangeChange\"\n      },\n      ngContentSelectors: _c0,\n      decls: 7,\n      vars: 8,\n      consts: [[\"tabindex\", \"0\", 1, \"carousel\", \"slide\", 3, \"mouseenter\", \"mouseleave\", \"mouseup\", \"keydown\", \"focusin\", \"focusout\", \"id\"], [4, \"ngIf\"], [1, \"carousel-inner\", 3, \"ngStyle\"], [\"class\", \"left carousel-control carousel-control-prev\", \"href\", \"javascript:void(0);\", \"tabindex\", \"0\", \"role\", \"button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"right carousel-control carousel-control-next\", \"href\", \"javascript:void(0);\", \"tabindex\", \"0\", \"role\", \"button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"carousel-indicators\"], [3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [3, \"click\"], [\"type\", \"button\", \"aria-current\", \"true\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"aria-current\", \"true\", 3, \"click\"], [\"href\", \"javascript:void(0);\", \"tabindex\", \"0\", \"role\", \"button\", 1, \"left\", \"carousel-control\", \"carousel-control-prev\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"icon-prev\", \"carousel-control-prev-icon\"], [1, \"sr-only\", \"visually-hidden\"], [\"href\", \"javascript:void(0);\", \"tabindex\", \"0\", \"role\", \"button\", 1, \"right\", \"carousel-control\", \"carousel-control-next\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"icon-next\", \"carousel-control-next-icon\"]],\n      template: function CarouselComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"mouseenter\", function CarouselComponent_Template_div_mouseenter_0_listener() {\n            return ctx.pause();\n          })(\"mouseleave\", function CarouselComponent_Template_div_mouseleave_0_listener() {\n            return ctx.onMouseLeave();\n          })(\"mouseup\", function CarouselComponent_Template_div_mouseup_0_listener() {\n            return ctx.onMouseUp();\n          })(\"keydown\", function CarouselComponent_Template_div_keydown_0_listener($event) {\n            return ctx.keydownPress($event);\n          })(\"focusin\", function CarouselComponent_Template_div_focusin_0_listener() {\n            return ctx.pauseFocusIn();\n          })(\"focusout\", function CarouselComponent_Template_div_focusout_0_listener() {\n            return ctx.pauseFocusOut();\n          });\n          i0.ɵɵtemplate(1, CarouselComponent_ng_container_1_Template, 3, 1, \"ng-container\", 1)(2, CarouselComponent_ng_container_2_Template, 3, 1, \"ng-container\", 1);\n          i0.ɵɵelementStart(3, \"div\", 2);\n          i0.ɵɵprojection(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, CarouselComponent_a_5_Template, 4, 3, \"a\", 3)(6, CarouselComponent_a_6_Template, 4, 3, \"a\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"id\", \"carousel\" + ctx.currentId);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx._bsVer.isBs5 && ctx.showIndicators && ctx.slides.length > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx._bsVer.isBs5 && ctx.showIndicators && ctx.slides.length > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(6, _c1, ctx.multilist ? \"flex\" : \"block\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.slides.length > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.slides.length > 1);\n        }\n      },\n      dependencies: [NgIf, NgFor, NgStyle],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CarouselComponent, [{\n    type: Component,\n    args: [{\n      selector: 'carousel',\n      standalone: true,\n      imports: [NgIf, NgFor, NgStyle],\n      template: \"<div (mouseenter)=\\\"pause()\\\"\\n     (mouseleave)=\\\"onMouseLeave()\\\"\\n     (mouseup)=\\\"onMouseUp()\\\"\\n     (keydown)=\\\"keydownPress($event)\\\"\\n     (focusin)=\\\"pauseFocusIn()\\\"\\n     (focusout)=\\\"pauseFocusOut()\\\"\\n     [id]=\\\"'carousel' + currentId\\\"\\n     class=\\\"carousel slide\\\" tabindex=\\\"0\\\">\\n  <ng-container *ngIf=\\\"!_bsVer.isBs5 && showIndicators && slides.length > 1\\\">\\n    <ol class=\\\"carousel-indicators\\\">\\n      <li *ngFor=\\\"let slide of indicatorsSlides(); let i = index;\\\"\\n          [class.active]=\\\"slide.active === true\\\"\\n          (click)=\\\"selectSlide(i)\\\">\\n      </li>\\n    </ol>\\n  </ng-container>\\n  <ng-container *ngIf=\\\"_bsVer.isBs5 && showIndicators && slides.length > 1\\\">\\n    <div class=\\\"carousel-indicators\\\">\\n      <button\\n        *ngFor=\\\"let slide of indicatorsSlides(); let i = index;\\\"\\n        [class.active]=\\\"slide.active === true\\\"\\n        (click)=\\\"selectSlide(i)\\\"\\n        type=\\\"button\\\"\\n        [attr.data-bs-target]=\\\"'#carousel' + currentId\\\"\\n        [attr.data-bs-slide-to]=\\\"i\\\" aria-current=\\\"true\\\"\\n      >\\n      </button>\\n    </div>\\n  </ng-container>\\n  <div class=\\\"carousel-inner\\\" [ngStyle]=\\\"{'display': multilist ? 'flex' : 'block'}\\\">\\n    <ng-content></ng-content>\\n  </div>\\n  <a class=\\\"left carousel-control carousel-control-prev\\\"\\n     href=\\\"javascript:void(0);\\\"\\n     [class.disabled]=\\\"checkDisabledClass('prev')\\\"\\n     [attr.data-bs-target]=\\\"'#carousel' + currentId\\\"\\n     *ngIf=\\\"slides.length > 1\\\"\\n     (click)=\\\"previousSlide()\\\"\\n     tabindex=\\\"0\\\" role=\\\"button\\\">\\n    <span class=\\\"icon-prev carousel-control-prev-icon\\\" aria-hidden=\\\"true\\\"></span>\\n    <span class=\\\"sr-only visually-hidden\\\">Previous</span>\\n  </a>\\n\\n  <a class=\\\"right carousel-control carousel-control-next\\\"\\n     href=\\\"javascript:void(0);\\\"\\n     *ngIf=\\\"slides.length > 1\\\"\\n     (click)=\\\"nextSlide()\\\"\\n     [class.disabled]=\\\"checkDisabledClass('next')\\\"\\n     [attr.data-bs-target]=\\\"'#carousel' + currentId\\\"\\n     tabindex=\\\"0\\\" role=\\\"button\\\">\\n    <span class=\\\"icon-next carousel-control-next-icon\\\" aria-hidden=\\\"true\\\"></span>\\n    <span class=\\\"sr-only visually-hidden\\\">Next</span>\\n  </a>\\n</div>\\n\"\n    }]\n  }], () => [{\n    type: CarouselConfig\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], {\n    noWrap: [{\n      type: Input\n    }],\n    noPause: [{\n      type: Input\n    }],\n    showIndicators: [{\n      type: Input\n    }],\n    pauseOnFocus: [{\n      type: Input\n    }],\n    indicatorsByChunk: [{\n      type: Input\n    }],\n    itemsPerSlide: [{\n      type: Input\n    }],\n    singleSlideOffset: [{\n      type: Input\n    }],\n    isAnimated: [{\n      type: Input\n    }],\n    activeSlideChange: [{\n      type: Output\n    }],\n    slideRangeChange: [{\n      type: Output\n    }],\n    activeSlide: [{\n      type: Input\n    }],\n    startFromIndex: [{\n      type: Input\n    }],\n    interval: [{\n      type: Input\n    }]\n  });\n})();\nclass SlideComponent {\n  constructor(carousel) {\n    /** Is current slide active */\n    this.active = false;\n    this.itemWidth = '100%';\n    this.order = 0;\n    this.isAnimated = false;\n    /** Wraps element by appropriate CSS classes */\n    this.addClass = true;\n    this.multilist = false;\n    this.carousel = carousel;\n  }\n  /** Fires changes in container collection after adding a new slide instance */\n  ngOnInit() {\n    this.carousel.addSlide(this);\n    this.itemWidth = `${100 / this.carousel.itemsPerSlide}%`;\n    this.multilist = this.carousel?.itemsPerSlide > 1;\n  }\n  /** Fires changes in container collection after removing of this slide instance */\n  ngOnDestroy() {\n    this.carousel.removeSlide(this);\n  }\n  static {\n    this.ɵfac = function SlideComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SlideComponent)(i0.ɵɵdirectiveInject(CarouselComponent));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SlideComponent,\n      selectors: [[\"slide\"]],\n      hostVars: 15,\n      hostBindings: function SlideComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-hidden\", !ctx.active);\n          i0.ɵɵstyleProp(\"width\", ctx.itemWidth)(\"order\", ctx.order);\n          i0.ɵɵclassProp(\"multilist-margin\", ctx.multilist)(\"active\", ctx.active)(\"carousel-animation\", ctx.isAnimated)(\"item\", ctx.addClass)(\"carousel-item\", ctx.addClass);\n        }\n      },\n      inputs: {\n        active: \"active\"\n      },\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 2,\n      consts: [[1, \"item\"]],\n      template: function SlideComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"active\", ctx.active);\n        }\n      },\n      styles: [\".carousel-animation[_nghost-%COMP%]{transition:opacity .6s ease,visibility .6s ease;float:left}.carousel-animation.active[_nghost-%COMP%]{opacity:1;visibility:visible}.carousel-animation[_nghost-%COMP%]:not(.active){display:block;position:absolute;opacity:0;visibility:hidden}.multilist-margin[_nghost-%COMP%]{margin-right:auto}.carousel-item[_nghost-%COMP%]{perspective:1000px}\"]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SlideComponent, [{\n    type: Component,\n    args: [{\n      selector: 'slide',\n      template: `\n    <div [class.active]=\"active\" class=\"item\">\n      <ng-content></ng-content>\n    </div>\n  `,\n      host: {\n        '[attr.aria-hidden]': '!active',\n        '[class.multilist-margin]': 'multilist'\n      },\n      standalone: true,\n      styles: [\":host.carousel-animation{transition:opacity .6s ease,visibility .6s ease;float:left}:host.carousel-animation.active{opacity:1;visibility:visible}:host.carousel-animation:not(.active){display:block;position:absolute;opacity:0;visibility:hidden}:host.multilist-margin{margin-right:auto}:host.carousel-item{perspective:1000px}\\n\"]\n    }]\n  }], () => [{\n    type: CarouselComponent\n  }], {\n    active: [{\n      type: HostBinding,\n      args: ['class.active']\n    }, {\n      type: Input\n    }],\n    itemWidth: [{\n      type: HostBinding,\n      args: ['style.width']\n    }],\n    order: [{\n      type: HostBinding,\n      args: ['style.order']\n    }],\n    isAnimated: [{\n      type: HostBinding,\n      args: ['class.carousel-animation']\n    }],\n    addClass: [{\n      type: HostBinding,\n      args: ['class.item']\n    }, {\n      type: HostBinding,\n      args: ['class.carousel-item']\n    }]\n  });\n})();\nclass CarouselModule {\n  // @deprecated method not required anymore, will be deleted in v19.0.0\n  static forRoot() {\n    return {\n      ngModule: CarouselModule,\n      providers: []\n    };\n  }\n  static {\n    this.ɵfac = function CarouselModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CarouselModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CarouselModule,\n      imports: [SlideComponent, CarouselComponent],\n      exports: [SlideComponent, CarouselComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CarouselModule, [{\n    type: NgModule,\n    args: [{\n      imports: [SlideComponent, CarouselComponent],\n      exports: [SlideComponent, CarouselComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CarouselComponent, CarouselConfig, CarouselModule, SlideComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4IA,IAAM,MAAM,OAAO,WAAW,eAAe,UAAU,CAAC;AACxD,IAAM,WAAW,IAAI;AACrB,IAAM,WAAW,IAAI;AAErB,IAAM,KAAK,IAAI,KAAK,MAAM,IAAI,GAAG,IAAI,MAAM;AAC3C,IAAM,cAAc,IAAI,cAAc,IAAI,cAAc;AACxD,IAAM,QAAQ,IAAI;AAClB,IAAM,aAAa,IAAI;AACvB,IAAM,gBAAgB,IAAI;AAC1B,IAAM,cAAc,IAAI;AACxB,IAAM,UAAU,IAAI;AACpB,IAAM,WAAW,IAAI;AACrB,IAAM,gBAAgB,IAAI;AAC1B,IAAI;AAAA,CACH,SAAUA,YAAW;AACpB,EAAAA,WAAU,OAAO,IAAI;AACrB,EAAAA,WAAU,OAAO,IAAI;AACvB,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI;AACJ,SAAS,kBAAkB;AACzB,QAAM,SAAS,IAAI,SAAS,cAAc,MAAM;AAChD,SAAO,YAAY;AACnB,SAAO,UAAU,IAAI,QAAQ;AAC7B,SAAO,UAAU,IAAI,MAAM;AAC3B,MAAI,SAAS,KAAK,YAAY,MAAM;AACpC,QAAM,eAAe,IAAI,iBAAiB,MAAM,EAAE;AAClD,MAAI,gBAAgB,WAAW,YAAY,GAAG;AAC5C,QAAI,SAAS,KAAK,YAAY,MAAM;AACpC,WAAO;AAAA,EACT;AACA,MAAI,SAAS,KAAK,YAAY,MAAM;AACpC,SAAO;AACT;AAIA,SAAS,QAAQ;AACf,MAAI,eAAgB,QAAO,mBAAmB;AAC9C,mBAAiB,gBAAgB;AACjC,SAAO,mBAAmB;AAC5B;AACA,SAAS,QAAQ;AACf,MAAI,eAAgB,QAAO,mBAAmB;AAC9C,mBAAiB,gBAAgB;AACjC,SAAO,mBAAmB;AAC5B;AACA,SAAS,WAAW;AAClB,SAAO;AAAA,IACL,OAAO,MAAM;AAAA,IACb,OAAO,MAAM;AAAA,EACf;AACF;AAMA,IAAM,aAAN,MAAiB;AAAA,EACf,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,UAAU,CAAC;AAAA,EAElB;AAAA,EACA,IAAI,UAAU;AACZ,QAAI,KAAK,WAAW,KAAK,WAAW,KAAK,YAAY,KAAK,QAAQ;AAChE,aAAO;AAAA,IACT;AACA,QAAI,UAAU,KAAK;AACnB,aAAS,QAAQ,GAAG,QAAQ,UAAU,SAAS;AAC7C,gBAAU,SAAS;AAAA,IACrB;AACA,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,IAAI,OAAO,WAAW,KAAK,QAAQ;AACjC,QAAI,WAAW,KAAK,WAAW,KAAK,QAAQ;AAC1C,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AACA,UAAM,OAAO;AAAA,MACX;AAAA,MACA,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AACA,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,WAAK,UAAU;AAAA,IACjB,OAAO;AACL,UAAI,aAAa,KAAK,KAAK,MAAM;AAE/B,aAAK,OAAO,KAAK;AACjB,aAAK,KAAK,WAAW;AACrB,aAAK,OAAO;AAAA,MACd,WAAW,aAAa,KAAK,UAAU,KAAK,MAAM;AAEhD,aAAK,KAAK,OAAO;AACjB,aAAK,WAAW,KAAK;AACrB,aAAK,OAAO;AAAA,MACd,OAAO;AAEL,cAAM,sBAAsB,KAAK,QAAQ,WAAW,CAAC;AACrD,cAAM,kBAAkB,qBAAqB;AAC7C,YAAI,uBAAuB,iBAAiB;AAC1C,8BAAoB,OAAO;AAC3B,0BAAgB,WAAW;AAC3B,eAAK,WAAW;AAChB,eAAK,OAAO;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,SAAK;AACL,SAAK,kCAAkC;AAAA,EACzC;AAAA,EACA,OAAO,WAAW,GAAG;AACnB,QAAI,KAAK,WAAW,KAAK,WAAW,KAAK,YAAY,KAAK,QAAQ;AAChE,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AACA,QAAI,aAAa,KAAK,KAAK,MAAM;AAE/B,WAAK,OAAO,KAAK,KAAK;AACtB,UAAI,KAAK,MAAM;AAEb,aAAK,KAAK,WAAW;AAAA,MACvB,OAAO;AAEL,aAAK,OAAO;AAAA,MACd;AAAA,IACF,WAAW,aAAa,KAAK,SAAS,KAAK,KAAK,MAAM,UAAU;AAE9D,WAAK,OAAO,KAAK,KAAK;AACtB,WAAK,KAAK,OAAO;AAAA,IACnB,OAAO;AAEL,YAAM,cAAc,KAAK,QAAQ,QAAQ;AACzC,UAAI,aAAa,QAAQ,YAAY,UAAU;AAC7C,oBAAY,KAAK,WAAW,YAAY;AACxC,oBAAY,SAAS,OAAO,YAAY;AAAA,MAC1C;AAAA,IACF;AACA,SAAK;AACL,SAAK,kCAAkC;AAAA,EACzC;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,QAAI,KAAK,WAAW,KAAK,WAAW,KAAK,YAAY,KAAK,QAAQ;AAChE,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AACA,UAAM,OAAO,KAAK,QAAQ,QAAQ;AAClC,QAAI,MAAM;AACR,WAAK,QAAQ;AACb,WAAK,kCAAkC;AAAA,IACzC;AAAA,EACF;AAAA,EACA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EACA,QAAQ,IAAI;AACV,QAAI,UAAU,KAAK;AACnB,UAAM,SAAS,CAAC;AAChB,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AACA,aAAS,QAAQ,GAAG,QAAQ,KAAK,QAAQ,SAAS;AAChD,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,MACT;AACA,UAAI,GAAG,QAAQ,OAAO,KAAK,GAAG;AAC5B,eAAO,KAAK;AAAA,UACV;AAAA,UACA,OAAO,QAAQ;AAAA,QACjB,CAAC;AAAA,MACH;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,QAAQ,MAAM;AACZ,SAAK,QAAQ,SAAO;AAClB,WAAK,IAAI,GAAG;AAAA,IACd,CAAC;AACD,WAAO,KAAK;AAAA,EACd;AAAA,EACA,MAAM;AACJ,QAAI,KAAK,WAAW,GAAG;AACrB,aAAO;AAAA,IACT;AACA,UAAM,OAAO,KAAK;AAClB,SAAK,OAAO,KAAK,SAAS,CAAC;AAC3B,WAAO,MAAM;AAAA,EACf;AAAA,EACA,WAAW,MAAM;AACf,SAAK,QAAQ;AACb,SAAK,QAAQ,SAAO;AAClB,WAAK,IAAI,KAAK,CAAC;AAAA,IACjB,CAAC;AACD,WAAO,KAAK;AAAA,EACd;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,WAAW,GAAG;AACrB,aAAO;AAAA,IACT;AACA,UAAM,WAAW,KAAK,MAAM;AAC5B,SAAK,OAAO;AACZ,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,IAAI;AACV,QAAI,UAAU,KAAK;AACnB,aAAS,QAAQ,GAAG,QAAQ,KAAK,QAAQ,SAAS;AAChD,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,SAAG,QAAQ,OAAO,KAAK;AACvB,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AAAA,EACA,QAAQ,OAAO;AACb,QAAI,UAAU,KAAK;AACnB,QAAI,WAAW;AACf,aAAS,QAAQ,GAAG,QAAQ,KAAK,QAAQ,SAAS;AAChD,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,UAAU,OAAO;AAC3B,mBAAW;AACX;AAAA,MACF;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA,EACA,KAAK,IAAI;AACP,QAAI,UAAU,KAAK;AACnB,QAAI,SAAS;AACb,WAAO,WAAW,CAAC,QAAQ;AACzB,UAAI,GAAG,QAAQ,KAAK,GAAG;AACrB,iBAAS;AACT;AAAA,MACF;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,IAAI;AACR,QAAI,UAAU,KAAK;AACnB,QAAI,SAAS;AACb,WAAO,WAAW,QAAQ;AACxB,UAAI,CAAC,GAAG,QAAQ,KAAK,GAAG;AACtB,iBAAS;AAAA,MACX;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW;AACT,WAAO;AAAA,EACT;AAAA,EACA,KAAK,IAAI;AACP,QAAI,UAAU,KAAK;AACnB,aAAS,QAAQ,GAAG,QAAQ,KAAK,QAAQ,SAAS;AAChD,UAAI,CAAC,SAAS;AACZ;AAAA,MACF;AACA,UAAI,GAAG,QAAQ,OAAO,KAAK,GAAG;AAC5B,eAAO,QAAQ;AAAA,MACjB;AACA,gBAAU,QAAQ;AAAA,IACpB;AAAA,EACF;AAAA,EACA,UAAU,IAAI;AACZ,QAAI,UAAU,KAAK;AACnB,aAAS,QAAQ,GAAG,QAAQ,KAAK,QAAQ,SAAS;AAChD,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,MACT;AACA,UAAI,GAAG,QAAQ,OAAO,KAAK,GAAG;AAC5B,eAAO;AAAA,MACT;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,UAAU;AAChB,QAAI,KAAK,WAAW,KAAK,WAAW,KAAK,YAAY,KAAK,QAAQ;AAChE,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AACA,QAAI,UAAU,KAAK;AACnB,aAAS,QAAQ,GAAG,QAAQ,UAAU,SAAS;AAC7C,gBAAU,SAAS;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EACA,oCAAoC;AAClC,UAAM,WAAW,CAAC;AAClB,QAAI,UAAU,KAAK;AACnB,WAAO,SAAS;AACd,eAAS,KAAK,QAAQ,KAAK;AAC3B,gBAAU,QAAQ;AAAA,IACpB;AACA,SAAK,UAAU;AAAA,EACjB;AACF;AAoDA,IAAM,WAAW,OAAO,YAAY,eAAe,EAAE,UAAU;;;ACve/D,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,SAAS,SAAS,qEAAqE;AACnG,YAAM,OAAU,cAAc,GAAG,EAAE;AACnC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,IAAI,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,IAAG,YAAY,UAAU,SAAS,WAAW,IAAI;AAAA,EACnD;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,MAAM,CAAC;AAC5B,IAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,MAAM,CAAC;AAC9E,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,iBAAiB,CAAC;AAAA,EACpD;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,WAAW,SAAS,SAAS,6EAA6E;AAC3G,YAAM,OAAU,cAAc,GAAG,EAAE;AACnC,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,YAAY,IAAI,CAAC;AAAA,IAChD,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,WAAW,IAAI;AACrB,UAAM,OAAO,IAAI;AACjB,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,UAAU,SAAS,WAAW,IAAI;AACjD,IAAG,YAAY,kBAAkB,cAAc,OAAO,SAAS,EAAE,oBAAoB,IAAI;AAAA,EAC3F;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,UAAU,CAAC;AACtF,IAAG,aAAa;AAChB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,iBAAiB,CAAC;AAAA,EACpD;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,oDAAoD;AAClF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,cAAc,CAAC;AAAA,IAC9C,CAAC;AACD,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,GAAG,UAAU;AACvB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAC5D,IAAG,YAAY,kBAAkB,cAAc,OAAO,SAAS;AAAA,EACjE;AACF;AACA,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,KAAK,EAAE;AAC5B,IAAG,WAAW,SAAS,SAAS,oDAAoD;AAClF,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,UAAU,CAAC;AAAA,IAC1C,CAAC;AACD,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,eAAe,GAAG,QAAQ,EAAE;AAC/B,IAAG,OAAO,GAAG,MAAM;AACnB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,YAAY,OAAO,mBAAmB,MAAM,CAAC;AAC5D,IAAG,YAAY,kBAAkB,cAAc,OAAO,SAAS;AAAA,EACjE;AACF;AACA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,cAAc;AAEZ,SAAK,WAAW;AAEhB,SAAK,UAAU;AAEf,SAAK,SAAS;AAEd,SAAK,iBAAiB;AAEtB,SAAK,eAAe;AAEpB,SAAK,oBAAoB;AAEzB,SAAK,gBAAgB;AAGrB,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,SAAS,cAAc,OAAO,WAAW;AACvC,MAAI,IAAI,MAAM;AACd,SAAO,KAAK;AACV,QAAI,UAAU,MAAM,CAAC,GAAG,GAAG,KAAK,GAAG;AACjC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,cAAc,OAAO,MAAM;AAClC,QAAM,MAAM,CAAC;AACb,QAAM,IAAI,KAAK,KAAK,MAAM,SAAS,IAAI;AACvC,MAAI,IAAI;AACR,SAAO,IAAI,GAAG;AACZ,UAAM,QAAQ,MAAM,OAAO,GAAG,MAAM,IAAI,KAAK,OAAO,MAAM,SAAS,MAAM,SAAS,IAAI;AACtF,QAAI,KAAK,KAAK;AACd;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU,YAAY,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAChF;AAkBA,IAAI;AAAA,CACH,SAAUC,YAAW;AACpB,EAAAA,WAAUA,WAAU,SAAS,IAAI,CAAC,IAAI;AACtC,EAAAA,WAAUA,WAAU,MAAM,IAAI,CAAC,IAAI;AACnC,EAAAA,WAAUA,WAAU,MAAM,IAAI,CAAC,IAAI;AACrC,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI,aAAa;AAIjB,IAAM,oBAAN,MAAM,mBAAkB;AAAA;AAAA,EAEtB,IAAI,YAAY,OAAO;AACrB,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AACA,QAAI,SAAS,KAAK,GAAG;AACnB,WAAK,oBAAoB;AAAA,IAC3B;AACA,QAAI,KAAK,QAAQ,UAAU,UAAU,KAAK,qBAAqB;AAC7D,WAAK,QAAQ,KAAK;AAAA,IACpB;AAAA,EACF;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,uBAAuB;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY;AACjB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,QAAQ,QAAQ;AAAA,EAC9B;AAAA,EACA,IAAI,sBAAsB;AACxB,UAAM,UAAU,KAAK,kBAAkB;AACvC,QAAI,CAAC,WAAW,mBAAmB,SAAS,CAAC,QAAQ,QAAQ;AAC3D,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,SAAS,CAAC;AAAA,EAC3B;AAAA,EACA,IAAI,qBAAqB;AACvB,UAAM,UAAU,KAAK,kBAAkB;AACvC,QAAI,CAAC,WAAW,mBAAmB,SAAS,CAAC,QAAQ,QAAQ;AAC3D,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,SAAS,KAAK,QAAQ,SAAS,CAAC;AAAA,EACjD;AAAA,EACA,IAAI,SAAS;AACX,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,YAAY,QAAQ,QAAQ,YAAY;AACtC,SAAK,SAAS;AACd,SAAK,aAAa;AAElB,SAAK,SAAS;AAEd,SAAK,UAAU;AAEf,SAAK,iBAAiB;AAEtB,SAAK,eAAe;AAGpB,SAAK,oBAAoB;AAEzB,SAAK,gBAAgB;AAGrB,SAAK,oBAAoB;AAEzB,SAAK,aAAa;AAElB,SAAK,oBAAoB,IAAI,aAAa,KAAK;AAE/C,SAAK,mBAAmB,IAAI,aAAa;AAEzC,SAAK,iBAAiB;AACtB,SAAK,YAAY;AACjB,SAAK,UAAU,IAAI,WAAW;AAC9B,SAAK,6BAA6B;AAClC,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,YAAY,WAAS,MAAM;AAChC,SAAK,uBAAuB,YAAU;AACpC,aAAO,QAAQ,CAAC,OAAO,UAAU,MAAM,KAAK,QAAQ,KAAK;AAAA,IAC3D;AACA,WAAO,OAAO,MAAM,MAAM;AAC1B,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,kBAAkB;AAChB,eAAW,MAAM;AACf,UAAI,KAAK,mBAAmB;AAC1B,aAAK,oBAAoB;AAAA,MAC3B;AACA,UAAI,KAAK,WAAW;AAClB,aAAK,iBAAiB,cAAc,KAAK,oBAAoB,GAAG,KAAK,aAAa;AAClF,aAAK,oBAAoB;AAAA,MAC3B;AACA,UAAI,KAAK,qBAAqB,CAAC,KAAK,WAAW;AAC7C,aAAK,QAAQ,KAAK,iBAAiB;AAAA,MACrC;AAAA,IACF,GAAG,CAAC;AAAA,EACN;AAAA,EACA,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AACd,SAAK,QAAQ,IAAI,KAAK;AACtB,QAAI,KAAK,aAAa,KAAK,QAAQ,UAAU,KAAK,eAAe;AAC/D,YAAM,SAAS;AAAA,IACjB;AACA,QAAI,CAAC,KAAK,aAAa,KAAK,YAAY;AACtC,YAAM,aAAa;AAAA,IACrB;AACA,QAAI,CAAC,KAAK,aAAa,KAAK,QAAQ,WAAW,GAAG;AAChD,WAAK,sBAAsB;AAC3B,UAAI,CAAC,KAAK,mBAAmB;AAC3B,aAAK,cAAc;AAAA,MACrB;AACA,WAAK,KAAK;AAAA,IACZ;AACA,QAAI,KAAK,aAAa,KAAK,QAAQ,SAAS,KAAK,eAAe;AAC9D,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,UAAM,WAAW,KAAK,QAAQ,QAAQ,KAAK;AAC3C,QAAI,KAAK,wBAAwB,UAAU;AAEzC,UAAI;AACJ,UAAI,KAAK,QAAQ,SAAS,GAAG;AAI3B,yBAAiB,CAAC,KAAK,OAAO,QAAQ,IAAI,WAAW,KAAK,SAAS,WAAW,IAAI;AAAA,MACpF;AACA,WAAK,QAAQ,OAAO,QAAQ;AAE5B,iBAAW,MAAM;AACf,aAAK,QAAQ,cAAc;AAAA,MAC7B,GAAG,CAAC;AAAA,IACN,OAAO;AACL,WAAK,QAAQ,OAAO,QAAQ;AAC5B,YAAM,oBAAoB,KAAK,qBAAqB;AACpD,iBAAW,MAAM;AAEf,aAAK,sBAAsB;AAC3B,aAAK,kBAAkB,KAAK,KAAK,mBAAmB;AAAA,MACtD,GAAG,CAAC;AAAA,IACN;AAAA,EACF;AAAA,EACA,sBAAsB,QAAQ,OAAO;AACnC,SAAK,KAAK,UAAU,MAAM,KAAK;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,QAAQ,OAAO;AACvB,QAAI,KAAK,WAAW;AAClB,WAAK,aAAa;AAAA,IACpB;AACA,SAAK,KAAK,UAAU,MAAM,KAAK;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,QAAQ,OAAO;AAC3B,QAAI,KAAK,WAAW;AAClB,WAAK,aAAa;AAAA,IACpB;AACA,SAAK,KAAK,UAAU,MAAM,KAAK;AAAA,EACjC;AAAA,EACA,uBAAuB;AACrB,WAAO,KAAK,OAAO,UAAU,KAAK,SAAS;AAAA,EAC7C;AAAA,EACA,sBAAsB;AACpB,WAAO,cAAc,KAAK,QAAQ,KAAK,SAAS;AAAA,EAClD;AAAA,EACA,KAAK,WAAW,QAAQ,OAAO;AAC7B,UAAM,oBAAoB,KAAK,qBAAqB;AACpD,UAAM,mBAAmB,KAAK,oBAAoB;AAClD,QAAI,KAAK,QAAQ;AACf,UAAI,cAAc,UAAU,QAAQ,KAAK,OAAO,gBAAgB,KAAK,cAAc,UAAU,QAAQ,sBAAsB,GAAG;AAC5H;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,cAAc,KAAK,mBAAmB,WAAW,KAAK,KAAK;AAAA,IAClE,OAAO;AACL,WAAK,cAAc,SAAS;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO;AAClB,QAAI,MAAM,YAAY,MAAM,MAAM,QAAQ,WAAW,MAAM,YAAY,MAAM,MAAM,QAAQ,SAAS;AAClG,WAAK,UAAU;AACf,YAAM,eAAe;AACrB;AAAA,IACF;AACA,QAAI,MAAM,YAAY,MAAM,MAAM,QAAQ,aAAa;AACrD,WAAK,cAAc;AACnB;AAAA,IACF;AACA,QAAI,MAAM,YAAY,MAAM,MAAM,QAAQ,cAAc;AACtD,WAAK,UAAU;AACf;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACb,QAAI,KAAK,cAAc;AACrB,WAAK,YAAY;AACjB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,OAAO;AACjB,QAAI,KAAK,WAAW;AAClB,WAAK,aAAa;AAAA,IACpB;AACA,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,cAAc,KAAK,oBAAoB,QAAQ,KAAK,gBAAgB;AAAA,IAC3E,OAAO;AACL,WAAK,iBAAiB,KAAK,oBAAoB,QAAQ,KAAK,gBAAgB,KAAK;AAAA,IACnF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACL,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY;AACjB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACN,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,YAAY;AACjB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,uBAAuB;AACrB,WAAO,KAAK,QAAQ,UAAU,KAAK,SAAS;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO;AACZ,WAAO,QAAQ,KAAK,KAAK,QAAQ;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,OAAO;AACb,WAAO,UAAU;AAAA,EACnB;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,OAAO,OAAO,CAAC,OAAO,UAAU,CAAC,KAAK,qBAAqB,QAAQ,KAAK,kBAAkB,CAAC;AAAA,EACzG;AAAA,EACA,sBAAsB;AACpB,UAAM,aAAa,KAAK,kBAAkB,KAAK,QAAQ,SAAS,KAAK,iBAAiB;AACtF,SAAK,WAAW;AAChB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,qBAAqB,KAAK,oBAAoB;AACnD,UAAI,KAAK,QAAQ,SAAS,aAAa,KAAK,eAAe;AACzD,cAAM,iBAAiB,KAAK,mBAAmB,MAAM,GAAG,UAAU;AAClE,aAAK,qBAAqB,CAAC,GAAG,KAAK,oBAAoB,GAAG,cAAc,EAAE,MAAM,eAAe,MAAM,EAAE,MAAM,GAAG,KAAK,aAAa;AAAA,MACpI,OAAO;AACL,aAAK,qBAAqB,KAAK,mBAAmB,MAAM,YAAY,aAAa,KAAK,aAAa;AAAA,MACrG;AACA,WAAK,mBAAmB,QAAQ,WAAS,MAAM,KAAK,SAAS,IAAI;AACjE,WAAK,qBAAqB,KAAK,kBAAkB;AAAA,IACnD,OAAO;AACL,WAAK,yBAAyB,UAAU;AAAA,IAC1C;AACA,SAAK,iBAAiB,KAAK,KAAK,kBAAkB,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,WAAW,OAAO;AACnC,QAAI,iBAAiB;AACrB,QAAI,CAAC,SAAS,KAAK,OAAO,KAAK,WAAW,KAAK,cAAc,UAAU,QAAQ,KAAK,QAAQ;AAC1F;AAAA,IACF;AACA,YAAQ,WAAW;AAAA,MACjB,KAAK,UAAU;AAGb,YAAI,OAAO,KAAK,wBAAwB,aAAa;AACnD,2BAAiB;AACjB;AAAA,QACF;AACA,YAAI,CAAC,KAAK,OAAO,KAAK,mBAAmB,GAAG;AAC1C,2BAAiB,KAAK,sBAAsB;AAC5C;AAAA,QACF;AACA,yBAAiB,CAAC,SAAS,KAAK,SAAS,KAAK,sBAAsB;AACpE;AAAA,MACF,KAAK,UAAU;AAGb,YAAI,OAAO,KAAK,wBAAwB,aAAa;AACnD,2BAAiB;AACjB;AAAA,QACF;AACA,YAAI,KAAK,sBAAsB,GAAG;AAChC,2BAAiB,KAAK,sBAAsB;AAC5C;AAAA,QACF;AACA,YAAI,CAAC,SAAS,KAAK,QAAQ;AACzB,2BAAiB,KAAK;AACtB;AAAA,QACF;AACA,yBAAiB,KAAK,QAAQ,SAAS;AACvC;AAAA,MACF;AACE,cAAM,IAAI,MAAM,mBAAmB;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK,OAAO,MAAM,EAAE,IAAI,CAAC,OAAO,UAAU;AAC/C,aAAO;AAAA,QACL;AAAA,QACA,MAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,OAAO;AACtB,QAAI,KAAK,eAAe,KAAK,GAAG;AAC9B;AAAA,IACF;AACA,SAAK,WAAW;AAChB,QAAI,CAAC,KAAK,mBAAmB;AAC3B,WAAK,yBAAyB,KAAK;AAAA,IACrC,OAAO;AACL,YAAM,aAAa,KAAK,kBAAkB,KAAK,IAAI,QAAQ,QAAQ,KAAK,gBAAgB;AACxF,YAAM,WAAW,KAAK,kBAAkB,KAAK,IAAI,QAAQ,KAAK,gBAAgB,QAAQ;AACtF,WAAK,qBAAqB,KAAK,oBAAoB,EAAE,MAAM,YAAY,QAAQ;AAC/E,WAAK,qBAAqB,KAAK,kBAAkB;AACjD,WAAK,mBAAmB,QAAQ,WAAS,MAAM,KAAK,SAAS,IAAI;AAAA,IACnE;AACA,SAAK,iBAAiB,KAAK,KAAK,kBAAkB,CAAC;AAAA,EACrD;AAAA,EACA,yBAAyB,OAAO;AAC9B,QAAI,CAAC,KAAK,gBAAgB;AACxB;AAAA,IACF;AACA,UAAM,gBAAgB,KAAK,eAAe,IAAI,CAAC,YAAY,MAAM;AAC/D,aAAO;AAAA,QACL,OAAO;AAAA,QACP,MAAM;AAAA,MACR;AAAA,IACF,CAAC,EAAE,KAAK,gBAAc;AACpB,aAAO,WAAW,KAAK,KAAK,WAAS,MAAM,UAAU,KAAK,MAAM;AAAA,IAClE,CAAC;AACD,QAAI,CAAC,eAAe;AAClB;AAAA,IACF;AACA,SAAK,6BAA6B,cAAc;AAChD,SAAK,eAAe,cAAc,KAAK,EAAE,QAAQ,WAAS;AACxD,YAAM,KAAK,SAAS;AAAA,IACtB,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,OAAO;AACvB,WAAO,QAAQ,IAAI,KAAK,iBAAiB,KAAK,QAAQ,KAAK,iBAAiB,KAAK,QAAQ;AAAA,EAC3F;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,KAAK,qBAAqB,KAAK,oBAAoB;AACrD,YAAM,iBAAiB,KAAK,mBAAmB,IAAI,WAAS,MAAM,KAAK;AACvE,aAAO,eAAe,QAAQ,KAAK,KAAK;AAAA,IAC1C;AACA,WAAO,SAAS,KAAK,oBAAoB,KAAK,SAAS,KAAK,qBAAqB;AAAA,EACnF;AAAA,EACA,aAAa;AACX,SAAK,OAAO,QAAQ,WAAS,MAAM,SAAS,KAAK;AAAA,EACnD;AAAA,EACA,yBAAyB;AACvB,QAAI,CAAC,KAAK,gBAAgB;AACxB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,+BAA+B,KAAK,eAAe,SAAS;AAAA,EAC1E;AAAA,EACA,0BAA0B;AACxB,WAAO,KAAK,+BAA+B;AAAA,EAC7C;AAAA,EACA,oBAAoB,WAAW;AAC7B,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,KAAK,QAAQ;AACf,0BAAoB,KAAK,qBAAqB;AAC9C,yBAAmB,KAAK,oBAAoB;AAC5C,oBAAc,cAAc,UAAU,OAAO,oBAAoB;AACjE,oBAAc,cAAc,UAAU,OAAO,oBAAoB,IAAI,CAAC,KAAK,OAAO,gBAAgB,IAAI,mBAAmB,IAAI;AAC7H,YAAM,cAAc,KAAK,QAAQ,IAAI,WAAW;AAChD,UAAI,aAAa;AACf,oBAAY,SAAS;AAAA,MACvB;AACA,YAAM,cAAc,KAAK,QAAQ,IAAI,WAAW;AAChD,UAAI,aAAa;AACf,oBAAY,SAAS;AAAA,MACvB;AACA,YAAM,kBAAkB,KAAK,oBAAoB,EAAE,OAAO,WAAS,MAAM,KAAK,MAAM;AACpF,WAAK,qBAAqB,eAAe;AACzC,UAAI,KAAK,mBAAmB;AAC1B,aAAK,qBAAqB;AAAA,MAC5B;AACA,WAAK,iBAAiB,KAAK,KAAK,kBAAkB,CAAC;AACnD;AAAA,IACF;AACA,QAAI,CAAC,KAAK,sBAAsB,CAAC,KAAK,mBAAmB,CAAC,GAAG;AAC3D;AAAA,IACF;AACA,QAAI;AACJ,wBAAoB,KAAK,mBAAmB,CAAC,EAAE;AAC/C,uBAAmB,KAAK,mBAAmB,KAAK,mBAAmB,SAAS,CAAC,EAAE;AAC/E,QAAI,cAAc,UAAU,MAAM;AAChC,WAAK,mBAAmB,MAAM;AAC9B,cAAQ,KAAK,OAAO,gBAAgB,IAAI,IAAI,mBAAmB;AAC/D,YAAM,OAAO,KAAK,QAAQ,IAAI,KAAK;AACnC,UAAI,MAAM;AACR,aAAK,mBAAmB,KAAK;AAAA,UAC3B;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,WAAK,mBAAmB,IAAI;AAC5B,cAAQ,KAAK,QAAQ,iBAAiB,IAAI,KAAK,QAAQ,SAAS,IAAI,oBAAoB;AACxF,YAAM,OAAO,KAAK,QAAQ,IAAI,KAAK;AACnC,UAAI,MAAM;AACR,aAAK,qBAAqB,CAAC;AAAA,UACzB;AAAA,UACA;AAAA,QACF,GAAG,GAAG,KAAK,kBAAkB;AAAA,MAC/B;AAAA,IACF;AACA,SAAK,WAAW;AAChB,SAAK,mBAAmB,QAAQ,WAAS,MAAM,KAAK,SAAS,IAAI;AACjE,SAAK,qBAAqB,KAAK,kBAAkB;AACjD,SAAK,iBAAiB,KAAK,KAAK,mBAAmB,IAAI,WAAS,MAAM,KAAK,CAAC;AAAA,EAC9E;AAAA,EACA,cAAc,WAAW;AACvB,QAAI,KAAK,mBAAmB;AAC1B,WAAK,oBAAoB,SAAS;AAAA,IACpC,OAAO;AACL,WAAK,WAAW;AAChB,UAAI,KAAK,QAAQ;AACf,aAAK,6BAA6B,cAAc,UAAU,OAAO,KAAK,6BAA6B,IAAI,KAAK,6BAA6B;AAAA,MAC3I,WAAW,cAAc,UAAU,MAAM;AACvC,aAAK,6BAA6B,KAAK,uBAAuB,IAAI,IAAI,KAAK,6BAA6B;AAAA,MAC1G,OAAO;AACL,YAAI,KAAK,wBAAwB,GAAG;AAClC,eAAK,6BAA6B,KAAK,iBAAiB,KAAK,eAAe,SAAS,IAAI;AAAA,QAC3F,OAAO;AACL,eAAK,6BAA6B,KAAK,6BAA6B;AAAA,QACtE;AAAA,MACF;AACA,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,KAAK,0BAA0B,EAAE,QAAQ,WAAS,MAAM,KAAK,SAAS,IAAI;AAAA,MAChG;AACA,WAAK,iBAAiB,KAAK,KAAK,kBAAkB,CAAC;AAAA,IACrD;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,QAAI,CAAC,KAAK,qBAAqB,KAAK,gBAAgB;AAClD,aAAO,KAAK,eAAe,KAAK,0BAA0B,EAAE,IAAI,WAAS,MAAM,KAAK;AAAA,IACtF;AACA,QAAI,KAAK,oBAAoB;AAC3B,aAAO,KAAK,mBAAmB,IAAI,WAAS,MAAM,KAAK;AAAA,IACzD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,OAAO;AACb,QAAI,MAAM,KAAK,GAAG;AAChB,WAAK,MAAM;AACX;AAAA,IACF;AACA,QAAI,CAAC,KAAK,aAAa,OAAO,KAAK,wBAAwB,aAAa;AACtE,YAAM,eAAe,KAAK,QAAQ,IAAI,KAAK,mBAAmB;AAC9D,UAAI,OAAO,iBAAiB,aAAa;AACvC,qBAAa,SAAS;AAAA,MACxB;AAAA,IACF;AACA,UAAM,YAAY,KAAK,QAAQ,IAAI,KAAK;AACxC,QAAI,OAAO,cAAc,aAAa;AACpC,WAAK,sBAAsB;AAC3B,gBAAU,SAAS;AACnB,WAAK,cAAc;AACnB,WAAK,kBAAkB,KAAK,KAAK;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACb,SAAK,WAAW;AAChB,UAAM,WAAW,CAAC,KAAK;AACvB,QAAI,CAAC,MAAM,QAAQ,KAAK,WAAW,KAAK,kBAAkB,KAAK,UAAU,GAAG;AAC1E,WAAK,kBAAkB,KAAK,OAAO,kBAAkB,MAAM;AACzD,eAAO,OAAO,YAAY,MAAM;AAC9B,gBAAM,YAAY,CAAC,KAAK;AACxB,eAAK,OAAO,IAAI,MAAM;AACpB,gBAAI,KAAK,aAAa,CAAC,MAAM,KAAK,QAAQ,KAAK,YAAY,KAAK,KAAK,OAAO,QAAQ;AAClF,mBAAK,sBAAsB;AAAA,YAC7B,OAAO;AACL,mBAAK,MAAM;AAAA,YACb;AAAA,UACF,CAAC;AAAA,QACH,GAAG,QAAQ;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa;AACX,QAAI,KAAK,iBAAiB;AACxB,oBAAc,KAAK,eAAe;AAClC,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,mBAAmB,YAAY;AAC7B,QAAI,eAAe,QAAQ;AACzB,aAAO,KAAK,gBAAgB,KAAK,KAAK,UAAU,CAAC,KAAK,aAAa,KAAK,uBAAuB,KAAK,UAAU,KAAK;AAAA,IACrH;AACA,WAAO,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,UAAU,CAAC,KAAK,aAAa,KAAK,sBAAsB,KAAK,UAAU,KAAK;AAAA,EAC3H;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,kBAAkB,cAAc,GAAM,kBAAqB,MAAM,GAAM,kBAAkB,WAAW,CAAC;AAAA,IAC9J;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,MACxB,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,mBAAmB;AAAA,QACnB,kBAAkB;AAAA,MACpB;AAAA,MACA,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,YAAY,KAAK,GAAG,YAAY,SAAS,GAAG,cAAc,cAAc,WAAW,WAAW,WAAW,YAAY,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,SAAS,GAAG,CAAC,SAAS,+CAA+C,QAAQ,uBAAuB,YAAY,KAAK,QAAQ,UAAU,GAAG,YAAY,SAAS,GAAG,MAAM,GAAG,CAAC,SAAS,gDAAgD,QAAQ,uBAAuB,YAAY,KAAK,QAAQ,UAAU,GAAG,YAAY,SAAS,GAAG,MAAM,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,UAAU,SAAS,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,QAAQ,UAAU,gBAAgB,QAAQ,GAAG,UAAU,SAAS,GAAG,SAAS,SAAS,GAAG,CAAC,QAAQ,UAAU,gBAAgB,QAAQ,GAAG,OAAO,GAAG,CAAC,QAAQ,uBAAuB,YAAY,KAAK,QAAQ,UAAU,GAAG,QAAQ,oBAAoB,yBAAyB,GAAG,OAAO,GAAG,CAAC,eAAe,QAAQ,GAAG,aAAa,4BAA4B,GAAG,CAAC,GAAG,WAAW,iBAAiB,GAAG,CAAC,QAAQ,uBAAuB,YAAY,KAAK,QAAQ,UAAU,GAAG,SAAS,oBAAoB,yBAAyB,GAAG,OAAO,GAAG,CAAC,eAAe,QAAQ,GAAG,aAAa,4BAA4B,CAAC;AAAA,MACjqC,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,cAAc,SAAS,uDAAuD;AAC1F,mBAAO,IAAI,MAAM;AAAA,UACnB,CAAC,EAAE,cAAc,SAAS,uDAAuD;AAC/E,mBAAO,IAAI,aAAa;AAAA,UAC1B,CAAC,EAAE,WAAW,SAAS,oDAAoD;AACzE,mBAAO,IAAI,UAAU;AAAA,UACvB,CAAC,EAAE,WAAW,SAAS,kDAAkD,QAAQ;AAC/E,mBAAO,IAAI,aAAa,MAAM;AAAA,UAChC,CAAC,EAAE,WAAW,SAAS,oDAAoD;AACzE,mBAAO,IAAI,aAAa;AAAA,UAC1B,CAAC,EAAE,YAAY,SAAS,qDAAqD;AAC3E,mBAAO,IAAI,cAAc;AAAA,UAC3B,CAAC;AACD,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,2CAA2C,GAAG,GAAG,gBAAgB,CAAC;AAC1J,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,KAAK,CAAC,EAAE,GAAG,gCAAgC,GAAG,GAAG,KAAK,CAAC;AAC9G,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,MAAM,aAAa,IAAI,SAAS;AAC9C,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,CAAC,IAAI,OAAO,SAAS,IAAI,kBAAkB,IAAI,OAAO,SAAS,CAAC;AACtF,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,OAAO,SAAS,IAAI,kBAAkB,IAAI,OAAO,SAAS,CAAC;AACrF,UAAG,UAAU;AACb,UAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,IAAI,YAAY,SAAS,OAAO,CAAC;AACrF,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,QAAQ,IAAI,OAAO,SAAS,CAAC;AAC3C,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,OAAO,SAAS,CAAC;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,MAAM,SAAO,OAAO;AAAA,MACnC,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,MAAM,SAAO,OAAO;AAAA,MAC9B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,UAAU;AAEpB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,aAAa;AAElB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,SAAS,SAAS,IAAI;AAC3B,SAAK,YAAY,GAAG,MAAM,KAAK,SAAS,aAAa;AACrD,SAAK,YAAY,KAAK,UAAU,gBAAgB;AAAA,EAClD;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,SAAS,YAAY,IAAI;AAAA,EAChC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAmB,kBAAkB,iBAAiB,CAAC;AAAA,IAC1F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,OAAO,CAAC;AAAA,MACrB,UAAU;AAAA,MACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,eAAe,CAAC,IAAI,MAAM;AACzC,UAAG,YAAY,SAAS,IAAI,SAAS,EAAE,SAAS,IAAI,KAAK;AACzD,UAAG,YAAY,oBAAoB,IAAI,SAAS,EAAE,UAAU,IAAI,MAAM,EAAE,sBAAsB,IAAI,UAAU,EAAE,QAAQ,IAAI,QAAQ,EAAE,iBAAiB,IAAI,QAAQ;AAAA,QACnK;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,MACV;AAAA,MACA,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC;AAAA,MACpB,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,UAAU,IAAI,MAAM;AAAA,QACrC;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,4XAA4X;AAAA,IACvY,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,MAAM;AAAA,QACJ,sBAAsB;AAAA,QACtB,4BAA4B;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,QAAQ,CAAC,uUAAuU;AAAA,IAClV,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA;AAAA,EAEnB,OAAO,UAAU;AACf,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,IACd;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,gBAAgB,iBAAiB;AAAA,MAC3C,SAAS,CAAC,gBAAgB,iBAAiB;AAAA,IAC7C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,gBAAgB,iBAAiB;AAAA,MAC3C,SAAS,CAAC,gBAAgB,iBAAiB;AAAA,IAC7C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["BsVerions", "Direction"]}