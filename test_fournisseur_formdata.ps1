# Créer un fichier logo temporaire
$logoContent = [System.Text.Encoding]::UTF8.GetBytes("fake logo content")
$logoPath = "temp_logo.png"
[System.IO.File]::WriteAllBytes($logoPath, $logoContent)

# Générer des valeurs uniques
$randomEmail = "test.fournisseur$(Get-Random)@example.com"
$randomMatricule = Get-Random -Minimum 10000000 -Maximum 99999999
$randomRib = "123$(Get-Random -Minimum 10000000000000000 -Maximum 99999999999999999)"

# Préparer les données pour FormData
$boundary = [System.Guid]::NewGuid().ToString()
$LF = "`r`n"

$bodyLines = @(
    "--$boundary",
    "Content-Disposition: form-data; name=`"Email`"",
    "",
    "$randomEmail",
    "--$boundary",
    "Content-Disposition: form-data; name=`"Password`"",
    "",
    "TestPassword123!",
    "--$boundary",
    "Content-Disposition: form-data; name=`"Nom`"",
    "",
    "Dupont",
    "--$boundary",
    "Content-Disposition: form-data; name=`"Prenom`"",
    "",
    "Jean",
    "--$boundary",
    "Content-Disposition: form-data; name=`"PhoneNumber`"",
    "",
    "12345678",
    "--$boundary",
    "Content-Disposition: form-data; name=`"MatriculeFiscale`"",
    "",
    "$randomMatricule",
    "--$boundary",
    "Content-Disposition: form-data; name=`"RaisonSociale`"",
    "",
    "Test Optique SARL",
    "--$boundary",
    "Content-Disposition: form-data; name=`"Description`"",
    "",
    "Magasin d'optique de test",
    "--$boundary",
    "Content-Disposition: form-data; name=`"RIB`"",
    "",
    "$randomRib",
    "--$boundary",
    "Content-Disposition: form-data; name=`"CodeBanque`"",
    "",
    "123",
    "--$boundary",
    "Content-Disposition: form-data; name=`"Commission`"",
    "",
    "0.75",
    "--$boundary",
    "Content-Disposition: form-data; name=`"DelaiPreparationJours`"",
    "",
    "2",
    "--$boundary",
    "Content-Disposition: form-data; name=`"FraisLivraisonBase`"",
    "",
    "9.99",
    "--$boundary",
    "Content-Disposition: form-data; name=`"adresseRue`"",
    "",
    "123 Rue de Test",
    "--$boundary",
    "Content-Disposition: form-data; name=`"adresseVille`"",
    "",
    "Tunis",
    "--$boundary",
    "Content-Disposition: form-data; name=`"adresseCodePostal`"",
    "",
    "1000",
    "--$boundary",
    "Content-Disposition: form-data; name=`"adressePays`"",
    "",
    "Tunisie",
    "--$boundary",
    "Content-Disposition: form-data; name=`"adresseEstPrincipale`"",
    "",
    "true",
    "--$boundary",
    "Content-Disposition: form-data; name=`"LogoFile`"; filename=`"logo.png`"",
    "Content-Type: image/png",
    "",
    "fake logo content",
    "--$boundary--"
)

$body = $bodyLines -join $LF

Write-Host "Inscription du fournisseur avec FormData..."
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5014/api/Fournisseurs' -Method POST -ContentType "multipart/form-data; boundary=$boundary" -Body $body
    Write-Host "Succès: $($response | ConvertTo-Json)"
} catch {
    Write-Host "Erreur: $($_.Exception.Message)"
    Write-Host "Détails: $($_.ErrorDetails.Message)"
}

Write-Host "`nVérification des notifications pour l'admin (ID=1)..."
try {
    $notifications = Invoke-RestMethod -Uri 'http://localhost:5014/api/notification/user/1' -Method GET
    Write-Host "Notifications trouvées: $($notifications.Count)"
    $notifications | ForEach-Object { Write-Host "- $($_.contenu)" }
} catch {
    Write-Host "Erreur lors de la récupération des notifications: $($_.Exception.Message)"
}

# Nettoyer le fichier temporaire
Remove-Item $logoPath -ErrorAction SilentlyContinue
