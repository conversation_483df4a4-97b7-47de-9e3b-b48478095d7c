﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class LignesCommandeFournisseurController : ControllerBase
    {
        private readonly ILigneCommandeFournisseurService _service;
        private readonly ILogger<LignesCommandeFournisseurController> _logger;

        public LignesCommandeFournisseurController(
            ILigneCommandeFournisseurService service,
            ILogger<LignesCommandeFournisseurController> logger)
        {
            _service = service;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<LigneCommandeFournisseurDto>>> GetByCommandeId(int commandeId)
        {
            try
            {
                var lignes = await _service.GetByCommandeIdAsync(commandeId);
                return Ok(lignes);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération des lignes pour la commande {commandeId}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpGet("{id:int}", Name = "GetLigneCommandeFournisseur")]
        public async Task<ActionResult<LigneCommandeFournisseurDto>> GetById(int commandeId, int id)
        {
            try
            {
                var ligne = await _service.GetByIdAsync(id);
                if (ligne.CommandeId != commandeId)
                    return BadRequest("Cette ligne n'appartient pas à la commande spécifiée");

                return Ok(ligne);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération de la ligne {id}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPost]
        public async Task<ActionResult<LigneCommandeFournisseurDto>> Create(
            int commandeId,
            [FromBody] CreateLigneCommandeFournisseurDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var ligne = await _service.CreateAsync(commandeId, dto);
                return CreatedAtRoute(
                    "GetLigneCommandeFournisseur",
                    new { commandeId, id = ligne.Id },
                    ligne);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la création d'une ligne pour la commande {commandeId}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPut("{id:int}")]
        public async Task<IActionResult> Update(
            int commandeId,
            int id,
            [FromBody] UpdateLigneCommandeFournisseurDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var ligne = await _service.GetByIdAsync(id);
                if (ligne.CommandeId != commandeId)
                    return BadRequest("Cette ligne n'appartient pas à la commande spécifiée");

                await _service.UpdateAsync(id, dto);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la mise à jour de la ligne {id}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpDelete("{id:int}")]
        public async Task<IActionResult> Delete(int commandeId, int id)
        {
            try
            {
                var ligne = await _service.GetByIdAsync(id);
                if (ligne.CommandeId != commandeId)
                    return BadRequest("Cette ligne n'appartient pas à la commande spécifiée");

                await _service.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la suppression de la ligne {id}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }
    }
}
