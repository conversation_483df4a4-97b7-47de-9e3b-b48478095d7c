.brands-carousel {
  background-color: var(--background-color);
  transition: background-color 0.5s;
  position: relative;
  display: flex;
  overflow: hidden;
  padding: 0 10px;
  .carousel {
    display: flex;
    transition: transform 0.3s ease;
    position: relative;
  }
  .brand-item {
    flex: 0 0 auto;
    margin: 0px 10px;
    background-color: var(--surface-color);
    border-radius: 15px;
    transition: background-color 0.5s;
    width: 80px;
    height: 55px;
    display: flex;
    align-items: center;
    overflow: hidden;
  }
  .brand-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    cursor: pointer;
    transition: transform 0.3s ease;
    &:hover {
      transform: scale(1.05);
    }
  }
}