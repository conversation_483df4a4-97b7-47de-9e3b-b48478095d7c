.cart-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
  background-color: var(--background-color);
  min-height: calc(100vh - 200px);

  h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: var(--text-color);
    font-weight: 600;
    text-align: center;
    padding: 1rem 0;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.loading {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
}

.error-message {
  background-color: rgba(231, 76, 60, 0.1);
  color: var(--error-color);
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 8px;
  border: 1px solid var(--error-color);
  display: flex;
  justify-content: space-between;
  align-items: center;

  button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--error-color);
  }
}

.cart-items {
  margin-bottom: 2rem;
  background: var(--card-background-color);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.cart-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  padding-right: 5rem; // Espace pour les boutons d'action
  border-bottom: 1px solid var(--border-color);
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    background-color: var(--card-background-color-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:last-child {
    border-bottom: none;
  }
}

.product-image {
  position: relative;
  flex-shrink: 0;

  .product-img {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 16px;
    border: 3px solid var(--border-color);
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      border-color: var(--primary-color);
    }
  }

  .promo-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--accent-color);
    color: white;
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(230, 126, 34, 0.3);
    animation: pulse 2s infinite;
    z-index: 2;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 0; // Pour permettre le text-overflow

  .product-name {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-color);
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .product-meta {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;

    .product-reference {
      font-size: 0.85rem;
      color: var(--secondary-color);
      font-family: 'Courier New', monospace;
      background: rgba(52, 152, 219, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
      display: inline-block;
      width: fit-content;
    }

    .product-brand {
      font-size: 0.9rem;
      color: var(--primary-color);
      font-weight: 500;

      &::before {
        content: "🏷️ ";
        margin-right: 4px;
      }
    }

    .product-stock {
      font-size: 0.85rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.3rem;

      &.in-stock {
        color: var(--success-color);

        &::before {
          content: "✅";
        }
      }

      &:not(.in-stock) {
        color: var(--error-color);

        &::before {
          content: "❌";
        }
      }
    }
  }
}

// Section droite avec prix et contrôles
.product-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1rem;
  flex-shrink: 0;
  min-width: 200px;

  .price-section {
    text-align: right;

    .price-with-promo {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;

      .original-price {
        text-decoration: line-through;
        color: var(--secondary-color);
        font-size: 0.95rem;
        font-weight: 500;
      }

      .promo-price {
        color: var(--accent-color);
        font-weight: 700;
        font-size: 1.4rem;
      }
    }

    .regular-price {
      font-weight: 700;
      color: var(--primary-color);
      font-size: 1.4rem;
    }
  }

  .quantity-control {
    display: flex;
    align-items: center;
    background: var(--card-background-color);
    border-radius: 12px;
    padding: 6px;
    border: 2px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    button {
      width: 40px;
      height: 40px;
      border: none;
      background: var(--card-background-color);
      font-size: 1.3rem;
      font-weight: bold;
      cursor: pointer;
      border-radius: 10px;
      color: var(--text-color);
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover:not(:disabled) {
        background: var(--primary-color);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
      }

      &:disabled {
        opacity: 0.4;
        cursor: not-allowed;
        background: var(--secondary-color);
      }
    }

    span {
      margin: 0 1.2rem;
      min-width: 35px;
      text-align: center;
      font-weight: 600;
      font-size: 1.2rem;
      color: var(--text-color);
    }
  }

  .subtotal {
    font-weight: 700;
    font-size: 1.3rem;
    color: var(--primary-color);
  }
}

// Boutons d'action (supprimer et favoris)
.item-actions {
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  display: flex;
  gap: 0.5rem;
  flex-direction: column;

  .action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 2px solid transparent;
    font-size: 1.1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-weight: 600;

    &.remove-btn {
      background: rgba(231, 76, 60, 0.1);
      color: var(--error-color);

      &:hover {
        background: var(--error-color);
        color: white;
        transform: scale(1.1);
      }
    }

    &.favorite-btn {
      background: rgba(255, 193, 7, 0.1);
      color: #ff9800;

      &:hover {
        background: #ff9800;
        color: white;
        transform: scale(1.1);
      }
    }
  }
}

.promo-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  background: var(--card-background-color);
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);

  input {
    flex: 1;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    background: var(--card-background-color);
    color: var(--text-color);
    transition: border-color 0.3s ease;

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    &::placeholder {
      color: var(--secondary-color);
    }
  }

  button {
    padding: 1rem 2rem;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: var(--card-shadow);
    }

    &:disabled {
      background: var(--secondary-color);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  }
}

.cart-summary {
  background: var(--card-background-color);
  color: var(--text-color);
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);

  .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    padding: 0.5rem 0;

    &.total {
      font-size: 1.4rem;
      font-weight: 700;
      margin-top: 1.5rem;
      padding-top: 1.5rem;
      border-top: 2px solid var(--primary-color);
      background: var(--primary-color);
      color: white;
      margin: 1.5rem -2rem -2rem -2rem;
      padding: 1.5rem 2rem;
      border-radius: 0 0 12px 12px;
    }

    .savings {
      color: var(--success-color);
      font-weight: 600;
    }

    .promo-applied {
      background: var(--success-color);
      color: white;
      padding: 4px 8px;
      border-radius: 50%;
      font-size: 0.8rem;
      font-weight: bold;
    }
  }
}

.cart-actions {
  display: flex;
  justify-content: space-between;
  gap: 1rem;

  button {
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
  }

  .clear-btn {
    background: var(--card-background-color);
    color: var(--text-color);
    border: 2px solid var(--border-color);
    max-width: 300px;

    &:hover {
      background: var(--secondary-color);
      transform: translateY(-1px);
      box-shadow: var(--card-shadow);
    }
  }

  .checkout-btn {
    background: var(--primary-color);
    color: var(--text-color);
    flex: 1;
    max-width: 300px;

    &:hover:not(:disabled) {
      background: var(--accent-color);
      transform: translateY(-1px);
      box-shadow: var(--card-shadow);
    }

    &:disabled {
      background: var(--secondary-color);
      cursor: not-allowed;
      opacity: 0.6;
      transform: none;
      box-shadow: none;
    }
  }
}

.empty-cart {
  text-align: center;
  padding: 3rem 0;

  img {
    width: 200px;
    margin-bottom: 1.5rem;
  }

  h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
  }

  p {
    color: #666;
    margin-bottom: 2rem;
  }

  button {
    padding: 0.75rem 2rem;
    background-color: #1976d2;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;

    &:hover {
      background-color: #1565c0;
    }
  }
}

/* Responsive */
@media (max-width: 768px) {
  .cart-item {
    gap: 1rem;
    padding: 1rem;
    padding-right: 4rem; // Espace réduit pour mobile

    .product-image {
      .product-img {
        width: 100px;
        height: 100px;
      }
    }

    .product-info {
      .product-name {
        font-size: 1.1rem;
        white-space: normal;
      }

      .product-meta {
        gap: 0.3rem;

        .product-reference,
        .product-brand,
        .product-stock {
          font-size: 0.8rem;
        }
      }
    }

    .product-controls {
      min-width: 160px;
      gap: 0.8rem;

      .price-section {
        .price-with-promo,
        .regular-price {
          font-size: 1.1rem;
        }
      }

      .quantity-control {
        button {
          width: 35px;
          height: 35px;
          font-size: 1.1rem;
        }

        span {
          margin: 0 0.8rem;
          font-size: 1rem;
        }
      }

      .subtotal {
        font-size: 1.1rem;
      }
    }

    .item-actions {
      top: 50%;
      right: 0.5rem;
      transform: translateY(-50%);

      .action-btn {
        width: 32px;
        height: 32px;
        font-size: 1rem;
      }
    }
  }

  .cart-actions {
    flex-direction: column;
    gap: 1rem;

    button {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .cart-container {
    padding: 0 0.5rem;
    margin: 1rem auto;

    h2 {
      font-size: 2rem;
    }
  }

  .cart-item {
    padding: 0.8rem;
    padding-right: 3.5rem; // Espace encore plus réduit pour très petits écrans
    gap: 0.8rem;

    .product-image .product-img {
      width: 80px;
      height: 80px;
    }

    .product-info {
      .product-name {
        font-size: 1rem;
      }

      .product-meta {
        .product-reference,
        .product-brand,
        .product-stock {
          font-size: 0.75rem;
        }
      }
    }

    .product-controls {
      min-width: 140px;

      .price-section {
        .price-with-promo,
        .regular-price {
          font-size: 1rem;
        }
      }

      .quantity-control {
        button {
          width: 32px;
          height: 32px;
          font-size: 1rem;
        }

        span {
          margin: 0 0.6rem;
          font-size: 0.9rem;
        }
      }

      .subtotal {
        font-size: 1rem;
      }
    }

    .item-actions {
      top: 50%;
      right: 0.3rem;
      transform: translateY(-50%);
      gap: 0.3rem;

      .action-btn {
        width: 28px;
        height: 28px;
        font-size: 0.9rem;
      }
    }
  }
}

// Styles pour les indicateurs de stock
.product-stock {
  font-weight: 600;
  font-size: 0.85rem;
  padding: 4px 8px;
  border-radius: 6px;

  &.in-stock {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
  }

  &.insufficient-stock {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ff9800;
  }

  &.out-of-stock {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--error-color);
  }
}

// Alerte de stock insuffisant
.stock-alert {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;

  .alert-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;

    .alert-icon {
      font-size: 1.5rem;
    }

    h4 {
      color: #856404;
      margin: 0;
      font-size: 1.1rem;
    }
  }

  p {
    color: #856404;
    margin-bottom: 10px;
  }

  ul {
    color: #856404;
    margin: 10px 0;
    padding-left: 20px;

    li {
      margin-bottom: 5px;
    }
  }
}

// Bouton désactivé pour stock insuffisant
.checkout-btn {
  &.disabled {
    background: var(--secondary-color) !important;
    color: #666 !important;
    cursor: not-allowed !important;

    &:hover {
      transform: none !important;
      box-shadow: none !important;
    }
  }
}
