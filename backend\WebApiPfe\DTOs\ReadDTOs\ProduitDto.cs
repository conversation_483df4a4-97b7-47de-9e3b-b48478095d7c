﻿namespace WebApiPfe.DTOs.ReadDTOs
{
    public class ProduitDto
    {
        public int Id { get; set; }
        public required string ReferenceOriginal { get; set; }
        public string? ReferenceFournisseur { get; set; }
        public required string CodeABarre { get; set; }
        public required string Nom { get; set; }
        public string? Description { get; set; }
        public decimal PrixAchatHT { get; set; }
        public decimal PrixVenteHT { get; set; }
        public decimal PrixVenteTTC { get; set; }
        public decimal PrixApresRemises { get; set; }
        public decimal PrixApresRemisesOutlet { get; set; }
        public decimal PrixApresAutresPromotions { get; set; }
        public decimal? PourcentageRemiseTotale { get; set; }
        public DateTime DateAjout { get; set; }
        public bool EstNouveau => (DateTime.UtcNow - DateAjout).TotalDays <= 30;
        public int TauxTVAId { get; set; }
        public int Stock { get; set; }
        public int SousCategorieId { get; set; }
        public int MarqueId { get; set; }
        public int FormeId { get; set; }
        public int FournisseurId { get; set; }
        public double NoteMoyenne { get; set; }
        public int NombreAvis { get; set; }
        public List<AvisDto>? Avis { get; set; }
        public MarqueDto? Marque { get; set; }
        public FormeDto? Forme { get; set; }
        public FournisseurDto? Fournisseur { get; set; }
        public SousCategorieDto? SousCategorie { get; set; }
        public List<ImageProduitDto> Images { get; set; } = new();
        public string? ImagePrincipaleUrl => Images?.FirstOrDefault(i => i.IsMain)?.ImageUrl;
        public List<PromotionDto> Promotions { get; set; } = new();
    }
}
