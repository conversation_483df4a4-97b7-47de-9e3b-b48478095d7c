<div class="favoris-container">
  <h1>Vos produits favoris</h1>

  <div *ngIf="loading" class="loading-spinner">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Chargement de vos favoris...</p>
  </div>

  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
    <button (click)="errorMessage = ''">×</button>
  </div>

  <div *ngIf="!loading && produits.length === 0" class="empty-favoris">
    <h2>Votre liste de favoris est vide</h2>
    <p>Ajoutez des produits à vos favoris pour les retrouver facilement</p>
    <button routerLink="/products">Parcourir les produits</button>
  </div>

  <div *ngIf="!loading && produits.length > 0" class="favoris-content">
    <div
      *ngFor="let category of objectKeys(groupedProducts)"
      class="category-group"
    >
      <h2>{{ category }}</h2>
      <div class="products-grid">
        <div
          *ngFor="
            let product of groupedProducts[category];
            trackBy: trackByProductId
          "
          class="product-card"
        >
          <div class="product-image">
            <img
              [src]="getSimpleProductImage(product)"
              [alt]="product.nom"
              class="product-img"
              (error)="onImageError($event)"
            />
            <button
              class="remove-favorite"
              (click)="removeFromFavorites(product.id)"
            >
              <mat-icon>favorite</mat-icon>
            </button>
            <div
              *ngIf="
                product.tauxRemiseTotale  &&
                product.tauxRemiseTotale  > 0
              "
              class="promo-badge"
            >
              -{{ product.tauxRemiseTotale  }}%
            </div>
          </div>

          <div class="product-info">
            <h3>{{ product.nom }}</h3>
            <div class="price-section">
              <span class="current-price">
                {{ product.prixFinalTTC | currency : "DT" }}
              </span>
              <span
                *ngIf="
                  product.prixApresOutlet &&
                  product.prixApresOutlet !== product.prixFinalTTC
                "
                class="intermediate-price text-sm text-gray-600 block"
              >
                {{ product.prixApresOutlet | currency : "DT" }}
              </span>

              <div
                *ngIf="
                  product.tauxRemiseTotale  &&
                  product.tauxRemiseTotale  > 0
                "
                class="promo-badge"
              >
                -{{ product.tauxRemiseTotale  }}%
              </div>
            </div>

            <div class="rating">
              <mat-icon
                *ngFor="let star of [1, 2, 3, 4, 5]"
                [class.filled]="star <= product.noteMoyenne"
              >
                {{ star <= product.noteMoyenne ? "star" : "star_border" }}
              </mat-icon>
              <span>({{ product.nombreAvis }})</span>
            </div>

            <div class="stock-status" [class.in-stock]="product.stock > 0">
              {{ product.stock > 0 ? "En stock" : "Rupture" }}
            </div>
          </div>

          <div class="product-actions">
            <button
              class="add-to-cart"
              (click)="addToCart(product)"
              [disabled]="product.stock <= 0"
            >
              <mat-icon>add_shopping_cart</mat-icon>
              Ajouter
            </button>
            <button
              class="view-details"
              [routerLink]="['/products', product.id]"
            >
              Voir détails
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
