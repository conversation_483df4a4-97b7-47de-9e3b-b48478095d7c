﻿using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
namespace WebApiPfe.Services.Interfaces
{
    public interface INotificationService
    {
        Task<NotificationDto> GetByIdAsync(int id);
        Task<List<NotificationDto>> GetByUserAsync(int userId);
        Task<List<NotificationDto>> GetUnreadByUserAsync(int userId);
        Task<NotificationDto> CreateAsync(CreateNotificationDto dto);
        Task MarkAsReadAsync(int id);
        Task DeleteAsync(int id);
        Task DeleteAllUserNotificationsAsync(int userId);
        Task NotifierTousLesAdminsAsync(string contenu);
        Task<List<NotificationDto>> GetAllAdminNotificationsAsync();
    }
}
