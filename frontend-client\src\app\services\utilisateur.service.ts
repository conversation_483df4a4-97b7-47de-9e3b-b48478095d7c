import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { UtilisateurReadDto } from '../models/UtilisateurReadDto';

@Injectable({
  providedIn: 'root'
})
export class UtilisateurService {
  private apiUrl = `${environment.apiUrl}/utilisateurs`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<UtilisateurReadDto[]> {
    return this.http.get<UtilisateurReadDto[]>(this.apiUrl);
  }

  getById(id: number): Observable<UtilisateurReadDto> {
    return this.http.get<UtilisateurReadDto>(`${this.apiUrl}/${id}`);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  toggleActivation(id: number): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/toggle-activation`, {});
  }
}
