<!-- Container principal -->
<div class="fournisseurs-wrapper">
  <!-- Version Desktop -->
  <div class="desktop-fournisseurs" *ngIf="!isMobileView">
    <div class="fournisseurs-container">
      <!-- Hitbox pour le hover -->
      <div
        class="fournisseurs-hitbox"
        (mouseenter)="delayClose()"
        (mouseleave)="handleMouseLeave()"
      ></div>

      <!-- Contenu visible -->
      <div class="fournisseurs-visible" *ngIf="fournisseurs.length > 0">
        <div class="fournisseurs-content">
          <h2>Nos Fournisseurs</h2>
          <div *ngIf="fournisseurs.length > 0" class="fournisseurs-section">
            <ul class="fournisseurs-list">
              <li
                *ngFor="let fournisseur of fournisseurs"
                class="fournisseur-item"
              >
                <div
                  (click)="navigateToFournisseur(fournisseur.id)"
                  class="fournisseur-link"
                  style="cursor: pointer"
                >
                  <div class="fournisseur-image-container">
                    <img
                      [src]="imageUrlService.getFournisseurLogoUrl(fournisseur.logoFile)"
                      [alt]="
                        fournisseur.nom + ' ' + fournisseur.prenom + ' logo'
                      "
                      class="fournisseur-image"
                      loading="lazy"
                    />
                  </div>
                  <p class="fournisseur-name">
                    {{ fournisseur.nom }} {{ fournisseur.prenom }}
                  </p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Version Mobile -->
  <div class="mobile-fournisseurs" *ngIf="isMobileView">
    <div class="mobile-fournisseurs-content" *ngIf="fournisseurs.length > 0">
      <div *ngIf="fournisseurs.length > 0" class="mobile-fournisseurs-section">
        <ul class="mobile-fournisseurs-list">
          <li
            *ngFor="let fournisseur of limitedFournisseurs"
            class="mobile-fournisseur-item"
          >
            <img
              [src]="fournisseur.logoFile"
              [alt]="fournisseur.nom + ' ' + fournisseur.prenom + ' logo'"
              class="fournisseur-image"
              loading="lazy"
            />
            <button
              (click)="navigateToFournisseur(fournisseur.id)"
              class="mobile-fournisseur-link"
            >
              {{ fournisseur.nom }} {{ fournisseur.prenom }}
            </button>
          </li>
        </ul>
        <div class="see-all-wrapper" *ngIf="fournisseurs.length > 10">
          <button class="see-all-button" (click)="navigateToAllFournisseurs()">
            Afficher toutes les fournisseurs
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
