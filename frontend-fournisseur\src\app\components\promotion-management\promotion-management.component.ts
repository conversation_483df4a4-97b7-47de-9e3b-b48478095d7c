import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PromotionSimpleService } from '../../services/promotion-simple.service';
import { AuthService } from '../../services/auth.service';
import { PromotionSimpleCreateDto, PromotionSimpleDto } from '../../models/promotion-simple.model';

@Component({
  selector: 'app-promotion-management',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './promotion-management.component.html',
  styleUrls: ['./promotion-management.component.css']
})
export class PromotionManagementComponent implements OnInit {
  promotions: PromotionSimpleDto[] = [];
  showCreateForm = false;
  loading = false;
  saving = false;
  error: string | null = null;
  successMessage: string | null = null;
  editingPromotion: PromotionSimpleDto | null = null;

  // Formulaire de création/modification
  formData: PromotionSimpleCreateDto = {
    type: 'CodePromo',
    pourcentageRemise: 10,
    dateDebut: '',
    dateFin: '',
    nomAffichage: '',
    nomPromotion: '', // Nouveau champ pour le nom de la promotion (ex: SUMMER)
    appliquerSurHT: false
  };

  constructor(
    private promotionService: PromotionSimpleService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadPromotions();
    this.initializeDates();
  }

  private initializeDates(): void {
    const today = new Date();
    const nextMonth = new Date(today);
    nextMonth.setMonth(today.getMonth() + 1);
    
    this.formData.dateDebut = today.toISOString().split('T')[0];
    this.formData.dateFin = nextMonth.toISOString().split('T')[0];
  }

  loadPromotions(): void {
    this.loading = true;
    this.error = null;
    
    this.promotionService.getMyPromotions().subscribe({
      next: (promotions) => {
        this.promotions = promotions;
        this.loading = false;
        console.log('✅ Promotions chargées:', promotions);
      },
      error: (err) => {
        this.error = 'Erreur lors du chargement des promotions';
        this.loading = false;
        console.error('❌ Erreur promotions:', err);
      }
    });
  }

  onTypeChange(): void {
    if (this.formData.type === 'CodePromo') {
      this.formData.codePromo = this.generatePromoCode();
    } else {
      this.formData.codePromo = undefined;
    }
  }

  generatePromoCode(): string {
    const prefix = 'PROMO';
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${prefix}${random}`;
  }

  createPromotion(): void {
    if (!this.validateForm()) {
      return;
    }

    // Vérifier l'authentification
    if (!this.authService.isAuthenticated()) {
      this.error = '⚠️ Vous devez être connecté pour créer une promotion';
      return;
    }

    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      this.error = '⚠️ Impossible de récupérer les informations utilisateur';
      return;
    }

    console.log('🔑 Utilisateur authentifié:', currentUser.email);
    console.log('📝 Données à envoyer:', this.formData);

    this.saving = true;
    this.error = null;
    this.successMessage = null;

    this.promotionService.create(this.formData).subscribe({
      next: (promotion) => {
        console.log('✅ Promotion créée:', promotion);
        this.promotions.unshift(promotion);
        this.successMessage = 'Promotion créée avec succès !';
        this.resetForm();
        this.showCreateForm = false;
        this.saving = false;
      },
      error: (err) => {
        this.error = 'Erreur lors de la création de la promotion';
        this.saving = false;
        console.error('❌ Erreur création promotion:', err);
      }
    });
  }

  updatePromotion(): void {
    console.log('🔄 Début de la modification de la promotion');
    console.log('📝 Données du formulaire:', this.formData);
    console.log('🎯 Promotion en cours d\'édition:', this.editingPromotion);

    if (!this.validateForm()) {
      console.log('❌ Validation du formulaire échouée');
      return;
    }

    if (!this.editingPromotion) {
      console.log('❌ Aucune promotion en cours d\'édition');
      this.error = 'Aucune promotion sélectionnée pour modification';
      return;
    }

    this.saving = true;
    this.error = null;
    this.successMessage = null;

    console.log('🚀 Envoi de la requête de modification pour l\'ID:', this.editingPromotion.id);

    this.promotionService.update(this.editingPromotion.id, this.formData).subscribe({
      next: (updatedPromotion) => {
        console.log('✅ Promotion modifiée avec succès:', updatedPromotion);

        // Mettre à jour la promotion dans la liste
        const index = this.promotions.findIndex(p => p.id === this.editingPromotion!.id);
        console.log('📍 Index de la promotion dans la liste:', index);

        if (index !== -1) {
          this.promotions[index] = updatedPromotion;
          console.log('✅ Promotion mise à jour dans la liste');
        } else {
          console.log('⚠️ Promotion non trouvée dans la liste pour mise à jour');
        }

        this.successMessage = 'Promotion modifiée avec succès !';
        this.resetForm();
        this.showCreateForm = false;
        this.editingPromotion = null;
        this.saving = false;
      },
      error: (err) => {
        console.error('❌ Erreur complète lors de la modification:', err);
        console.error('❌ Statut de l\'erreur:', err.status);
        console.error('❌ Message de l\'erreur:', err.message);
        console.error('❌ Corps de l\'erreur:', err.error);

        this.error = `Erreur lors de la modification: ${err.error?.message || err.message || 'Erreur inconnue'}`;
        this.saving = false;
      }
    });
  }

  editPromotion(promotion: PromotionSimpleDto): void {
    console.log('🔄 Début de l\'édition de la promotion:', promotion);

    this.editingPromotion = promotion;
    this.showCreateForm = true;
    this.error = null;
    this.successMessage = null;

    // Remplir le formulaire avec les données de la promotion
    this.formData = {
      type: promotion.type,
      pourcentageRemise: promotion.pourcentageRemise,
      dateDebut: this.formatDateForInput(promotion.dateDebut),
      dateFin: this.formatDateForInput(promotion.dateFin),
      nomAffichage: promotion.nomAffichage || '', // Sécurité contre null/undefined
      nomPromotion: promotion.nomPromotion || '',
      codePromo: promotion.codePromo || '',
      description: promotion.description || '',
      appliquerSurHT: promotion.appliquerSurHT,
      categorieId: promotion.categorieId,
      sousCategorieId: promotion.sousCategorieId,
      marqueId: promotion.marqueId,
      formeId: promotion.formeId,
      produitsApplicablesIds: promotion.produitsApplicablesIds,
      limitUtilisation: promotion.limitUtilisation
    };

    console.log('✅ Formulaire rempli avec les données:', this.formData);
    console.log('🔍 Vérification nomAffichage:', {
      original: promotion.nomAffichage,
      final: this.formData.nomAffichage,
      type: typeof this.formData.nomAffichage
    });
  }

  savePromotion(): void {
    console.log('🔄 savePromotion appelée');
    console.log('🎯 Mode édition:', !!this.editingPromotion);
    console.log('📝 Données du formulaire:', this.formData);

    if (this.editingPromotion) {
      console.log('➡️ Appel de updatePromotion()');
      this.updatePromotion();
    } else {
      console.log('➡️ Appel de createPromotion()');
      this.createPromotion();
    }
  }

  cancelEdit(): void {
    this.resetForm();
    this.editingPromotion = null;
    this.showCreateForm = false;
  }

  // Méthode de test temporaire pour diagnostiquer le problème
  testUpdate(): void {
    if (!this.editingPromotion) {
      console.log('❌ Aucune promotion en cours d\'édition pour le test');
      return;
    }

    console.log('🧪 Test de l\'API de modification');
    console.log('🎯 Promotion à modifier:', this.editingPromotion);

    // Test avec des données minimales
    const testData = {
      nomAffichage: 'Test Modification',
      type: this.editingPromotion.type,
      pourcentageRemise: 15
    };

    console.log('📝 Données de test:', testData);

    this.promotionService.update(this.editingPromotion.id, testData).subscribe({
      next: (result) => {
        console.log('✅ Test réussi:', result);
        alert('Test API réussi ! Vérifiez la console pour les détails.');
      },
      error: (err) => {
        console.error('❌ Test échoué:', err);
        alert(`Test API échoué: ${err.status} - ${err.message}`);
      }
    });
  }

  private formatDateForInput(dateString: string): string {
    if (!dateString) {
      console.log('⚠️ formatDateForInput: dateString vide');
      return '';
    }

    console.log('🔄 formatDateForInput: entrée =', dateString);

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        console.log('❌ formatDateForInput: date invalide');
        return '';
      }

      const result = date.toISOString().split('T')[0];
      console.log('✅ formatDateForInput: sortie =', result);
      return result;
    } catch (error) {
      console.error('❌ formatDateForInput: erreur =', error);
      return '';
    }
  }

  private validateForm(): boolean {
    console.log('🔍 Validation du formulaire - formData:', this.formData);
    console.log('🔍 nomAffichage:', {
      value: this.formData.nomAffichage,
      type: typeof this.formData.nomAffichage,
      length: this.formData.nomAffichage?.length,
      trimmed: this.formData.nomAffichage?.trim()
    });

    if (!this.formData.nomAffichage || !this.formData.nomAffichage.trim()) {
      console.log('❌ Validation échouée: nomAffichage manquant');
      this.error = 'Le nom d\'affichage est requis';
      return false;
    }

    if (this.formData.pourcentageRemise <= 0 || this.formData.pourcentageRemise > 100) {
      this.error = 'Le pourcentage de remise doit être entre 1 et 100';
      return false;
    }

    if (this.formData.type === 'CodePromo' && !this.formData.codePromo?.trim()) {
      this.error = 'Le code promo est requis';
      return false;
    }

    if (new Date(this.formData.dateDebut) >= new Date(this.formData.dateFin)) {
      this.error = 'La date de fin doit être après la date de début';
      return false;
    }

    return true;
  }

  resetForm(): void {
    this.formData = {
      type: 'CodePromo',
      pourcentageRemise: 10,
      dateDebut: '',
      dateFin: '',
      nomAffichage: '',
      nomPromotion: '', // Nouveau champ pour le nom de la promotion
      appliquerSurHT: false
    };
    this.initializeDates();
    this.onTypeChange();
    this.error = null;
    this.successMessage = null;
  }

  togglePromotion(promotion: PromotionSimpleDto): void {
    this.promotionService.toggle(promotion.id).subscribe({
      next: () => {
        promotion.estValide = !promotion.estValide;
        console.log('✅ Statut promotion mis à jour');
      },
      error: (err) => {
        console.error('❌ Erreur toggle promotion:', err);
      }
    });
  }

  deletePromotion(promotion: PromotionSimpleDto): void {
    console.log('🗑️ Bouton Supprimer cliqué pour:', promotion);

    if (!confirm(`Êtes-vous sûr de vouloir supprimer la promotion "${promotion.nomAffichage}" ?`)) {
      return;
    }

    console.log('🚀 Envoi de la requête de suppression pour l\'ID:', promotion.id);

    this.promotionService.delete(promotion.id).subscribe({
      next: () => {
        this.promotions = this.promotions.filter(p => p.id !== promotion.id);
        console.log('✅ Promotion supprimée avec succès');
        this.successMessage = 'Promotion supprimée avec succès !';
      },
      error: (err) => {
        console.error('❌ Erreur suppression promotion:', err);
        this.error = 'Erreur lors de la suppression: ' + (err.error?.message || err.message);
      }
    });
  }

  deleteAllPromotions(): void {
    console.log('🗑️ Bouton Supprimer tout cliqué');

    if (!confirm(`⚠️ ATTENTION ⚠️\n\nÊtes-vous sûr de vouloir supprimer TOUTES les ${this.promotions.length} promotions ?\n\nCette action est irréversible !`)) {
      return;
    }

    if (!confirm('🚨 DERNIÈRE CONFIRMATION 🚨\n\nVoulez-vous vraiment supprimer TOUTES vos promotions ?\n\nTapez "OUI" pour confirmer.')) {
      return;
    }

    this.loading = true;
    console.log('🚀 Suppression de toutes les promotions...');

    this.promotionService.deleteAll().subscribe({
      next: () => {
        this.promotions = [];
        this.loading = false;
        console.log('✅ Toutes les promotions ont été supprimées');
        alert('✅ Toutes les promotions ont été supprimées avec succès !');
        this.successMessage = 'Toutes les promotions ont été supprimées avec succès !';
      },
      error: (err) => {
        this.loading = false;
        console.error('❌ Erreur suppression globale:', err);
        this.error = 'Erreur lors de la suppression des promotions: ' + (err.error?.message || err.message);
        alert('❌ Erreur lors de la suppression: ' + (err.error?.message || err.message));
      }
    });
  }



  getStatusClass(promotion: PromotionSimpleDto): string {
    if (!promotion.estValide) return 'status-inactive';
    
    const now = new Date();
    const debut = new Date(promotion.dateDebut);
    const fin = new Date(promotion.dateFin);
    
    if (now < debut) return 'status-pending';
    if (now > fin) return 'status-expired';
    return 'status-active';
  }

  getStatusText(promotion: PromotionSimpleDto): string {
    if (!promotion.estValide) return 'Désactivée';

    const now = new Date();
    const debut = new Date(promotion.dateDebut);
    const fin = new Date(promotion.dateFin);

    if (now < debut) return 'En attente';
    if (now > fin) return 'Expirée';
    return 'Active';
  }

  /**
   * Obtenir la classe CSS pour le type de promotion
   */
  getTypeClass(type: any): string {
    if (typeof type === 'string') {
      return type.toLowerCase();
    }

    // Si c'est un enum ou un nombre, convertir en string
    const typeStr = String(type).toLowerCase();

    // Mapper les valeurs possibles
    switch (typeStr) {
      case 'codepromo':
      case '0':
        return 'codepromo';
      case 'automatique':
      case '1':
        return 'automatique';
      case 'outlet':
      case '2':
        return 'outlet';
      default:
        return 'unknown';
    }
  }

  /**
   * Obtenir le texte d'affichage pour le type de promotion
   */
  getTypeText(type: any): string {
    if (typeof type === 'string') {
      switch (type.toLowerCase()) {
        case 'codepromo':
          return '🎫 Code Promo';
        case 'automatique':
          return '⚡ Automatique';
        case 'outlet':
          return '🏷️ Outlet';
        default:
          return '❓ Inconnu';
      }
    }

    // Si c'est un enum ou un nombre
    const typeStr = String(type);
    switch (typeStr) {
      case '0':
        return '🎫 Code Promo';
      case '1':
        return '⚡ Automatique';
      case '2':
        return '🏷️ Outlet';
      default:
        return `❓ Type ${typeStr}`;
    }
  }

  /**
   * Obtenir le nombre de promotions actives
   */
  getActivePromotionsCount(): number {
    return this.promotions.filter(promotion => {
      if (!promotion.estValide) return false;

      const now = new Date();
      const debut = new Date(promotion.dateDebut);
      const fin = new Date(promotion.dateFin);

      return now >= debut && now <= fin;
    }).length;
  }

  /**
   * Obtenir le nombre de promotions à venir
   */
  getPendingPromotionsCount(): number {
    return this.promotions.filter(promotion => {
      if (!promotion.estValide) return false;

      const now = new Date();
      const debut = new Date(promotion.dateDebut);

      return now < debut;
    }).length;
  }

  /**
   * Formater une date pour l'affichage
   */
  formatDate(date: string | Date): string {
    if (!date) return '';

    const dateObj = typeof date === 'string' ? new Date(date) : date;

    return dateObj.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }
}
