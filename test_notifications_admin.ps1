Write-Host "Vérification des notifications pour l'admin (ID=2063)..."
try {
    $notifications = Invoke-RestMethod -Uri 'http://localhost:5014/api/notification/user/2063' -Method GET
    Write-Host "Notifications trouvées: $($notifications.Count)"
    if ($notifications.Count -gt 0) {
        $notifications | ForEach-Object { 
            Write-Host "- ID: $($_.id)"
            $contenuCourt = if ($_.contenu.Length -gt 100) { $_.contenu.Substring(0, 100) + "..." } else { $_.contenu }
            Write-Host "  Contenu: $contenuCourt"
            Write-Host "  Date: $($_.dateEnvoi)"
            Write-Host "  Lue: $($_.estLue)"
            Write-Host ""
        }
    } else {
        Write-Host "Aucune notification trouvée"
    }
} catch {
    Write-Host "Erreur lors de la récupération des notifications: $($_.Exception.Message)"
    Write-Host "Détails: $($_.ErrorDetails.Message)"
}
