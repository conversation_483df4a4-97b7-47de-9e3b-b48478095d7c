import { AvisDto } from "./AvisDto";
import { FormeDto } from "./FormeDto";
import { FournisseurDto } from "./FournisseurDto";
import { ImageProduitDto } from "./ImageProduitDto";
import { MarqueDto } from "./MarqueDto";
import { PromotionDto } from "./PromotionDto";
import { SousCategorieDto } from "./SousCategorieDto";
import { TauxTVADto } from "./TauxTVADto";

export interface ProduitDto {
  id: number;
  referenceOriginal: string;
  referenceFournisseur?: string;
  codeABarre: string;
  nom: string;
  description?: string;

  prixAchatHT: number;
  prixVenteHT: number;
  prixVenteTTC: number;
  prixApresRemises: number;
  prixApresRemisesOutlet: number;
  prixApresAutresPromotions: number;
  pourcentageRemiseTotale?: number;

  tauxTVAId: number;
  tauxTVA?: TauxTVADto;

  stock: number;
  noteMoyenne: number;
  nombreAvis: number;
  dateAjout: Date;

  estNouveau: boolean;

  sousCategorieId: number;
  sousCategorie?: SousCategorieDto;
  marqueId: number;
  marque?: MarqueDto;
  formeId: number;
  forme?: FormeDto;
  fournisseurId: number;
  fournisseur?: FournisseurDto;

  avis?: AvisDto[];
  images: ImageProduitDto[];
  imagePrincipaleUrl?: string;

  promotions: PromotionDto[];
}
