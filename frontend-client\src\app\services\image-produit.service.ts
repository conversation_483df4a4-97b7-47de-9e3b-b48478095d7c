import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ImageProduitDto } from '../models/ImageProduitDto';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ImageProduitService {
  private readonly baseUrl = `${environment.apiUrl}/produits`;

  constructor(private http: HttpClient) {}

  getAll(produitId: number): Observable<ImageProduitDto[]> {
    return this.http.get<ImageProduitDto[]>(`${this.baseUrl}/${produitId}/imagesproduit`);
  }

  getById(produitId: number, imageId: number): Observable<ImageProduitDto> {
    return this.http.get<ImageProduitDto>(`${this.baseUrl}/${produitId}/imagesproduit/${imageId}`);
  }

  create(produitId: number, dto: ImageProduitDto): Observable<ImageProduitDto> {
    return this.http.post<ImageProduitDto>(`${this.baseUrl}/${produitId}/imagesproduit`, dto);
  }

  update(produitId: number, imageId: number, dto: ImageProduitDto): Observable<void> {
    return this.http.put<void>(`${this.baseUrl}/${produitId}/imagesproduit/${imageId}`, dto);
  }

  delete(produitId: number, imageId: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${produitId}/imagesproduit/${imageId}`);
  }

  reorder(produitId: number, newOrder: number[]): Observable<void> {
    return this.http.patch<void>(`${this.baseUrl}/${produitId}/imagesproduit/reorder`, newOrder);
  }
}
