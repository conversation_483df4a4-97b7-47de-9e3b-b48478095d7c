import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
@Injectable({
  providedIn: 'root',
})

export class ThemeService {
  private isDarkModeSubject = new BehaviorSubject<boolean>(false);
  isDarkMode$ = this.isDarkModeSubject.asObservable();
  constructor() {
    const savedTheme = localStorage.getItem('theme');
    this.isDarkModeSubject.next(savedTheme === 'dark');
    this.applyTheme();
  }

  toggleTheme(): void {
    const newMode = !this.isDarkModeSubject.value;
    this.isDarkModeSubject.next(newMode);
    this.applyTheme();
    localStorage.setItem('theme', newMode ? 'dark' : 'light');
  }

  private applyTheme(): void {
    const theme = this.isDarkModeSubject.value ? 'dark' : 'light';
    document.documentElement.setAttribute('data-theme', theme);

}}
