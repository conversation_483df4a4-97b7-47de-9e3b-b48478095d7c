﻿using AutoMapper;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Models.Entity;

namespace WebApiPfe.Services.Profile
{
    public class MappingProfile : AutoMapper.Profile
    {
        private readonly string _backendBaseUrl = "https://localhost:7264";

        public MappingProfile()
        {
            // 1. Mappings Catégories/Formes/Marques
            ConfigureCategoryMappings();

            // 2. Mappings Produits
            ConfigureProductMappings();

            // 3. Mappings Utilisateurs
            ConfigureUserMappings();

            // 4. Mappings Commandes et Fournisseurs
            ConfigureOrderMappings();

            // 5. Mappings Paniers et Favoris
            ConfigureCartMappings();

            // 6. Mappings Adresses
            ConfigureAddressMappings();

            // 7. Mappings Livraisons
            ConfigureDeliveryMappings();

            // 8. Mappings Notifications
            ConfigureNotificationMappings();

            // 9. Mappings Avis
            ConfigureReviewMappings();

            // 10. Mappings Remboursements
            ConfigureRefundMappings();

            // 11. Mappings Statistiques et Admin
            ConfigureAdminMappings();
        }

        private void ConfigureCategoryMappings()
        {
            CreateMap<SousCategorie, SousCategorieDto>();
            CreateMap<CreateFormeDto, Forme>();
            CreateMap<Forme, FormeDto>();
            CreateMap<Marque, MarqueDto>();
            CreateMap<TauxTVA, TauxTVADto>();
            CreateMap<CreateCategorieDto, Categorie>();
            CreateMap<UpdateCategorieDto, Categorie>();
            CreateMap<Categorie, CategorieDto>();
            CreateMap<Categorie, CategorieGestionDto>();
            CreateMap<SousCategorie, SousCategorieGestionDto>();
        }

        private void ConfigureProductMappings()
        {
            CreateMap<Produit, ProduitDto>()
                .ForMember(dest => dest.Images, opt => opt.MapFrom(src => src.Images))
                .ForMember(dest => dest.Avis, opt => opt.MapFrom(src => src.Avis))
                .ForMember(dest => dest.NoteMoyenne, opt => opt.MapFrom(src => src.NoteMoyenne))
                .ForMember(dest => dest.NombreAvis, opt => opt.MapFrom(src => src.NombreAvis))
               .ForMember(dest => dest.Promotions,
                     opt => opt.MapFrom(src => src.PromotionsApplicables))
               .ForMember(dest => dest.Forme, opt => opt.MapFrom(src => src.Forme))
                .ForMember(dest => dest.SousCategorie, opt => opt.MapFrom(src => src.SousCategorie))
                .ForMember(dest => dest.Fournisseur, opt => opt.MapFrom(src => src.Fournisseur))
                .ForMember(dest => dest.Marque, opt => opt.MapFrom(src => src.Marque)); 

            CreateMap<ProduitCreateDto, Produit>()
                .ForMember(dest => dest.Images, opt => opt.Ignore());
            CreateMap<ProduitUpdateDto, Produit>();
            CreateMap<ProduitStockUpdateDto, Produit>();
            CreateMap<ImagesProduit, ImageProduitDto>();
            CreateMap<ImageProduitCreateDto, ImagesProduit>();
            CreateMap<ImageProduitUpdateDto, ImagesProduit>()
                .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));
        }

        private void ConfigureUserMappings()
        {
            CreateMap<UtilisateurCreateDto, Utilisateur>();
            CreateMap<UtilisateurCreateDto, Fournisseur>();
            CreateMap<FournisseurCreateDto, Fournisseur>()
                .IncludeBase<UtilisateurCreateDto, Utilisateur>();
            CreateMap<UtilisateurCreateDto, Client>();
            CreateMap<ClientCreateDto, Client>()
                .IncludeBase<UtilisateurCreateDto, Utilisateur>();
            CreateMap<Utilisateur, UtilisateurBaseDto>();
            CreateMap<AdminReadDto, Admin>();
            CreateMap<Utilisateur, UtilisateurGestionDto>();
        }

        private void ConfigureOrderMappings()
        {
            CreateMap<Commande, CommandeDto>();
            CreateMap<CreateCommandeFournisseurDto, CommandeFournisseur>();
            CreateMap<CommandeFournisseur, CommandeFournisseurDto>();
            CreateMap<CreateLigneCommandeFournisseurDto, LigneCommandeFournisseur>();
            CreateMap<LigneCommandeFournisseur, LigneCommandeFournisseurDto>();
            CreateMap<Commande, CommandeGestionDto>();
            CreateMap<Commande, CommandeDto>()
                .ForMember(dest => dest.MontantTotal,
                    opt => opt.MapFrom(src => src.DetailsCommandes.Sum(d => d.Quantite * d.PrixUnitaireTTC)));
        }

        private void ConfigureCartMappings()
        {
            CreateMap<Panier, PanierDto>();
            CreateMap<ItemPanier, ItemPanierDto>();
            CreateMap<AddItemPanierDto, ItemPanier>();
            CreateMap<UpdateItemPanierDto, ItemPanier>();
            CreateMap<CreatePanierDto, Panier>();
            CreateMap<UpdatePanierDto, Panier>();
            CreateMap<FavoriCreateDto, Favori>();
            CreateMap<Favori, FavoriResponseDto>();
        }

        private void ConfigureAddressMappings()
        {
            CreateMap<Adresse, AdresseDto>().ReverseMap();
            CreateMap<AdresseCreateDto, Adresse>()
                .ForMember(dest => dest.UtilisateurId, opt => opt.MapFrom(src => src.EntityId));
            CreateMap<AdresseUpdateDto, Adresse>()
                .ForAllMembers(opts => opts.Condition((src, dest, srcMember) => srcMember != null));
        }

        private void ConfigureDeliveryMappings()
        {
            CreateMap<Livraison, LivraisonDto>();
            CreateMap<CreateLivraisonDto, Livraison>();
            CreateMap<UpdateLivraisonDto, Livraison>();
        }

        private void ConfigureNotificationMappings()
        {
            CreateMap<Notification, NotificationDto>();
            CreateMap<CreateNotificationDto, Notification>();
            CreateMap<UpdateNotificationDto, Notification>();
        }

        private void ConfigureReviewMappings()
        {
            CreateMap<Avis, AvisDto>();
            CreateMap<AvisCreateDto, Avis>();
            CreateMap<AvisUpdateDto, Avis>();
        }

        private void ConfigureRefundMappings()
        {
            CreateMap<Remboursement, RemboursementDto>();
            CreateMap<CreateRemboursementDto, Remboursement>();
            CreateMap<UpdateRemboursementDto, Remboursement>();
        }

        private void ConfigureAdminMappings()
        {
            CreateMap<DetailsCommande, DetailsCommandeDto>();
            CreateMap<CreateDetailsCommandeDto, DetailsCommande>();
            CreateMap<UpdateDetailsCommandeDto, DetailsCommande>();
            CreateMap<DetailsCommande, DetailsCommandeDto>();
            CreateMap<Promotion, PromotionDto>()
                .ForMember(dest => dest.EstValide, opt => opt.MapFrom(src => src.EstValide()))
                .ForMember(dest => dest.ProduitsApplicablesIds,
                    opt => opt.MapFrom(src => src.ProduitsApplicables.Select(p => p.Id).ToList()));

            CreateMap<CreatePromotionDto, Promotion>();
            CreateMap<PromotionGestionDto, Promotion>();
            CreateMap<Promotion, PromotionGestionDto>();

            // Mapping pour PromotionUtilisee
            CreateMap<PromotionUtilisee, PromotionUtiliseeDto>();

            // Mapping pour la mise à jour du profil client
            CreateMap<ClientProfileUpdateDto, Client>();

            CreateMap<Client, ClientDto>()
                .IncludeBase<Utilisateur, UtilisateurBaseDto>()
                .ForMember(dest => dest.AdresseLivraison,
                    opt => opt.MapFrom(src => src.Adresses.FirstOrDefault(a => a.EstPrincipale)))
                .ForMember(dest => dest.Adresses,
                    opt => opt.MapFrom(src => src.Adresses))
                .ForMember(dest => dest.Commandes,
                    opt => opt.MapFrom(src => src.Commandes))
                .ForMember(dest => dest.Avis,
                    opt => opt.MapFrom(src => src.Avis))
                .ForMember(dest => dest.Favoris,
                    opt => opt.MapFrom(src => src.Favoris))
                .ForMember(dest => dest.Panier,
                    opt => opt.MapFrom(src => src.Panier))
                .ForMember(dest => dest.NombreCommandes,
                    opt => opt.MapFrom(src => src.Commandes != null ? src.Commandes.Count : 0));

            CreateMap<Fournisseur, FournisseurDto>()
                .IncludeBase<Utilisateur, UtilisateurBaseDto>()
                .ForMember(dest => dest.LogoFile, opt =>
                    opt.MapFrom(src =>
                        string.IsNullOrEmpty(src.LogoFile) ? null :
                        $"{_backendBaseUrl}{src.LogoFile}"))
                .ForMember(dest => dest.Adresses, opt =>
                    opt.MapFrom(src => src.Adresses));

            // Mappings pour la validation des fournisseurs
            CreateMap<Fournisseur, FournisseurValidationDto>()
                .ForMember(dest => dest.DateInscription, opt => opt.MapFrom(src => src.DateInscription))
                .ForMember(dest => dest.Adresses, opt => opt.MapFrom(src => src.Adresses));
        }
    }
}
