import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ProduitService } from 'src/app/services/produit.service';
import { CarouselModule } from 'primeng/carousel';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { ProduitCard } from 'src/app/models/ProduitCard';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Router } from '@angular/router';
import { FavoritesService } from 'src/app/services/favorites.service';
import { CartService } from 'src/app/services/cart.service';
import { AuthService } from 'src/app/auth/auth.service';
import { ImageUrlService } from 'src/app/services/image-url.service';

@Component({
  selector: 'app-nouveaux-arrivages',
  standalone: true,
  imports: [
    CommonModule,
    CarouselModule,
    ButtonModule,
    TagModule,
    MatIconModule,
    MatProgressSpinnerModule,
  ],
  providers: [ProduitService],
  templateUrl: './nouveaux-arrivages.component.html',
  styleUrl: './nouveaux-arrivages.component.scss',
})
export class NouveauxArrivagesComponent implements OnInit {
  nouveauxArrivages: ProduitCard[] = [];
  loading = true;
  error: string | null = null;
  numVisible: number = 5; // 5 produits par défaut sur desktop
  responsiveOptions = [
    {
      breakpoint: '1200px',
      numVisible: 4,
      numScroll: 1,
    },
    {
      breakpoint: '992px',
      numVisible: 3,
      numScroll: 1,
    },
    {
      breakpoint: '768px',
      numVisible: 2,
      numScroll: 1,
    },
    {
      breakpoint: '576px',
      numVisible: 1,
      numScroll: 1,
    },
  ];

  constructor(
    private produitService: ProduitService,
    private router: Router,
    private cartService: CartService,
    private authService: AuthService,
    private favoritesService: FavoritesService,
    public imageUrlService: ImageUrlService
  ) {}

  ngOnInit(): void {
    this.loadNouveauxArrivages();
  }

  getMainImage(produit: ProduitCard): string {
    const main = produit.images?.find((img) => img.isMain);
    const imageUrl = main ? main.imageUrl : produit.imageUrl;
    return this.imageUrlService.getProduitImageUrl(imageUrl);
  }
  goToProductDetails(
    productId: number,
    queryParams?: Record<string, any>
  ): void {
    if (!productId || productId <= 0) {
      console.error('ID produit invalide:', productId);
      return;
    }
    console.log(`Navigation vers le produit ${productId}`, {
      params: queryParams,
      timestamp: new Date().toISOString(),
    });
    this.router
      .navigate(['/products', productId], { queryParams })
      .then((success) => {
        if (!success) {
          console.error(
            'Échec de navigation - Route probablement non configurée'
          );
        }
      })
      .catch((err) => console.error('Erreur de navigation:', err));
  }
  loadNouveauxArrivages(): void {
    this.loading = true;
    this.error = null;

    this.produitService.getNouveauxArrivages().subscribe({
      next: (produits) => {
        this.nouveauxArrivages = produits;
        console.log('✅ Nouveaux arrivages chargés:', produits);
        console.log('📊 Données avis pour le premier produit:', {
          noteMoyenne: produits[0]?.noteMoyenne,
          nombreAvis: produits[0]?.nombreAvis
        });
        this.loading = false;
      },
      error: (err) => {
        console.error('❌ Erreur nouveaux arrivages:', err);
        this.error = 'Impossible de charger les nouveaux arrivages';
        this.loading = false;
      },
    });
  }
  getPrixFinal(produit: ProduitCard): string {
    return produit.prixFinalTTC?.toFixed(2) ?? '0.00';
  }

  estNouvelleArrivee(produit: ProduitCard): boolean {
    if (!produit.dateAjout) return false;

    const dateAjout = new Date(produit.dateAjout);
    const aujourdHui = new Date();
    const differenceJours =
      (aujourdHui.getTime() - dateAjout.getTime()) / (1000 * 3600 * 24);

    return differenceJours <= 30;
  }
  ajouterAuPanier(produit: ProduitCard) {
    const clientId = this.getClientId();
    if (!clientId) {
      alert('Veuillez vous connecter pour ajouter au panier.');
      return;
    }

    const item = { produitId: produit.id, quantite: 1 };

    this.cartService.addItemByClientId(clientId, item).subscribe({
      next: (result) => {
        if (result.quantite > 1) {
          alert(`Quantité mise à jour ! Vous avez maintenant ${result.quantite} de ce produit dans votre panier.`);
        } else {
          alert('Produit ajouté au panier !');
        }
      },
      error: (err) => {
        console.error('Erreur lors de l\'ajout au panier:', err);
        let errorMessage = 'Erreur lors de l\'ajout au panier';
        if (err.error?.message || typeof err.error === 'string') {
          errorMessage += ': ' + (err.error.message || err.error);
        }
        alert(errorMessage);
      }
    });
  }

  toggleFavori(produit: ProduitCard) {
    const clientId = this.getClientId();
    if (!clientId) {
      alert('Veuillez vous connecter pour gérer vos favoris.');
      return;
    }
    this.favoritesService.verifierFavori(clientId, produit.id).subscribe({
      next: (isFav) => {
        if (isFav) {
          this.favoritesService
            .supprimerFavoriParProduitId(clientId, produit.id)
            .subscribe(() => {
              alert('Produit retiré des favoris');
            });
        } else {
          this.favoritesService
            .ajouterFavori(clientId, produit.id)
            .subscribe(() => {
              alert('Produit ajouté aux favoris');
            });
        }
      },
      error: (err) => console.error(err),
    });
  }
  private getClientId(): number | null {
    const user = this.authService.getCurrentUser();
    return user ? user.id : null;
  }
}
