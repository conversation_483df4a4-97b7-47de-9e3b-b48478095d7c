import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { catchError, finalize, forkJoin, of } from 'rxjs';
import { MarqueDto } from 'src/app/models/MarqueDto';
import { ProduitCard } from 'src/app/models/ProduitCard';
import { ProductListComponent } from 'src/app/pages/product-list/product-list.component';
import { MarqueService } from 'src/app/services/marque.service';
import { ProduitService } from 'src/app/services/produit.service';

@Component({
  selector: 'app-products-by-marque',
  standalone: true,
  imports: [ProductListComponent],
  providers:[ProduitService, MarqueService],
  template: `
    <div class="nom-header">
      <h1>{{ marque?.name}}</h1>
    </div>
    <app-product-list [produits]="produits"></app-product-list>
  `,
  styleUrl: './products-by-marque.component.scss',
})
export class ProductsByMarqueComponent implements OnInit {
  marqueId!: number;
  produits: ProduitCard[] = [];
  marque?: MarqueDto;
  loading = true;
  error: string | null = null;
  constructor(
    private route: ActivatedRoute,
    private produitService: ProduitService,
    private marqueService: MarqueService
  ) {}

  ngOnInit() {
    this.route.params.subscribe((params) => {
      this.marqueId = +params['id'];
      this.chargerDonnees();
    });
  }
  chargerDonnees(): void {
    this.loading = true;
    this.error = null;
    
    forkJoin([
      this.produitService.getByMarque(this.marqueId).pipe(
        catchError(err => {
          console.error('Erreur produits:', err);
          return of([] as ProduitCard[]);
        })
      ),
      this.marqueService.getById(this.marqueId).pipe(
        catchError(err => {
          console.error('Erreur marque:', err);
          return of({
            id: this.marqueId,
            name: 'Marque inconnue',
            logo: ''
          } as MarqueDto);
        })
      )
    ]).pipe(
      finalize(() => this.loading = false)
    ).subscribe({
      next: ([produits, marque]) => {
        this.produits = produits;
        this.marque = marque;
      },
      error: (err) => {
        console.error(err);
        this.error = 'Erreur lors du chargement des données';
      }
    });
  }
}
