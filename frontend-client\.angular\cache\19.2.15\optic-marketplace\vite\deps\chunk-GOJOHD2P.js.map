{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/date-formats-K6TQue-Y.mjs"], "sourcesContent": ["import { InjectionToken, inject, LOCALE_ID } from '@angular/core';\nimport { Subject } from 'rxjs';\n\n/** InjectionToken for datepicker that can be used to override default locale code. */\nconst MAT_DATE_LOCALE = new InjectionToken('MAT_DATE_LOCALE', {\n  providedIn: 'root',\n  factory: MAT_DATE_LOCALE_FACTORY\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_DATE_LOCALE_FACTORY() {\n  return inject(LOCALE_ID);\n}\nconst NOT_IMPLEMENTED = 'Method not implemented';\n/** Adapts type `D` to be usable as a date by cdk-based components that work with dates. */\nclass DateAdapter {\n  /** The locale to use for all dates. */\n  locale;\n  _localeChanges = new Subject();\n  /** A stream that emits when the locale changes. */\n  localeChanges = this._localeChanges;\n  /**\n   * Sets the time of one date to the time of another.\n   * @param target Date whose time will be set.\n   * @param hours New hours to set on the date object.\n   * @param minutes New minutes to set on the date object.\n   * @param seconds New seconds to set on the date object.\n   */\n  setTime(target, hours, minutes, seconds) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Gets the hours component of the given date.\n   * @param date The date to extract the hours from.\n   */\n  getHours(date) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Gets the minutes component of the given date.\n   * @param date The date to extract the minutes from.\n   */\n  getMinutes(date) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Gets the seconds component of the given date.\n   * @param date The date to extract the seconds from.\n   */\n  getSeconds(date) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Parses a date with a specific time from a user-provided value.\n   * @param value The value to parse.\n   * @param parseFormat The expected format of the value being parsed\n   *     (type is implementation-dependent).\n   */\n  parseTime(value, parseFormat) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Adds an amount of seconds to the specified date.\n   * @param date Date to which to add the seconds.\n   * @param amount Amount of seconds to add to the date.\n   */\n  addSeconds(date, amount) {\n    throw new Error(NOT_IMPLEMENTED);\n  }\n  /**\n   * Given a potential date object, returns that same date object if it is\n   * a valid date, or `null` if it's not a valid date.\n   * @param obj The object to check.\n   * @returns A date or `null`.\n   */\n  getValidDateOrNull(obj) {\n    return this.isDateInstance(obj) && this.isValid(obj) ? obj : null;\n  }\n  /**\n   * Attempts to deserialize a value to a valid date object. This is different from parsing in that\n   * deserialize should only accept non-ambiguous, locale-independent formats (e.g. a ISO 8601\n   * string). The default implementation does not allow any deserialization, it simply checks that\n   * the given value is already a valid date object or null. The `<mat-datepicker>` will call this\n   * method on all of its `@Input()` properties that accept dates. It is therefore possible to\n   * support passing values from your backend directly to these properties by overriding this method\n   * to also deserialize the format used by your backend.\n   * @param value The value to be deserialized into a date object.\n   * @returns The deserialized date object, either a valid date, null if the value can be\n   *     deserialized into a null date (e.g. the empty string), or an invalid date.\n   */\n  deserialize(value) {\n    if (value == null || this.isDateInstance(value) && this.isValid(value)) {\n      return value;\n    }\n    return this.invalid();\n  }\n  /**\n   * Sets the locale used for all dates.\n   * @param locale The new locale.\n   */\n  setLocale(locale) {\n    this.locale = locale;\n    this._localeChanges.next();\n  }\n  /**\n   * Compares two dates.\n   * @param first The first date to compare.\n   * @param second The second date to compare.\n   * @returns 0 if the dates are equal, a number less than 0 if the first date is earlier,\n   *     a number greater than 0 if the first date is later.\n   */\n  compareDate(first, second) {\n    return this.getYear(first) - this.getYear(second) || this.getMonth(first) - this.getMonth(second) || this.getDate(first) - this.getDate(second);\n  }\n  /**\n   * Compares the time values of two dates.\n   * @param first First date to compare.\n   * @param second Second date to compare.\n   * @returns 0 if the times are equal, a number less than 0 if the first time is earlier,\n   *     a number greater than 0 if the first time is later.\n   */\n  compareTime(first, second) {\n    return this.getHours(first) - this.getHours(second) || this.getMinutes(first) - this.getMinutes(second) || this.getSeconds(first) - this.getSeconds(second);\n  }\n  /**\n   * Checks if two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameDate(first, second) {\n    if (first && second) {\n      let firstValid = this.isValid(first);\n      let secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareDate(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n  /**\n   * Checks if the times of two dates are equal.\n   * @param first The first date to check.\n   * @param second The second date to check.\n   * @returns Whether the times of the two dates are equal.\n   *     Null dates are considered equal to other null dates.\n   */\n  sameTime(first, second) {\n    if (first && second) {\n      const firstValid = this.isValid(first);\n      const secondValid = this.isValid(second);\n      if (firstValid && secondValid) {\n        return !this.compareTime(first, second);\n      }\n      return firstValid == secondValid;\n    }\n    return first == second;\n  }\n  /**\n   * Clamp the given date between min and max dates.\n   * @param date The date to clamp.\n   * @param min The minimum value to allow. If null or omitted no min is enforced.\n   * @param max The maximum value to allow. If null or omitted no max is enforced.\n   * @returns `min` if `date` is less than `min`, `max` if date is greater than `max`,\n   *     otherwise `date`.\n   */\n  clampDate(date, min, max) {\n    if (min && this.compareDate(date, min) < 0) {\n      return min;\n    }\n    if (max && this.compareDate(date, max) > 0) {\n      return max;\n    }\n    return date;\n  }\n}\nconst MAT_DATE_FORMATS = new InjectionToken('mat-date-formats');\nexport { DateAdapter as D, MAT_DATE_LOCALE as M, MAT_DATE_FORMATS as a, MAT_DATE_LOCALE_FACTORY as b };\n"], "mappings": ";;;;;;;;;;AAIA,IAAM,kBAAkB,IAAI,eAAe,mBAAmB;AAAA,EAC5D,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAMD,SAAS,0BAA0B;AACjC,SAAO,OAAO,SAAS;AACzB;AACA,IAAM,kBAAkB;AAExB,IAAM,cAAN,MAAkB;AAAA;AAAA,EAEhB;AAAA,EACA,iBAAiB,IAAI,QAAQ;AAAA;AAAA,EAE7B,gBAAgB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrB,QAAQ,QAAQ,OAAO,SAAS,SAAS;AACvC,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,MAAM;AACb,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,MAAM;AACf,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,MAAM;AACf,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,OAAO,aAAa;AAC5B,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,MAAM,QAAQ;AACvB,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,KAAK;AACtB,WAAO,KAAK,eAAe,GAAG,KAAK,KAAK,QAAQ,GAAG,IAAI,MAAM;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,YAAY,OAAO;AACjB,QAAI,SAAS,QAAQ,KAAK,eAAe,KAAK,KAAK,KAAK,QAAQ,KAAK,GAAG;AACtE,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,QAAQ;AAChB,SAAK,SAAS;AACd,SAAK,eAAe,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,OAAO,QAAQ;AACzB,WAAO,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS,MAAM,KAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,MAAM;AAAA,EAChJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,OAAO,QAAQ;AACzB,WAAO,KAAK,SAAS,KAAK,IAAI,KAAK,SAAS,MAAM,KAAK,KAAK,WAAW,KAAK,IAAI,KAAK,WAAW,MAAM,KAAK,KAAK,WAAW,KAAK,IAAI,KAAK,WAAW,MAAM;AAAA,EAC5J;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO,QAAQ;AACtB,QAAI,SAAS,QAAQ;AACnB,UAAI,aAAa,KAAK,QAAQ,KAAK;AACnC,UAAI,cAAc,KAAK,QAAQ,MAAM;AACrC,UAAI,cAAc,aAAa;AAC7B,eAAO,CAAC,KAAK,YAAY,OAAO,MAAM;AAAA,MACxC;AACA,aAAO,cAAc;AAAA,IACvB;AACA,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,OAAO,QAAQ;AACtB,QAAI,SAAS,QAAQ;AACnB,YAAM,aAAa,KAAK,QAAQ,KAAK;AACrC,YAAM,cAAc,KAAK,QAAQ,MAAM;AACvC,UAAI,cAAc,aAAa;AAC7B,eAAO,CAAC,KAAK,YAAY,OAAO,MAAM;AAAA,MACxC;AACA,aAAO,cAAc;AAAA,IACvB;AACA,WAAO,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,MAAM,KAAK,KAAK;AACxB,QAAI,OAAO,KAAK,YAAY,MAAM,GAAG,IAAI,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,QAAI,OAAO,KAAK,YAAY,MAAM,GAAG,IAAI,GAAG;AAC1C,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,mBAAmB,IAAI,eAAe,kBAAkB;", "names": []}