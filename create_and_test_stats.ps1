# Créer un fournisseur et tester les statistiques
$baseUrl = "http://localhost:5014/api"

Write-Host "=== CRÉATION ET TEST STATISTIQUES ===" -ForegroundColor Cyan

# 1. Créer un nouveau fournisseur
Write-Host "`n1. Création d'un nouveau fournisseur..." -ForegroundColor Yellow

$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$email = "teststats$<EMAIL>"
$password = "TestStats123!"

$fournisseurData = @{
    nom = "Stats"
    prenom = "Test"
    email = $email
    password = $password
    phoneNumber = "12345678"
    raisonSociale = "Stats Test SARL"
    matriculeFiscale = "STATS$timestamp"
    rib = "12345678901234567890"
    codeBanque = "12345"
    commission = 5.0
    delaiPreparationJours = 2
    fraisLivraisonBase = 10.0
    description = "Fournisseur de test pour statistiques - $timestamp"
} | ConvertTo-Json

$headers = @{"Content-Type" = "application/json"}

$newFournisseur = Invoke-RestMethod -Uri "$baseUrl/Auth/register/fournisseur" -Method Post -Headers $headers -Body $fournisseurData
Write-Host "✅ Fournisseur créé - ID: $($newFournisseur.id)" -ForegroundColor Green
Write-Host "Email: $email" -ForegroundColor White
Write-Host "Password: $password" -ForegroundColor White

# 2. Connexion
Write-Host "`n2. Connexion..." -ForegroundColor Yellow

$loginData = @{
    email = $email
    motDePasse = $password
} | ConvertTo-Json

$loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
Write-Host "✅ Connecté - ID: $($loginResponse.utilisateur.id)" -ForegroundColor Green

$headers["Authorization"] = "Bearer $($loginResponse.token)"
$fournisseurId = $loginResponse.utilisateur.id

# 3. Test de l'endpoint statistiques
Write-Host "`n3. Test endpoint statistiques..." -ForegroundColor Yellow

$statsUrl = "$baseUrl/StatistiquesFournisseur/dashboard/$fournisseurId"
Write-Host "URL: $statsUrl" -ForegroundColor Gray

try {
    $stats = Invoke-RestMethod -Uri $statsUrl -Method Get -Headers $headers
    Write-Host "✅ Statistiques récupérées !" -ForegroundColor Green
    
    Write-Host "`nSTATISTIQUES:" -ForegroundColor Cyan
    Write-Host "- Total produits: $($stats.totalProduits)" -ForegroundColor White
    Write-Host "- Commandes actives: $($stats.commandesActives)" -ForegroundColor White
    Write-Host "- Livraisons en cours: $($stats.livraisonsEnCours)" -ForegroundColor White
    Write-Host "- CA mensuel: $([math]::Round($stats.chiffreAffaireMensuel, 2)) TND" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
}

# 4. Test évolution des ventes
Write-Host "`n4. Test évolution des ventes..." -ForegroundColor Yellow

try {
    $evolutionUrl = "$baseUrl/StatistiquesFournisseur/evolution-ventes/$fournisseurId"
    $evolution = Invoke-RestMethod -Uri $evolutionUrl -Method Get -Headers $headers
    Write-Host "✅ Évolution récupérée - $($evolution.Count) mois" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Erreur évolution: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== RÉSUMÉ ===" -ForegroundColor Cyan
Write-Host "✅ Nouveau contrôleur StatistiquesFournisseur testé" -ForegroundColor Green
Write-Host "✅ Données retournées en valeurs numériques pour formatage TND" -ForegroundColor Green
Write-Host "✅ Frontend peut maintenant afficher les statistiques en dinars" -ForegroundColor Green

Write-Host "`n=== FIN ===" -ForegroundColor Cyan
