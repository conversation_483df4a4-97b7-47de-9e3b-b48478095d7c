import { Component, OnInit, signal, computed, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { AuthService } from '../../../services/auth.service';
import { LoginRequest } from '../../../models';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;

  // Angular 19: Signals pour l'état du composant
  isLoading = signal(false);
  errorMessage = signal('');
  returnUrl = signal('');
  isFormValid = signal(false);

  // Angular 19: Computed signals pour l'état du formulaire
  canSubmit = computed(() => this.isFormValid() && !this.isLoading());
  hasError = computed(() => this.errorMessage().length > 0);

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });

    // Angular 19: Écouter les changements du formulaire pour mettre à jour les signals
    this.loginForm.statusChanges.subscribe(() => {
      this.isFormValid.set(this.loginForm.valid);
    });

    // Initialiser l'état du formulaire
    this.isFormValid.set(this.loginForm.valid);

    // Angular 19: Effect pour logger les changements d'état
    effect(() => {
      if (this.hasError()) {
        console.log('❌ Erreur de connexion:', this.errorMessage());
      }
      if (this.isLoading()) {
        console.log('⏳ Connexion en cours...');
      }
    });
  }

  ngOnInit(): void {
    // Rediriger si déjà connecté
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
      return;
    }

    // Angular 19: Récupérer l'URL de retour avec signal
    this.returnUrl.set(this.route.snapshot.queryParams['returnUrl'] || '/dashboard');
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      // Angular 19: Mise à jour des signals
      this.isLoading.set(true);
      this.errorMessage.set('');

      const credentials: LoginRequest = {
        email: this.loginForm.value.email!,
        motDePasse: this.loginForm.value.password!  // Mapper password vers motDePasse
      };

      this.authService.login(credentials).subscribe({
        next: (response) => {
          this.isLoading.set(false);

          // Rediriger selon le rôle de l'utilisateur
          const userRole = response.utilisateur.role;

          if (userRole === 'Fournisseur') {
            // Rediriger vers le dashboard fournisseur
            this.router.navigate(['/dashboard']);
          } else if (userRole === 'Admin') {
            // Rediriger vers le dashboard admin
            this.router.navigate(['/admin']);
          } else {
            this.errorMessage.set('Accès réservé aux fournisseurs et administrateurs uniquement.');
            this.authService.logout();
          }
        },
        error: (error) => {
          this.isLoading.set(false);

          // Gérer les erreurs spécifiques de validation
          if (error.message === 'VALIDATION_PENDING') {
            this.errorMessage.set('⏳ Votre compte fournisseur est en attente de validation par l\'administration. Vous recevrez une notification une fois votre compte validé.');
          } else if (error.message === 'VALIDATION_REJECTED') {
            this.errorMessage.set('❌ Votre demande d\'inscription en tant que fournisseur a été rejetée par l\'administration.');
          } else if (error.message === 'ACCOUNT_SUSPENDED') {
            this.errorMessage.set('⚠️ Votre compte fournisseur a été suspendu par l\'administration.');
          } else {
            this.errorMessage.set(error.error?.message || 'Erreur de connexion. Vérifiez vos identifiants.');
          }
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Remplir le formulaire avec des comptes de test
   */
  fillTestAccount(type: 'fournisseur' | 'fournisseur2'): void {
    if (type === 'fournisseur') {
      this.loginForm.patchValue({
        email: '<EMAIL>',
        password: 'Fournisseur123!'
      });
    } else if (type === 'fournisseur2') {
      this.loginForm.patchValue({
        email: '<EMAIL>',
        password: 'password123'
      });
    }
  }

  /**
   * Tester la connexion API
   */
  testApiConnection(): void {
    console.log('🔍 Test de la connexion API...');

    // Tester plusieurs URLs
    const testUrls = [
      'http://localhost:7264/api/Auth/login',
      'https://localhost:7264/api/Auth/login',
      'http://localhost:5000/api/Auth/login',
      'https://localhost:5001/api/Auth/login',
      'http://localhost:7265/api/Auth/login'
    ];

    console.log('🔍 Test de connectivité sur plusieurs ports...');

    testUrls.forEach((url, index) => {
      fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: 'test', motDePasse: 'test' })
      })
      .then(response => {
        console.log(`✅ ${url} - Status: ${response.status} (${response.statusText})`);
        if (response.status !== 0) {
          alert(`✅ Backend trouvé sur: ${url}\nStatus: ${response.status}`);
        }
      })
      .catch(error => {
        console.log(`❌ ${url} - Erreur: ${error.message}`);
        if (index === testUrls.length - 1) {
          alert('❌ Aucun backend accessible sur les ports testés.\n\nVérifiez que votre backend ASP.NET Core est démarré.');
        }
      });
    });

    console.log('⚙️ Configuration: Backend API test');
  }

  // Getters pour faciliter l'accès aux contrôles dans le template
  get email() { return this.loginForm.get('email'); }
  get password() { return this.loginForm.get('password'); }
}
