# Debug simple des produits
$baseUrl = "http://localhost:5014/api"

Write-Host "=== DEBUG PRODUITS SIMPLE ===" -ForegroundColor Red

# 1. Vérifier produits
Write-Host "`nVérification produits..." -ForegroundColor Yellow
try {
    $produits = Invoke-RestMethod -Uri "$baseUrl/Produits" -Method Get
    Write-Host "✅ $($produits.Count) produits trouvés" -ForegroundColor Green
    
    if ($produits.Count -gt 0) {
        Write-Host "Premiers produits:" -ForegroundColor White
        $produits | Select-Object -First 5 | ForEach-Object {
            Write-Host "  - ID: $($_.id) | $($_.nom) | Fournisseur: $($_.fournisseurId) | Prix: $($_.prixVenteHT) TND" -ForegroundColor Gray
        }
        
        Write-Host "Produits par fournisseur:" -ForegroundColor White
        $produits | Group-Object fournisseurId | ForEach-Object {
            Write-Host "  - Fournisseur $($_.Name): $($_.Count) produits" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ AUCUN PRODUIT !" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Erreur produits: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Vérifier fournisseurs
Write-Host "`nVérification fournisseurs..." -ForegroundColor Yellow
try {
    $fournisseurs = Invoke-RestMethod -Uri "$baseUrl/Fournisseurs" -Method Get
    Write-Host "✅ $($fournisseurs.Count) fournisseurs trouvés" -ForegroundColor Green
    
    Write-Host "Premiers fournisseurs:" -ForegroundColor White
    $fournisseurs | Select-Object -First 3 | ForEach-Object {
        Write-Host "  - ID: $($_.id) | $($_.email) | $($_.nom) $($_.prenom)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ Erreur fournisseurs: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Test produits d'un fournisseur spécifique
Write-Host "`nTest produits fournisseur 11..." -ForegroundColor Yellow
try {
    $produitsFournisseur = Invoke-RestMethod -Uri "$baseUrl/Fournisseurs/11/produits" -Method Get
    Write-Host "✅ $($produitsFournisseur.Count) produits pour fournisseur 11" -ForegroundColor Green
    
    if ($produitsFournisseur.Count -gt 0) {
        foreach ($p in $produitsFournisseur) {
            Write-Host "  - $($p.nom): $($p.prixVenteHT) TND" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. Vérifier promotions
Write-Host "`nVérification promotions..." -ForegroundColor Yellow
try {
    $promotions = Invoke-RestMethod -Uri "$baseUrl/Promotions" -Method Get
    Write-Host "✅ $($promotions.Count) promotions trouvées" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur promotions: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== RÉSUMÉ ===" -ForegroundColor Red
Write-Host "Vérifiez les résultats ci-dessus pour identifier les problèmes" -ForegroundColor White
