import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { FormeDto } from '../models/FormeDto';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class FormeService {
  private baseUrl = `${environment.apiUrl}/Formes`; 

  constructor(private http: HttpClient) {}

  getAll(): Observable<FormeDto[]> {
    return this.http.get<FormeDto[]>(this.baseUrl);
  }

  getById(id: number): Observable<FormeDto> {
    return this.http.get<FormeDto>(`${this.baseUrl}/${id}`);
  }

  getByCategorie(categorieId: number): Observable<FormeDto[]> {
    return this.http.get<FormeDto[]>(`${this.baseUrl}/by-categorie/${categorieId}`);
  }

  getForDropdown(): Observable<{ [key: number]: string }> {
    return this.http.get<{ [key: number]: string }>(`${this.baseUrl}/dropdown`);
  }

  create(forme: FormeDto): Observable<FormeDto> {
    return this.http.post<FormeDto>(this.baseUrl, forme);
  }

  update(id: number, forme: FormeDto): Observable<void> {
    return this.http.put<void>(`${this.baseUrl}/${id}`, forme);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${id}`);
  }
}
