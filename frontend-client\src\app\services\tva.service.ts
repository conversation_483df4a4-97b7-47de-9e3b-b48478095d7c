import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { TauxTVADto } from '../models/TauxTVADto';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TvaService {

  private readonly apiUrl = `${environment.apiUrl}/tva`;

  constructor(private http: HttpClient) {}

  getAll(inclureInactifs: boolean = false): Observable<TauxTVADto[]> {
    const params = new HttpParams().set('inclureInactifs', inclureInactifs);
    return this.http.get<TauxTVADto[]>(this.apiUrl, { params });
  }

  getById(id: number): Observable<TauxTVADto> {
    return this.http.get<TauxTVADto>(`${this.apiUrl}/${id}`);
  }

  getTauxActuel(): Observable<TauxTVADto> {
    return this.http.get<TauxTVADto>(`${this.apiUrl}/actuel`);
  }

  getDropdown(): Observable<{ [key: number]: string }> {
    return this.http.get<{ [key: number]: string }>(`${this.apiUrl}/dropdown`);
  }

  create(taux: TauxTVADto): Observable<TauxTVADto> {
    return this.http.post<TauxTVADto>(this.apiUrl, taux);
  }

  update(id: number, taux: TauxTVADto): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, taux);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  calculerTTC(tauxId: number, prixHT: number): Observable<number> {
    const params = new HttpParams()
      .set('tauxId', tauxId)
      .set('prixHT', prixHT);
    return this.http.post<number>(`${this.apiUrl}/calculer-ttc`, null, { params });
  }

  calculerHT(tauxId: number, prixTTC: number): Observable<number> {
    const params = new HttpParams()
      .set('tauxId', tauxId)
      .set('prixTTC', prixTTC);
    return this.http.post<number>(`${this.apiUrl}/calculer-ht`, null, { params });
  }
}
