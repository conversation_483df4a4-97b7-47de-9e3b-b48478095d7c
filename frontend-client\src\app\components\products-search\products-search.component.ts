import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { catchError, finalize, of } from 'rxjs';
import { ProduitCard } from 'src/app/models/ProduitCard';
import { ProductListComponent } from 'src/app/pages/product-list/product-list.component';
import { ProduitService } from 'src/app/services/produit.service';

@Component({
  selector: 'app-products-search',
  standalone: true,
  imports: [ProductListComponent],
  providers:[ProduitService],
  template: `
    <div class="nom-header">
      <h2>Résultats pour "{{ query }}"</h2>
    </div>
    <app-product-list [produits]="produits"></app-product-list>
  `,
  styleUrl: './products-search.component.scss',
})
export class ProductsSearchComponent implements OnInit {
  query: string = '';
  produits: ProduitCard[] = [];
  loading = false;
  error: string | null = null;
  constructor(
    private route: ActivatedRoute,
    private produitService: ProduitService
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe((params) => {
      this.query = params['q'] || '';
      if (this.query.trim()) {
        this.rechercherProduits();
      } else {
        this.produits = [];
      }
    });
  }
  rechercherProduits(): void {
    if (!this.query.trim()) {
      this.produits = [];
      return;
    }

    this.loading = true;
    this.error = null;

    this.produitService.search(this.query).pipe(
      catchError(err => {
        console.error('Erreur de recherche:', err);
        this.error = 'Une erreur est survenue lors de la recherche';
        return of([]);
      }),
      finalize(() => this.loading = false)
    ).subscribe({
      next: (produits) => {
        this.produits = produits;
      },
      error: (err) => {
        console.error(err);
        this.error = 'Impossible de charger les résultats';
      }
    });
  }
}
