# Test final dashboard
$baseUrl = "http://localhost:5014/api"

Write-Host "=== TEST FINAL DASHBOARD ===" -ForegroundColor Cyan

# Test avec fournisseur existant
$fournisseurId = 11
$email = "<EMAIL>"

# Essayer différents mots de passe
$passwords = @("123456", "password", "admin", "test123", "visionplus", "123")

Write-Host "Test connexion fournisseur ID: $fournisseurId" -ForegroundColor Yellow

foreach ($pwd in $passwords) {
    Write-Host "Essai mot de passe: $pwd" -ForegroundColor Gray
    
    $loginData = @{
        email = $email
        motDePasse = $pwd
    } | ConvertTo-Json
    
    $headers = @{"Content-Type" = "application/json"}
    
    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
        Write-Host "✅ CONNEXION REUSSIE avec: $pwd" -ForegroundColor Green
        
        $headers["Authorization"] = "Bearer $($loginResponse.token)"
        $fournisseurId = $loginResponse.utilisateur.id
        
        # Test statistiques
        Write-Host "Test statistiques..." -ForegroundColor Yellow
        
        try {
            $stats = Invoke-RestMethod -Uri "$baseUrl/StatistiquesFournisseur/dashboard/$fournisseurId" -Method Get -Headers $headers
            
            Write-Host "✅ STATISTIQUES OK !" -ForegroundColor Green
            Write-Host "Produits: $($stats.totalProduits)" -ForegroundColor White
            Write-Host "CA mensuel: $($stats.chiffreAffaireMensuel) TND" -ForegroundColor White
            
            if ($stats.topProduits) {
                Write-Host "Top produits: $($stats.topProduits.Count)" -ForegroundColor White
            }
            
            break
            
        } catch {
            Write-Host "❌ Erreur stats: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        break
        
    } catch {
        # Continue avec le prochain mot de passe
    }
}

Write-Host "=== FIN TEST ===" -ForegroundColor Cyan
