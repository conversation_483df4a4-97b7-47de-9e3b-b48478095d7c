<h2>Votre Profil</h2>
<div class="profile-card">
  <div class="profile-item">
    <i class="fas fa-user"></i>
    <span><strong>Nom:</strong> {{ user.nom }}</span>
  </div>
  <div class="profile-item">
    <i class="fas fa-user-tag"></i>
    <span><strong>Prénom:</strong> {{ user.prenom }}</span>
  </div>
  <div class="profile-item">
    <i class="fas fa-envelope"></i>
    <span><strong>Email:</strong> {{ user.email }}</span>
  </div>
  <div class="profile-item">
    <i class="fas fa-phone"></i>
    <span><strong>Téléphone:</strong> {{ user.phoneNumber }}</span>
  </div>
  <div class="profile-item">
    <i class="fas fa-birthday-cake"></i>
    <span
      ><strong>Date de Naissance:</strong>
      {{ user.dateNaissance | date : "dd/MM/yyyy" }}</span
    >
  </div>
  <div class="profile-item">
    <i class="fas fa-home"></i>
    <span><strong>Adresse de Livraison:</strong>
      <span *ngIf="user.adresseLivraison?.rue; else noAddress">
        {{ user.adresseLivraison?.rue }},
        {{ user.adresseLivraison?.codePostal }}
        {{ user.adresseLivraison?.ville }}
      </span>
      <ng-template #noAddress>
        <em>Aucune adresse de livraison définie</em>
      </ng-template>
    </span>
  </div>
  <div class="profile-actions">
    <button routerLink="/user/orders" class="btn-orders">Mes Commandes</button>
    <button routerLink="/user/edit-profile" class="btn-edit">
      Modifier Profil
    </button>
    <button (click)="logout()" class="btn-logout">Se Déconnecter</button>
  </div>
</div>
