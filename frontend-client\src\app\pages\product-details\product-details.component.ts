import { Component, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TagModule } from 'primeng/tag';
import { PaginatorModule } from 'primeng/paginator';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ProduitCard } from 'src/app/models/ProduitCard';
import { ProduitDto } from 'src/app/models/ProduitDto';
import { ProduitService } from 'src/app/services/produit.service';
import { CarouselModule } from 'primeng/carousel';
import { AvisService } from 'src/app/services/avis.service';
import { FormsModule } from '@angular/forms';
import { CartService } from 'src/app/services/cart.service';
import { FavoritesService } from 'src/app/services/favorites.service';
import { AuthService } from 'src/app/auth/auth.service';
import { ImageUrlService } from 'src/app/services/image-url.service';

@Component({
  selector: 'app-product-details',
  templateUrl: './product-details.component.html',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    TagModule,
    PaginatorModule,
    MatIconModule,
    MatProgressSpinnerModule,
    CarouselModule,
    FormsModule,
  ],
  styleUrls: ['./product-details.component.scss'],
})
export class ProductDetailsComponent {
  responsiveOptions = [
    {
      breakpoint: '1200px',
      numVisible: 4,
      numScroll: 4,
    },
    {
      breakpoint: '992px',
      numVisible: 3,
      numScroll: 3,
    },
    {
      breakpoint: '768px',
      numVisible: 2,
      numScroll: 2,
    },
    {
      breakpoint: '576px',
      numVisible: 2,
      numScroll: 2,
    },
  ];
  product!: ProduitCard;
  selectedImage: string = '';
  selectedImageObject?: { imageUrl: string; ordre?: number };
  isFavorite = false;
  produitsFournisseur: ProduitCard[] = [];
  produitsMarque: ProduitCard[] = [];
  produitsSimilaire: ProduitCard[] = [];
  moyenneNote = 0;
  nombreAvis = 0;
  repartitionAvis: { [key: number]: number } = {};
  nouvelleNote = 0;
  nouveauCommentaire = '';
  monAvisExistant: any = null;
  tousLesAvis: any[] = [];
  afficherModalAvis = false;
  afficherTousLesAvis = false;

  constructor(
    private cartService: CartService,
    private favoritesService: FavoritesService,
    private produitService: ProduitService,
    private avisService: AvisService,
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService,
    public imageUrlService: ImageUrlService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    const id = Number(this.route.snapshot.paramMap.get('id'));
    if (id) {
      this.produitService.getById(id).subscribe((prod) => {
        this.product = prod;
        console.log('🔍 Produit chargé:', this.product);
        console.log('🔍 Nombre d\'avis initial du produit:', this.product.nombreAvis);

        this.selectedImage = this.imageUrlService.getProduitImageUrl(
          prod.images?.[0]?.imageUrl
        );
        this.selectedImageObject = prod.images?.[0];

        // Charger les avis après que le produit soit défini
        this.chargerMoyenneAvis();
        this.chargerRepartitionAvis();
        this.verifierMonAvisExistant();
        this.verifierSiFavori(); // Vérifier si le produit est dans les favoris

        if (this.product?.fournisseur?.id) {
          this.loadProduitsFournisseur(this.product.fournisseur.id);
        }
        if (this.product?.marque?.id) {
          this.loadProduitsMarque(this.product.marque.id);
        }
        if (this.product?.sousCategorie?.id) {
          this.loadProduitsSimilaire(this.product.sousCategorie.id);
        }
      });
    }
  }

  choisirNote(note: number) {
    this.nouvelleNote = note;
  }

  envoyerAvis() {
    if (this.nouvelleNote === 0) {
      alert('Merci de choisir une note.');
      return;
    }

    // Vérifications de debug détaillées
    const token = localStorage.getItem('pfe-auth');
    const currentUser = this.authService.getCurrentUser();
    const isAuthenticated = this.authService.isAuthenticated();
    const userData = localStorage.getItem('pfe-user-context');

    console.log('=== DEBUG AUTHENTIFICATION ===');
    console.log('🔐 Token présent:', !!token);
    console.log('🎫 Token complet:', token);
    console.log('👤 Utilisateur connecté (getCurrentUser):', !!currentUser);
    console.log('✅ isAuthenticated():', isAuthenticated);
    console.log('📧 Email utilisateur:', currentUser?.email);
    console.log('📋 User data localStorage:', userData);
    console.log('🔍 AuthService isLoggedIn():', this.authService.isLoggedIn());
    console.log('===============================');

    if (!currentUser || !isAuthenticated) {
      console.log('❌ Utilisateur non authentifié - arrêt de l\'envoi');
      alert('Vous devez être connecté pour laisser un avis. Veuillez vous connecter.');
      return;
    }

    // Vérifier si l'utilisateur modifie un avis existant
    if (this.monAvisExistant) {
      const confirmation = confirm(
        `Vous avez déjà donné une note de ${this.monAvisExistant.note}/5 avec le commentaire: "${this.monAvisExistant.commentaire || 'Aucun commentaire'}"\n\nVoulez-vous modifier votre avis ?`
      );
      if (!confirmation) {
        return;
      }
    }

    console.log('📝 Envoi avis pour produit:', this.product.id);
    console.log('📝 Note:', this.nouvelleNote);
    console.log('📝 Commentaire:', this.nouveauCommentaire);

    this.avisService
      .ajouterAvis(this.product.id, {
        note: this.nouvelleNote,
        commentaire: this.nouveauCommentaire,
      })
      .subscribe({
        next: (avis) => {
          console.log('✅ Avis ajouté/modifié avec succès:', avis);
          const message = this.monAvisExistant
            ? 'Votre avis a été modifié avec succès ! ❤️'
            : 'Votre avis a été enregistré avec succès ! ❤️';
          alert(message);
          this.nouvelleNote = 0;
          this.nouveauCommentaire = '';
          this.chargerMoyenneAvis();
          this.chargerRepartitionAvis(); // Ceci va mettre à jour nombreAvis
          this.verifierMonAvisExistant(); // Recharger l'avis existant
          console.log('🔄 Après envoi avis - nombreAvis:', this.nombreAvis);
        },
        error: (err) => {
          console.error('❌ Erreur lors de l\'envoi de l\'avis:', err);
          if (err.status === 401) {
            alert('Vous devez être connecté pour laisser un avis. Veuillez vous connecter.');
          } else {
            alert("Erreur lors de l'envoi de l'avis: " + (err.error?.message || err.message));
          }
        },
      });
  }
  chargerRepartitionAvis() {
    console.log('📊 Chargement répartition avis pour produit:', this.product.id);
    this.avisService.getAvisByProduit(this.product.id).subscribe({
      next: (avis) => {
        console.log('✅ Avis récupérés:', avis);
        console.log('📊 Nombre d\'avis reçus:', avis.length);

        this.tousLesAvis = avis; // Stocker tous les avis
        this.nombreAvis = avis.length; // Mettre à jour le nombre d'avis
        this.product.nombreAvis = avis.length; // Mettre à jour aussi dans le produit

        console.log('📊 this.nombreAvis mis à jour:', this.nombreAvis);
        console.log('📊 this.product.nombreAvis mis à jour:', this.product.nombreAvis);

        // Forcer la détection des changements
        this.cdr.detectChanges();

        this.repartitionAvis = {};
        for (const a of avis) {
          const note = a.note ?? 0;
          this.repartitionAvis[note] = (this.repartitionAvis[note] || 0) + 1;
        }
        console.log('📊 Répartition calculée:', this.repartitionAvis);
      },
      error: (err: any) => {
        console.error('❌ Erreur chargement avis:', err);
      },
    });
  }

  chargerMoyenneAvis() {
    console.log('⭐ Chargement moyenne avis pour produit:', this.product.id);
    this.avisService.getMoyenne(this.product.id).subscribe({
      next: (note) => {
        console.log('✅ Moyenne récupérée:', note);
        this.moyenneNote = note;
        // Mettre à jour aussi la note du produit pour l'affichage dans rating-section
        this.product.noteMoyenne = note;
      },
      error: (err) => {
        console.error('❌ Erreur chargement moyenne:', err);
      },
    });
    this.produitService.getById(this.product.id).subscribe({
      next: (produit) => {
        console.log('✅ Nombre d\'avis récupéré:', produit.nombreAvis);
        this.nombreAvis = produit.nombreAvis || 0;
        // Mettre à jour aussi le nombre d'avis du produit pour l'affichage dans review-count
        this.product.nombreAvis = produit.nombreAvis || 0;
      },
      error: (err) => {
        console.error('❌ Erreur chargement produit pour nombre avis:', err);
      }
    });
  }
  ouvrirListeAvis() {
    this.afficherModalAvis = true;
  }

  fermerModalAvis() {
    this.afficherModalAvis = false;
  }

  toggleAffichageAvis() {
    this.afficherTousLesAvis = !this.afficherTousLesAvis;
    if (this.afficherTousLesAvis && this.tousLesAvis.length === 0) {
      this.chargerRepartitionAvis(); // Utilise la méthode existante qui charge tous les avis
    }
  }



  verifierSiFavori() {
    const clientId = this.getClientId();
    if (!clientId) {
      this.isFavorite = false;
      return;
    }

    this.favoritesService.verifierFavori(clientId, this.product.id).subscribe({
      next: (estFavori) => {
        this.isFavorite = estFavori;
        console.log('✅ Statut favori vérifié:', this.isFavorite);
      },
      error: (err) => {
        console.error('❌ Erreur vérification favoris:', err);
        this.isFavorite = false;
      }
    });
  }

  ouvrirAjoutAvis() {
    alert("TODO: ouvrir la fenêtre d'ajout d'avis");
  }

  verifierMonAvisExistant() {
    if (!this.authService.isAuthenticated()) {
      return; // Pas connecté, pas d'avis existant
    }

    this.avisService.getMonAvis(this.product.id).subscribe({
      next: (avis) => {
        this.monAvisExistant = avis;
        if (avis) {
          console.log('✅ Avis existant trouvé:', avis);
          // Pré-remplir le formulaire avec l'avis existant
          this.nouvelleNote = avis.note;
          this.nouveauCommentaire = avis.commentaire || '';
        } else {
          console.log('ℹ️ Aucun avis existant pour ce produit');
          this.monAvisExistant = null;
        }
      },
      error: (err) => {
        console.error('❌ Erreur vérification avis existant:', err);
        this.monAvisExistant = null;
      }
    });
  }
  loadProduitsFournisseur(fournisseurId: number) {
    this.produitService.getByFournisseur(fournisseurId).subscribe({
      next: (produits: ProduitCard[]) => {
        this.produitsFournisseur = produits.filter(
          (p: ProduitCard) => p.id !== this.product.id
        );
      },
      error: (err: any) => {
        console.error('Erreur chargement produits fournisseur', err);
      },
    });
  }
  loadProduitsMarque(marqueId: number) {
    this.produitService.getByMarque(marqueId).subscribe({
      next: (produits: ProduitCard[]) => {
        this.produitsMarque = produits.filter(
          (p: ProduitCard) => p.id !== this.product.id
        );
      },
      error: (err: any) => {
        console.error('Erreur chargement produits marque', err);
      },
    });
  }
  loadProduitsSimilaire(sousCategorieId: number) {
    this.produitService.getBySousCategorie(sousCategorieId).subscribe({
      next: (produits: ProduitCard[]) => {
        this.produitsSimilaire = produits.filter(
          (p: ProduitCard) => p.id !== this.product.id
        );
      },
      error: (err: any) => {
        console.error('Erreur chargement produits sous categorie', err);
      },
    });
  }
  getMainImage(produit: ProduitDto): string {
    if (produit.images?.length > 0) {
      return this.imageUrlService.getProduitImageUrl(produit.images[0].imageUrl);
    }
    return this.imageUrlService.getPlaceholderUrl();
  }

  goToProductDetails(produitId: number) {
    this.router.navigate(['/product', produitId]);
  }

  get fournisseurAdressePrincipale() {
    return (
      this.product.fournisseur?.adresses?.find((a) => a.estPrincipale) ??
      this.product.fournisseur?.adresses?.[0]
    );
  }

  onThumbnailClick(image: string) {
    this.selectedImage = this.imageUrlService.getProduitImageUrl(image);
    this.selectedImageObject = this.product.images.find(
      (img) => img.imageUrl === image
    );
  }

  toggleFavorite() {
    const clientId = this.getClientId();
    if (!clientId) {
      alert('Vous devez être connecté pour ajouter aux favoris');
      return;
    }

    if (this.isFavorite) {
      // Retirer des favoris
      this.favoritesService.supprimerFavoriParProduitId(clientId, this.product.id).subscribe({
        next: () => {
          this.isFavorite = false;
          console.log('✅ Produit retiré des favoris');
          // Optionnel : afficher un message de succès
          // alert('Produit retiré des favoris');
        },
        error: (err) => {
          console.error('❌ Erreur lors de la suppression des favoris:', err);
          alert('Erreur lors de la suppression des favoris');
        }
      });
    } else {
      // Ajouter aux favoris
      this.favoritesService.ajouterFavori(clientId, this.product.id).subscribe({
        next: () => {
          this.isFavorite = true;
          console.log('✅ Produit ajouté aux favoris');
          // Optionnel : afficher un message de succès
          // alert('Produit ajouté aux favoris');
        },
        error: (err) => {
          console.error('❌ Erreur lors de l\'ajout aux favoris:', err);
          alert('Erreur lors de l\'ajout aux favoris');
        }
      });
    }
  }
  ajouterAuPanier() {
    const clientId = this.getClientId();
    if (!clientId) {
      alert('Veuillez vous connecter pour ajouter au panier.');
      return;
    }

    const item = { produitId: this.product.id, quantite: 1 };

    this.cartService.addItemByClientId(clientId, item).subscribe({
      next: (result) => {
        if (result.quantite > 1) {
          alert(`Quantité mise à jour ! Vous avez maintenant ${result.quantite} de ce produit dans votre panier.`);
        } else {
          alert('Produit ajouté au panier !');
        }
      },
      error: (err) => {
        console.error('Erreur lors de l\'ajout au panier:', err);
        let errorMessage = 'Erreur lors de l\'ajout au panier';
        if (err.error?.message || typeof err.error === 'string') {
          errorMessage += ': ' + (err.error.message || err.error);
        }
        alert(errorMessage);
      }
    });
  }


  private getClientId(): number | null {
    const user = this.authService.getCurrentUser();
    return user ? user.id : null;
  }
}
