/**
 * Configuration de l'administrateur OptiLet
 * Username et mot de passe définis en dur pour l'accès admin
 *
 * 🔗 URL d'accès : http://localhost:4200/adminOptiLet
 * 👤 Username : adminOptiLet
 
 *
 * ⚠️ IMPORTANT : Pour changer les credentials, modifiez les valeurs ci-dessous
 */

export interface AdminCredentials {
  username: string;
  password: string;
  role: string;
  permissions: string[];
}

export const ADMIN_CONFIG: AdminCredentials = {
  username: 'adminOptiLet',
  password: 'OptiLet2024!Admin',
  role: 'SUPER_ADMIN',
  permissions: [
    'MANAGE_USERS',
    'MANAGE_PRODUCTS',
    '<PERSON><PERSON>GE_ORDERS',
    'MANAGE_CATEGORIES',
    'MANAGE_PROMOTIONS',
    'VIEW_STATISTICS',
    'MANAGE_SETTINGS',
    'MODERATE_CONTENT',
    'MANAGE_REFUNDS',
    'SYSTEM_ADMIN'
  ]
};

/**
 * Informations complètes de l'administrateur
 */
export const ADMIN_USER_INFO = {
  id: 1,
  username: ADMIN_CONFIG.username,
  email: '<EMAIL>',
  nom: 'Administrateur',
  prenom: 'OptiLet',
  role: ADMIN_CONFIG.role,
  permissions: ADMIN_CONFIG.permissions,
  isActive: true,
  lastLogin: new Date(),
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date(),
  profileImage: 'https://via.placeholder.com/150x150?text=ADMIN',
  phoneNumber: '+33123456789',
  department: 'Administration Système',
  twoFactorEnabled: false, // Désactivé pour simplifier
  sessionTimeout: 3600
};
