<section class="nouveaux-arrivages">
  <h2 class="text-xl font-bold mb-4">Nouveaux Arrivages</h2>
  <p-carousel
    [value]="nouveauxArrivages"
    [numVisible]="numVisible"
    [numScroll]="1"
    [circular]="true"
    [responsiveOptions]="responsiveOptions"
    autoplayInterval="3000"
  >
    <ng-template let-produit pTemplate="item">
      <div
        class="card border border-surface-200 dark:border-surface-700 rounded m-2"
        (click)="goToProductDetails(produit.id)"
        style="cursor: pointer"
      >
        <div class="card-image relative">
          <img
            [src]="getMainImage(produit)"
            [alt]="produit.nom"
            class="w-full"
            loading="lazy"
          />

          <div class="product-badges">
            <span *ngIf="estNouvelleArrivee(produit)" class="badge-new">
              Nouveau
            </span>
            <span
              *ngIf="produit.tauxRemiseTotale > 0"
              class="badge-discount"
            >
              -{{ produit.tauxRemiseTotale }}%
            </span>
          </div>
        </div>

        <div class="card-content p-4">
          <h3
            class="mb-2 font-medium cursor-pointer text-primary-600 hover:underline"
          >
            {{ produit.nom }}
          </h3>
          <div class="product-info-right">
            <p class="product-brand">
              {{ produit.marque?.name || "Marque inconnue" }}
            </p>
            <p class="product-fournisseur">
              {{ produit.fournisseur?.raisonSociale || "Fournisseur inconnue" }}
            </p>
            <div
              *ngIf="produit.noteMoyenne != null"
              class="rating-section flex items-center"
            >
              <mat-icon
                *ngFor="let star of [1, 2, 3, 4, 5]"
                aria-label="Évaluation"
                [class.filled]="star <= produit.noteMoyenne"
              >
                {{ star <= produit.noteMoyenne ? "star" : "star_border" }}
              </mat-icon>
              <span class="review-count text-xs ml-1"
                >({{ produit.nombreAvis || 0 }})</span
              >
            </div>
          </div>
          <p class="truncate mb-4">{{ produit.description }}</p>

          <div class="price-section">
            <div class="price-stack">
              <div class="price-final">
                {{ produit.prixFinalTTC | currency : 'DT' }}
              </div>
              <div
                class="price-intermediate"
                *ngIf="
                  produit.prixApresOutlet &&
                  produit.prixApresOutlet !== produit.prixFinalTTC
                "
              >
                Ancien prix :
                <span class="amount">{{
                  produit.prixApresOutlet | currency : 'DT'
                }}</span>
              </div>
              <div
                class="price-original"
                *ngIf="
                  produit.prixOriginalTTC &&
                  produit.prixOriginalTTC !== produit.prixApresOutlet
                "
              >
                Prix d'origine :
                <span class="amount">{{
                  produit.prixOriginalTTC | currency : 'DT'
                }}</span>
              </div>
            </div>
          </div>

          <span class="button-container flex">
            <p-button
              icon="pi pi-heart"
              styleClass="p-button-secondary p-button-outlined"
              aria-label="Ajouter aux favoris"
              (click)="toggleFavori(produit)"
            ></p-button>
            <p-button
              icon="pi pi-shopping-cart"
              styleClass="p-button-primary ml-2"
              aria-label="Ajouter au panier"
              (click)="ajouterAuPanier(produit)"
            ></p-button>
          </span>
        </div>
      </div>
    </ng-template>
  </p-carousel>
</section>
