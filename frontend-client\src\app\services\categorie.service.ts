import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { CategorieDto } from '../models/CategorieDto';
import { CategorieDropdown } from '../models/CategorieDropdown';
import { environment } from 'src/environments/environment';

@Injectable({ providedIn: 'root' })
export class CategorieService {
  private baseUrl = `${environment.apiUrl}/categories`; 

  constructor(private http: HttpClient) {}

  getAll(): Observable<CategorieDto[]> {
    return this.http.get<CategorieDto[]>(this.baseUrl);
  }

  getById(id: number): Observable<CategorieDto> {
    return this.http.get<CategorieDto>(`${this.baseUrl}/${id}`);
  }

  create(categorie: Partial<CategorieDto>): Observable<CategorieDto> {
    return this.http.post<CategorieDto>(this.baseUrl, categorie);
  }

  update(id: number, categorie: Partial<CategorieDto>): Observable<void> {
    return this.http.put<void>(`${this.baseUrl}/${id}`, categorie);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${id}`);
  }

  getSousCategories(categorieId: number): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/${categorieId}/sous-categories`);
  }

  getProduitsCount(categorieId: number): Observable<number> {
    return this.http.get<number>(`${this.baseUrl}/${categorieId}/produits-count`);
  }

  getForDropdown(): Observable<CategorieDropdown[]> {
    return this.http.get<CategorieDropdown[]>(`${this.baseUrl}/dropdown`);
  }

  toggleVisibility(id: number): Observable<void> {
    return this.http.patch<void>(`${this.baseUrl}/${id}/toggle-visibility`, {});
  }

  checkNameExists(name: string, excludeId?: number): Observable<boolean> {
    let params = new HttpParams().set('name', name);
    if (excludeId) {
      params = params.set('excludeId', excludeId.toString());
    }
    return this.http.get<boolean>(`${this.baseUrl}/check-name`, { params });
  }
}
