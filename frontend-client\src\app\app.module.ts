import { NgModule, LOCALE_ID } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { registerLocaleData } from '@angular/common';
import localeFr from '@angular/common/locales/fr';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatCardModule } from '@angular/material/card';
import { AppComponent } from './app.component';
import { FooterComponent } from './components/footer/footer.component';
import { ProductDetailsComponent } from './pages/product-details/product-details.component';
import { MatMenuModule } from '@angular/material/menu';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';
import { HeaderComponent } from './components/header/header.component';
import { AppRoutingModule } from './app-routing.module';
import { HomeComponent } from './pages/home/<USER>';
import { HomeModule } from './pages/home/<USER>';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { appConfig } from './app.config';
import { ToggleSwitchModule } from 'primeng/toggleswitch';
import { SubCategoriesComponent } from './components/sub-categories/sub-categories.component';
import { BackToTopComponent } from './components/back-to-top/back-to-top.component';
import { ProductListComponent } from './pages/product-list/product-list.component';
import { PaginatorModule } from 'primeng/paginator';
import {
  provideHttpClient,
  withInterceptors,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { MarquesListComponent } from './components/marques-list/marques-list.component';
import { FournisseursListComponent } from './components/fournisseurs-list/fournisseurs-list.component';
import { authInterceptor } from './auth/auth.interceptor';
import { JwtModule } from '@auth0/angular-jwt';
import { MarquesAllComponent } from './components/marques-all/marques-all.component';
import { NotificationIconComponent } from './components/notification-icon/notification-icon.component';

// Enregistrer les données de localisation française
registerLocaleData(localeFr);

@NgModule({
  declarations: [
    AppComponent,
    FooterComponent,
    HeaderComponent,
    SubCategoriesComponent,
    MarquesListComponent,
    FournisseursListComponent,
    MarquesAllComponent,
  ],
  imports: [
    FormsModule,
    RouterModule,
    MatToolbarModule,
    BrowserModule,
    MatMenuModule,
    MatFormFieldModule,
    MatInputModule,
    MatDividerModule,
    AppRoutingModule,
    MatToolbarModule,
    BrowserAnimationsModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    MatCardModule,
    HomeModule,
    ToggleSwitchModule,
    BackToTopComponent,
    HomeComponent,
    ProductListComponent,
    ProductDetailsComponent,
    NotificationIconComponent,
    PaginatorModule,
    JwtModule.forRoot({
      config: {
        tokenGetter: () => null, // Désactiver l'intercepteur JWT
        allowedDomains: [],
        disallowedRoutes: [],
        skipWhenExpired: true
      },
    }),

  ],
  providers: [
    ...appConfig.providers,
    provideHttpClient(withInterceptors([authInterceptor])),
    { provide: LOCALE_ID, useValue: 'fr-FR' },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
