﻿namespace WebApiPfe.DTOs.ReadDTOs
{
    public class PaiementResponseDto
    {
        public int Id { get; set; }
        public string TransactionId { get; set; }
        public string TransactionIdMasked => TransactionId?[^4..].PadLeft(TransactionId?.Length ?? 0, '*');
        public decimal Montant { get; set; }
        public string Statut { get; set; }
        public DateTime DateCreation { get; set; }
        public string Methode { get; set; }
        public int CommandeId { get; set; }
    }
}
