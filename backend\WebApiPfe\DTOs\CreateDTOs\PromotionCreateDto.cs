using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.CreateDTOs
{
    public class PromotionCreateDto
    {
        [Required(ErrorMessage = "Le type de promotion est obligatoire")]
        public string Type { get; set; } = string.Empty; // "CodePromo" ou "PromotionAutomatique"

        [Required(ErrorMessage = "Le pourcentage de remise est obligatoire")]
        [Range(1, 100, ErrorMessage = "La remise doit être entre 1% et 100%")]
        public decimal PourcentageRemise { get; set; }

        [Required(ErrorMessage = "La date de début est obligatoire")]
        public string DateDebut { get; set; } = string.Empty;

        [Required(ErrorMessage = "La date de fin est obligatoire")]
        public string DateFin { get; set; } = string.Empty;

        public string? CodePromo { get; set; }

        [Required(ErrorMessage = "Le nom d'affichage est obligatoire")]
        [StringLength(100, ErrorMessage = "Le nom d'affichage ne peut pas dépasser 100 caractères")]
        public string NomAffichage { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "Le nom de promotion ne peut pas dépasser 50 caractères")]
        public string? NomPromotion { get; set; }

        [StringLength(500, ErrorMessage = "La description ne peut pas dépasser 500 caractères")]
        public string? Description { get; set; }

        // Critères d'application (optionnels)
        public int? CategorieId { get; set; }
        public int? SousCategorieId { get; set; }
        public int? MarqueId { get; set; }
        public int? FormeId { get; set; }
        public List<int>? ProduitsApplicablesIds { get; set; }

        // Options
        public bool AppliquerSurHT { get; set; } = false;
        public int? LimitUtilisation { get; set; }

        // Sera défini par le contrôleur
        public int FournisseurId { get; set; }
    }
}
