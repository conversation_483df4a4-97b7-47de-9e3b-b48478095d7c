﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProduitsController : ControllerBase
    {
        private readonly IProduitService _produitService;
        private readonly IPromotionService _promotionService;
        private readonly ILogger<ProduitsController> _logger;
        public ProduitsController(IProduitService produitService, ILogger<ProduitsController> logger, IPromotionService promotionService)
        {
            _produitService = produitService;
            _logger = logger;
            _promotionService = promotionService;
        }
        //CRUD
        [HttpPost]
        [Consumes("multipart/form-data")]
        public async Task<ActionResult<ProduitDto>> Create([FromForm] ProduitCreateDto produitDto)
        {
            try
            {
                Console.WriteLine($"🔍 ProduitCreateDto reçu:");
                Console.WriteLine($"  - Nom: {produitDto?.Nom}");
                Console.WriteLine($"  - ReferenceOriginal: {produitDto?.ReferenceOriginal}");
                Console.WriteLine($"  - CodeABarre: {produitDto?.CodeABarre}");
                Console.WriteLine($"  - FournisseurId: {produitDto?.FournisseurId}");
                Console.WriteLine($"  - SousCategorieId: {produitDto?.SousCategorieId}");
                Console.WriteLine($"  - MarqueId: {produitDto?.MarqueId}");
                Console.WriteLine($"  - FormeId: {produitDto?.FormeId}");
                Console.WriteLine($"  - TauxTVAId: {produitDto?.TauxTVAId}");
                Console.WriteLine($"  - PrixAchatHT: {produitDto?.PrixAchatHT}");
                Console.WriteLine($"  - PrixVenteHT: {produitDto?.PrixVenteHT}");
                Console.WriteLine($"  - Stock: {produitDto?.Stock}");

                Console.WriteLine("🔍 Vérification du ModelState...");
                if (!ModelState.IsValid)
                {
                    Console.WriteLine("❌ ModelState invalide:");
                    foreach (var error in ModelState)
                    {
                        Console.WriteLine($"  - {error.Key}: {string.Join(", ", error.Value.Errors.Select(e => e.ErrorMessage))}");
                    }
                    return BadRequest(ModelState);
                }
                Console.WriteLine("✅ ModelState valide");

                // Validation simple des fichiers (mais on ne les traite pas ici)
                Console.WriteLine("🔍 Validation des fichiers...");
                var allowedMimeTypes = new[] { "image/jpeg", "image/png", "image/gif", "image/avif", "image/webp" };
                foreach (var file in produitDto.ImageFiles ?? Enumerable.Empty<IFormFile>())
                {
                    if (file.Length == 0) continue;

                    if (!allowedMimeTypes.Contains(file.ContentType.ToLower()))
                    {
                        Console.WriteLine($"❌ Type de fichier non supporté: {file.ContentType}");
                        return BadRequest($"Type de fichier non supporté: {file.ContentType}");
                    }

                    if (file.Length > 5 * 1024 * 1024)
                    {
                        Console.WriteLine($"❌ Fichier trop volumineux: {file.FileName}");
                        return BadRequest($"Fichier trop volumineux: {file.FileName} (max 5MB)");
                    }
                }
                Console.WriteLine("✅ Validation des fichiers terminée");

                Console.WriteLine("🚀 Appel du service CreateAsync...");
                var created = await _produitService.CreateAsync(produitDto);
                Console.WriteLine("✅ Service CreateAsync terminé avec succès");
                return CreatedAtAction(nameof(GetById), new { id = created.Id }, created);
            }
            catch (ArgumentException ex)
            {
                Console.WriteLine($"❌ ArgumentException: {ex.Message}");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Exception non gérée: {ex.Message}");
                Console.WriteLine($"❌ StackTrace: {ex.StackTrace}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }
        [HttpPut("{id}")]
        [Consumes("multipart/form-data")]
        public async Task<IActionResult> Update(int id, [FromForm] ProduitUpdateDto produitDto)
        {
            if (id != produitDto.Id)
                return BadRequest("ID mismatch");

            // Validation minimale des fichiers
            var allowedMimeTypes = new[] { "image/jpeg", "image/png", "image/gif", "image/avif", "image/webp" };
            foreach (var file in produitDto.ImageFiles ?? Enumerable.Empty<IFormFile>())
            {
                if (file.Length == 0) continue;

                if (!allowedMimeTypes.Contains(file.ContentType.ToLower()))
                    return BadRequest($"Type de fichier non supporté: {file.ContentType}");

                if (file.Length > 5 * 1024 * 1024)
                    return BadRequest($"Fichier trop volumineux: {file.FileName} (max 5MB)");
            }

            try
            {
                await _produitService.UpdateAsync(produitDto);
                return NoContent();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception)
            {
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                await _produitService.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        //Recuperation
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ProduitDto>>> GetAll()
        {
            var produits = await _produitService.GetAllAsync();
            return Ok(produits);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ProduitDto>> GetById(int id)
        {
            try
            {
                var produit = await _produitService.GetByIdAsync(id);
                return Ok(produit);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpGet("by-reference-original/{reference}")]
        public async Task<ActionResult<ProduitDto>> GetByReferenceOriginal(string reference)
        {
            var produit = await _produitService.GetByReferenceOriginalAsync(reference);
            if (produit == null) return NotFound();
            return Ok(produit);
        }

        [HttpGet("by-reference-fournisseur/{reference}")]
        public async Task<ActionResult<ProduitDto>> GetByReferenceFournisseur(string reference)
        {
            var produit = await _produitService.GetByReferenceFournisseurAsync(reference);
            if (produit == null) return NotFound();
            return Ok(produit);
        }

        [HttpGet("by-code-barre/{codeABarre}")]
        public async Task<ActionResult<ProduitDto>> GetByCodeABarre(string codeABarre)
        {
            try
            {
                var produit = await _produitService.GetByCodeABarreAsync(codeABarre);
                return Ok(produit);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        //Recherche/Filtrage
        [HttpGet("search")]
        public async Task<ActionResult<IEnumerable<ProduitDto>>> Search([FromQuery] string term)
        {
            if (string.IsNullOrWhiteSpace(term))
                return BadRequest("Le terme de recherche est requis");

            var produits = await _produitService.SearchAsync(term);
            return Ok(produits);
        }

        [HttpGet("by-sous-categorie/{sousCategorieId}")]
        public async Task<ActionResult<IEnumerable<ProduitDto>>> GetBySousCategorie(int sousCategorieId)
        {
            var produits = await _produitService.GetBySousCategorieAsync(sousCategorieId);
            return Ok(produits);
        }

        [HttpGet("by-categorie/{categorieId}")]
        public async Task<ActionResult<IEnumerable<ProduitDto>>> GetByCategorie(int categorieId)
        {
            var produits = await _produitService.GetByCategorieAsync(categorieId);
            return Ok(produits);
        }

        [HttpGet("by-fournisseur/{fournisseurId}")]
        public async Task<ActionResult<IEnumerable<ProduitDto>>> GetByFournisseur(int fournisseurId)
        {
            var produits = await _produitService.GetByFournisseurAsync(fournisseurId);
            return Ok(produits);
        }

        [HttpGet("by-marque/{marqueId}")]
        public async Task<ActionResult<IEnumerable<ProduitDto>>> GetByMarque(int marqueId)
        {
            var produits = await _produitService.GetByMarqueAsync(marqueId);
            return Ok(produits);
        }

        [HttpGet("by-forme/{formeId}")]
        public async Task<ActionResult<IEnumerable<ProduitDto>>> GetByForme(int formeId)
        {
            var produits = await _produitService.GetByFormeAsync(formeId);
            return Ok(produits);
        }

        [HttpGet("promotions")]
        public async Task<ActionResult<IEnumerable<ProduitDto>>> GetProduitsEnPromotion()
        {
            var produits = await _produitService.GetProduitsEnPromotionAsync();
            return Ok(produits);
        }

        [HttpGet("en-stock")]
        public async Task<ActionResult<IEnumerable<ProduitDto>>> GetProduitsEnStock()
        {
            var produits = await _produitService.GetProduitsEnStockAsync();
            return Ok(produits);
        }

        [HttpGet("{produitId}/images")]
        public async Task<ActionResult<IEnumerable<ImageProduitDto>>> GetImagesForProduit(int produitId)
        {
            return Ok(await _produitService.GetImagesForProduitAsync(produitId));
        }

        //Autre
        [HttpGet("dropdown")]
        public async Task<ActionResult<Dictionary<int, string>>> GetForDropdown()
        {
            return Ok(await _produitService.GetProduitsForDropdownAsync());
        }
        [HttpGet("meilleures-ventes")]
        public async Task<ActionResult<IEnumerable<ProduitDto>>> GetMeilleuresVentes([FromQuery] int limit = 20)
        {
            var produits = await _produitService.GetMeilleuresVentesAsync(limit);
            return Ok(produits);
        }

        [HttpGet("nouveaux-arrivages")]
        public async Task<ActionResult<IEnumerable<ProduitDto>>> GetNouveauxArrivages([FromQuery] int limit = 10)
        {
            var produits = await _produitService.GetNouveauxArrivagesAsync(limit);
            return Ok(produits);
        }

        //Images
        [HttpPut("{produitId}/images/{imageId}")]
        public async Task<IActionResult> UpdateImage(int produitId, int imageId, [FromBody] ImageProduitUpdateDto imageDto)
        {
            try
            {
                if (imageDto.Id != imageId || imageDto.Id <= 0 || produitId <= 0)
                    return BadRequest("IDs invalides");

                await _produitService.UpdateImageAsync(produitId, imageDto);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpPatch("{produitId}/images/{imageId}/set-main")]
        public async Task<IActionResult> SetMainImage([FromRoute] int produitId, [FromRoute] int imageId)
        {
            try
            {
                await _produitService.SetMainImageAsync(produitId, imageId);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }
        
        [HttpDelete("{id}/images/{imageId}")]
        public async Task<IActionResult> DeleteImage(int id, int imageId)
        {
            try
            {
                var imageUrl = await _produitService.RemoveImageFromProduitAsync(id, imageId);
                ImageHandler.DeleteImage(imageUrl);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        //Mise à jour specifiques
        [HttpPatch("{id}/stock")]
        public async Task<IActionResult> UpdateStock(int id, [FromBody] ProduitStockUpdateDto stockDto)
        {
            if (id != stockDto.Id)
                return BadRequest("ID mismatch");

            try
            {
                await _produitService.UpdateStockAsync(stockDto);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }
       
        [HttpPatch("{id}/prix")]
        public async Task<IActionResult> UpdatePrix(int id, [FromBody] ProduitPrixUpdateDto dto)
        {
            if (dto == null)
                return BadRequest("Données manquantes");

            try
            {
                await _produitService.UpdatePrixAsync(id, dto.NouveauPrixHT);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}
