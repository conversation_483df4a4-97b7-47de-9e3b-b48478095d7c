import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import {
  Promotion,
  CreatePromotionDto,
  UpdatePromotionDto,
  TypePromotion,
  TypeReduction,
  TypeCondition,
  CreateConditionPromotionDto,
  CreateProduitPromotionDto,
  ResultatValidationPromotion,
  TemplatePromotion
} from '../../../models/promotion.model';
import { PromotionService } from '../../../services/promotion.service';
import { ProduitService } from '../../../services/produit.service';
import { AuthService } from '../../../services/auth.service';
import { Produit } from '../../../models';

@Component({
  selector: 'app-promotion-form',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './promotion-form.component.html',
  styleUrls: ['./promotion-form.component.css']
})
export class PromotionFormComponent implements OnInit {
  // Mode du formulaire
  isEditMode = false;
  promotionId: number | null = null;
  
  // Données du formulaire
  promotion: CreatePromotionDto = {
    nomPromotion: '',
    description: '',
    type: 'automatique',
    typeReduction: 'pourcentage',
    valeurReduction: 0,
    dateDebut: new Date(),
    dateFin: new Date(),
    conditions: [],
    produitsConcernes: [],
    categoriesConcernees: [],
    cumulable: false,
    priorite: 1,
    fournisseurId: 0
  };

  // États du formulaire
  currentStep = 1;
  totalSteps = 4;
  isLoading = false;
  isSaving = false;
  error = '';
  validationResult: ResultatValidationPromotion | null = null;

  // Données de référence
  produits: Produit[] = [];
  selectedProduits: Set<number> = new Set();
  
  // Options pour les listes déroulantes
  typesPromotion: { value: TypePromotion, label: string, description: string }[] = [
    { 
      value: 'automatique', 
      label: 'Promotion Automatique', 
      description: 'Appliquée automatiquement selon les conditions définies' 
    },
    { 
      value: 'code_promo', 
      label: 'Code Promo', 
      description: 'Nécessite la saisie d\'un code par le client' 
    },
    { 
      value: 'outlet', 
      label: 'Outlet / Déstockage', 
      description: 'Promotion pour écouler les stocks' 
    }
  ];

  typesReduction: { value: TypeReduction, label: string, description: string }[] = [
    { 
      value: 'pourcentage', 
      label: 'Pourcentage', 
      description: 'Réduction en pourcentage du prix' 
    },
    { 
      value: 'montant_fixe', 
      label: 'Montant fixe', 
      description: 'Réduction d\'un montant fixe' 
    },
    { 
      value: 'prix_fixe', 
      label: 'Prix fixe', 
      description: 'Prix de vente fixe pour le produit' 
    }
  ];

  typesCondition: { value: TypeCondition, label: string }[] = [
    { value: 'montant_minimum', label: 'Montant minimum de commande' },
    { value: 'quantite_minimum', label: 'Quantité minimum' },
    { value: 'produit_specifique', label: 'Produit spécifique requis' },
    { value: 'categorie', label: 'Catégorie spécifique' },
    { value: 'premiere_commande', label: 'Première commande client' },
    { value: 'client_fidele', label: 'Client fidèle' }
  ];

  operateurs = [
    { value: 'egal', label: 'Égal à' },
    { value: 'superieur', label: 'Supérieur à' },
    { value: 'superieur_egal', label: 'Supérieur ou égal à' },
    { value: 'inferieur', label: 'Inférieur à' },
    { value: 'inferieur_egal', label: 'Inférieur ou égal à' },
    { value: 'contient', label: 'Contient' }
  ];

  constructor(
    private promotionService: PromotionService,
    private produitService: ProduitService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadProduits();
    this.checkRouteParams();
  }

  /**
   * Initialiser le formulaire
   */
  initializeForm(): void {
    const fournisseurId = this.authService.getCurrentUserId();
    if (!fournisseurId) {
      this.error = 'Utilisateur non connecté';
      return;
    }

    this.promotion.fournisseurId = fournisseurId;
    
    // Définir les dates par défaut
    const now = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + 1);
    
    this.promotion.dateDebut = now;
    this.promotion.dateFin = endDate;
  }

  /**
   * Charger les produits du fournisseur
   */
  loadProduits(): void {
    const fournisseurId = this.authService.getCurrentUserId();
    if (!fournisseurId) return;

    this.produitService.getByFournisseur(fournisseurId).subscribe({
      next: (produits) => {
        this.produits = produits;
      },
      error: (error) => {
        console.error('Error loading products:', error);
      }
    });
  }

  /**
   * Vérifier les paramètres de route
   */
  checkRouteParams(): void {
    // Vérifier si c'est un mode édition
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.isEditMode = true;
      this.promotionId = parseInt(id);
      this.loadPromotion(this.promotionId);
    }

    // Vérifier les paramètres de query
    this.route.queryParams.subscribe(params => {
      if (params['template']) {
        this.loadTemplate(parseInt(params['template']));
      }
      if (params['duplicate']) {
        this.duplicatePromotion(parseInt(params['duplicate']));
      }
    });
  }

  /**
   * Charger une promotion existante
   */
  loadPromotion(id: number): void {
    this.isLoading = true;
    this.promotionService.getPromotionById(id).subscribe({
      next: (promotion) => {
        this.promotion = {
          nomPromotion: promotion.nomPromotion,
          description: promotion.description,
          type: promotion.type,
          typeReduction: promotion.typeReduction,
          valeurReduction: promotion.valeurReduction,
          dateDebut: promotion.dateDebut,
          dateFin: promotion.dateFin,
          codePromo: promotion.codePromo,
          utilisationMax: promotion.utilisationMax,
          utilisationParClient: promotion.utilisationParClient,
          conditions: promotion.conditions.map(c => ({
            type: c.type,
            valeur: c.valeur,
            valeurTexte: c.valeurTexte,
            operateur: c.operateur,
            obligatoire: c.obligatoire
          })),
          produitsConcernes: promotion.produitsConcernes.map(p => ({
            produitId: p.produitId,
            inclus: p.inclus
          })),
          categoriesConcernees: promotion.categoriesConcernees,
          cumulable: promotion.cumulable,
          priorite: promotion.priorite,
          actifLundiVendredi: promotion.actifLundiVendredi,
          actifWeekend: promotion.actifWeekend,
          heureDebut: promotion.heureDebut,
          heureFin: promotion.heureFin,
          fournisseurId: promotion.fournisseurId
        };
        
        // Mettre à jour la sélection des produits
        this.selectedProduits.clear();
        promotion.produitsConcernes.forEach(p => {
          if (p.inclus) {
            this.selectedProduits.add(p.produitId);
          }
        });
        
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Erreur lors du chargement de la promotion';
        console.error('Error loading promotion:', error);
        this.isLoading = false;
      }
    });
  }

  /**
   * Charger un template
   */
  loadTemplate(templateId: number): void {
    this.promotionService.getTemplatesPromotion().subscribe({
      next: (templates) => {
        const template = templates.find(t => t.id === templateId);
        if (template) {
          this.promotion.nomPromotion = template.nomPromotion;
          this.promotion.description = template.description;
          this.promotion.type = template.type;
          this.promotion.typeReduction = template.typeReduction;
          this.promotion.valeurReduction = template.valeurReduction;
          this.promotion.conditions = [...template.conditions];
        }
      },
      error: (error) => {
        console.error('Error loading template:', error);
      }
    });
  }

  /**
   * Dupliquer une promotion
   */
  duplicatePromotion(id: number): void {
    this.loadPromotion(id);
    this.isEditMode = false;
    this.promotionId = null;
    this.promotion.nomPromotion = this.promotion.nomPromotion + ' (Copie)';
  }

  // ==================== NAVIGATION ENTRE ÉTAPES ====================

  /**
   * Aller à l'étape suivante
   */
  nextStep(): void {
    if (this.currentStep < this.totalSteps) {
      this.currentStep++;
    }
  }

  /**
   * Aller à l'étape précédente
   */
  previousStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

  /**
   * Aller à une étape spécifique
   */
  goToStep(step: number): void {
    if (step >= 1 && step <= this.totalSteps) {
      this.currentStep = step;
    }
  }

  /**
   * Vérifier si une étape est valide
   */
  isStepValid(step: number): boolean {
    switch (step) {
      case 1:
        return this.promotion.nomPromotion.trim().length >= 3 &&
               this.promotion.description.trim().length >= 10;
      case 2:
        return this.promotion.valeurReduction > 0 &&
               (this.promotion.type !== 'code_promo' || (this.promotion.codePromo != null && this.promotion.codePromo.length >= 3));
      case 3:
        return this.promotion.dateDebut < this.promotion.dateFin;
      case 4:
        return true; // Étape de révision, toujours valide
      default:
        return false;
    }
  }

  // ==================== GESTION DES CONDITIONS ====================

  /**
   * Ajouter une condition
   */
  addCondition(): void {
    const newCondition: CreateConditionPromotionDto = {
      type: 'montant_minimum',
      valeur: 0,
      operateur: 'superieur_egal',
      obligatoire: true
    };
    this.promotion.conditions.push(newCondition);
  }

  /**
   * Supprimer une condition
   */
  removeCondition(index: number): void {
    this.promotion.conditions.splice(index, 1);
  }

  // ==================== GESTION DES PRODUITS ====================

  /**
   * Basculer la sélection d'un produit
   */
  toggleProduitSelection(produitId: number): void {
    if (this.selectedProduits.has(produitId)) {
      this.selectedProduits.delete(produitId);
    } else {
      this.selectedProduits.add(produitId);
    }
    this.updateProduitsConcernes();
  }

  /**
   * Mettre à jour la liste des produits concernés
   */
  updateProduitsConcernes(): void {
    this.promotion.produitsConcernes = Array.from(this.selectedProduits).map(produitId => ({
      produitId,
      inclus: true
    }));
  }

  /**
   * Vérifier si un produit est sélectionné
   */
  isProduitSelected(produitId: number): boolean {
    return this.selectedProduits.has(produitId);
  }

  // ==================== VALIDATION ET SAUVEGARDE ====================

  /**
   * Valider la promotion
   */
  validatePromotion(): void {
    this.promotionService.validatePromotion(this.promotion).subscribe({
      next: (result) => {
        this.validationResult = result;
      },
      error: (error) => {
        console.error('Error validating promotion:', error);
      }
    });
  }

  /**
   * Sauvegarder la promotion
   */
  savePromotion(): void {
    this.isSaving = true;
    this.error = '';

    if (this.isEditMode && this.promotionId) {
      // Mode édition
      const updateDto: UpdatePromotionDto = { ...this.promotion };
      this.promotionService.updatePromotion(this.promotionId, updateDto).subscribe({
        next: () => {
          this.router.navigate(['/dashboard/promotions']);
        },
        error: (error) => {
          this.error = 'Erreur lors de la mise à jour de la promotion';
          console.error('Error updating promotion:', error);
          this.isSaving = false;
        }
      });
    } else {
      // Mode création
      this.promotionService.createPromotion(this.promotion).subscribe({
        next: () => {
          this.router.navigate(['/dashboard/promotions']);
        },
        error: (error) => {
          this.error = 'Erreur lors de la création de la promotion';
          console.error('Error creating promotion:', error);
          this.isSaving = false;
        }
      });
    }
  }

  /**
   * Annuler et retourner à la liste
   */
  cancel(): void {
    this.router.navigate(['/dashboard/promotions']);
  }

  // ==================== UTILITAIRES ====================

  /**
   * Formater le prix
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND'
    }).format(price);
  }

  /**
   * Obtenir l'icône pour le type de promotion
   */
  getTypeIcon(type: TypePromotion): string {
    const icons = {
      'automatique': '⚡',
      'code_promo': '🎫',
      'outlet': '🏷️'
    };
    return icons[type] || '📋';
  }

  /**
   * Obtenir le texte de la réduction
   */
  getReductionPreview(): string {
    switch (this.promotion.typeReduction) {
      case 'pourcentage':
        return `-${this.promotion.valeurReduction}%`;
      case 'montant_fixe':
        return `-${this.formatPrice(this.promotion.valeurReduction)}`;
      case 'prix_fixe':
        return `${this.formatPrice(this.promotion.valeurReduction)}`;
      default:
        return '';
    }
  }
}
