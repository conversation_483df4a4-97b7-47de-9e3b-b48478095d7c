using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestDataController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly ILogger<TestDataController> _logger;

        public TestDataController(AppDbContext context, ILogger<TestDataController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpPost("create-test-commande-fournisseur")]
        public async Task<IActionResult> CreateTestCommandeFournisseur()
        {
            try
            {
                // Récupérer le premier fournisseur
                var fournisseur = await _context.Fournisseurs.FirstOrDefaultAsync();
                if (fournisseur == null)
                {
                    return BadRequest(new { Error = "Aucun fournisseur trouvé" });
                }

                // Récupérer le premier produit
                var produit = await _context.Produits.FirstOrDefaultAsync();
                if (produit == null)
                {
                    return BadRequest(new { Error = "Aucun produit trouvé" });
                }

                // Créer une commande fournisseur de test
                var commandeFournisseur = new CommandeFournisseur
                {
                    CommandeClientId = 1, // Supposons que la commande 1 existe
                    FournisseurId = fournisseur.Id,
                    Reference = $"TEST-{DateTime.Now:yyyyMMdd-HHmmss}",
                    DateCreation = DateTime.UtcNow,
                    Statut = StatutCommandeFournisseur.EnAttente,
                    FraisLivraison = fournisseur.FraisLivraisonBase,
                    MontantTotal = 0
                };

                _context.CommandesFournisseurs.Add(commandeFournisseur);
                await _context.SaveChangesAsync();

                // Créer une ligne de commande
                var ligneCommande = new LigneCommandeFournisseur
                {
                    CommandeId = commandeFournisseur.Id,
                    ProduitId = produit.Id,
                    Quantite = 2,
                    PrixUnitaire = produit.PrixVenteHT,
                    MontantLigne = produit.PrixVenteHT * 2
                };

                _context.LignesCommandeFournisseur.Add(ligneCommande);

                // Mettre à jour le montant total
                commandeFournisseur.MontantTotal = ligneCommande.MontantLigne + commandeFournisseur.FraisLivraison;
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    Message = "Commande fournisseur de test créée",
                    CommandeFournisseurId = commandeFournisseur.Id,
                    FournisseurNom = fournisseur.RaisonSociale,
                    ProduitNom = produit.Nom,
                    MontantTotal = commandeFournisseur.MontantTotal
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création de la commande fournisseur de test");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        [HttpPost("fix-all-commandes")]
        public async Task<IActionResult> FixAllCommandes()
        {
            try
            {
                // Récupérer toutes les commandes sans commandes fournisseurs
                var commandes = await _context.Commandes
                    .Include(c => c.DetailsCommandes)
                        .ThenInclude(d => d.Produit)
                    .Where(c => !_context.CommandesFournisseurs.Any(cf => cf.CommandeClientId == c.Id))
                    .ToListAsync();

                var commandesTraitees = 0;

                foreach (var commande in commandes)
                {
                    await CreerCommandesFournisseursForCommande(commande.Id);
                    commandesTraitees++;
                }

                return Ok(new
                {
                    Message = $"{commandesTraitees} commandes traitées",
                    CommandesTraitees = commandesTraitees
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        private async Task CreerCommandesFournisseursForCommande(int commandeId)
        {
            var commande = await _context.Commandes
                .Include(c => c.DetailsCommandes)
                    .ThenInclude(d => d.Produit)
                .FirstOrDefaultAsync(c => c.Id == commandeId);

            if (commande == null) return;

            var fournisseurParDefaut = await _context.Fournisseurs.FirstOrDefaultAsync();
            if (fournisseurParDefaut == null) return;

            // Grouper par fournisseur
            var detailsParFournisseur = commande.DetailsCommandes
                .Where(d => d.Produit != null)
                .GroupBy(d => d.Produit.FournisseurId > 0 ? d.Produit.FournisseurId : fournisseurParDefaut.Id)
                .ToList();

            foreach (var groupe in detailsParFournisseur)
            {
                var fournisseurId = groupe.Key;
                var details = groupe.ToList();

                var commandeFournisseur = new CommandeFournisseur
                {
                    CommandeClientId = commandeId,
                    FournisseurId = fournisseurId,
                    Reference = $"CMD-F{fournisseurId}-{DateTime.Now:yyyyMMdd-HHmmss}",
                    DateCreation = DateTime.UtcNow,
                    Statut = StatutCommandeFournisseur.EnAttente,
                    FraisLivraison = await _context.Fournisseurs
                        .Where(f => f.Id == fournisseurId)
                        .Select(f => f.FraisLivraisonBase)
                        .FirstOrDefaultAsync(),
                    MontantTotal = 0
                };

                _context.CommandesFournisseurs.Add(commandeFournisseur);
                await _context.SaveChangesAsync();

                decimal montantTotal = 0;
                foreach (var detail in details)
                {
                    var ligne = new LigneCommandeFournisseur
                    {
                        CommandeId = commandeFournisseur.Id,
                        ProduitId = detail.ProduitId,
                        Quantite = detail.Quantite,
                        PrixUnitaire = detail.PrixUnitaireHT,
                        MontantLigne = detail.PrixUnitaireHT * detail.Quantite
                    };

                    _context.LignesCommandeFournisseur.Add(ligne);
                    montantTotal += ligne.MontantLigne;
                }

                commandeFournisseur.MontantTotal = montantTotal + commandeFournisseur.FraisLivraison;
                await _context.SaveChangesAsync();
            }
        }


    }
}
