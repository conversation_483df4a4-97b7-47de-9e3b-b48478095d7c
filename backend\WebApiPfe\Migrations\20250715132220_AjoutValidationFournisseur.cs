﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WebApiPfe.Migrations
{
    /// <inheritdoc />
    public partial class AjoutValidationFournisseur : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CommentaireValidation",
                table: "AspNetUsers",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateValidation",
                table: "AspNetUsers",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "StatutValidation",
                table: "AspNetUsers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ValidePar",
                table: "AspNetUsers",
                type: "int",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CommentaireValidation",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "DateValidation",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "StatutValidation",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "ValidePar",
                table: "AspNetUsers");
        }
    }
}
