import { Component, OnInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { 
  FournisseurValidationService, 
  FournisseurValidationDto, 
  StatutValidationFournisseur,
  ValiderFournisseurRequest 
} from '../../../services/fournisseur-validation.service';
import { NotificationService, NotificationDto } from '../../../services/notification.service';

@Component({
  selector: 'app-fournisseur-validation',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './fournisseur-validation.component.html',
  styleUrls: ['./fournisseur-validation.component.css']
})
export class FournisseurValidationComponent implements OnInit {
  // Signals pour la gestion d'état
  fournisseurs = signal<FournisseurValidationDto[]>([]);
  notifications = signal<NotificationDto[]>([]);
  isLoading = signal(false);
  error = signal('');
  selectedFournisseur = signal<FournisseurValidationDto | null>(null);
  
  // Modal de validation
  showValidationModal = signal(false);
  validationComment = signal('');
  validationAction = signal<'accept' | 'reject' | null>(null);
  
  // Filtres
  searchTerm = signal('');
  selectedStatus = signal('');
  
  // Computed signals
  filteredFournisseurs = computed(() => {
    const fournisseurs = this.fournisseurs();
    const search = this.searchTerm().toLowerCase();
    const status = this.selectedStatus();
    
    return fournisseurs.filter(fournisseur => {
      const matchesSearch = !search || 
        fournisseur.raisonSociale.toLowerCase().includes(search) ||
        fournisseur.nom.toLowerCase().includes(search) ||
        fournisseur.prenom.toLowerCase().includes(search) ||
        fournisseur.email.toLowerCase().includes(search) ||
        fournisseur.matriculeFiscale.includes(search);
      
      const matchesStatus = !status || 
        (status === 'en-attente' && fournisseur.statutValidation === StatutValidationFournisseur.EnAttente) ||
        (status === 'valide' && fournisseur.statutValidation === StatutValidationFournisseur.Valide) ||
        (status === 'rejete' && fournisseur.statutValidation === StatutValidationFournisseur.Rejete);
      
      return matchesSearch && matchesStatus;
    });
  });

  fournisseursEnAttente = computed(() => 
    this.fournisseurs().filter(f => f.statutValidation === StatutValidationFournisseur.EnAttente)
  );

  notificationsNonLues = computed(() => 
    this.notifications().filter(n => !n.estLue)
  );

  constructor(
    private fournisseurValidationService: FournisseurValidationService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.loadFournisseurs();
    this.loadNotifications();
  }

  loadFournisseurs(): void {
    this.isLoading.set(true);
    this.error.set('');
    
    this.fournisseurValidationService.getTousFournisseurs().subscribe({
      next: (fournisseurs) => {
        this.fournisseurs.set(fournisseurs);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des fournisseurs:', error);
        this.error.set('Erreur lors du chargement des fournisseurs');
        this.isLoading.set(false);
      }
    });
  }

  loadNotifications(): void {
    this.notificationService.getAllAdminNotifications().subscribe({
      next: (notifications) => {
        this.notifications.set(notifications);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des notifications:', error);
      }
    });
  }

  viewFournisseurDetails(fournisseur: FournisseurValidationDto): void {
    this.selectedFournisseur.set(fournisseur);
  }

  closeDetails(): void {
    this.selectedFournisseur.set(null);
  }

  openValidationModal(action: 'accept' | 'reject'): void {
    this.validationAction.set(action);
    this.validationComment.set('');
    this.showValidationModal.set(true);
  }

  closeValidationModal(): void {
    this.showValidationModal.set(false);
    this.validationAction.set(null);
    this.validationComment.set('');
  }

  confirmValidation(): void {
    const fournisseur = this.selectedFournisseur();
    const action = this.validationAction();
    
    if (!fournisseur || !action) return;

    const request: ValiderFournisseurRequest = {
      fournisseurId: fournisseur.id,
      accepter: action === 'accept',
      commentaire: this.validationComment() || undefined
    };

    this.fournisseurValidationService.validerFournisseur(request).subscribe({
      next: (response) => {
        // Recharger les données
        this.loadData();
        
        // Fermer les modals
        this.closeValidationModal();
        this.closeDetails();
        
        // Afficher un message de succès
        const actionText = action === 'accept' ? 'validé' : 'rejeté';
        alert(`Fournisseur ${actionText} avec succès !`);
      },
      error: (error) => {
        console.error('Erreur lors de la validation:', error);
        alert('Erreur lors de la validation du fournisseur');
      }
    });
  }

  markNotificationAsRead(notification: NotificationDto): void {
    if (!notification.estLue) {
      this.notificationService.markAsRead(notification.id).subscribe({
        next: () => {
          // Mettre à jour la notification localement
          const updated = this.notifications().map(n => 
            n.id === notification.id ? { ...n, estLue: true } : n
          );
          this.notifications.set(updated);
        },
        error: (error) => {
          console.error('Erreur lors du marquage comme lu:', error);
        }
      });
    }
  }

  deleteNotification(notificationId: number): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette notification ?')) {
      this.notificationService.deleteNotification(notificationId).subscribe({
        next: () => {
          const updated = this.notifications().filter(n => n.id !== notificationId);
          this.notifications.set(updated);
        },
        error: (error) => {
          console.error('Erreur lors de la suppression:', error);
        }
      });
    }
  }

  getStatutValidationText(statut: StatutValidationFournisseur): string {
    switch (statut) {
      case StatutValidationFournisseur.EnAttente:
        return 'En attente';
      case StatutValidationFournisseur.Valide:
        return 'Validé';
      case StatutValidationFournisseur.Rejete:
        return 'Rejeté';
      case StatutValidationFournisseur.Suspendu:
        return 'Suspendu';
      default:
        return 'Inconnu';
    }
  }

  getStatutValidationClass(statut: StatutValidationFournisseur): string {
    switch (statut) {
      case StatutValidationFournisseur.EnAttente:
        return 'status-pending';
      case StatutValidationFournisseur.Valide:
        return 'status-approved';
      case StatutValidationFournisseur.Rejete:
        return 'status-rejected';
      case StatutValidationFournisseur.Suspendu:
        return 'status-suspended';
      default:
        return 'status-unknown';
    }
  }

  formatDate(date: Date | string): string {
    const d = new Date(date);
    return d.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  extractFournisseurIdFromNotification(content: string): number | null {
    // Essayer d'extraire l'ID du fournisseur depuis le contenu de la notification
    const match = content.match(/Matricule fiscal\s*:\s*(\d+)/);
    if (match) {
      const matricule = match[1];
      const fournisseur = this.fournisseurs().find(f => f.matriculeFiscale === matricule);
      return fournisseur?.id || null;
    }
    return null;
  }

  viewFournisseurFromNotification(notification: NotificationDto): void {
    const fournisseurId = this.extractFournisseurIdFromNotification(notification.contenu);
    if (fournisseurId) {
      const fournisseur = this.fournisseurs().find(f => f.id === fournisseurId);
      if (fournisseur) {
        this.viewFournisseurDetails(fournisseur);
        this.markNotificationAsRead(notification);
      }
    }
  }

  // Expose enum for template
  StatutValidationFournisseur = StatutValidationFournisseur;
}
