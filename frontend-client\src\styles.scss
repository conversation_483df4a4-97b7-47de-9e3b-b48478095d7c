@use "node_modules/bootstrap/dist/css/bootstrap.min.css" as *;
@use "node_modules/@fortawesome/fontawesome-free/css/all.min.css" as *;
@use "@angular/material" as mat;

@import "node_modules/bootstrap/scss/functions";
@import "node_modules/bootstrap/scss/variables";
@import "node_modules/bootstrap/scss/mixins";
@import "primeicons/primeicons.css";

/* Light Mode */
:root {
  --background-color: #f4f9f4;
  --text-color: #2c3e50;
  --text-color-hover: #3498db;
  --text-color-background-hover: #ffffff;
  --primary-color: #3498db;
  --primary-color-hover: #5dade2;
  --secondary-color: #95a5a6;
  --secondary-color-hover: #bdc3c7;
  --accent-color: #e67e22;
  --accent-color-hover: #f39c12;
  --border-color: #dcdcdc;
  --card-background-color: #ffffff;
  --card-background-color-hover: #e5edf1e9;
  --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  --error-color: #e74c3c;
  --success-color: #27ae60;
  --toggle-icon-color: #f39c12;
  --overlay-color: rgba(255, 255, 255, 0.9);
  --gradient-primary: linear-gradient(135deg, #3498db, #5dade2);
}

/* Dark Mode */
[data-theme="dark"] {
  --background-color: #34495e;
  --text-color: #ecf0f1;
  --text-color-hover: #5dade2;
  --text-color-background-hover: #ffffff;
  --primary-color: #3498db;
  --primary-color-hover: #5dade2;
  --secondary-color: #95a5a6;
  --secondary-color-hover: #bdc3c7;
  --accent-color: #e67e22;
  --accent-color-hover: #f39c12;
  --border-color: #7f8c8d;
  --card-background-color: #2c3e50;
  --card-background-color-hover: #43596f;
  --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  --error-color: #e74c3c;
  --success-color: #27ae60;
  --toggle-icon-color: #ecf0f1;
  --overlay-color: rgba(52, 73, 94, 0.8);
  --gradient-primary: linear-gradient(135deg, #3498db, #5dade2);
}


.carousel-indicators {
  display: none !important;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  filter: invert(1); 
}

@import "node_modules/bootstrap/scss/bootstrap";

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: background-color 0.5s, color 0.5s;
}

* {
  scrollbar-width: thin;
  scrollbar-color: var(--accent-color) transparent;
}

*::-webkit-scrollbar {
  width: 6px;
}

*::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 3px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

/* Styles pour le datepicker Angular Material */
.mat-datepicker-popup {
  background-color: var(--card-background-color) !important;
  color: var(--text-color) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: var(--card-shadow) !important;
}

.mat-calendar {
  background-color: var(--card-background-color) !important;
  color: var(--text-color) !important;
}

.mat-calendar-header {
  background-color: var(--card-background-color) !important;
  color: var(--text-color) !important;
}

.mat-calendar-body-cell-content {
  background-color: var(--card-background-color) !important;
  color: var(--text-color) !important;
}

.mat-calendar-body-selected {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.mat-calendar-body-today:not(.mat-calendar-body-selected) {
  border-color: var(--primary-color) !important;
}

/* Assurer que les inputs de date ont un background opaque */
.mat-form-field input[matDatepicker] {
  background-color: var(--card-background-color) !important;
  color: var(--text-color) !important;
}

.mat-form-field-appearance-outline .mat-form-field-outline {
  background-color: var(--card-background-color) !important;
}
