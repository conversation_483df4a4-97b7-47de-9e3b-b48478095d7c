﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FavorisController : ControllerBase
    {
        private readonly IFavoriService _favoriService;

        public FavorisController(IFavoriService favoriService)
        {
            _favoriService = favoriService;
        }

        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<FavoriResponseDto>> AjouterFavori([FromBody] FavoriCreateDto dto)
        {
            try
            {
                var result = await _favoriService.AjouterFavoriAsync(dto);
                return CreatedAtAction(nameof(GetFavoriById), new { id = result.Id }, result);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<FavoriResponseDto>> GetFavoriById(int id)
        {
            var favori = await _favoriService.ObtenirFavoriAvecDetailsAsync(id);
            return favori != null ? Ok(favori) : NotFound();
        }

        [HttpGet("verifier")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<bool>> VerifierFavoriExiste(
           [FromQuery] int clientId,
           [FromQuery] int produitId)
        {
            return await _favoriService.VerifierFavoriExisteAsync(clientId, produitId);
        }
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SupprimerFavori(int id)
        {
            var success = await _favoriService.SupprimerFavoriAsync(id);
            return success ? NoContent() : NotFound();
        }

        [HttpGet("client/{clientId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<FavoriResponseDto>>> ListerParClient(int clientId)
        {
            var favoris = await _favoriService.ListerFavorisParClientAsync(clientId);
            return Ok(favoris);
        }
    }
}
