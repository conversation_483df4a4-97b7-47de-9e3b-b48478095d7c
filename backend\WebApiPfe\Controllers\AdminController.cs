﻿using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AdminController : ControllerBase
    {
        private readonly IAdminService _adminService;

        public AdminController(IAdminService adminService)
        {
            _adminService = adminService;
        }

        // 1. Gestion des utilisateurs
        [HttpGet("utilisateurs")]
        public async Task<IActionResult> GetUtilisateurs()
        {
            var utilisateurs = await _adminService.GetAllUtilisateursAsync();
            return Ok(utilisateurs);
        }

        [HttpPatch("utilisateurs/{id}/toggle")]
        public async Task<IActionResult> ToggleActivationUtilisateur(int id)
        {
            var result = await _adminService.ToggleActivationUtilisateurAsync(id);
            if (!result) return NotFound();
            return NoContent();
        }

        [HttpDelete("utilisateurs/{id}")]
        public async Task<IActionResult> SupprimerUtilisateur(int id)
        {
            var result = await _adminService.SupprimerUtilisateurAsync(id);
            if (!result) return NotFound();
            return NoContent();
        }

        // 2. Gestion des catégories
        [HttpPost("categories")]
        public async Task<IActionResult> AjouterCategorie(CategorieGestionDto dto)
        {
            var result = await _adminService.AjouterCategorieAsync(dto);
            return result ? Ok() : BadRequest();
        }

        [HttpPut("categories/{id}")]
        public async Task<IActionResult> ModifierCategorie(int id, CategorieGestionDto dto)
        {
            var result = await _adminService.ModifierCategorieAsync(id, dto);
            return result ? Ok() : NotFound();
        }

        [HttpDelete("categories/{id}")]
        public async Task<IActionResult> SupprimerCategorie(int id)
        {
            var result = await _adminService.SupprimerCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        [HttpPatch("categories/{id}/valider")]
        public async Task<IActionResult> ValiderCategorie(int id)
        {
            var result = await _adminService.ValiderCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        [HttpPatch("categories/{id}/refuser")]
        public async Task<IActionResult> RefuserCategorie(int id)
        {
            var result = await _adminService.RefuserCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        // 3. Gestion des sous-catégories
        [HttpPost("souscategories")]
        public async Task<IActionResult> AjouterSousCategorie(SousCategorieGestionDto dto)
        {
            var result = await _adminService.AjouterSousCategorieAsync(dto);
            return result ? Ok() : BadRequest();
        }

        [HttpPut("souscategories/{id}")]
        public async Task<IActionResult> ModifierSousCategorie(int id, SousCategorieGestionDto dto)
        {
            var result = await _adminService.ModifierSousCategorieAsync(id, dto);
            return result ? Ok() : NotFound();
        }

        [HttpDelete("souscategories/{id}")]
        public async Task<IActionResult> SupprimerSousCategorie(int id)
        {
            var result = await _adminService.SupprimerSousCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        [HttpPatch("souscategories/{id}/valider")]
        public async Task<IActionResult> ValiderSousCategorie(int id)
        {
            var result = await _adminService.ValidersousCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        [HttpPatch("souscategories/{id}/refuser")]
        public async Task<IActionResult> RefuserSousCategorie(int id)
        {
            var result = await _adminService.RefusersousCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        // 4. Commandes / Transactions
        [HttpGet("commandes")]
        public async Task<IActionResult> GetCommandes()
        {
            var commandes = await _adminService.GetCommandesAsync();
            return Ok(commandes);
        }

        [HttpPatch("commandes/{id}/annuler")]
        public async Task<IActionResult> AnnulerCommande(int id)
        {
            var result = await _adminService.AnnulerCommandeAsync(id);
            return result ? Ok() : NotFound();
        }

        // 5. Visibilité / Promotions
        [HttpPatch("produits/{produitId}/enavant")]
        public async Task<IActionResult> MettreEnAvantProduit(int produitId)
        {
            var result = await _adminService.MettreEnAvantProduitAsync(produitId);
            return result ? Ok() : NotFound();
        }
        // 6. Statistiques
        [HttpGet("statistiques")]
        public async Task<IActionResult> ObtenirStatistiques()
        {
            var stats = await _adminService.ObtenirStatistiquesGeneralesAsync();
            return Ok(stats);
        }
    }
}