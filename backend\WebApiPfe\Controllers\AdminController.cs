﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Models.Entity;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AdminController : ControllerBase
    {
        private readonly IAdminService _adminService;
        private readonly AppDbContext _context;
        private readonly INotificationService _notificationService;
        private readonly ILogger<AdminController> _logger;

        public AdminController(
            IAdminService adminService,
            AppDbContext context,
            INotificationService notificationService,
            ILogger<AdminController> logger)
        {
            _adminService = adminService;
            _context = context;
            _notificationService = notificationService;
            _logger = logger;
        }

        // 1. Gestion des utilisateurs
        [HttpGet("utilisateurs")]
        public async Task<IActionResult> GetUtilisateurs()
        {
            var utilisateurs = await _adminService.GetAllUtilisateursAsync();
            return Ok(utilisateurs);
        }

        [HttpPatch("utilisateurs/{id}/toggle")]
        public async Task<IActionResult> ToggleActivationUtilisateur(int id)
        {
            var result = await _adminService.ToggleActivationUtilisateurAsync(id);
            if (!result) return NotFound();
            return NoContent();
        }

        [HttpDelete("utilisateurs/{id}")]
        public async Task<IActionResult> SupprimerUtilisateur(int id)
        {
            var result = await _adminService.SupprimerUtilisateurAsync(id);
            if (!result) return NotFound();
            return NoContent();
        }

        // 2. Gestion des catégories
        [HttpPost("categories")]
        public async Task<IActionResult> AjouterCategorie(CategorieGestionDto dto)
        {
            var result = await _adminService.AjouterCategorieAsync(dto);
            return result ? Ok() : BadRequest();
        }

        [HttpPut("categories/{id}")]
        public async Task<IActionResult> ModifierCategorie(int id, CategorieGestionDto dto)
        {
            var result = await _adminService.ModifierCategorieAsync(id, dto);
            return result ? Ok() : NotFound();
        }

        [HttpDelete("categories/{id}")]
        public async Task<IActionResult> SupprimerCategorie(int id)
        {
            var result = await _adminService.SupprimerCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        [HttpPatch("categories/{id}/valider")]
        public async Task<IActionResult> ValiderCategorie(int id)
        {
            var result = await _adminService.ValiderCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        [HttpPatch("categories/{id}/refuser")]
        public async Task<IActionResult> RefuserCategorie(int id)
        {
            var result = await _adminService.RefuserCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        // 3. Gestion des sous-catégories
        [HttpPost("souscategories")]
        public async Task<IActionResult> AjouterSousCategorie(SousCategorieGestionDto dto)
        {
            var result = await _adminService.AjouterSousCategorieAsync(dto);
            return result ? Ok() : BadRequest();
        }

        [HttpPut("souscategories/{id}")]
        public async Task<IActionResult> ModifierSousCategorie(int id, SousCategorieGestionDto dto)
        {
            var result = await _adminService.ModifierSousCategorieAsync(id, dto);
            return result ? Ok() : NotFound();
        }

        [HttpDelete("souscategories/{id}")]
        public async Task<IActionResult> SupprimerSousCategorie(int id)
        {
            var result = await _adminService.SupprimerSousCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        [HttpPatch("souscategories/{id}/valider")]
        public async Task<IActionResult> ValiderSousCategorie(int id)
        {
            var result = await _adminService.ValidersousCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        [HttpPatch("souscategories/{id}/refuser")]
        public async Task<IActionResult> RefuserSousCategorie(int id)
        {
            var result = await _adminService.RefusersousCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        // 4. Commandes / Transactions
        [HttpGet("commandes")]
        public async Task<IActionResult> GetCommandes()
        {
            var commandes = await _adminService.GetCommandesAsync();
            var response = new
            {
                success = true,
                data = commandes,
                total = commandes.Count(),
                message = "Commandes récupérées avec succès"
            };
            return Ok(response);
        }

        [HttpPatch("commandes/{id}/annuler")]
        public async Task<IActionResult> AnnulerCommande(int id)
        {
            var result = await _adminService.AnnulerCommandeAsync(id);
            return result ? Ok() : NotFound();
        }

        // 5. Visibilité / Promotions
        [HttpPatch("produits/{produitId}/enavant")]
        public async Task<IActionResult> MettreEnAvantProduit(int produitId)
        {
            var result = await _adminService.MettreEnAvantProduitAsync(produitId);
            return result ? Ok() : NotFound();
        }
        // 6. Statistiques
        [HttpGet("statistiques")]
        public async Task<IActionResult> ObtenirStatistiques()
        {
            var stats = await _adminService.ObtenirStatistiquesGeneralesAsync();
            return Ok(stats);
        }

        // 7. Gestion des fournisseurs
        [HttpGet("fournisseurs")]
        public async Task<ActionResult<List<FournisseurValidationDto>>> GetAllFournisseurs()
        {
            try
            {
                var fournisseurs = await _context.Fournisseurs
                    .Include(f => f.Adresses)
                    .Select(f => new FournisseurValidationDto
                    {
                        Id = f.Id,
                        Email = f.Email,
                        Nom = f.Nom,
                        Prenom = f.Prenom,
                        PhoneNumber = f.PhoneNumber,
                        MatriculeFiscale = f.MatriculeFiscale,
                        RaisonSociale = f.RaisonSociale,
                        Description = f.Description,
                        RIB = f.RIB,
                        CodeBanque = f.CodeBanque,
                        Commission = f.Commission,
                        DelaiPreparationJours = f.DelaiPreparationJours,
                        FraisLivraisonBase = f.FraisLivraisonBase,
                        LogoFile = f.LogoFile,
                        StatutValidation = f.StatutValidation,
                        DateInscription = f.DateInscription,
                        DateValidation = f.DateValidation,
                        ValidePar = f.ValidePar,
                        CommentaireValidation = f.CommentaireValidation,
                        Adresses = f.Adresses.Select(a => new AdresseDto
                        {
                            Id = a.Id,
                            Rue = a.Rue,
                            Ville = a.Ville,
                            CodePostal = a.CodePostal,
                            Pays = a.Pays,
                            EstPrincipale = a.EstPrincipale
                        }).ToList()
                    })
                    .ToListAsync();

                return Ok(fournisseurs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des fournisseurs");
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        [HttpGet("fournisseurs/en-attente")]
        public async Task<ActionResult<List<FournisseurValidationDto>>> GetFournisseursEnAttente()
        {
            try
            {
                var fournisseurs = await _context.Fournisseurs
                    .Include(f => f.Adresses)
                    .Where(f => f.StatutValidation == StatutValidationFournisseur.EnAttente)
                    .Select(f => new FournisseurValidationDto
                    {
                        Id = f.Id,
                        Email = f.Email,
                        Nom = f.Nom,
                        Prenom = f.Prenom,
                        PhoneNumber = f.PhoneNumber,
                        MatriculeFiscale = f.MatriculeFiscale,
                        RaisonSociale = f.RaisonSociale,
                        Description = f.Description,
                        RIB = f.RIB,
                        CodeBanque = f.CodeBanque,
                        Commission = f.Commission,
                        DelaiPreparationJours = f.DelaiPreparationJours,
                        FraisLivraisonBase = f.FraisLivraisonBase,
                        LogoFile = f.LogoFile,
                        StatutValidation = f.StatutValidation,
                        DateInscription = f.DateInscription,
                        DateValidation = f.DateValidation,
                        ValidePar = f.ValidePar,
                        CommentaireValidation = f.CommentaireValidation,
                        Adresses = f.Adresses.Select(a => new AdresseDto
                        {
                            Id = a.Id,
                            Rue = a.Rue,
                            Ville = a.Ville,
                            CodePostal = a.CodePostal,
                            Pays = a.Pays,
                            EstPrincipale = a.EstPrincipale
                        }).ToList()
                    })
                    .ToListAsync();

                return Ok(fournisseurs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des fournisseurs en attente");
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        [HttpGet("fournisseurs/{id}")]
        public async Task<ActionResult<FournisseurValidationDto>> GetFournisseurById(int id)
        {
            try
            {
                var fournisseur = await _context.Fournisseurs
                    .Include(f => f.Adresses)
                    .Where(f => f.Id == id)
                    .Select(f => new FournisseurValidationDto
                    {
                        Id = f.Id,
                        Email = f.Email,
                        Nom = f.Nom,
                        Prenom = f.Prenom,
                        PhoneNumber = f.PhoneNumber,
                        MatriculeFiscale = f.MatriculeFiscale,
                        RaisonSociale = f.RaisonSociale,
                        Description = f.Description,
                        RIB = f.RIB,
                        CodeBanque = f.CodeBanque,
                        Commission = f.Commission,
                        DelaiPreparationJours = f.DelaiPreparationJours,
                        FraisLivraisonBase = f.FraisLivraisonBase,
                        LogoFile = f.LogoFile,
                        StatutValidation = f.StatutValidation,
                        DateInscription = f.DateInscription,
                        DateValidation = f.DateValidation,
                        ValidePar = f.ValidePar,
                        CommentaireValidation = f.CommentaireValidation,
                        Adresses = f.Adresses.Select(a => new AdresseDto
                        {
                            Id = a.Id,
                            Rue = a.Rue,
                            Ville = a.Ville,
                            CodePostal = a.CodePostal,
                            Pays = a.Pays,
                            EstPrincipale = a.EstPrincipale
                        }).ToList()
                    })
                    .FirstOrDefaultAsync();

                if (fournisseur == null)
                {
                    return NotFound("Fournisseur introuvable");
                }

                return Ok(fournisseur);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération du fournisseur {Id}", id);
                return StatusCode(500, "Erreur interne du serveur");
            }
        }

        [HttpPost("fournisseurs/valider")]
        public async Task<IActionResult> ValiderFournisseur([FromBody] ValiderFournisseurRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var fournisseur = await _context.Fournisseurs.FindAsync(request.FournisseurId);
                if (fournisseur == null)
                {
                    return NotFound("Fournisseur introuvable");
                }

                if (fournisseur.StatutValidation != StatutValidationFournisseur.EnAttente)
                {
                    return BadRequest("Ce fournisseur a déjà été traité");
                }

                // Récupérer l'ID de l'admin connecté
                var adminIdClaim = User.FindFirst("id")?.Value;
                if (!int.TryParse(adminIdClaim, out int adminId))
                {
                    return Unauthorized("Impossible d'identifier l'administrateur");
                }

                // Mettre à jour le statut du fournisseur
                fournisseur.StatutValidation = request.Accepter
                    ? StatutValidationFournisseur.Valide
                    : StatutValidationFournisseur.Rejete;
                fournisseur.DateValidation = DateTime.UtcNow;
                fournisseur.ValidePar = adminId;
                fournisseur.CommentaireValidation = request.Commentaire;

                await _context.SaveChangesAsync();

                // Envoyer une notification au fournisseur
                var action = request.Accepter ? "validé" : "rejeté";
                var contenuNotification = $"🏪 STATUT DE VOTRE COMPTE FOURNISSEUR\n\n" +
                    $"Votre demande d'inscription en tant que fournisseur a été {action}.\n\n" +
                    $"📋 Détails :\n" +
                    $"• Raison sociale : {fournisseur.RaisonSociale}\n" +
                    $"• Date de traitement : {DateTime.Now:dd/MM/yyyy HH:mm}\n";

                if (!string.IsNullOrEmpty(request.Commentaire))
                {
                    contenuNotification += $"• Commentaire : {request.Commentaire}\n";
                }

                if (request.Accepter)
                {
                    contenuNotification += "\n✅ Félicitations ! Vous pouvez maintenant accéder à votre espace fournisseur.";
                }
                else
                {
                    contenuNotification += "\n❌ Votre demande a été rejetée. Vous pouvez nous contacter pour plus d'informations.";
                }

                await _notificationService.CreateAsync(new CreateNotificationDto
                {
                    Contenu = contenuNotification,
                    UtilisateurId = fournisseur.Id
                });

                _logger.LogInformation("Fournisseur {FournisseurId} {Action} par l'admin {AdminId}",
                    request.FournisseurId, action, adminId);

                return Ok(new { Message = $"Fournisseur {action} avec succès" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la validation du fournisseur {FournisseurId}", request.FournisseurId);
                return StatusCode(500, "Erreur interne du serveur");
            }
        }
    }
}