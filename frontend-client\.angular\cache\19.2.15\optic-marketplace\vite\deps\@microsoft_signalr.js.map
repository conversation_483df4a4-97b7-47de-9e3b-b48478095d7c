{"version": 3, "sources": ["../../../../../../node_modules/@microsoft/signalr/dist/esm/Errors.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/HttpClient.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/ILogger.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/Loggers.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/Utils.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/FetchHttpClient.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/XhrHttpClient.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/DefaultHttpClient.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/TextMessageFormat.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/HandshakeProtocol.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/IHubProtocol.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/Subject.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/MessageBuffer.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/HubConnection.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/DefaultReconnectPolicy.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/HeaderNames.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/AccessTokenHttpClient.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/ITransport.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/AbortController.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/LongPollingTransport.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/ServerSentEventsTransport.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/WebSocketTransport.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/HttpConnection.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/JsonHubProtocol.js", "../../../../../../node_modules/@microsoft/signalr/dist/esm/HubConnectionBuilder.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n/** Error thrown when an HTTP request fails. */\nexport class HttpError extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.HttpError}.\r\n   *\r\n   * @param {string} errorMessage A descriptive error message.\r\n   * @param {number} statusCode The HTTP status code represented by this error.\r\n   */\n  constructor(errorMessage, statusCode) {\n    const trueProto = new.target.prototype;\n    super(`${errorMessage}: Status code '${statusCode}'`);\n    this.statusCode = statusCode;\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when a timeout elapses. */\nexport class TimeoutError extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.TimeoutError}.\r\n   *\r\n   * @param {string} errorMessage A descriptive error message.\r\n   */\n  constructor(errorMessage = \"A timeout occurred.\") {\n    const trueProto = new.target.prototype;\n    super(errorMessage);\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when an action is aborted. */\nexport class AbortError extends Error {\n  /** Constructs a new instance of {@link AbortError}.\r\n   *\r\n   * @param {string} errorMessage A descriptive error message.\r\n   */\n  constructor(errorMessage = \"An abort occurred.\") {\n    const trueProto = new.target.prototype;\n    super(errorMessage);\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when the selected transport is unsupported by the browser. */\n/** @private */\nexport class UnsupportedTransportError extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.UnsupportedTransportError}.\r\n   *\r\n   * @param {string} message A descriptive error message.\r\n   * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n   */\n  constructor(message, transport) {\n    const trueProto = new.target.prototype;\n    super(message);\n    this.transport = transport;\n    this.errorType = 'UnsupportedTransportError';\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when the selected transport is disabled by the browser. */\n/** @private */\nexport class DisabledTransportError extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.DisabledTransportError}.\r\n   *\r\n   * @param {string} message A descriptive error message.\r\n   * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n   */\n  constructor(message, transport) {\n    const trueProto = new.target.prototype;\n    super(message);\n    this.transport = transport;\n    this.errorType = 'DisabledTransportError';\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when the selected transport cannot be started. */\n/** @private */\nexport class FailedToStartTransportError extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.FailedToStartTransportError}.\r\n   *\r\n   * @param {string} message A descriptive error message.\r\n   * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n   */\n  constructor(message, transport) {\n    const trueProto = new.target.prototype;\n    super(message);\n    this.transport = transport;\n    this.errorType = 'FailedToStartTransportError';\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when the negotiation with the server failed to complete. */\n/** @private */\nexport class FailedToNegotiateWithServerError extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.FailedToNegotiateWithServerError}.\r\n   *\r\n   * @param {string} message A descriptive error message.\r\n   */\n  constructor(message) {\n    const trueProto = new.target.prototype;\n    super(message);\n    this.errorType = 'FailedToNegotiateWithServerError';\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n/** Error thrown when multiple errors have occurred. */\n/** @private */\nexport class AggregateErrors extends Error {\n  /** Constructs a new instance of {@link @microsoft/signalr.AggregateErrors}.\r\n   *\r\n   * @param {string} message A descriptive error message.\r\n   * @param {Error[]} innerErrors The collection of errors this error is aggregating.\r\n   */\n  constructor(message, innerErrors) {\n    const trueProto = new.target.prototype;\n    super(message);\n    this.innerErrors = innerErrors;\n    // Workaround issue in Typescript compiler\n    // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\n    this.__proto__ = trueProto;\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n/** Represents an HTTP response. */\nexport class HttpResponse {\n  constructor(statusCode, statusText, content) {\n    this.statusCode = statusCode;\n    this.statusText = statusText;\n    this.content = content;\n  }\n}\n/** Abstraction over an HTTP client.\r\n *\r\n * This class provides an abstraction over an HTTP client so that a different implementation can be provided on different platforms.\r\n */\nexport class HttpClient {\n  get(url, options) {\n    return this.send({\n      ...options,\n      method: \"GET\",\n      url\n    });\n  }\n  post(url, options) {\n    return this.send({\n      ...options,\n      method: \"POST\",\n      url\n    });\n  }\n  delete(url, options) {\n    return this.send({\n      ...options,\n      method: \"DELETE\",\n      url\n    });\n  }\n  /** Gets all cookies that apply to the specified URL.\r\n   *\r\n   * @param url The URL that the cookies are valid for.\r\n   * @returns {string} A string containing all the key-value cookie pairs for the specified URL.\r\n   */\n  // @ts-ignore\n  getCookieString(url) {\n    return \"\";\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n// These values are designed to match the ASP.NET Log Levels since that's the pattern we're emulating here.\n/** Indicates the severity of a log message.\r\n *\r\n * Log Levels are ordered in increasing severity. So `Debug` is more severe than `Trace`, etc.\r\n */\nexport var LogLevel;\n(function (LogLevel) {\n  /** Log level for very low severity diagnostic messages. */\n  LogLevel[LogLevel[\"Trace\"] = 0] = \"Trace\";\n  /** Log level for low severity diagnostic messages. */\n  LogLevel[LogLevel[\"Debug\"] = 1] = \"Debug\";\n  /** Log level for informational diagnostic messages. */\n  LogLevel[LogLevel[\"Information\"] = 2] = \"Information\";\n  /** Log level for diagnostic messages that indicate a non-fatal problem. */\n  LogLevel[LogLevel[\"Warning\"] = 3] = \"Warning\";\n  /** Log level for diagnostic messages that indicate a failure in the current operation. */\n  LogLevel[LogLevel[\"Error\"] = 4] = \"Error\";\n  /** Log level for diagnostic messages that indicate a failure that will terminate the entire application. */\n  LogLevel[LogLevel[\"Critical\"] = 5] = \"Critical\";\n  /** The highest possible log level. Used when configuring logging to indicate that no log messages should be emitted. */\n  LogLevel[LogLevel[\"None\"] = 6] = \"None\";\n})(LogLevel || (LogLevel = {}));\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n/** A logger that does nothing when log messages are sent to it. */\nexport class NullLogger {\n  constructor() {}\n  /** @inheritDoc */\n  // eslint-disable-next-line\n  log(_logLevel, _message) {}\n}\n/** The singleton instance of the {@link @microsoft/signalr.NullLogger}. */\nNullLogger.instance = new NullLogger();\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { LogLevel } from \"./ILogger\";\nimport { NullLogger } from \"./Loggers\";\n// Version token that will be replaced by the prepack command\n/** The version of the SignalR client. */\nexport const VERSION = \"8.0.7\";\n/** @private */\nexport class Arg {\n  static isRequired(val, name) {\n    if (val === null || val === undefined) {\n      throw new Error(`The '${name}' argument is required.`);\n    }\n  }\n  static isNotEmpty(val, name) {\n    if (!val || val.match(/^\\s*$/)) {\n      throw new Error(`The '${name}' argument should not be empty.`);\n    }\n  }\n  static isIn(val, values, name) {\n    // TypeScript enums have keys for **both** the name and the value of each enum member on the type itself.\n    if (!(val in values)) {\n      throw new Error(`Unknown ${name} value: ${val}.`);\n    }\n  }\n}\n/** @private */\nexport class Platform {\n  // react-native has a window but no document so we should check both\n  static get isBrowser() {\n    return !Platform.isNode && typeof window === \"object\" && typeof window.document === \"object\";\n  }\n  // WebWorkers don't have a window object so the isBrowser check would fail\n  static get isWebWorker() {\n    return !Platform.isNode && typeof self === \"object\" && \"importScripts\" in self;\n  }\n  // react-native has a window but no document\n  static get isReactNative() {\n    return !Platform.isNode && typeof window === \"object\" && typeof window.document === \"undefined\";\n  }\n  // Node apps shouldn't have a window object, but WebWorkers don't either\n  // so we need to check for both WebWorker and window\n  static get isNode() {\n    return typeof process !== \"undefined\" && process.release && process.release.name === \"node\";\n  }\n}\n/** @private */\nexport function getDataDetail(data, includeContent) {\n  let detail = \"\";\n  if (isArrayBuffer(data)) {\n    detail = `Binary data of length ${data.byteLength}`;\n    if (includeContent) {\n      detail += `. Content: '${formatArrayBuffer(data)}'`;\n    }\n  } else if (typeof data === \"string\") {\n    detail = `String data of length ${data.length}`;\n    if (includeContent) {\n      detail += `. Content: '${data}'`;\n    }\n  }\n  return detail;\n}\n/** @private */\nexport function formatArrayBuffer(data) {\n  const view = new Uint8Array(data);\n  // Uint8Array.map only supports returning another Uint8Array?\n  let str = \"\";\n  view.forEach(num => {\n    const pad = num < 16 ? \"0\" : \"\";\n    str += `0x${pad}${num.toString(16)} `;\n  });\n  // Trim of trailing space.\n  return str.substr(0, str.length - 1);\n}\n// Also in signalr-protocol-msgpack/Utils.ts\n/** @private */\nexport function isArrayBuffer(val) {\n  return val && typeof ArrayBuffer !== \"undefined\" && (val instanceof ArrayBuffer ||\n  // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\n  val.constructor && val.constructor.name === \"ArrayBuffer\");\n}\n/** @private */\nexport async function sendMessage(logger, transportName, httpClient, url, content, options) {\n  const headers = {};\n  const [name, value] = getUserAgentHeader();\n  headers[name] = value;\n  logger.log(LogLevel.Trace, `(${transportName} transport) sending data. ${getDataDetail(content, options.logMessageContent)}.`);\n  const responseType = isArrayBuffer(content) ? \"arraybuffer\" : \"text\";\n  const response = await httpClient.post(url, {\n    content,\n    headers: {\n      ...headers,\n      ...options.headers\n    },\n    responseType,\n    timeout: options.timeout,\n    withCredentials: options.withCredentials\n  });\n  logger.log(LogLevel.Trace, `(${transportName} transport) request complete. Response status: ${response.statusCode}.`);\n}\n/** @private */\nexport function createLogger(logger) {\n  if (logger === undefined) {\n    return new ConsoleLogger(LogLevel.Information);\n  }\n  if (logger === null) {\n    return NullLogger.instance;\n  }\n  if (logger.log !== undefined) {\n    return logger;\n  }\n  return new ConsoleLogger(logger);\n}\n/** @private */\nexport class SubjectSubscription {\n  constructor(subject, observer) {\n    this._subject = subject;\n    this._observer = observer;\n  }\n  dispose() {\n    const index = this._subject.observers.indexOf(this._observer);\n    if (index > -1) {\n      this._subject.observers.splice(index, 1);\n    }\n    if (this._subject.observers.length === 0 && this._subject.cancelCallback) {\n      this._subject.cancelCallback().catch(_ => {});\n    }\n  }\n}\n/** @private */\nexport class ConsoleLogger {\n  constructor(minimumLogLevel) {\n    this._minLevel = minimumLogLevel;\n    this.out = console;\n  }\n  log(logLevel, message) {\n    if (logLevel >= this._minLevel) {\n      const msg = `[${new Date().toISOString()}] ${LogLevel[logLevel]}: ${message}`;\n      switch (logLevel) {\n        case LogLevel.Critical:\n        case LogLevel.Error:\n          this.out.error(msg);\n          break;\n        case LogLevel.Warning:\n          this.out.warn(msg);\n          break;\n        case LogLevel.Information:\n          this.out.info(msg);\n          break;\n        default:\n          // console.debug only goes to attached debuggers in Node, so we use console.log for Trace and Debug\n          this.out.log(msg);\n          break;\n      }\n    }\n  }\n}\n/** @private */\nexport function getUserAgentHeader() {\n  let userAgentHeaderName = \"X-SignalR-User-Agent\";\n  if (Platform.isNode) {\n    userAgentHeaderName = \"User-Agent\";\n  }\n  return [userAgentHeaderName, constructUserAgent(VERSION, getOsName(), getRuntime(), getRuntimeVersion())];\n}\n/** @private */\nexport function constructUserAgent(version, os, runtime, runtimeVersion) {\n  // Microsoft SignalR/[Version] ([Detailed Version]; [Operating System]; [Runtime]; [Runtime Version])\n  let userAgent = \"Microsoft SignalR/\";\n  const majorAndMinor = version.split(\".\");\n  userAgent += `${majorAndMinor[0]}.${majorAndMinor[1]}`;\n  userAgent += ` (${version}; `;\n  if (os && os !== \"\") {\n    userAgent += `${os}; `;\n  } else {\n    userAgent += \"Unknown OS; \";\n  }\n  userAgent += `${runtime}`;\n  if (runtimeVersion) {\n    userAgent += `; ${runtimeVersion}`;\n  } else {\n    userAgent += \"; Unknown Runtime Version\";\n  }\n  userAgent += \")\";\n  return userAgent;\n}\n// eslint-disable-next-line spaced-comment\n/*#__PURE__*/\nfunction getOsName() {\n  if (Platform.isNode) {\n    switch (process.platform) {\n      case \"win32\":\n        return \"Windows NT\";\n      case \"darwin\":\n        return \"macOS\";\n      case \"linux\":\n        return \"Linux\";\n      default:\n        return process.platform;\n    }\n  } else {\n    return \"\";\n  }\n}\n// eslint-disable-next-line spaced-comment\n/*#__PURE__*/\nfunction getRuntimeVersion() {\n  if (Platform.isNode) {\n    return process.versions.node;\n  }\n  return undefined;\n}\nfunction getRuntime() {\n  if (Platform.isNode) {\n    return \"NodeJS\";\n  } else {\n    return \"Browser\";\n  }\n}\n/** @private */\nexport function getErrorString(e) {\n  if (e.stack) {\n    return e.stack;\n  } else if (e.message) {\n    return e.message;\n  }\n  return `${e}`;\n}\n/** @private */\nexport function getGlobalThis() {\n  // globalThis is semi-new and not available in Node until v12\n  if (typeof globalThis !== \"undefined\") {\n    return globalThis;\n  }\n  if (typeof self !== \"undefined\") {\n    return self;\n  }\n  if (typeof window !== \"undefined\") {\n    return window;\n  }\n  if (typeof global !== \"undefined\") {\n    return global;\n  }\n  throw new Error(\"could not find global\");\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\nimport { HttpClient, HttpResponse } from \"./HttpClient\";\nimport { LogLevel } from \"./ILogger\";\nimport { Platform, getGlobalThis, isArrayBuffer } from \"./Utils\";\nexport class FetchHttpClient extends HttpClient {\n  constructor(logger) {\n    super();\n    this._logger = logger;\n    // Node added a fetch implementation to the global scope starting in v18.\n    // We need to add a cookie jar in node to be able to share cookies with WebSocket\n    if (typeof fetch === \"undefined\" || Platform.isNode) {\n      // In order to ignore the dynamic require in webpack builds we need to do this magic\n      // @ts-ignore: TS doesn't know about these names\n      const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\n      // Cookies aren't automatically handled in Node so we need to add a CookieJar to preserve cookies across requests\n      this._jar = new (requireFunc(\"tough-cookie\").CookieJar)();\n      if (typeof fetch === \"undefined\") {\n        this._fetchType = requireFunc(\"node-fetch\");\n      } else {\n        // Use fetch from Node if available\n        this._fetchType = fetch;\n      }\n      // node-fetch doesn't have a nice API for getting and setting cookies\n      // fetch-cookie will wrap a fetch implementation with a default CookieJar or a provided one\n      this._fetchType = requireFunc(\"fetch-cookie\")(this._fetchType, this._jar);\n    } else {\n      this._fetchType = fetch.bind(getGlobalThis());\n    }\n    if (typeof AbortController === \"undefined\") {\n      // In order to ignore the dynamic require in webpack builds we need to do this magic\n      // @ts-ignore: TS doesn't know about these names\n      const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\n      // Node needs EventListener methods on AbortController which our custom polyfill doesn't provide\n      this._abortControllerType = requireFunc(\"abort-controller\");\n    } else {\n      this._abortControllerType = AbortController;\n    }\n  }\n  /** @inheritDoc */\n  async send(request) {\n    // Check that abort was not signaled before calling send\n    if (request.abortSignal && request.abortSignal.aborted) {\n      throw new AbortError();\n    }\n    if (!request.method) {\n      throw new Error(\"No method defined.\");\n    }\n    if (!request.url) {\n      throw new Error(\"No url defined.\");\n    }\n    const abortController = new this._abortControllerType();\n    let error;\n    // Hook our abortSignal into the abort controller\n    if (request.abortSignal) {\n      request.abortSignal.onabort = () => {\n        abortController.abort();\n        error = new AbortError();\n      };\n    }\n    // If a timeout has been passed in, setup a timeout to call abort\n    // Type needs to be any to fit window.setTimeout and NodeJS.setTimeout\n    let timeoutId = null;\n    if (request.timeout) {\n      const msTimeout = request.timeout;\n      timeoutId = setTimeout(() => {\n        abortController.abort();\n        this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\n        error = new TimeoutError();\n      }, msTimeout);\n    }\n    if (request.content === \"\") {\n      request.content = undefined;\n    }\n    if (request.content) {\n      // Explicitly setting the Content-Type header for React Native on Android platform.\n      request.headers = request.headers || {};\n      if (isArrayBuffer(request.content)) {\n        request.headers[\"Content-Type\"] = \"application/octet-stream\";\n      } else {\n        request.headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\n      }\n    }\n    let response;\n    try {\n      response = await this._fetchType(request.url, {\n        body: request.content,\n        cache: \"no-cache\",\n        credentials: request.withCredentials === true ? \"include\" : \"same-origin\",\n        headers: {\n          \"X-Requested-With\": \"XMLHttpRequest\",\n          ...request.headers\n        },\n        method: request.method,\n        mode: \"cors\",\n        redirect: \"follow\",\n        signal: abortController.signal\n      });\n    } catch (e) {\n      if (error) {\n        throw error;\n      }\n      this._logger.log(LogLevel.Warning, `Error from HTTP request. ${e}.`);\n      throw e;\n    } finally {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n      if (request.abortSignal) {\n        request.abortSignal.onabort = null;\n      }\n    }\n    if (!response.ok) {\n      const errorMessage = await deserializeContent(response, \"text\");\n      throw new HttpError(errorMessage || response.statusText, response.status);\n    }\n    const content = deserializeContent(response, request.responseType);\n    const payload = await content;\n    return new HttpResponse(response.status, response.statusText, payload);\n  }\n  getCookieString(url) {\n    let cookies = \"\";\n    if (Platform.isNode && this._jar) {\n      // @ts-ignore: unused variable\n      this._jar.getCookies(url, (e, c) => cookies = c.join(\"; \"));\n    }\n    return cookies;\n  }\n}\nfunction deserializeContent(response, responseType) {\n  let content;\n  switch (responseType) {\n    case \"arraybuffer\":\n      content = response.arrayBuffer();\n      break;\n    case \"text\":\n      content = response.text();\n      break;\n    case \"blob\":\n    case \"document\":\n    case \"json\":\n      throw new Error(`${responseType} is not supported.`);\n    default:\n      content = response.text();\n      break;\n  }\n  return content;\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\nimport { HttpClient, HttpResponse } from \"./HttpClient\";\nimport { LogLevel } from \"./ILogger\";\nimport { isArrayBuffer } from \"./Utils\";\nexport class XhrHttpClient extends HttpClient {\n  constructor(logger) {\n    super();\n    this._logger = logger;\n  }\n  /** @inheritDoc */\n  send(request) {\n    // Check that abort was not signaled before calling send\n    if (request.abortSignal && request.abortSignal.aborted) {\n      return Promise.reject(new AbortError());\n    }\n    if (!request.method) {\n      return Promise.reject(new Error(\"No method defined.\"));\n    }\n    if (!request.url) {\n      return Promise.reject(new Error(\"No url defined.\"));\n    }\n    return new Promise((resolve, reject) => {\n      const xhr = new XMLHttpRequest();\n      xhr.open(request.method, request.url, true);\n      xhr.withCredentials = request.withCredentials === undefined ? true : request.withCredentials;\n      xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\n      if (request.content === \"\") {\n        request.content = undefined;\n      }\n      if (request.content) {\n        // Explicitly setting the Content-Type header for React Native on Android platform.\n        if (isArrayBuffer(request.content)) {\n          xhr.setRequestHeader(\"Content-Type\", \"application/octet-stream\");\n        } else {\n          xhr.setRequestHeader(\"Content-Type\", \"text/plain;charset=UTF-8\");\n        }\n      }\n      const headers = request.headers;\n      if (headers) {\n        Object.keys(headers).forEach(header => {\n          xhr.setRequestHeader(header, headers[header]);\n        });\n      }\n      if (request.responseType) {\n        xhr.responseType = request.responseType;\n      }\n      if (request.abortSignal) {\n        request.abortSignal.onabort = () => {\n          xhr.abort();\n          reject(new AbortError());\n        };\n      }\n      if (request.timeout) {\n        xhr.timeout = request.timeout;\n      }\n      xhr.onload = () => {\n        if (request.abortSignal) {\n          request.abortSignal.onabort = null;\n        }\n        if (xhr.status >= 200 && xhr.status < 300) {\n          resolve(new HttpResponse(xhr.status, xhr.statusText, xhr.response || xhr.responseText));\n        } else {\n          reject(new HttpError(xhr.response || xhr.responseText || xhr.statusText, xhr.status));\n        }\n      };\n      xhr.onerror = () => {\n        this._logger.log(LogLevel.Warning, `Error from HTTP request. ${xhr.status}: ${xhr.statusText}.`);\n        reject(new HttpError(xhr.statusText, xhr.status));\n      };\n      xhr.ontimeout = () => {\n        this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\n        reject(new TimeoutError());\n      };\n      xhr.send(request.content);\n    });\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { AbortError } from \"./Errors\";\nimport { FetchHttpClient } from \"./FetchHttpClient\";\nimport { HttpClient } from \"./HttpClient\";\nimport { Platform } from \"./Utils\";\nimport { XhrHttpClient } from \"./XhrHttpClient\";\n/** Default implementation of {@link @microsoft/signalr.HttpClient}. */\nexport class DefaultHttpClient extends HttpClient {\n  /** Creates a new instance of the {@link @microsoft/signalr.DefaultHttpClient}, using the provided {@link @microsoft/signalr.ILogger} to log messages. */\n  constructor(logger) {\n    super();\n    if (typeof fetch !== \"undefined\" || Platform.isNode) {\n      this._httpClient = new FetchHttpClient(logger);\n    } else if (typeof XMLHttpRequest !== \"undefined\") {\n      this._httpClient = new XhrHttpClient(logger);\n    } else {\n      throw new Error(\"No usable HttpClient found.\");\n    }\n  }\n  /** @inheritDoc */\n  send(request) {\n    // Check that abort was not signaled before calling send\n    if (request.abortSignal && request.abortSignal.aborted) {\n      return Promise.reject(new AbortError());\n    }\n    if (!request.method) {\n      return Promise.reject(new Error(\"No method defined.\"));\n    }\n    if (!request.url) {\n      return Promise.reject(new Error(\"No url defined.\"));\n    }\n    return this._httpClient.send(request);\n  }\n  getCookieString(url) {\n    return this._httpClient.getCookieString(url);\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n// Not exported from index\n/** @private */\nexport class TextMessageFormat {\n  static write(output) {\n    return `${output}${TextMessageFormat.RecordSeparator}`;\n  }\n  static parse(input) {\n    if (input[input.length - 1] !== TextMessageFormat.RecordSeparator) {\n      throw new Error(\"Message is incomplete.\");\n    }\n    const messages = input.split(TextMessageFormat.RecordSeparator);\n    messages.pop();\n    return messages;\n  }\n}\nTextMessageFormat.RecordSeparatorCode = 0x1e;\nTextMessageFormat.RecordSeparator = String.fromCharCode(TextMessageFormat.RecordSeparatorCode);\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { TextMessageFormat } from \"./TextMessageFormat\";\nimport { isArrayBuffer } from \"./Utils\";\n/** @private */\nexport class HandshakeProtocol {\n  // Handshake request is always JSON\n  writeHandshakeRequest(handshakeRequest) {\n    return TextMessageFormat.write(JSON.stringify(handshakeRequest));\n  }\n  parseHandshakeResponse(data) {\n    let messageData;\n    let remainingData;\n    if (isArrayBuffer(data)) {\n      // Format is binary but still need to read JSON text from handshake response\n      const binaryData = new Uint8Array(data);\n      const separatorIndex = binaryData.indexOf(TextMessageFormat.RecordSeparatorCode);\n      if (separatorIndex === -1) {\n        throw new Error(\"Message is incomplete.\");\n      }\n      // content before separator is handshake response\n      // optional content after is additional messages\n      const responseLength = separatorIndex + 1;\n      messageData = String.fromCharCode.apply(null, Array.prototype.slice.call(binaryData.slice(0, responseLength)));\n      remainingData = binaryData.byteLength > responseLength ? binaryData.slice(responseLength).buffer : null;\n    } else {\n      const textData = data;\n      const separatorIndex = textData.indexOf(TextMessageFormat.RecordSeparator);\n      if (separatorIndex === -1) {\n        throw new Error(\"Message is incomplete.\");\n      }\n      // content before separator is handshake response\n      // optional content after is additional messages\n      const responseLength = separatorIndex + 1;\n      messageData = textData.substring(0, responseLength);\n      remainingData = textData.length > responseLength ? textData.substring(responseLength) : null;\n    }\n    // At this point we should have just the single handshake message\n    const messages = TextMessageFormat.parse(messageData);\n    const response = JSON.parse(messages[0]);\n    if (response.type) {\n      throw new Error(\"Expected a handshake response from the server.\");\n    }\n    const responseMessage = response;\n    // multiple messages could have arrived with handshake\n    // return additional data to be parsed as usual, or null if all parsed\n    return [remainingData, responseMessage];\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n/** Defines the type of a Hub Message. */\nexport var MessageType;\n(function (MessageType) {\n  /** Indicates the message is an Invocation message and implements the {@link @microsoft/signalr.InvocationMessage} interface. */\n  MessageType[MessageType[\"Invocation\"] = 1] = \"Invocation\";\n  /** Indicates the message is a StreamItem message and implements the {@link @microsoft/signalr.StreamItemMessage} interface. */\n  MessageType[MessageType[\"StreamItem\"] = 2] = \"StreamItem\";\n  /** Indicates the message is a Completion message and implements the {@link @microsoft/signalr.CompletionMessage} interface. */\n  MessageType[MessageType[\"Completion\"] = 3] = \"Completion\";\n  /** Indicates the message is a Stream Invocation message and implements the {@link @microsoft/signalr.StreamInvocationMessage} interface. */\n  MessageType[MessageType[\"StreamInvocation\"] = 4] = \"StreamInvocation\";\n  /** Indicates the message is a Cancel Invocation message and implements the {@link @microsoft/signalr.CancelInvocationMessage} interface. */\n  MessageType[MessageType[\"CancelInvocation\"] = 5] = \"CancelInvocation\";\n  /** Indicates the message is a Ping message and implements the {@link @microsoft/signalr.PingMessage} interface. */\n  MessageType[MessageType[\"Ping\"] = 6] = \"Ping\";\n  /** Indicates the message is a Close message and implements the {@link @microsoft/signalr.CloseMessage} interface. */\n  MessageType[MessageType[\"Close\"] = 7] = \"Close\";\n  MessageType[MessageType[\"Ack\"] = 8] = \"Ack\";\n  MessageType[MessageType[\"Sequence\"] = 9] = \"Sequence\";\n})(MessageType || (MessageType = {}));\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { SubjectSubscription } from \"./Utils\";\n/** Stream implementation to stream items to the server. */\nexport class Subject {\n  constructor() {\n    this.observers = [];\n  }\n  next(item) {\n    for (const observer of this.observers) {\n      observer.next(item);\n    }\n  }\n  error(err) {\n    for (const observer of this.observers) {\n      if (observer.error) {\n        observer.error(err);\n      }\n    }\n  }\n  complete() {\n    for (const observer of this.observers) {\n      if (observer.complete) {\n        observer.complete();\n      }\n    }\n  }\n  subscribe(observer) {\n    this.observers.push(observer);\n    return new SubjectSubscription(this, observer);\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { MessageType } from \"./IHubProtocol\";\nimport { isArrayBuffer } from \"./Utils\";\n/** @private */\nexport class MessageBuffer {\n  constructor(protocol, connection, bufferSize) {\n    this._bufferSize = 100000;\n    this._messages = [];\n    this._totalMessageCount = 0;\n    this._waitForSequenceMessage = false;\n    // Message IDs start at 1 and always increment by 1\n    this._nextReceivingSequenceId = 1;\n    this._latestReceivedSequenceId = 0;\n    this._bufferedByteCount = 0;\n    this._reconnectInProgress = false;\n    this._protocol = protocol;\n    this._connection = connection;\n    this._bufferSize = bufferSize;\n  }\n  async _send(message) {\n    const serializedMessage = this._protocol.writeMessage(message);\n    let backpressurePromise = Promise.resolve();\n    // Only count invocation messages. Acks, pings, etc. don't need to be resent on reconnect\n    if (this._isInvocationMessage(message)) {\n      this._totalMessageCount++;\n      let backpressurePromiseResolver = () => {};\n      let backpressurePromiseRejector = () => {};\n      if (isArrayBuffer(serializedMessage)) {\n        this._bufferedByteCount += serializedMessage.byteLength;\n      } else {\n        this._bufferedByteCount += serializedMessage.length;\n      }\n      if (this._bufferedByteCount >= this._bufferSize) {\n        backpressurePromise = new Promise((resolve, reject) => {\n          backpressurePromiseResolver = resolve;\n          backpressurePromiseRejector = reject;\n        });\n      }\n      this._messages.push(new BufferedItem(serializedMessage, this._totalMessageCount, backpressurePromiseResolver, backpressurePromiseRejector));\n    }\n    try {\n      // If this is set it means we are reconnecting or resending\n      // We don't want to send on a disconnected connection\n      // And we don't want to send if resend is running since that would mean sending\n      // this message twice\n      if (!this._reconnectInProgress) {\n        await this._connection.send(serializedMessage);\n      }\n    } catch {\n      this._disconnected();\n    }\n    await backpressurePromise;\n  }\n  _ack(ackMessage) {\n    let newestAckedMessage = -1;\n    // Find index of newest message being acked\n    for (let index = 0; index < this._messages.length; index++) {\n      const element = this._messages[index];\n      if (element._id <= ackMessage.sequenceId) {\n        newestAckedMessage = index;\n        if (isArrayBuffer(element._message)) {\n          this._bufferedByteCount -= element._message.byteLength;\n        } else {\n          this._bufferedByteCount -= element._message.length;\n        }\n        // resolve items that have already been sent and acked\n        element._resolver();\n      } else if (this._bufferedByteCount < this._bufferSize) {\n        // resolve items that now fall under the buffer limit but haven't been acked\n        element._resolver();\n      } else {\n        break;\n      }\n    }\n    if (newestAckedMessage !== -1) {\n      // We're removing everything including the message pointed to, so add 1\n      this._messages = this._messages.slice(newestAckedMessage + 1);\n    }\n  }\n  _shouldProcessMessage(message) {\n    if (this._waitForSequenceMessage) {\n      if (message.type !== MessageType.Sequence) {\n        return false;\n      } else {\n        this._waitForSequenceMessage = false;\n        return true;\n      }\n    }\n    // No special processing for acks, pings, etc.\n    if (!this._isInvocationMessage(message)) {\n      return true;\n    }\n    const currentId = this._nextReceivingSequenceId;\n    this._nextReceivingSequenceId++;\n    if (currentId <= this._latestReceivedSequenceId) {\n      if (currentId === this._latestReceivedSequenceId) {\n        // Should only hit this if we just reconnected and the server is sending\n        // Messages it has buffered, which would mean it hasn't seen an Ack for these messages\n        this._ackTimer();\n      }\n      // Ignore, this is a duplicate message\n      return false;\n    }\n    this._latestReceivedSequenceId = currentId;\n    // Only start the timer for sending an Ack message when we have a message to ack. This also conveniently solves\n    // timer throttling by not having a recursive timer, and by starting the timer via a network call (recv)\n    this._ackTimer();\n    return true;\n  }\n  _resetSequence(message) {\n    if (message.sequenceId > this._nextReceivingSequenceId) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this._connection.stop(new Error(\"Sequence ID greater than amount of messages we've received.\"));\n      return;\n    }\n    this._nextReceivingSequenceId = message.sequenceId;\n  }\n  _disconnected() {\n    this._reconnectInProgress = true;\n    this._waitForSequenceMessage = true;\n  }\n  async _resend() {\n    const sequenceId = this._messages.length !== 0 ? this._messages[0]._id : this._totalMessageCount + 1;\n    await this._connection.send(this._protocol.writeMessage({\n      type: MessageType.Sequence,\n      sequenceId\n    }));\n    // Get a local variable to the _messages, just in case messages are acked while resending\n    // Which would slice the _messages array (which creates a new copy)\n    const messages = this._messages;\n    for (const element of messages) {\n      await this._connection.send(element._message);\n    }\n    this._reconnectInProgress = false;\n  }\n  _dispose(error) {\n    error !== null && error !== void 0 ? error : error = new Error(\"Unable to reconnect to server.\");\n    // Unblock backpressure if any\n    for (const element of this._messages) {\n      element._rejector(error);\n    }\n  }\n  _isInvocationMessage(message) {\n    // There is no way to check if something implements an interface.\n    // So we individually check the messages in a switch statement.\n    // To make sure we don't miss any message types we rely on the compiler\n    // seeing the function returns a value and it will do the\n    // exhaustive check for us on the switch statement, since we don't use 'case default'\n    switch (message.type) {\n      case MessageType.Invocation:\n      case MessageType.StreamItem:\n      case MessageType.Completion:\n      case MessageType.StreamInvocation:\n      case MessageType.CancelInvocation:\n        return true;\n      case MessageType.Close:\n      case MessageType.Sequence:\n      case MessageType.Ping:\n      case MessageType.Ack:\n        return false;\n    }\n  }\n  _ackTimer() {\n    if (this._ackTimerHandle === undefined) {\n      this._ackTimerHandle = setTimeout(async () => {\n        try {\n          if (!this._reconnectInProgress) {\n            await this._connection.send(this._protocol.writeMessage({\n              type: MessageType.Ack,\n              sequenceId: this._latestReceivedSequenceId\n            }));\n          }\n          // Ignore errors, that means the connection is closed and we don't care about the Ack message anymore.\n        } catch {}\n        clearTimeout(this._ackTimerHandle);\n        this._ackTimerHandle = undefined;\n        // 1 second delay so we don't spam Ack messages if there are many messages being received at once.\n      }, 1000);\n    }\n  }\n}\nclass BufferedItem {\n  constructor(message, id, resolver, rejector) {\n    this._message = message;\n    this._id = id;\n    this._resolver = resolver;\n    this._rejector = rejector;\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { HandshakeProtocol } from \"./HandshakeProtocol\";\nimport { AbortError } from \"./Errors\";\nimport { MessageType } from \"./IHubProtocol\";\nimport { LogLevel } from \"./ILogger\";\nimport { Subject } from \"./Subject\";\nimport { Arg, getErrorString, Platform } from \"./Utils\";\nimport { MessageBuffer } from \"./MessageBuffer\";\nconst DEFAULT_TIMEOUT_IN_MS = 30 * 1000;\nconst DEFAULT_PING_INTERVAL_IN_MS = 15 * 1000;\nconst DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE = 100000;\n/** Describes the current state of the {@link HubConnection} to the server. */\nexport var HubConnectionState;\n(function (HubConnectionState) {\n  /** The hub connection is disconnected. */\n  HubConnectionState[\"Disconnected\"] = \"Disconnected\";\n  /** The hub connection is connecting. */\n  HubConnectionState[\"Connecting\"] = \"Connecting\";\n  /** The hub connection is connected. */\n  HubConnectionState[\"Connected\"] = \"Connected\";\n  /** The hub connection is disconnecting. */\n  HubConnectionState[\"Disconnecting\"] = \"Disconnecting\";\n  /** The hub connection is reconnecting. */\n  HubConnectionState[\"Reconnecting\"] = \"Reconnecting\";\n})(HubConnectionState || (HubConnectionState = {}));\n/** Represents a connection to a SignalR Hub. */\nexport class HubConnection {\n  /** @internal */\n  // Using a public static factory method means we can have a private constructor and an _internal_\n  // create method that can be used by HubConnectionBuilder. An \"internal\" constructor would just\n  // be stripped away and the '.d.ts' file would have no constructor, which is interpreted as a\n  // public parameter-less constructor.\n  static create(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize) {\n    return new HubConnection(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize);\n  }\n  constructor(connection, logger, protocol, reconnectPolicy, serverTimeoutInMilliseconds, keepAliveIntervalInMilliseconds, statefulReconnectBufferSize) {\n    this._nextKeepAlive = 0;\n    this._freezeEventListener = () => {\n      this._logger.log(LogLevel.Warning, \"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://learn.microsoft.com/aspnet/core/signalr/javascript-client#bsleep\");\n    };\n    Arg.isRequired(connection, \"connection\");\n    Arg.isRequired(logger, \"logger\");\n    Arg.isRequired(protocol, \"protocol\");\n    this.serverTimeoutInMilliseconds = serverTimeoutInMilliseconds !== null && serverTimeoutInMilliseconds !== void 0 ? serverTimeoutInMilliseconds : DEFAULT_TIMEOUT_IN_MS;\n    this.keepAliveIntervalInMilliseconds = keepAliveIntervalInMilliseconds !== null && keepAliveIntervalInMilliseconds !== void 0 ? keepAliveIntervalInMilliseconds : DEFAULT_PING_INTERVAL_IN_MS;\n    this._statefulReconnectBufferSize = statefulReconnectBufferSize !== null && statefulReconnectBufferSize !== void 0 ? statefulReconnectBufferSize : DEFAULT_STATEFUL_RECONNECT_BUFFER_SIZE;\n    this._logger = logger;\n    this._protocol = protocol;\n    this.connection = connection;\n    this._reconnectPolicy = reconnectPolicy;\n    this._handshakeProtocol = new HandshakeProtocol();\n    this.connection.onreceive = data => this._processIncomingData(data);\n    this.connection.onclose = error => this._connectionClosed(error);\n    this._callbacks = {};\n    this._methods = {};\n    this._closedCallbacks = [];\n    this._reconnectingCallbacks = [];\n    this._reconnectedCallbacks = [];\n    this._invocationId = 0;\n    this._receivedHandshakeResponse = false;\n    this._connectionState = HubConnectionState.Disconnected;\n    this._connectionStarted = false;\n    this._cachedPingMessage = this._protocol.writeMessage({\n      type: MessageType.Ping\n    });\n  }\n  /** Indicates the state of the {@link HubConnection} to the server. */\n  get state() {\n    return this._connectionState;\n  }\n  /** Represents the connection id of the {@link HubConnection} on the server. The connection id will be null when the connection is either\r\n   *  in the disconnected state or if the negotiation step was skipped.\r\n   */\n  get connectionId() {\n    return this.connection ? this.connection.connectionId || null : null;\n  }\n  /** Indicates the url of the {@link HubConnection} to the server. */\n  get baseUrl() {\n    return this.connection.baseUrl || \"\";\n  }\n  /**\r\n   * Sets a new url for the HubConnection. Note that the url can only be changed when the connection is in either the Disconnected or\r\n   * Reconnecting states.\r\n   * @param {string} url The url to connect to.\r\n   */\n  set baseUrl(url) {\n    if (this._connectionState !== HubConnectionState.Disconnected && this._connectionState !== HubConnectionState.Reconnecting) {\n      throw new Error(\"The HubConnection must be in the Disconnected or Reconnecting state to change the url.\");\n    }\n    if (!url) {\n      throw new Error(\"The HubConnection url must be a valid url.\");\n    }\n    this.connection.baseUrl = url;\n  }\n  /** Starts the connection.\r\n   *\r\n   * @returns {Promise<void>} A Promise that resolves when the connection has been successfully established, or rejects with an error.\r\n   */\n  start() {\n    this._startPromise = this._startWithStateTransitions();\n    return this._startPromise;\n  }\n  async _startWithStateTransitions() {\n    if (this._connectionState !== HubConnectionState.Disconnected) {\n      return Promise.reject(new Error(\"Cannot start a HubConnection that is not in the 'Disconnected' state.\"));\n    }\n    this._connectionState = HubConnectionState.Connecting;\n    this._logger.log(LogLevel.Debug, \"Starting HubConnection.\");\n    try {\n      await this._startInternal();\n      if (Platform.isBrowser) {\n        // Log when the browser freezes the tab so users know why their connection unexpectedly stopped working\n        window.document.addEventListener(\"freeze\", this._freezeEventListener);\n      }\n      this._connectionState = HubConnectionState.Connected;\n      this._connectionStarted = true;\n      this._logger.log(LogLevel.Debug, \"HubConnection connected successfully.\");\n    } catch (e) {\n      this._connectionState = HubConnectionState.Disconnected;\n      this._logger.log(LogLevel.Debug, `HubConnection failed to start successfully because of error '${e}'.`);\n      return Promise.reject(e);\n    }\n  }\n  async _startInternal() {\n    this._stopDuringStartError = undefined;\n    this._receivedHandshakeResponse = false;\n    // Set up the promise before any connection is (re)started otherwise it could race with received messages\n    const handshakePromise = new Promise((resolve, reject) => {\n      this._handshakeResolver = resolve;\n      this._handshakeRejecter = reject;\n    });\n    await this.connection.start(this._protocol.transferFormat);\n    try {\n      let version = this._protocol.version;\n      if (!this.connection.features.reconnect) {\n        // Stateful Reconnect starts with HubProtocol version 2, newer clients connecting to older servers will fail to connect due to\n        // the handshake only supporting version 1, so we will try to send version 1 during the handshake to keep old servers working.\n        version = 1;\n      }\n      const handshakeRequest = {\n        protocol: this._protocol.name,\n        version\n      };\n      this._logger.log(LogLevel.Debug, \"Sending handshake request.\");\n      await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(handshakeRequest));\n      this._logger.log(LogLevel.Information, `Using HubProtocol '${this._protocol.name}'.`);\n      // defensively cleanup timeout in case we receive a message from the server before we finish start\n      this._cleanupTimeout();\n      this._resetTimeoutPeriod();\n      this._resetKeepAliveInterval();\n      await handshakePromise;\n      // It's important to check the stopDuringStartError instead of just relying on the handshakePromise\n      // being rejected on close, because this continuation can run after both the handshake completed successfully\n      // and the connection was closed.\n      if (this._stopDuringStartError) {\n        // It's important to throw instead of returning a rejected promise, because we don't want to allow any state\n        // transitions to occur between now and the calling code observing the exceptions. Returning a rejected promise\n        // will cause the calling continuation to get scheduled to run later.\n        // eslint-disable-next-line @typescript-eslint/no-throw-literal\n        throw this._stopDuringStartError;\n      }\n      const useStatefulReconnect = this.connection.features.reconnect || false;\n      if (useStatefulReconnect) {\n        this._messageBuffer = new MessageBuffer(this._protocol, this.connection, this._statefulReconnectBufferSize);\n        this.connection.features.disconnected = this._messageBuffer._disconnected.bind(this._messageBuffer);\n        this.connection.features.resend = () => {\n          if (this._messageBuffer) {\n            return this._messageBuffer._resend();\n          }\n        };\n      }\n      if (!this.connection.features.inherentKeepAlive) {\n        await this._sendMessage(this._cachedPingMessage);\n      }\n    } catch (e) {\n      this._logger.log(LogLevel.Debug, `Hub handshake failed with error '${e}' during start(). Stopping HubConnection.`);\n      this._cleanupTimeout();\n      this._cleanupPingTimer();\n      // HttpConnection.stop() should not complete until after the onclose callback is invoked.\n      // This will transition the HubConnection to the disconnected state before HttpConnection.stop() completes.\n      await this.connection.stop(e);\n      throw e;\n    }\n  }\n  /** Stops the connection.\r\n   *\r\n   * @returns {Promise<void>} A Promise that resolves when the connection has been successfully terminated, or rejects with an error.\r\n   */\n  async stop() {\n    // Capture the start promise before the connection might be restarted in an onclose callback.\n    const startPromise = this._startPromise;\n    this.connection.features.reconnect = false;\n    this._stopPromise = this._stopInternal();\n    await this._stopPromise;\n    try {\n      // Awaiting undefined continues immediately\n      await startPromise;\n    } catch (e) {\n      // This exception is returned to the user as a rejected Promise from the start method.\n    }\n  }\n  _stopInternal(error) {\n    if (this._connectionState === HubConnectionState.Disconnected) {\n      this._logger.log(LogLevel.Debug, `Call to HubConnection.stop(${error}) ignored because it is already in the disconnected state.`);\n      return Promise.resolve();\n    }\n    if (this._connectionState === HubConnectionState.Disconnecting) {\n      this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\n      return this._stopPromise;\n    }\n    const state = this._connectionState;\n    this._connectionState = HubConnectionState.Disconnecting;\n    this._logger.log(LogLevel.Debug, \"Stopping HubConnection.\");\n    if (this._reconnectDelayHandle) {\n      // We're in a reconnect delay which means the underlying connection is currently already stopped.\n      // Just clear the handle to stop the reconnect loop (which no one is waiting on thankfully) and\n      // fire the onclose callbacks.\n      this._logger.log(LogLevel.Debug, \"Connection stopped during reconnect delay. Done reconnecting.\");\n      clearTimeout(this._reconnectDelayHandle);\n      this._reconnectDelayHandle = undefined;\n      this._completeClose();\n      return Promise.resolve();\n    }\n    if (state === HubConnectionState.Connected) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this._sendCloseMessage();\n    }\n    this._cleanupTimeout();\n    this._cleanupPingTimer();\n    this._stopDuringStartError = error || new AbortError(\"The connection was stopped before the hub handshake could complete.\");\n    // HttpConnection.stop() should not complete until after either HttpConnection.start() fails\n    // or the onclose callback is invoked. The onclose callback will transition the HubConnection\n    // to the disconnected state if need be before HttpConnection.stop() completes.\n    return this.connection.stop(error);\n  }\n  async _sendCloseMessage() {\n    try {\n      await this._sendWithProtocol(this._createCloseMessage());\n    } catch {\n      // Ignore, this is a best effort attempt to let the server know the client closed gracefully.\n    }\n  }\n  /** Invokes a streaming hub method on the server using the specified name and arguments.\r\n   *\r\n   * @typeparam T The type of the items returned by the server.\r\n   * @param {string} methodName The name of the server method to invoke.\r\n   * @param {any[]} args The arguments used to invoke the server method.\r\n   * @returns {IStreamResult<T>} An object that yields results from the server as they are received.\r\n   */\n  stream(methodName, ...args) {\n    const [streams, streamIds] = this._replaceStreamingParams(args);\n    const invocationDescriptor = this._createStreamInvocation(methodName, args, streamIds);\n    // eslint-disable-next-line prefer-const\n    let promiseQueue;\n    const subject = new Subject();\n    subject.cancelCallback = () => {\n      const cancelInvocation = this._createCancelInvocation(invocationDescriptor.invocationId);\n      delete this._callbacks[invocationDescriptor.invocationId];\n      return promiseQueue.then(() => {\n        return this._sendWithProtocol(cancelInvocation);\n      });\n    };\n    this._callbacks[invocationDescriptor.invocationId] = (invocationEvent, error) => {\n      if (error) {\n        subject.error(error);\n        return;\n      } else if (invocationEvent) {\n        // invocationEvent will not be null when an error is not passed to the callback\n        if (invocationEvent.type === MessageType.Completion) {\n          if (invocationEvent.error) {\n            subject.error(new Error(invocationEvent.error));\n          } else {\n            subject.complete();\n          }\n        } else {\n          subject.next(invocationEvent.item);\n        }\n      }\n    };\n    promiseQueue = this._sendWithProtocol(invocationDescriptor).catch(e => {\n      subject.error(e);\n      delete this._callbacks[invocationDescriptor.invocationId];\n    });\n    this._launchStreams(streams, promiseQueue);\n    return subject;\n  }\n  _sendMessage(message) {\n    this._resetKeepAliveInterval();\n    return this.connection.send(message);\n  }\n  /**\r\n   * Sends a js object to the server.\r\n   * @param message The js object to serialize and send.\r\n   */\n  _sendWithProtocol(message) {\n    if (this._messageBuffer) {\n      return this._messageBuffer._send(message);\n    } else {\n      return this._sendMessage(this._protocol.writeMessage(message));\n    }\n  }\n  /** Invokes a hub method on the server using the specified name and arguments. Does not wait for a response from the receiver.\r\n   *\r\n   * The Promise returned by this method resolves when the client has sent the invocation to the server. The server may still\r\n   * be processing the invocation.\r\n   *\r\n   * @param {string} methodName The name of the server method to invoke.\r\n   * @param {any[]} args The arguments used to invoke the server method.\r\n   * @returns {Promise<void>} A Promise that resolves when the invocation has been successfully sent, or rejects with an error.\r\n   */\n  send(methodName, ...args) {\n    const [streams, streamIds] = this._replaceStreamingParams(args);\n    const sendPromise = this._sendWithProtocol(this._createInvocation(methodName, args, true, streamIds));\n    this._launchStreams(streams, sendPromise);\n    return sendPromise;\n  }\n  /** Invokes a hub method on the server using the specified name and arguments.\r\n   *\r\n   * The Promise returned by this method resolves when the server indicates it has finished invoking the method. When the promise\r\n   * resolves, the server has finished invoking the method. If the server method returns a result, it is produced as the result of\r\n   * resolving the Promise.\r\n   *\r\n   * @typeparam T The expected return type.\r\n   * @param {string} methodName The name of the server method to invoke.\r\n   * @param {any[]} args The arguments used to invoke the server method.\r\n   * @returns {Promise<T>} A Promise that resolves with the result of the server method (if any), or rejects with an error.\r\n   */\n  invoke(methodName, ...args) {\n    const [streams, streamIds] = this._replaceStreamingParams(args);\n    const invocationDescriptor = this._createInvocation(methodName, args, false, streamIds);\n    const p = new Promise((resolve, reject) => {\n      // invocationId will always have a value for a non-blocking invocation\n      this._callbacks[invocationDescriptor.invocationId] = (invocationEvent, error) => {\n        if (error) {\n          reject(error);\n          return;\n        } else if (invocationEvent) {\n          // invocationEvent will not be null when an error is not passed to the callback\n          if (invocationEvent.type === MessageType.Completion) {\n            if (invocationEvent.error) {\n              reject(new Error(invocationEvent.error));\n            } else {\n              resolve(invocationEvent.result);\n            }\n          } else {\n            reject(new Error(`Unexpected message type: ${invocationEvent.type}`));\n          }\n        }\n      };\n      const promiseQueue = this._sendWithProtocol(invocationDescriptor).catch(e => {\n        reject(e);\n        // invocationId will always have a value for a non-blocking invocation\n        delete this._callbacks[invocationDescriptor.invocationId];\n      });\n      this._launchStreams(streams, promiseQueue);\n    });\n    return p;\n  }\n  on(methodName, newMethod) {\n    if (!methodName || !newMethod) {\n      return;\n    }\n    methodName = methodName.toLowerCase();\n    if (!this._methods[methodName]) {\n      this._methods[methodName] = [];\n    }\n    // Preventing adding the same handler multiple times.\n    if (this._methods[methodName].indexOf(newMethod) !== -1) {\n      return;\n    }\n    this._methods[methodName].push(newMethod);\n  }\n  off(methodName, method) {\n    if (!methodName) {\n      return;\n    }\n    methodName = methodName.toLowerCase();\n    const handlers = this._methods[methodName];\n    if (!handlers) {\n      return;\n    }\n    if (method) {\n      const removeIdx = handlers.indexOf(method);\n      if (removeIdx !== -1) {\n        handlers.splice(removeIdx, 1);\n        if (handlers.length === 0) {\n          delete this._methods[methodName];\n        }\n      }\n    } else {\n      delete this._methods[methodName];\n    }\n  }\n  /** Registers a handler that will be invoked when the connection is closed.\r\n   *\r\n   * @param {Function} callback The handler that will be invoked when the connection is closed. Optionally receives a single argument containing the error that caused the connection to close (if any).\r\n   */\n  onclose(callback) {\n    if (callback) {\n      this._closedCallbacks.push(callback);\n    }\n  }\n  /** Registers a handler that will be invoked when the connection starts reconnecting.\r\n   *\r\n   * @param {Function} callback The handler that will be invoked when the connection starts reconnecting. Optionally receives a single argument containing the error that caused the connection to start reconnecting (if any).\r\n   */\n  onreconnecting(callback) {\n    if (callback) {\n      this._reconnectingCallbacks.push(callback);\n    }\n  }\n  /** Registers a handler that will be invoked when the connection successfully reconnects.\r\n   *\r\n   * @param {Function} callback The handler that will be invoked when the connection successfully reconnects.\r\n   */\n  onreconnected(callback) {\n    if (callback) {\n      this._reconnectedCallbacks.push(callback);\n    }\n  }\n  _processIncomingData(data) {\n    this._cleanupTimeout();\n    if (!this._receivedHandshakeResponse) {\n      data = this._processHandshakeResponse(data);\n      this._receivedHandshakeResponse = true;\n    }\n    // Data may have all been read when processing handshake response\n    if (data) {\n      // Parse the messages\n      const messages = this._protocol.parseMessages(data, this._logger);\n      for (const message of messages) {\n        if (this._messageBuffer && !this._messageBuffer._shouldProcessMessage(message)) {\n          // Don't process the message, we are either waiting for a SequenceMessage or received a duplicate message\n          continue;\n        }\n        switch (message.type) {\n          case MessageType.Invocation:\n            this._invokeClientMethod(message).catch(e => {\n              this._logger.log(LogLevel.Error, `Invoke client method threw error: ${getErrorString(e)}`);\n            });\n            break;\n          case MessageType.StreamItem:\n          case MessageType.Completion:\n            {\n              const callback = this._callbacks[message.invocationId];\n              if (callback) {\n                if (message.type === MessageType.Completion) {\n                  delete this._callbacks[message.invocationId];\n                }\n                try {\n                  callback(message);\n                } catch (e) {\n                  this._logger.log(LogLevel.Error, `Stream callback threw error: ${getErrorString(e)}`);\n                }\n              }\n              break;\n            }\n          case MessageType.Ping:\n            // Don't care about pings\n            break;\n          case MessageType.Close:\n            {\n              this._logger.log(LogLevel.Information, \"Close message received from server.\");\n              const error = message.error ? new Error(\"Server returned an error on close: \" + message.error) : undefined;\n              if (message.allowReconnect === true) {\n                // It feels wrong not to await connection.stop() here, but processIncomingData is called as part of an onreceive callback which is not async,\n                // this is already the behavior for serverTimeout(), and HttpConnection.Stop() should catch and log all possible exceptions.\n                // eslint-disable-next-line @typescript-eslint/no-floating-promises\n                this.connection.stop(error);\n              } else {\n                // We cannot await stopInternal() here, but subsequent calls to stop() will await this if stopInternal() is still ongoing.\n                this._stopPromise = this._stopInternal(error);\n              }\n              break;\n            }\n          case MessageType.Ack:\n            if (this._messageBuffer) {\n              this._messageBuffer._ack(message);\n            }\n            break;\n          case MessageType.Sequence:\n            if (this._messageBuffer) {\n              this._messageBuffer._resetSequence(message);\n            }\n            break;\n          default:\n            this._logger.log(LogLevel.Warning, `Invalid message type: ${message.type}.`);\n            break;\n        }\n      }\n    }\n    this._resetTimeoutPeriod();\n  }\n  _processHandshakeResponse(data) {\n    let responseMessage;\n    let remainingData;\n    try {\n      [remainingData, responseMessage] = this._handshakeProtocol.parseHandshakeResponse(data);\n    } catch (e) {\n      const message = \"Error parsing handshake response: \" + e;\n      this._logger.log(LogLevel.Error, message);\n      const error = new Error(message);\n      this._handshakeRejecter(error);\n      throw error;\n    }\n    if (responseMessage.error) {\n      const message = \"Server returned handshake error: \" + responseMessage.error;\n      this._logger.log(LogLevel.Error, message);\n      const error = new Error(message);\n      this._handshakeRejecter(error);\n      throw error;\n    } else {\n      this._logger.log(LogLevel.Debug, \"Server handshake complete.\");\n    }\n    this._handshakeResolver();\n    return remainingData;\n  }\n  _resetKeepAliveInterval() {\n    if (this.connection.features.inherentKeepAlive) {\n      return;\n    }\n    // Set the time we want the next keep alive to be sent\n    // Timer will be setup on next message receive\n    this._nextKeepAlive = new Date().getTime() + this.keepAliveIntervalInMilliseconds;\n    this._cleanupPingTimer();\n  }\n  _resetTimeoutPeriod() {\n    if (!this.connection.features || !this.connection.features.inherentKeepAlive) {\n      // Set the timeout timer\n      this._timeoutHandle = setTimeout(() => this.serverTimeout(), this.serverTimeoutInMilliseconds);\n      // Set keepAlive timer if there isn't one\n      if (this._pingServerHandle === undefined) {\n        let nextPing = this._nextKeepAlive - new Date().getTime();\n        if (nextPing < 0) {\n          nextPing = 0;\n        }\n        // The timer needs to be set from a networking callback to avoid Chrome timer throttling from causing timers to run once a minute\n        this._pingServerHandle = setTimeout(async () => {\n          if (this._connectionState === HubConnectionState.Connected) {\n            try {\n              await this._sendMessage(this._cachedPingMessage);\n            } catch {\n              // We don't care about the error. It should be seen elsewhere in the client.\n              // The connection is probably in a bad or closed state now, cleanup the timer so it stops triggering\n              this._cleanupPingTimer();\n            }\n          }\n        }, nextPing);\n      }\n    }\n  }\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  serverTimeout() {\n    // The server hasn't talked to us in a while. It doesn't like us anymore ... :(\n    // Terminate the connection, but we don't need to wait on the promise. This could trigger reconnecting.\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.connection.stop(new Error(\"Server timeout elapsed without receiving a message from the server.\"));\n  }\n  async _invokeClientMethod(invocationMessage) {\n    const methodName = invocationMessage.target.toLowerCase();\n    const methods = this._methods[methodName];\n    if (!methods) {\n      this._logger.log(LogLevel.Warning, `No client method with the name '${methodName}' found.`);\n      // No handlers provided by client but the server is expecting a response still, so we send an error\n      if (invocationMessage.invocationId) {\n        this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\n        await this._sendWithProtocol(this._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null));\n      }\n      return;\n    }\n    // Avoid issues with handlers removing themselves thus modifying the list while iterating through it\n    const methodsCopy = methods.slice();\n    // Server expects a response\n    const expectsResponse = invocationMessage.invocationId ? true : false;\n    // We preserve the last result or exception but still call all handlers\n    let res;\n    let exception;\n    let completionMessage;\n    for (const m of methodsCopy) {\n      try {\n        const prevRes = res;\n        res = await m.apply(this, invocationMessage.arguments);\n        if (expectsResponse && res && prevRes) {\n          this._logger.log(LogLevel.Error, `Multiple results provided for '${methodName}'. Sending error to server.`);\n          completionMessage = this._createCompletionMessage(invocationMessage.invocationId, `Client provided multiple results.`, null);\n        }\n        // Ignore exception if we got a result after, the exception will be logged\n        exception = undefined;\n      } catch (e) {\n        exception = e;\n        this._logger.log(LogLevel.Error, `A callback for the method '${methodName}' threw error '${e}'.`);\n      }\n    }\n    if (completionMessage) {\n      await this._sendWithProtocol(completionMessage);\n    } else if (expectsResponse) {\n      // If there is an exception that means either no result was given or a handler after a result threw\n      if (exception) {\n        completionMessage = this._createCompletionMessage(invocationMessage.invocationId, `${exception}`, null);\n      } else if (res !== undefined) {\n        completionMessage = this._createCompletionMessage(invocationMessage.invocationId, null, res);\n      } else {\n        this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\n        // Client didn't provide a result or throw from a handler, server expects a response so we send an error\n        completionMessage = this._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null);\n      }\n      await this._sendWithProtocol(completionMessage);\n    } else {\n      if (res) {\n        this._logger.log(LogLevel.Error, `Result given for '${methodName}' method but server is not expecting a result.`);\n      }\n    }\n  }\n  _connectionClosed(error) {\n    this._logger.log(LogLevel.Debug, `HubConnection.connectionClosed(${error}) called while in state ${this._connectionState}.`);\n    // Triggering this.handshakeRejecter is insufficient because it could already be resolved without the continuation having run yet.\n    this._stopDuringStartError = this._stopDuringStartError || error || new AbortError(\"The underlying connection was closed before the hub handshake could complete.\");\n    // If the handshake is in progress, start will be waiting for the handshake promise, so we complete it.\n    // If it has already completed, this should just noop.\n    if (this._handshakeResolver) {\n      this._handshakeResolver();\n    }\n    this._cancelCallbacksWithError(error || new Error(\"Invocation canceled due to the underlying connection being closed.\"));\n    this._cleanupTimeout();\n    this._cleanupPingTimer();\n    if (this._connectionState === HubConnectionState.Disconnecting) {\n      this._completeClose(error);\n    } else if (this._connectionState === HubConnectionState.Connected && this._reconnectPolicy) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this._reconnect(error);\n    } else if (this._connectionState === HubConnectionState.Connected) {\n      this._completeClose(error);\n    }\n    // If none of the above if conditions were true were called the HubConnection must be in either:\n    // 1. The Connecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail it.\n    // 2. The Reconnecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail the current reconnect attempt\n    //    and potentially continue the reconnect() loop.\n    // 3. The Disconnected state in which case we're already done.\n  }\n  _completeClose(error) {\n    if (this._connectionStarted) {\n      this._connectionState = HubConnectionState.Disconnected;\n      this._connectionStarted = false;\n      if (this._messageBuffer) {\n        this._messageBuffer._dispose(error !== null && error !== void 0 ? error : new Error(\"Connection closed.\"));\n        this._messageBuffer = undefined;\n      }\n      if (Platform.isBrowser) {\n        window.document.removeEventListener(\"freeze\", this._freezeEventListener);\n      }\n      try {\n        this._closedCallbacks.forEach(c => c.apply(this, [error]));\n      } catch (e) {\n        this._logger.log(LogLevel.Error, `An onclose callback called with error '${error}' threw error '${e}'.`);\n      }\n    }\n  }\n  async _reconnect(error) {\n    const reconnectStartTime = Date.now();\n    let previousReconnectAttempts = 0;\n    let retryError = error !== undefined ? error : new Error(\"Attempting to reconnect due to a unknown error.\");\n    let nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, 0, retryError);\n    if (nextRetryDelay === null) {\n      this._logger.log(LogLevel.Debug, \"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt.\");\n      this._completeClose(error);\n      return;\n    }\n    this._connectionState = HubConnectionState.Reconnecting;\n    if (error) {\n      this._logger.log(LogLevel.Information, `Connection reconnecting because of error '${error}'.`);\n    } else {\n      this._logger.log(LogLevel.Information, \"Connection reconnecting.\");\n    }\n    if (this._reconnectingCallbacks.length !== 0) {\n      try {\n        this._reconnectingCallbacks.forEach(c => c.apply(this, [error]));\n      } catch (e) {\n        this._logger.log(LogLevel.Error, `An onreconnecting callback called with error '${error}' threw error '${e}'.`);\n      }\n      // Exit early if an onreconnecting callback called connection.stop().\n      if (this._connectionState !== HubConnectionState.Reconnecting) {\n        this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.\");\n        return;\n      }\n    }\n    while (nextRetryDelay !== null) {\n      this._logger.log(LogLevel.Information, `Reconnect attempt number ${previousReconnectAttempts} will start in ${nextRetryDelay} ms.`);\n      await new Promise(resolve => {\n        this._reconnectDelayHandle = setTimeout(resolve, nextRetryDelay);\n      });\n      this._reconnectDelayHandle = undefined;\n      if (this._connectionState !== HubConnectionState.Reconnecting) {\n        this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state during reconnect delay. Done reconnecting.\");\n        return;\n      }\n      try {\n        await this._startInternal();\n        this._connectionState = HubConnectionState.Connected;\n        this._logger.log(LogLevel.Information, \"HubConnection reconnected successfully.\");\n        if (this._reconnectedCallbacks.length !== 0) {\n          try {\n            this._reconnectedCallbacks.forEach(c => c.apply(this, [this.connection.connectionId]));\n          } catch (e) {\n            this._logger.log(LogLevel.Error, `An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${e}'.`);\n          }\n        }\n        return;\n      } catch (e) {\n        this._logger.log(LogLevel.Information, `Reconnect attempt failed because of error '${e}'.`);\n        if (this._connectionState !== HubConnectionState.Reconnecting) {\n          this._logger.log(LogLevel.Debug, `Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`);\n          // The TypeScript compiler thinks that connectionState must be Connected here. The TypeScript compiler is wrong.\n          if (this._connectionState === HubConnectionState.Disconnecting) {\n            this._completeClose();\n          }\n          return;\n        }\n        retryError = e instanceof Error ? e : new Error(e.toString());\n        nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, Date.now() - reconnectStartTime, retryError);\n      }\n    }\n    this._logger.log(LogLevel.Information, `Reconnect retries have been exhausted after ${Date.now() - reconnectStartTime} ms and ${previousReconnectAttempts} failed attempts. Connection disconnecting.`);\n    this._completeClose();\n  }\n  _getNextRetryDelay(previousRetryCount, elapsedMilliseconds, retryReason) {\n    try {\n      return this._reconnectPolicy.nextRetryDelayInMilliseconds({\n        elapsedMilliseconds,\n        previousRetryCount,\n        retryReason\n      });\n    } catch (e) {\n      this._logger.log(LogLevel.Error, `IRetryPolicy.nextRetryDelayInMilliseconds(${previousRetryCount}, ${elapsedMilliseconds}) threw error '${e}'.`);\n      return null;\n    }\n  }\n  _cancelCallbacksWithError(error) {\n    const callbacks = this._callbacks;\n    this._callbacks = {};\n    Object.keys(callbacks).forEach(key => {\n      const callback = callbacks[key];\n      try {\n        callback(null, error);\n      } catch (e) {\n        this._logger.log(LogLevel.Error, `Stream 'error' callback called with '${error}' threw error: ${getErrorString(e)}`);\n      }\n    });\n  }\n  _cleanupPingTimer() {\n    if (this._pingServerHandle) {\n      clearTimeout(this._pingServerHandle);\n      this._pingServerHandle = undefined;\n    }\n  }\n  _cleanupTimeout() {\n    if (this._timeoutHandle) {\n      clearTimeout(this._timeoutHandle);\n    }\n  }\n  _createInvocation(methodName, args, nonblocking, streamIds) {\n    if (nonblocking) {\n      if (streamIds.length !== 0) {\n        return {\n          arguments: args,\n          streamIds,\n          target: methodName,\n          type: MessageType.Invocation\n        };\n      } else {\n        return {\n          arguments: args,\n          target: methodName,\n          type: MessageType.Invocation\n        };\n      }\n    } else {\n      const invocationId = this._invocationId;\n      this._invocationId++;\n      if (streamIds.length !== 0) {\n        return {\n          arguments: args,\n          invocationId: invocationId.toString(),\n          streamIds,\n          target: methodName,\n          type: MessageType.Invocation\n        };\n      } else {\n        return {\n          arguments: args,\n          invocationId: invocationId.toString(),\n          target: methodName,\n          type: MessageType.Invocation\n        };\n      }\n    }\n  }\n  _launchStreams(streams, promiseQueue) {\n    if (streams.length === 0) {\n      return;\n    }\n    // Synchronize stream data so they arrive in-order on the server\n    if (!promiseQueue) {\n      promiseQueue = Promise.resolve();\n    }\n    // We want to iterate over the keys, since the keys are the stream ids\n    // eslint-disable-next-line guard-for-in\n    for (const streamId in streams) {\n      streams[streamId].subscribe({\n        complete: () => {\n          promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId)));\n        },\n        error: err => {\n          let message;\n          if (err instanceof Error) {\n            message = err.message;\n          } else if (err && err.toString) {\n            message = err.toString();\n          } else {\n            message = \"Unknown error\";\n          }\n          promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId, message)));\n        },\n        next: item => {\n          promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createStreamItemMessage(streamId, item)));\n        }\n      });\n    }\n  }\n  _replaceStreamingParams(args) {\n    const streams = [];\n    const streamIds = [];\n    for (let i = 0; i < args.length; i++) {\n      const argument = args[i];\n      if (this._isObservable(argument)) {\n        const streamId = this._invocationId;\n        this._invocationId++;\n        // Store the stream for later use\n        streams[streamId] = argument;\n        streamIds.push(streamId.toString());\n        // remove stream from args\n        args.splice(i, 1);\n      }\n    }\n    return [streams, streamIds];\n  }\n  _isObservable(arg) {\n    // This allows other stream implementations to just work (like rxjs)\n    return arg && arg.subscribe && typeof arg.subscribe === \"function\";\n  }\n  _createStreamInvocation(methodName, args, streamIds) {\n    const invocationId = this._invocationId;\n    this._invocationId++;\n    if (streamIds.length !== 0) {\n      return {\n        arguments: args,\n        invocationId: invocationId.toString(),\n        streamIds,\n        target: methodName,\n        type: MessageType.StreamInvocation\n      };\n    } else {\n      return {\n        arguments: args,\n        invocationId: invocationId.toString(),\n        target: methodName,\n        type: MessageType.StreamInvocation\n      };\n    }\n  }\n  _createCancelInvocation(id) {\n    return {\n      invocationId: id,\n      type: MessageType.CancelInvocation\n    };\n  }\n  _createStreamItemMessage(id, item) {\n    return {\n      invocationId: id,\n      item,\n      type: MessageType.StreamItem\n    };\n  }\n  _createCompletionMessage(id, error, result) {\n    if (error) {\n      return {\n        error,\n        invocationId: id,\n        type: MessageType.Completion\n      };\n    }\n    return {\n      invocationId: id,\n      result,\n      type: MessageType.Completion\n    };\n  }\n  _createCloseMessage() {\n    return {\n      type: MessageType.Close\n    };\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n// 0, 2, 10, 30 second delays before reconnect attempts.\nconst DEFAULT_RETRY_DELAYS_IN_MILLISECONDS = [0, 2000, 10000, 30000, null];\n/** @private */\nexport class DefaultReconnectPolicy {\n  constructor(retryDelays) {\n    this._retryDelays = retryDelays !== undefined ? [...retryDelays, null] : DEFAULT_RETRY_DELAYS_IN_MILLISECONDS;\n  }\n  nextRetryDelayInMilliseconds(retryContext) {\n    return this._retryDelays[retryContext.previousRetryCount];\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nexport class HeaderNames {}\nHeaderNames.Authorization = \"Authorization\";\nHeaderNames.Cookie = \"<PERSON>ie\";\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { HeaderNames } from \"./HeaderNames\";\nimport { HttpClient } from \"./HttpClient\";\n/** @private */\nexport class AccessTokenHttpClient extends HttpClient {\n  constructor(innerClient, accessTokenFactory) {\n    super();\n    this._innerClient = innerClient;\n    this._accessTokenFactory = accessTokenFactory;\n  }\n  async send(request) {\n    let allowRetry = true;\n    if (this._accessTokenFactory && (!this._accessToken || request.url && request.url.indexOf(\"/negotiate?\") > 0)) {\n      // don't retry if the request is a negotiate or if we just got a potentially new token from the access token factory\n      allowRetry = false;\n      this._accessToken = await this._accessTokenFactory();\n    }\n    this._setAuthorizationHeader(request);\n    const response = await this._innerClient.send(request);\n    if (allowRetry && response.statusCode === 401 && this._accessTokenFactory) {\n      this._accessToken = await this._accessTokenFactory();\n      this._setAuthorizationHeader(request);\n      return await this._innerClient.send(request);\n    }\n    return response;\n  }\n  _setAuthorizationHeader(request) {\n    if (!request.headers) {\n      request.headers = {};\n    }\n    if (this._accessToken) {\n      request.headers[HeaderNames.Authorization] = `Bearer ${this._accessToken}`;\n    }\n    // don't remove the header if there isn't an access token factory, the user manually added the header in this case\n    else if (this._accessTokenFactory) {\n      if (request.headers[HeaderNames.Authorization]) {\n        delete request.headers[HeaderNames.Authorization];\n      }\n    }\n  }\n  getCookieString(url) {\n    return this._innerClient.getCookieString(url);\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n// This will be treated as a bit flag in the future, so we keep it using power-of-two values.\n/** Specifies a specific HTTP transport type. */\nexport var HttpTransportType;\n(function (HttpTransportType) {\n  /** Specifies no transport preference. */\n  HttpTransportType[HttpTransportType[\"None\"] = 0] = \"None\";\n  /** Specifies the WebSockets transport. */\n  HttpTransportType[HttpTransportType[\"WebSockets\"] = 1] = \"WebSockets\";\n  /** Specifies the Server-Sent Events transport. */\n  HttpTransportType[HttpTransportType[\"ServerSentEvents\"] = 2] = \"ServerSentEvents\";\n  /** Specifies the Long Polling transport. */\n  HttpTransportType[HttpTransportType[\"LongPolling\"] = 4] = \"LongPolling\";\n})(HttpTransportType || (HttpTransportType = {}));\n/** Specifies the transfer format for a connection. */\nexport var TransferFormat;\n(function (TransferFormat) {\n  /** Specifies that only text data will be transmitted over the connection. */\n  TransferFormat[TransferFormat[\"Text\"] = 1] = \"Text\";\n  /** Specifies that binary data will be transmitted over the connection. */\n  TransferFormat[TransferFormat[\"Binary\"] = 2] = \"Binary\";\n})(TransferFormat || (TransferFormat = {}));\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\n// it's a very new API right now.\n// Not exported from index.\n/** @private */\nexport class AbortController {\n  constructor() {\n    this._isAborted = false;\n    this.onabort = null;\n  }\n  abort() {\n    if (!this._isAborted) {\n      this._isAborted = true;\n      if (this.onabort) {\n        this.onabort();\n      }\n    }\n  }\n  get signal() {\n    return this;\n  }\n  get aborted() {\n    return this._isAborted;\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { AbortController } from \"./AbortController\";\nimport { HttpError, TimeoutError } from \"./Errors\";\nimport { LogLevel } from \"./ILogger\";\nimport { TransferFormat } from \"./ITransport\";\nimport { Arg, getDataDetail, getUserAgentHeader, sendMessage } from \"./Utils\";\n// Not exported from 'index', this type is internal.\n/** @private */\nexport class LongPollingTransport {\n  // This is an internal type, not exported from 'index' so this is really just internal.\n  get pollAborted() {\n    return this._pollAbort.aborted;\n  }\n  constructor(httpClient, logger, options) {\n    this._httpClient = httpClient;\n    this._logger = logger;\n    this._pollAbort = new AbortController();\n    this._options = options;\n    this._running = false;\n    this.onreceive = null;\n    this.onclose = null;\n  }\n  async connect(url, transferFormat) {\n    Arg.isRequired(url, \"url\");\n    Arg.isRequired(transferFormat, \"transferFormat\");\n    Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\n    this._url = url;\n    this._logger.log(LogLevel.Trace, \"(LongPolling transport) Connecting.\");\n    // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\n    if (transferFormat === TransferFormat.Binary && typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\") {\n      throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\n    }\n    const [name, value] = getUserAgentHeader();\n    const headers = {\n      [name]: value,\n      ...this._options.headers\n    };\n    const pollOptions = {\n      abortSignal: this._pollAbort.signal,\n      headers,\n      timeout: 100000,\n      withCredentials: this._options.withCredentials\n    };\n    if (transferFormat === TransferFormat.Binary) {\n      pollOptions.responseType = \"arraybuffer\";\n    }\n    // Make initial long polling request\n    // Server uses first long polling request to finish initializing connection and it returns without data\n    const pollUrl = `${url}&_=${Date.now()}`;\n    this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\n    const response = await this._httpClient.get(pollUrl, pollOptions);\n    if (response.statusCode !== 200) {\n      this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\n      // Mark running as false so that the poll immediately ends and runs the close logic\n      this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\n      this._running = false;\n    } else {\n      this._running = true;\n    }\n    this._receiving = this._poll(this._url, pollOptions);\n  }\n  async _poll(url, pollOptions) {\n    try {\n      while (this._running) {\n        try {\n          const pollUrl = `${url}&_=${Date.now()}`;\n          this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\n          const response = await this._httpClient.get(pollUrl, pollOptions);\n          if (response.statusCode === 204) {\n            this._logger.log(LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\n            this._running = false;\n          } else if (response.statusCode !== 200) {\n            this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\n            // Unexpected status code\n            this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\n            this._running = false;\n          } else {\n            // Process the response\n            if (response.content) {\n              this._logger.log(LogLevel.Trace, `(LongPolling transport) data received. ${getDataDetail(response.content, this._options.logMessageContent)}.`);\n              if (this.onreceive) {\n                this.onreceive(response.content);\n              }\n            } else {\n              // This is another way timeout manifest.\n              this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\n            }\n          }\n        } catch (e) {\n          if (!this._running) {\n            // Log but disregard errors that occur after stopping\n            this._logger.log(LogLevel.Trace, `(LongPolling transport) Poll errored after shutdown: ${e.message}`);\n          } else {\n            if (e instanceof TimeoutError) {\n              // Ignore timeouts and reissue the poll.\n              this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\n            } else {\n              // Close the connection with the error as the result.\n              this._closeError = e;\n              this._running = false;\n            }\n          }\n        }\n      }\n    } finally {\n      this._logger.log(LogLevel.Trace, \"(LongPolling transport) Polling complete.\");\n      // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\n      // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\n      if (!this.pollAborted) {\n        this._raiseOnClose();\n      }\n    }\n  }\n  async send(data) {\n    if (!this._running) {\n      return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\n    }\n    return sendMessage(this._logger, \"LongPolling\", this._httpClient, this._url, data, this._options);\n  }\n  async stop() {\n    this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stopping polling.\");\n    // Tell receiving loop to stop, abort any current request, and then wait for it to finish\n    this._running = false;\n    this._pollAbort.abort();\n    try {\n      await this._receiving;\n      // Send DELETE to clean up long polling on the server\n      this._logger.log(LogLevel.Trace, `(LongPolling transport) sending DELETE request to ${this._url}.`);\n      const headers = {};\n      const [name, value] = getUserAgentHeader();\n      headers[name] = value;\n      const deleteOptions = {\n        headers: {\n          ...headers,\n          ...this._options.headers\n        },\n        timeout: this._options.timeout,\n        withCredentials: this._options.withCredentials\n      };\n      let error;\n      try {\n        await this._httpClient.delete(this._url, deleteOptions);\n      } catch (err) {\n        error = err;\n      }\n      if (error) {\n        if (error instanceof HttpError) {\n          if (error.statusCode === 404) {\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) A 404 response was returned from sending a DELETE request.\");\n          } else {\n            this._logger.log(LogLevel.Trace, `(LongPolling transport) Error sending a DELETE request: ${error}`);\n          }\n        }\n      } else {\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) DELETE request accepted.\");\n      }\n    } finally {\n      this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stop finished.\");\n      // Raise close event here instead of in polling\n      // It needs to happen after the DELETE request is sent\n      this._raiseOnClose();\n    }\n  }\n  _raiseOnClose() {\n    if (this.onclose) {\n      let logMessage = \"(LongPolling transport) Firing onclose event.\";\n      if (this._closeError) {\n        logMessage += \" Error: \" + this._closeError;\n      }\n      this._logger.log(LogLevel.Trace, logMessage);\n      this.onclose(this._closeError);\n    }\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { LogLevel } from \"./ILogger\";\nimport { TransferFormat } from \"./ITransport\";\nimport { Arg, getDataDetail, getUserAgentHeader, Platform, sendMessage } from \"./Utils\";\n/** @private */\nexport class ServerSentEventsTransport {\n  constructor(httpClient, accessToken, logger, options) {\n    this._httpClient = httpClient;\n    this._accessToken = accessToken;\n    this._logger = logger;\n    this._options = options;\n    this.onreceive = null;\n    this.onclose = null;\n  }\n  async connect(url, transferFormat) {\n    Arg.isRequired(url, \"url\");\n    Arg.isRequired(transferFormat, \"transferFormat\");\n    Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\n    this._logger.log(LogLevel.Trace, \"(SSE transport) Connecting.\");\n    // set url before accessTokenFactory because this._url is only for send and we set the auth header instead of the query string for send\n    this._url = url;\n    if (this._accessToken) {\n      url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(this._accessToken)}`;\n    }\n    return new Promise((resolve, reject) => {\n      let opened = false;\n      if (transferFormat !== TransferFormat.Text) {\n        reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\n        return;\n      }\n      let eventSource;\n      if (Platform.isBrowser || Platform.isWebWorker) {\n        eventSource = new this._options.EventSource(url, {\n          withCredentials: this._options.withCredentials\n        });\n      } else {\n        // Non-browser passes cookies via the dictionary\n        const cookies = this._httpClient.getCookieString(url);\n        const headers = {};\n        headers.Cookie = cookies;\n        const [name, value] = getUserAgentHeader();\n        headers[name] = value;\n        eventSource = new this._options.EventSource(url, {\n          withCredentials: this._options.withCredentials,\n          headers: {\n            ...headers,\n            ...this._options.headers\n          }\n        });\n      }\n      try {\n        eventSource.onmessage = e => {\n          if (this.onreceive) {\n            try {\n              this._logger.log(LogLevel.Trace, `(SSE transport) data received. ${getDataDetail(e.data, this._options.logMessageContent)}.`);\n              this.onreceive(e.data);\n            } catch (error) {\n              this._close(error);\n              return;\n            }\n          }\n        };\n        // @ts-ignore: not using event on purpose\n        eventSource.onerror = e => {\n          // EventSource doesn't give any useful information about server side closes.\n          if (opened) {\n            this._close();\n          } else {\n            reject(new Error(\"EventSource failed to connect. The connection could not be found on the server,\" + \" either the connection ID is not present on the server, or a proxy is refusing/buffering the connection.\" + \" If you have multiple servers check that sticky sessions are enabled.\"));\n          }\n        };\n        eventSource.onopen = () => {\n          this._logger.log(LogLevel.Information, `SSE connected to ${this._url}`);\n          this._eventSource = eventSource;\n          opened = true;\n          resolve();\n        };\n      } catch (e) {\n        reject(e);\n        return;\n      }\n    });\n  }\n  async send(data) {\n    if (!this._eventSource) {\n      return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\n    }\n    return sendMessage(this._logger, \"SSE\", this._httpClient, this._url, data, this._options);\n  }\n  stop() {\n    this._close();\n    return Promise.resolve();\n  }\n  _close(e) {\n    if (this._eventSource) {\n      this._eventSource.close();\n      this._eventSource = undefined;\n      if (this.onclose) {\n        this.onclose(e);\n      }\n    }\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { HeaderNames } from \"./HeaderNames\";\nimport { LogLevel } from \"./ILogger\";\nimport { TransferFormat } from \"./ITransport\";\nimport { Arg, getDataDetail, getUserAgentHeader, Platform } from \"./Utils\";\n/** @private */\nexport class WebSocketTransport {\n  constructor(httpClient, accessTokenFactory, logger, logMessageContent, webSocketConstructor, headers) {\n    this._logger = logger;\n    this._accessTokenFactory = accessTokenFactory;\n    this._logMessageContent = logMessageContent;\n    this._webSocketConstructor = webSocketConstructor;\n    this._httpClient = httpClient;\n    this.onreceive = null;\n    this.onclose = null;\n    this._headers = headers;\n  }\n  async connect(url, transferFormat) {\n    Arg.isRequired(url, \"url\");\n    Arg.isRequired(transferFormat, \"transferFormat\");\n    Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\n    this._logger.log(LogLevel.Trace, \"(WebSockets transport) Connecting.\");\n    let token;\n    if (this._accessTokenFactory) {\n      token = await this._accessTokenFactory();\n    }\n    return new Promise((resolve, reject) => {\n      url = url.replace(/^http/, \"ws\");\n      let webSocket;\n      const cookies = this._httpClient.getCookieString(url);\n      let opened = false;\n      if (Platform.isNode || Platform.isReactNative) {\n        const headers = {};\n        const [name, value] = getUserAgentHeader();\n        headers[name] = value;\n        if (token) {\n          headers[HeaderNames.Authorization] = `Bearer ${token}`;\n        }\n        if (cookies) {\n          headers[HeaderNames.Cookie] = cookies;\n        }\n        // Only pass headers when in non-browser environments\n        webSocket = new this._webSocketConstructor(url, undefined, {\n          headers: {\n            ...headers,\n            ...this._headers\n          }\n        });\n      } else {\n        if (token) {\n          url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\n        }\n      }\n      if (!webSocket) {\n        // Chrome is not happy with passing 'undefined' as protocol\n        webSocket = new this._webSocketConstructor(url);\n      }\n      if (transferFormat === TransferFormat.Binary) {\n        webSocket.binaryType = \"arraybuffer\";\n      }\n      webSocket.onopen = _event => {\n        this._logger.log(LogLevel.Information, `WebSocket connected to ${url}.`);\n        this._webSocket = webSocket;\n        opened = true;\n        resolve();\n      };\n      webSocket.onerror = event => {\n        let error = null;\n        // ErrorEvent is a browser only type we need to check if the type exists before using it\n        if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\n          error = event.error;\n        } else {\n          error = \"There was an error with the transport\";\n        }\n        this._logger.log(LogLevel.Information, `(WebSockets transport) ${error}.`);\n      };\n      webSocket.onmessage = message => {\n        this._logger.log(LogLevel.Trace, `(WebSockets transport) data received. ${getDataDetail(message.data, this._logMessageContent)}.`);\n        if (this.onreceive) {\n          try {\n            this.onreceive(message.data);\n          } catch (error) {\n            this._close(error);\n            return;\n          }\n        }\n      };\n      webSocket.onclose = event => {\n        // Don't call close handler if connection was never established\n        // We'll reject the connect call instead\n        if (opened) {\n          this._close(event);\n        } else {\n          let error = null;\n          // ErrorEvent is a browser only type we need to check if the type exists before using it\n          if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\n            error = event.error;\n          } else {\n            error = \"WebSocket failed to connect. The connection could not be found on the server,\" + \" either the endpoint may not be a SignalR endpoint,\" + \" the connection ID is not present on the server, or there is a proxy blocking WebSockets.\" + \" If you have multiple servers check that sticky sessions are enabled.\";\n          }\n          reject(new Error(error));\n        }\n      };\n    });\n  }\n  send(data) {\n    if (this._webSocket && this._webSocket.readyState === this._webSocketConstructor.OPEN) {\n      this._logger.log(LogLevel.Trace, `(WebSockets transport) sending data. ${getDataDetail(data, this._logMessageContent)}.`);\n      this._webSocket.send(data);\n      return Promise.resolve();\n    }\n    return Promise.reject(\"WebSocket is not in the OPEN state\");\n  }\n  stop() {\n    if (this._webSocket) {\n      // Manually invoke onclose callback inline so we know the HttpConnection was closed properly before returning\n      // This also solves an issue where websocket.onclose could take 18+ seconds to trigger during network disconnects\n      this._close(undefined);\n    }\n    return Promise.resolve();\n  }\n  _close(event) {\n    // webSocket will be null if the transport did not start successfully\n    if (this._webSocket) {\n      // Clear websocket handlers because we are considering the socket closed now\n      this._webSocket.onclose = () => {};\n      this._webSocket.onmessage = () => {};\n      this._webSocket.onerror = () => {};\n      this._webSocket.close();\n      this._webSocket = undefined;\n    }\n    this._logger.log(LogLevel.Trace, \"(WebSockets transport) socket closed.\");\n    if (this.onclose) {\n      if (this._isCloseEvent(event) && (event.wasClean === false || event.code !== 1000)) {\n        this.onclose(new Error(`WebSocket closed with status code: ${event.code} (${event.reason || \"no reason given\"}).`));\n      } else if (event instanceof Error) {\n        this.onclose(event);\n      } else {\n        this.onclose();\n      }\n    }\n  }\n  _isCloseEvent(event) {\n    return event && typeof event.wasClean === \"boolean\" && typeof event.code === \"number\";\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { AccessTokenHttpClient } from \"./AccessTokenHttpClient\";\nimport { DefaultHttpClient } from \"./DefaultHttpClient\";\nimport { AggregateErrors, DisabledTransportError, FailedToNegotiateWithServerError, FailedToStartTransportError, HttpError, UnsupportedTransportError, AbortError } from \"./Errors\";\nimport { LogLevel } from \"./ILogger\";\nimport { HttpTransportType, TransferFormat } from \"./ITransport\";\nimport { LongPollingTransport } from \"./LongPollingTransport\";\nimport { ServerSentEventsTransport } from \"./ServerSentEventsTransport\";\nimport { Arg, createLogger, getUserAgentHeader, Platform } from \"./Utils\";\nimport { WebSocketTransport } from \"./WebSocketTransport\";\nconst MAX_REDIRECTS = 100;\n/** @private */\nexport class HttpConnection {\n  constructor(url, options = {}) {\n    this._stopPromiseResolver = () => {};\n    this.features = {};\n    this._negotiateVersion = 1;\n    Arg.isRequired(url, \"url\");\n    this._logger = createLogger(options.logger);\n    this.baseUrl = this._resolveUrl(url);\n    options = options || {};\n    options.logMessageContent = options.logMessageContent === undefined ? false : options.logMessageContent;\n    if (typeof options.withCredentials === \"boolean\" || options.withCredentials === undefined) {\n      options.withCredentials = options.withCredentials === undefined ? true : options.withCredentials;\n    } else {\n      throw new Error(\"withCredentials option was not a 'boolean' or 'undefined' value\");\n    }\n    options.timeout = options.timeout === undefined ? 100 * 1000 : options.timeout;\n    let webSocketModule = null;\n    let eventSourceModule = null;\n    if (Platform.isNode && typeof require !== \"undefined\") {\n      // In order to ignore the dynamic require in webpack builds we need to do this magic\n      // @ts-ignore: TS doesn't know about these names\n      const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\n      webSocketModule = requireFunc(\"ws\");\n      eventSourceModule = requireFunc(\"eventsource\");\n    }\n    if (!Platform.isNode && typeof WebSocket !== \"undefined\" && !options.WebSocket) {\n      options.WebSocket = WebSocket;\n    } else if (Platform.isNode && !options.WebSocket) {\n      if (webSocketModule) {\n        options.WebSocket = webSocketModule;\n      }\n    }\n    if (!Platform.isNode && typeof EventSource !== \"undefined\" && !options.EventSource) {\n      options.EventSource = EventSource;\n    } else if (Platform.isNode && !options.EventSource) {\n      if (typeof eventSourceModule !== \"undefined\") {\n        options.EventSource = eventSourceModule;\n      }\n    }\n    this._httpClient = new AccessTokenHttpClient(options.httpClient || new DefaultHttpClient(this._logger), options.accessTokenFactory);\n    this._connectionState = \"Disconnected\" /* ConnectionState.Disconnected */;\n    this._connectionStarted = false;\n    this._options = options;\n    this.onreceive = null;\n    this.onclose = null;\n  }\n  async start(transferFormat) {\n    transferFormat = transferFormat || TransferFormat.Binary;\n    Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\n    this._logger.log(LogLevel.Debug, `Starting connection with transfer format '${TransferFormat[transferFormat]}'.`);\n    if (this._connectionState !== \"Disconnected\" /* ConnectionState.Disconnected */) {\n      return Promise.reject(new Error(\"Cannot start an HttpConnection that is not in the 'Disconnected' state.\"));\n    }\n    this._connectionState = \"Connecting\" /* ConnectionState.Connecting */;\n    this._startInternalPromise = this._startInternal(transferFormat);\n    await this._startInternalPromise;\n    // The TypeScript compiler thinks that connectionState must be Connecting here. The TypeScript compiler is wrong.\n    if (this._connectionState === \"Disconnecting\" /* ConnectionState.Disconnecting */) {\n      // stop() was called and transitioned the client into the Disconnecting state.\n      const message = \"Failed to start the HttpConnection before stop() was called.\";\n      this._logger.log(LogLevel.Error, message);\n      // We cannot await stopPromise inside startInternal since stopInternal awaits the startInternalPromise.\n      await this._stopPromise;\n      return Promise.reject(new AbortError(message));\n    } else if (this._connectionState !== \"Connected\" /* ConnectionState.Connected */) {\n      // stop() was called and transitioned the client into the Disconnecting state.\n      const message = \"HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!\";\n      this._logger.log(LogLevel.Error, message);\n      return Promise.reject(new AbortError(message));\n    }\n    this._connectionStarted = true;\n  }\n  send(data) {\n    if (this._connectionState !== \"Connected\" /* ConnectionState.Connected */) {\n      return Promise.reject(new Error(\"Cannot send data if the connection is not in the 'Connected' State.\"));\n    }\n    if (!this._sendQueue) {\n      this._sendQueue = new TransportSendQueue(this.transport);\n    }\n    // Transport will not be null if state is connected\n    return this._sendQueue.send(data);\n  }\n  async stop(error) {\n    if (this._connectionState === \"Disconnected\" /* ConnectionState.Disconnected */) {\n      this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnected state.`);\n      return Promise.resolve();\n    }\n    if (this._connectionState === \"Disconnecting\" /* ConnectionState.Disconnecting */) {\n      this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\n      return this._stopPromise;\n    }\n    this._connectionState = \"Disconnecting\" /* ConnectionState.Disconnecting */;\n    this._stopPromise = new Promise(resolve => {\n      // Don't complete stop() until stopConnection() completes.\n      this._stopPromiseResolver = resolve;\n    });\n    // stopInternal should never throw so just observe it.\n    await this._stopInternal(error);\n    await this._stopPromise;\n  }\n  async _stopInternal(error) {\n    // Set error as soon as possible otherwise there is a race between\n    // the transport closing and providing an error and the error from a close message\n    // We would prefer the close message error.\n    this._stopError = error;\n    try {\n      await this._startInternalPromise;\n    } catch (e) {\n      // This exception is returned to the user as a rejected Promise from the start method.\n    }\n    // The transport's onclose will trigger stopConnection which will run our onclose event.\n    // The transport should always be set if currently connected. If it wasn't set, it's likely because\n    // stop was called during start() and start() failed.\n    if (this.transport) {\n      try {\n        await this.transport.stop();\n      } catch (e) {\n        this._logger.log(LogLevel.Error, `HttpConnection.transport.stop() threw error '${e}'.`);\n        this._stopConnection();\n      }\n      this.transport = undefined;\n    } else {\n      this._logger.log(LogLevel.Debug, \"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.\");\n    }\n  }\n  async _startInternal(transferFormat) {\n    // Store the original base url and the access token factory since they may change\n    // as part of negotiating\n    let url = this.baseUrl;\n    this._accessTokenFactory = this._options.accessTokenFactory;\n    this._httpClient._accessTokenFactory = this._accessTokenFactory;\n    try {\n      if (this._options.skipNegotiation) {\n        if (this._options.transport === HttpTransportType.WebSockets) {\n          // No need to add a connection ID in this case\n          this.transport = this._constructTransport(HttpTransportType.WebSockets);\n          // We should just call connect directly in this case.\n          // No fallback or negotiate in this case.\n          await this._startTransport(url, transferFormat);\n        } else {\n          throw new Error(\"Negotiation can only be skipped when using the WebSocket transport directly.\");\n        }\n      } else {\n        let negotiateResponse = null;\n        let redirects = 0;\n        do {\n          negotiateResponse = await this._getNegotiationResponse(url);\n          // the user tries to stop the connection when it is being started\n          if (this._connectionState === \"Disconnecting\" /* ConnectionState.Disconnecting */ || this._connectionState === \"Disconnected\" /* ConnectionState.Disconnected */) {\n            throw new AbortError(\"The connection was stopped during negotiation.\");\n          }\n          if (negotiateResponse.error) {\n            throw new Error(negotiateResponse.error);\n          }\n          if (negotiateResponse.ProtocolVersion) {\n            throw new Error(\"Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.\");\n          }\n          if (negotiateResponse.url) {\n            url = negotiateResponse.url;\n          }\n          if (negotiateResponse.accessToken) {\n            // Replace the current access token factory with one that uses\n            // the returned access token\n            const accessToken = negotiateResponse.accessToken;\n            this._accessTokenFactory = () => accessToken;\n            // set the factory to undefined so the AccessTokenHttpClient won't retry with the same token, since we know it won't change until a connection restart\n            this._httpClient._accessToken = accessToken;\n            this._httpClient._accessTokenFactory = undefined;\n          }\n          redirects++;\n        } while (negotiateResponse.url && redirects < MAX_REDIRECTS);\n        if (redirects === MAX_REDIRECTS && negotiateResponse.url) {\n          throw new Error(\"Negotiate redirection limit exceeded.\");\n        }\n        await this._createTransport(url, this._options.transport, negotiateResponse, transferFormat);\n      }\n      if (this.transport instanceof LongPollingTransport) {\n        this.features.inherentKeepAlive = true;\n      }\n      if (this._connectionState === \"Connecting\" /* ConnectionState.Connecting */) {\n        // Ensure the connection transitions to the connected state prior to completing this.startInternalPromise.\n        // start() will handle the case when stop was called and startInternal exits still in the disconnecting state.\n        this._logger.log(LogLevel.Debug, \"The HttpConnection connected successfully.\");\n        this._connectionState = \"Connected\" /* ConnectionState.Connected */;\n      }\n      // stop() is waiting on us via this.startInternalPromise so keep this.transport around so it can clean up.\n      // This is the only case startInternal can exit in neither the connected nor disconnected state because stopConnection()\n      // will transition to the disconnected state. start() will wait for the transition using the stopPromise.\n    } catch (e) {\n      this._logger.log(LogLevel.Error, \"Failed to start the connection: \" + e);\n      this._connectionState = \"Disconnected\" /* ConnectionState.Disconnected */;\n      this.transport = undefined;\n      // if start fails, any active calls to stop assume that start will complete the stop promise\n      this._stopPromiseResolver();\n      return Promise.reject(e);\n    }\n  }\n  async _getNegotiationResponse(url) {\n    const headers = {};\n    const [name, value] = getUserAgentHeader();\n    headers[name] = value;\n    const negotiateUrl = this._resolveNegotiateUrl(url);\n    this._logger.log(LogLevel.Debug, `Sending negotiation request: ${negotiateUrl}.`);\n    try {\n      const response = await this._httpClient.post(negotiateUrl, {\n        content: \"\",\n        headers: {\n          ...headers,\n          ...this._options.headers\n        },\n        timeout: this._options.timeout,\n        withCredentials: this._options.withCredentials\n      });\n      if (response.statusCode !== 200) {\n        return Promise.reject(new Error(`Unexpected status code returned from negotiate '${response.statusCode}'`));\n      }\n      const negotiateResponse = JSON.parse(response.content);\n      if (!negotiateResponse.negotiateVersion || negotiateResponse.negotiateVersion < 1) {\n        // Negotiate version 0 doesn't use connectionToken\n        // So we set it equal to connectionId so all our logic can use connectionToken without being aware of the negotiate version\n        negotiateResponse.connectionToken = negotiateResponse.connectionId;\n      }\n      if (negotiateResponse.useStatefulReconnect && this._options._useStatefulReconnect !== true) {\n        return Promise.reject(new FailedToNegotiateWithServerError(\"Client didn't negotiate Stateful Reconnect but the server did.\"));\n      }\n      return negotiateResponse;\n    } catch (e) {\n      let errorMessage = \"Failed to complete negotiation with the server: \" + e;\n      if (e instanceof HttpError) {\n        if (e.statusCode === 404) {\n          errorMessage = errorMessage + \" Either this is not a SignalR endpoint or there is a proxy blocking the connection.\";\n        }\n      }\n      this._logger.log(LogLevel.Error, errorMessage);\n      return Promise.reject(new FailedToNegotiateWithServerError(errorMessage));\n    }\n  }\n  _createConnectUrl(url, connectionToken) {\n    if (!connectionToken) {\n      return url;\n    }\n    return url + (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + `id=${connectionToken}`;\n  }\n  async _createTransport(url, requestedTransport, negotiateResponse, requestedTransferFormat) {\n    let connectUrl = this._createConnectUrl(url, negotiateResponse.connectionToken);\n    if (this._isITransport(requestedTransport)) {\n      this._logger.log(LogLevel.Debug, \"Connection was provided an instance of ITransport, using that directly.\");\n      this.transport = requestedTransport;\n      await this._startTransport(connectUrl, requestedTransferFormat);\n      this.connectionId = negotiateResponse.connectionId;\n      return;\n    }\n    const transportExceptions = [];\n    const transports = negotiateResponse.availableTransports || [];\n    let negotiate = negotiateResponse;\n    for (const endpoint of transports) {\n      const transportOrError = this._resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat, (negotiate === null || negotiate === void 0 ? void 0 : negotiate.useStatefulReconnect) === true);\n      if (transportOrError instanceof Error) {\n        // Store the error and continue, we don't want to cause a re-negotiate in these cases\n        transportExceptions.push(`${endpoint.transport} failed:`);\n        transportExceptions.push(transportOrError);\n      } else if (this._isITransport(transportOrError)) {\n        this.transport = transportOrError;\n        if (!negotiate) {\n          try {\n            negotiate = await this._getNegotiationResponse(url);\n          } catch (ex) {\n            return Promise.reject(ex);\n          }\n          connectUrl = this._createConnectUrl(url, negotiate.connectionToken);\n        }\n        try {\n          await this._startTransport(connectUrl, requestedTransferFormat);\n          this.connectionId = negotiate.connectionId;\n          return;\n        } catch (ex) {\n          this._logger.log(LogLevel.Error, `Failed to start the transport '${endpoint.transport}': ${ex}`);\n          negotiate = undefined;\n          transportExceptions.push(new FailedToStartTransportError(`${endpoint.transport} failed: ${ex}`, HttpTransportType[endpoint.transport]));\n          if (this._connectionState !== \"Connecting\" /* ConnectionState.Connecting */) {\n            const message = \"Failed to select transport before stop() was called.\";\n            this._logger.log(LogLevel.Debug, message);\n            return Promise.reject(new AbortError(message));\n          }\n        }\n      }\n    }\n    if (transportExceptions.length > 0) {\n      return Promise.reject(new AggregateErrors(`Unable to connect to the server with any of the available transports. ${transportExceptions.join(\" \")}`, transportExceptions));\n    }\n    return Promise.reject(new Error(\"None of the transports supported by the client are supported by the server.\"));\n  }\n  _constructTransport(transport) {\n    switch (transport) {\n      case HttpTransportType.WebSockets:\n        if (!this._options.WebSocket) {\n          throw new Error(\"'WebSocket' is not supported in your environment.\");\n        }\n        return new WebSocketTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options.logMessageContent, this._options.WebSocket, this._options.headers || {});\n      case HttpTransportType.ServerSentEvents:\n        if (!this._options.EventSource) {\n          throw new Error(\"'EventSource' is not supported in your environment.\");\n        }\n        return new ServerSentEventsTransport(this._httpClient, this._httpClient._accessToken, this._logger, this._options);\n      case HttpTransportType.LongPolling:\n        return new LongPollingTransport(this._httpClient, this._logger, this._options);\n      default:\n        throw new Error(`Unknown transport: ${transport}.`);\n    }\n  }\n  _startTransport(url, transferFormat) {\n    this.transport.onreceive = this.onreceive;\n    if (this.features.reconnect) {\n      this.transport.onclose = async e => {\n        let callStop = false;\n        if (this.features.reconnect) {\n          try {\n            this.features.disconnected();\n            await this.transport.connect(url, transferFormat);\n            await this.features.resend();\n          } catch {\n            callStop = true;\n          }\n        } else {\n          this._stopConnection(e);\n          return;\n        }\n        if (callStop) {\n          this._stopConnection(e);\n        }\n      };\n    } else {\n      this.transport.onclose = e => this._stopConnection(e);\n    }\n    return this.transport.connect(url, transferFormat);\n  }\n  _resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat, useStatefulReconnect) {\n    const transport = HttpTransportType[endpoint.transport];\n    if (transport === null || transport === undefined) {\n      this._logger.log(LogLevel.Debug, `Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\n      return new Error(`Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\n    } else {\n      if (transportMatches(requestedTransport, transport)) {\n        const transferFormats = endpoint.transferFormats.map(s => TransferFormat[s]);\n        if (transferFormats.indexOf(requestedTransferFormat) >= 0) {\n          if (transport === HttpTransportType.WebSockets && !this._options.WebSocket || transport === HttpTransportType.ServerSentEvents && !this._options.EventSource) {\n            this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it is not supported in your environment.'`);\n            return new UnsupportedTransportError(`'${HttpTransportType[transport]}' is not supported in your environment.`, transport);\n          } else {\n            this._logger.log(LogLevel.Debug, `Selecting transport '${HttpTransportType[transport]}'.`);\n            try {\n              this.features.reconnect = transport === HttpTransportType.WebSockets ? useStatefulReconnect : undefined;\n              return this._constructTransport(transport);\n            } catch (ex) {\n              return ex;\n            }\n          }\n        } else {\n          this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it does not support the requested transfer format '${TransferFormat[requestedTransferFormat]}'.`);\n          return new Error(`'${HttpTransportType[transport]}' does not support ${TransferFormat[requestedTransferFormat]}.`);\n        }\n      } else {\n        this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it was disabled by the client.`);\n        return new DisabledTransportError(`'${HttpTransportType[transport]}' is disabled by the client.`, transport);\n      }\n    }\n  }\n  _isITransport(transport) {\n    return transport && typeof transport === \"object\" && \"connect\" in transport;\n  }\n  _stopConnection(error) {\n    this._logger.log(LogLevel.Debug, `HttpConnection.stopConnection(${error}) called while in state ${this._connectionState}.`);\n    this.transport = undefined;\n    // If we have a stopError, it takes precedence over the error from the transport\n    error = this._stopError || error;\n    this._stopError = undefined;\n    if (this._connectionState === \"Disconnected\" /* ConnectionState.Disconnected */) {\n      this._logger.log(LogLevel.Debug, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is already in the disconnected state.`);\n      return;\n    }\n    if (this._connectionState === \"Connecting\" /* ConnectionState.Connecting */) {\n      this._logger.log(LogLevel.Warning, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is still in the connecting state.`);\n      throw new Error(`HttpConnection.stopConnection(${error}) was called while the connection is still in the connecting state.`);\n    }\n    if (this._connectionState === \"Disconnecting\" /* ConnectionState.Disconnecting */) {\n      // A call to stop() induced this call to stopConnection and needs to be completed.\n      // Any stop() awaiters will be scheduled to continue after the onclose callback fires.\n      this._stopPromiseResolver();\n    }\n    if (error) {\n      this._logger.log(LogLevel.Error, `Connection disconnected with error '${error}'.`);\n    } else {\n      this._logger.log(LogLevel.Information, \"Connection disconnected.\");\n    }\n    if (this._sendQueue) {\n      this._sendQueue.stop().catch(e => {\n        this._logger.log(LogLevel.Error, `TransportSendQueue.stop() threw error '${e}'.`);\n      });\n      this._sendQueue = undefined;\n    }\n    this.connectionId = undefined;\n    this._connectionState = \"Disconnected\" /* ConnectionState.Disconnected */;\n    if (this._connectionStarted) {\n      this._connectionStarted = false;\n      try {\n        if (this.onclose) {\n          this.onclose(error);\n        }\n      } catch (e) {\n        this._logger.log(LogLevel.Error, `HttpConnection.onclose(${error}) threw error '${e}'.`);\n      }\n    }\n  }\n  _resolveUrl(url) {\n    // startsWith is not supported in IE\n    if (url.lastIndexOf(\"https://\", 0) === 0 || url.lastIndexOf(\"http://\", 0) === 0) {\n      return url;\n    }\n    if (!Platform.isBrowser) {\n      throw new Error(`Cannot resolve '${url}'.`);\n    }\n    // Setting the url to the href propery of an anchor tag handles normalization\n    // for us. There are 3 main cases.\n    // 1. Relative path normalization e.g \"b\" -> \"http://localhost:5000/a/b\"\n    // 2. Absolute path normalization e.g \"/a/b\" -> \"http://localhost:5000/a/b\"\n    // 3. Networkpath reference normalization e.g \"//localhost:5000/a/b\" -> \"http://localhost:5000/a/b\"\n    const aTag = window.document.createElement(\"a\");\n    aTag.href = url;\n    this._logger.log(LogLevel.Information, `Normalizing '${url}' to '${aTag.href}'.`);\n    return aTag.href;\n  }\n  _resolveNegotiateUrl(url) {\n    const negotiateUrl = new URL(url);\n    if (negotiateUrl.pathname.endsWith('/')) {\n      negotiateUrl.pathname += \"negotiate\";\n    } else {\n      negotiateUrl.pathname += \"/negotiate\";\n    }\n    const searchParams = new URLSearchParams(negotiateUrl.searchParams);\n    if (!searchParams.has(\"negotiateVersion\")) {\n      searchParams.append(\"negotiateVersion\", this._negotiateVersion.toString());\n    }\n    if (searchParams.has(\"useStatefulReconnect\")) {\n      if (searchParams.get(\"useStatefulReconnect\") === \"true\") {\n        this._options._useStatefulReconnect = true;\n      }\n    } else if (this._options._useStatefulReconnect === true) {\n      searchParams.append(\"useStatefulReconnect\", \"true\");\n    }\n    negotiateUrl.search = searchParams.toString();\n    return negotiateUrl.toString();\n  }\n}\nfunction transportMatches(requestedTransport, actualTransport) {\n  return !requestedTransport || (actualTransport & requestedTransport) !== 0;\n}\n/** @private */\nexport class TransportSendQueue {\n  constructor(_transport) {\n    this._transport = _transport;\n    this._buffer = [];\n    this._executing = true;\n    this._sendBufferedData = new PromiseSource();\n    this._transportResult = new PromiseSource();\n    this._sendLoopPromise = this._sendLoop();\n  }\n  send(data) {\n    this._bufferData(data);\n    if (!this._transportResult) {\n      this._transportResult = new PromiseSource();\n    }\n    return this._transportResult.promise;\n  }\n  stop() {\n    this._executing = false;\n    this._sendBufferedData.resolve();\n    return this._sendLoopPromise;\n  }\n  _bufferData(data) {\n    if (this._buffer.length && typeof this._buffer[0] !== typeof data) {\n      throw new Error(`Expected data to be of type ${typeof this._buffer} but was of type ${typeof data}`);\n    }\n    this._buffer.push(data);\n    this._sendBufferedData.resolve();\n  }\n  async _sendLoop() {\n    while (true) {\n      await this._sendBufferedData.promise;\n      if (!this._executing) {\n        if (this._transportResult) {\n          this._transportResult.reject(\"Connection stopped.\");\n        }\n        break;\n      }\n      this._sendBufferedData = new PromiseSource();\n      const transportResult = this._transportResult;\n      this._transportResult = undefined;\n      const data = typeof this._buffer[0] === \"string\" ? this._buffer.join(\"\") : TransportSendQueue._concatBuffers(this._buffer);\n      this._buffer.length = 0;\n      try {\n        await this._transport.send(data);\n        transportResult.resolve();\n      } catch (error) {\n        transportResult.reject(error);\n      }\n    }\n  }\n  static _concatBuffers(arrayBuffers) {\n    const totalLength = arrayBuffers.map(b => b.byteLength).reduce((a, b) => a + b);\n    const result = new Uint8Array(totalLength);\n    let offset = 0;\n    for (const item of arrayBuffers) {\n      result.set(new Uint8Array(item), offset);\n      offset += item.byteLength;\n    }\n    return result.buffer;\n  }\n}\nclass PromiseSource {\n  constructor() {\n    this.promise = new Promise((resolve, reject) => [this._resolver, this._rejecter] = [resolve, reject]);\n  }\n  resolve() {\n    this._resolver();\n  }\n  reject(reason) {\n    this._rejecter(reason);\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { MessageType } from \"./IHubProtocol\";\nimport { LogLevel } from \"./ILogger\";\nimport { TransferFormat } from \"./ITransport\";\nimport { NullLogger } from \"./Loggers\";\nimport { TextMessageFormat } from \"./TextMessageFormat\";\nconst JSON_HUB_PROTOCOL_NAME = \"json\";\n/** Implements the JSON Hub Protocol. */\nexport class JsonHubProtocol {\n  constructor() {\n    /** @inheritDoc */\n    this.name = JSON_HUB_PROTOCOL_NAME;\n    /** @inheritDoc */\n    this.version = 2;\n    /** @inheritDoc */\n    this.transferFormat = TransferFormat.Text;\n  }\n  /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n   *\r\n   * @param {string} input A string containing the serialized representation.\r\n   * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n   */\n  parseMessages(input, logger) {\n    // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\n    if (typeof input !== \"string\") {\n      throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\n    }\n    if (!input) {\n      return [];\n    }\n    if (logger === null) {\n      logger = NullLogger.instance;\n    }\n    // Parse the messages\n    const messages = TextMessageFormat.parse(input);\n    const hubMessages = [];\n    for (const message of messages) {\n      const parsedMessage = JSON.parse(message);\n      if (typeof parsedMessage.type !== \"number\") {\n        throw new Error(\"Invalid payload.\");\n      }\n      switch (parsedMessage.type) {\n        case MessageType.Invocation:\n          this._isInvocationMessage(parsedMessage);\n          break;\n        case MessageType.StreamItem:\n          this._isStreamItemMessage(parsedMessage);\n          break;\n        case MessageType.Completion:\n          this._isCompletionMessage(parsedMessage);\n          break;\n        case MessageType.Ping:\n          // Single value, no need to validate\n          break;\n        case MessageType.Close:\n          // All optional values, no need to validate\n          break;\n        case MessageType.Ack:\n          this._isAckMessage(parsedMessage);\n          break;\n        case MessageType.Sequence:\n          this._isSequenceMessage(parsedMessage);\n          break;\n        default:\n          // Future protocol changes can add message types, old clients can ignore them\n          logger.log(LogLevel.Information, \"Unknown message type '\" + parsedMessage.type + \"' ignored.\");\n          continue;\n      }\n      hubMessages.push(parsedMessage);\n    }\n    return hubMessages;\n  }\n  /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string and returns it.\r\n   *\r\n   * @param {HubMessage} message The message to write.\r\n   * @returns {string} A string containing the serialized representation of the message.\r\n   */\n  writeMessage(message) {\n    return TextMessageFormat.write(JSON.stringify(message));\n  }\n  _isInvocationMessage(message) {\n    this._assertNotEmptyString(message.target, \"Invalid payload for Invocation message.\");\n    if (message.invocationId !== undefined) {\n      this._assertNotEmptyString(message.invocationId, \"Invalid payload for Invocation message.\");\n    }\n  }\n  _isStreamItemMessage(message) {\n    this._assertNotEmptyString(message.invocationId, \"Invalid payload for StreamItem message.\");\n    if (message.item === undefined) {\n      throw new Error(\"Invalid payload for StreamItem message.\");\n    }\n  }\n  _isCompletionMessage(message) {\n    if (message.result && message.error) {\n      throw new Error(\"Invalid payload for Completion message.\");\n    }\n    if (!message.result && message.error) {\n      this._assertNotEmptyString(message.error, \"Invalid payload for Completion message.\");\n    }\n    this._assertNotEmptyString(message.invocationId, \"Invalid payload for Completion message.\");\n  }\n  _isAckMessage(message) {\n    if (typeof message.sequenceId !== 'number') {\n      throw new Error(\"Invalid SequenceId for Ack message.\");\n    }\n  }\n  _isSequenceMessage(message) {\n    if (typeof message.sequenceId !== 'number') {\n      throw new Error(\"Invalid SequenceId for Sequence message.\");\n    }\n  }\n  _assertNotEmptyString(value, errorMessage) {\n    if (typeof value !== \"string\" || value === \"\") {\n      throw new Error(errorMessage);\n    }\n  }\n}\n", "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { DefaultReconnectPolicy } from \"./DefaultReconnectPolicy\";\nimport { HttpConnection } from \"./HttpConnection\";\nimport { HubConnection } from \"./HubConnection\";\nimport { LogLevel } from \"./ILogger\";\nimport { JsonHubProtocol } from \"./JsonHubProtocol\";\nimport { NullLogger } from \"./Loggers\";\nimport { Arg, ConsoleLogger } from \"./Utils\";\nconst LogLevelNameMapping = {\n  trace: LogLevel.Trace,\n  debug: LogLevel.Debug,\n  info: LogLevel.Information,\n  information: LogLevel.Information,\n  warn: LogLevel.Warning,\n  warning: LogLevel.Warning,\n  error: LogLevel.Error,\n  critical: LogLevel.Critical,\n  none: LogLevel.None\n};\nfunction parseLogLevel(name) {\n  // Case-insensitive matching via lower-casing\n  // Yes, I know case-folding is a complicated problem in Unicode, but we only support\n  // the ASCII strings defined in LogLevelNameMapping anyway, so it's fine -anurse.\n  const mapping = LogLevelNameMapping[name.toLowerCase()];\n  if (typeof mapping !== \"undefined\") {\n    return mapping;\n  } else {\n    throw new Error(`Unknown log level: ${name}`);\n  }\n}\n/** A builder for configuring {@link @microsoft/signalr.HubConnection} instances. */\nexport class HubConnectionBuilder {\n  configureLogging(logging) {\n    Arg.isRequired(logging, \"logging\");\n    if (isLogger(logging)) {\n      this.logger = logging;\n    } else if (typeof logging === \"string\") {\n      const logLevel = parseLogLevel(logging);\n      this.logger = new ConsoleLogger(logLevel);\n    } else {\n      this.logger = new ConsoleLogger(logging);\n    }\n    return this;\n  }\n  withUrl(url, transportTypeOrOptions) {\n    Arg.isRequired(url, \"url\");\n    Arg.isNotEmpty(url, \"url\");\n    this.url = url;\n    // Flow-typing knows where it's at. Since HttpTransportType is a number and IHttpConnectionOptions is guaranteed\n    // to be an object, we know (as does TypeScript) this comparison is all we need to figure out which overload was called.\n    if (typeof transportTypeOrOptions === \"object\") {\n      this.httpConnectionOptions = {\n        ...this.httpConnectionOptions,\n        ...transportTypeOrOptions\n      };\n    } else {\n      this.httpConnectionOptions = {\n        ...this.httpConnectionOptions,\n        transport: transportTypeOrOptions\n      };\n    }\n    return this;\n  }\n  /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified Hub Protocol.\r\n   *\r\n   * @param {IHubProtocol} protocol The {@link @microsoft/signalr.IHubProtocol} implementation to use.\r\n   */\n  withHubProtocol(protocol) {\n    Arg.isRequired(protocol, \"protocol\");\n    this.protocol = protocol;\n    return this;\n  }\n  withAutomaticReconnect(retryDelaysOrReconnectPolicy) {\n    if (this.reconnectPolicy) {\n      throw new Error(\"A reconnectPolicy has already been set.\");\n    }\n    if (!retryDelaysOrReconnectPolicy) {\n      this.reconnectPolicy = new DefaultReconnectPolicy();\n    } else if (Array.isArray(retryDelaysOrReconnectPolicy)) {\n      this.reconnectPolicy = new DefaultReconnectPolicy(retryDelaysOrReconnectPolicy);\n    } else {\n      this.reconnectPolicy = retryDelaysOrReconnectPolicy;\n    }\n    return this;\n  }\n  /** Configures {@link @microsoft/signalr.HubConnection.serverTimeoutInMilliseconds} for the {@link @microsoft/signalr.HubConnection}.\r\n   *\r\n   * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n   */\n  withServerTimeout(milliseconds) {\n    Arg.isRequired(milliseconds, \"milliseconds\");\n    this._serverTimeoutInMilliseconds = milliseconds;\n    return this;\n  }\n  /** Configures {@link @microsoft/signalr.HubConnection.keepAliveIntervalInMilliseconds} for the {@link @microsoft/signalr.HubConnection}.\r\n   *\r\n   * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n   */\n  withKeepAliveInterval(milliseconds) {\n    Arg.isRequired(milliseconds, \"milliseconds\");\n    this._keepAliveIntervalInMilliseconds = milliseconds;\n    return this;\n  }\n  /** Enables and configures options for the Stateful Reconnect feature.\r\n   *\r\n   * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n   */\n  withStatefulReconnect(options) {\n    if (this.httpConnectionOptions === undefined) {\n      this.httpConnectionOptions = {};\n    }\n    this.httpConnectionOptions._useStatefulReconnect = true;\n    this._statefulReconnectBufferSize = options === null || options === void 0 ? void 0 : options.bufferSize;\n    return this;\n  }\n  /** Creates a {@link @microsoft/signalr.HubConnection} from the configuration options specified in this builder.\r\n   *\r\n   * @returns {HubConnection} The configured {@link @microsoft/signalr.HubConnection}.\r\n   */\n  build() {\n    // If httpConnectionOptions has a logger, use it. Otherwise, override it with the one\n    // provided to configureLogger\n    const httpConnectionOptions = this.httpConnectionOptions || {};\n    // If it's 'null', the user **explicitly** asked for null, don't mess with it.\n    if (httpConnectionOptions.logger === undefined) {\n      // If our logger is undefined or null, that's OK, the HttpConnection constructor will handle it.\n      httpConnectionOptions.logger = this.logger;\n    }\n    // Now create the connection\n    if (!this.url) {\n      throw new Error(\"The 'HubConnectionBuilder.withUrl' method must be called before building the connection.\");\n    }\n    const connection = new HttpConnection(this.url, httpConnectionOptions);\n    return HubConnection.create(connection, this.logger || NullLogger.instance, this.protocol || new JsonHubProtocol(), this.reconnectPolicy, this._serverTimeoutInMilliseconds, this._keepAliveIntervalInMilliseconds, this._statefulReconnectBufferSize);\n  }\n}\nfunction isLogger(logger) {\n  return logger.log !== undefined;\n}\n"], "mappings": ";;;;;;;;AAGO,IAAM,YAAN,cAAwB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,YAAY,cAAc,YAAY;AACpC,UAAM,YAAY,WAAW;AAC7B,UAAM,GAAG,YAAY,kBAAkB,UAAU,GAAG;AACpD,SAAK,aAAa;AAGlB,SAAK,YAAY;AAAA,EACnB;AACF;AAEO,IAAM,eAAN,cAA2B,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtC,YAAY,eAAe,uBAAuB;AAChD,UAAM,YAAY,WAAW;AAC7B,UAAM,YAAY;AAGlB,SAAK,YAAY;AAAA,EACnB;AACF;AAEO,IAAM,aAAN,cAAyB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpC,YAAY,eAAe,sBAAsB;AAC/C,UAAM,YAAY,WAAW;AAC7B,UAAM,YAAY;AAGlB,SAAK,YAAY;AAAA,EACnB;AACF;AAGO,IAAM,4BAAN,cAAwC,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnD,YAAY,SAAS,WAAW;AAC9B,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AAGjB,SAAK,YAAY;AAAA,EACnB;AACF;AAGO,IAAM,yBAAN,cAAqC,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhD,YAAY,SAAS,WAAW;AAC9B,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AAGjB,SAAK,YAAY;AAAA,EACnB;AACF;AAGO,IAAM,8BAAN,cAA0C,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrD,YAAY,SAAS,WAAW;AAC9B,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AAGjB,SAAK,YAAY;AAAA,EACnB;AACF;AAGO,IAAM,mCAAN,cAA+C,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1D,YAAY,SAAS;AACnB,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,YAAY;AAGjB,SAAK,YAAY;AAAA,EACnB;AACF;AAGO,IAAM,kBAAN,cAA8B,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzC,YAAY,SAAS,aAAa;AAChC,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,cAAc;AAGnB,SAAK,YAAY;AAAA,EACnB;AACF;;;ACjIO,IAAM,eAAN,MAAmB;AAAA,EACxB,YAAY,YAAY,YAAY,SAAS;AAC3C,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,UAAU;AAAA,EACjB;AACF;AAKO,IAAM,aAAN,MAAiB;AAAA,EACtB,IAAI,KAAK,SAAS;AAChB,WAAO,KAAK,KAAK,iCACZ,UADY;AAAA,MAEf,QAAQ;AAAA,MACR;AAAA,IACF,EAAC;AAAA,EACH;AAAA,EACA,KAAK,KAAK,SAAS;AACjB,WAAO,KAAK,KAAK,iCACZ,UADY;AAAA,MAEf,QAAQ;AAAA,MACR;AAAA,IACF,EAAC;AAAA,EACH;AAAA,EACA,OAAO,KAAK,SAAS;AACnB,WAAO,KAAK,KAAK,iCACZ,UADY;AAAA,MAEf,QAAQ;AAAA,MACR;AAAA,IACF,EAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,KAAK;AACnB,WAAO;AAAA,EACT;AACF;;;ACtCO,IAAI;AAAA,CACV,SAAUA,WAAU;AAEnB,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAElC,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAElC,EAAAA,UAASA,UAAS,aAAa,IAAI,CAAC,IAAI;AAExC,EAAAA,UAASA,UAAS,SAAS,IAAI,CAAC,IAAI;AAEpC,EAAAA,UAASA,UAAS,OAAO,IAAI,CAAC,IAAI;AAElC,EAAAA,UAASA,UAAS,UAAU,IAAI,CAAC,IAAI;AAErC,EAAAA,UAASA,UAAS,MAAM,IAAI,CAAC,IAAI;AACnC,GAAG,aAAa,WAAW,CAAC,EAAE;;;ACpBvB,IAAM,aAAN,MAAiB;AAAA,EACtB,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA,EAGf,IAAI,WAAW,UAAU;AAAA,EAAC;AAC5B;AAEA,WAAW,WAAW,IAAI,WAAW;;;ACJ9B,IAAM,UAAU;AAEhB,IAAM,MAAN,MAAU;AAAA,EACf,OAAO,WAAW,KAAK,MAAM;AAC3B,QAAI,QAAQ,QAAQ,QAAQ,QAAW;AACrC,YAAM,IAAI,MAAM,QAAQ,IAAI,yBAAyB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO,WAAW,KAAK,MAAM;AAC3B,QAAI,CAAC,OAAO,IAAI,MAAM,OAAO,GAAG;AAC9B,YAAM,IAAI,MAAM,QAAQ,IAAI,iCAAiC;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO,KAAK,KAAK,QAAQ,MAAM;AAE7B,QAAI,EAAE,OAAO,SAAS;AACpB,YAAM,IAAI,MAAM,WAAW,IAAI,WAAW,GAAG,GAAG;AAAA,IAClD;AAAA,EACF;AACF;AAEO,IAAM,WAAN,MAAM,UAAS;AAAA;AAAA,EAEpB,WAAW,YAAY;AACrB,WAAO,CAAC,UAAS,UAAU,OAAO,WAAW,YAAY,OAAO,OAAO,aAAa;AAAA,EACtF;AAAA;AAAA,EAEA,WAAW,cAAc;AACvB,WAAO,CAAC,UAAS,UAAU,OAAO,SAAS,YAAY,mBAAmB;AAAA,EAC5E;AAAA;AAAA,EAEA,WAAW,gBAAgB;AACzB,WAAO,CAAC,UAAS,UAAU,OAAO,WAAW,YAAY,OAAO,OAAO,aAAa;AAAA,EACtF;AAAA;AAAA;AAAA,EAGA,WAAW,SAAS;AAClB,WAAO,OAAO,YAAY,eAAe,QAAQ,WAAW,QAAQ,QAAQ,SAAS;AAAA,EACvF;AACF;AAEO,SAAS,cAAc,MAAM,gBAAgB;AAClD,MAAI,SAAS;AACb,MAAI,cAAc,IAAI,GAAG;AACvB,aAAS,yBAAyB,KAAK,UAAU;AACjD,QAAI,gBAAgB;AAClB,gBAAU,eAAe,kBAAkB,IAAI,CAAC;AAAA,IAClD;AAAA,EACF,WAAW,OAAO,SAAS,UAAU;AACnC,aAAS,yBAAyB,KAAK,MAAM;AAC7C,QAAI,gBAAgB;AAClB,gBAAU,eAAe,IAAI;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AAEO,SAAS,kBAAkB,MAAM;AACtC,QAAM,OAAO,IAAI,WAAW,IAAI;AAEhC,MAAI,MAAM;AACV,OAAK,QAAQ,SAAO;AAClB,UAAM,MAAM,MAAM,KAAK,MAAM;AAC7B,WAAO,KAAK,GAAG,GAAG,IAAI,SAAS,EAAE,CAAC;AAAA,EACpC,CAAC;AAED,SAAO,IAAI,OAAO,GAAG,IAAI,SAAS,CAAC;AACrC;AAGO,SAAS,cAAc,KAAK;AACjC,SAAO,OAAO,OAAO,gBAAgB,gBAAgB,eAAe;AAAA,EAEpE,IAAI,eAAe,IAAI,YAAY,SAAS;AAC9C;AAEA,SAAsB,YAAY,QAAQ,eAAe,YAAY,KAAK,SAAS,SAAS;AAAA;AAC1F,UAAM,UAAU,CAAC;AACjB,UAAM,CAAC,MAAM,KAAK,IAAI,mBAAmB;AACzC,YAAQ,IAAI,IAAI;AAChB,WAAO,IAAI,SAAS,OAAO,IAAI,aAAa,6BAA6B,cAAc,SAAS,QAAQ,iBAAiB,CAAC,GAAG;AAC7H,UAAM,eAAe,cAAc,OAAO,IAAI,gBAAgB;AAC9D,UAAM,WAAW,MAAM,WAAW,KAAK,KAAK;AAAA,MAC1C;AAAA,MACA,SAAS,kCACJ,UACA,QAAQ;AAAA,MAEb;AAAA,MACA,SAAS,QAAQ;AAAA,MACjB,iBAAiB,QAAQ;AAAA,IAC3B,CAAC;AACD,WAAO,IAAI,SAAS,OAAO,IAAI,aAAa,kDAAkD,SAAS,UAAU,GAAG;AAAA,EACtH;AAAA;AAEO,SAAS,aAAa,QAAQ;AACnC,MAAI,WAAW,QAAW;AACxB,WAAO,IAAI,cAAc,SAAS,WAAW;AAAA,EAC/C;AACA,MAAI,WAAW,MAAM;AACnB,WAAO,WAAW;AAAA,EACpB;AACA,MAAI,OAAO,QAAQ,QAAW;AAC5B,WAAO;AAAA,EACT;AACA,SAAO,IAAI,cAAc,MAAM;AACjC;AAEO,IAAM,sBAAN,MAA0B;AAAA,EAC/B,YAAY,SAAS,UAAU;AAC7B,SAAK,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,UAAU;AACR,UAAM,QAAQ,KAAK,SAAS,UAAU,QAAQ,KAAK,SAAS;AAC5D,QAAI,QAAQ,IAAI;AACd,WAAK,SAAS,UAAU,OAAO,OAAO,CAAC;AAAA,IACzC;AACA,QAAI,KAAK,SAAS,UAAU,WAAW,KAAK,KAAK,SAAS,gBAAgB;AACxE,WAAK,SAAS,eAAe,EAAE,MAAM,OAAK;AAAA,MAAC,CAAC;AAAA,IAC9C;AAAA,EACF;AACF;AAEO,IAAM,gBAAN,MAAoB;AAAA,EACzB,YAAY,iBAAiB;AAC3B,SAAK,YAAY;AACjB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,IAAI,UAAU,SAAS;AACrB,QAAI,YAAY,KAAK,WAAW;AAC9B,YAAM,MAAM,KAAI,oBAAI,KAAK,GAAE,YAAY,CAAC,KAAK,SAAS,QAAQ,CAAC,KAAK,OAAO;AAC3E,cAAQ,UAAU;AAAA,QAChB,KAAK,SAAS;AAAA,QACd,KAAK,SAAS;AACZ,eAAK,IAAI,MAAM,GAAG;AAClB;AAAA,QACF,KAAK,SAAS;AACZ,eAAK,IAAI,KAAK,GAAG;AACjB;AAAA,QACF,KAAK,SAAS;AACZ,eAAK,IAAI,KAAK,GAAG;AACjB;AAAA,QACF;AAEE,eAAK,IAAI,IAAI,GAAG;AAChB;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;AAEO,SAAS,qBAAqB;AACnC,MAAI,sBAAsB;AAC1B,MAAI,SAAS,QAAQ;AACnB,0BAAsB;AAAA,EACxB;AACA,SAAO,CAAC,qBAAqB,mBAAmB,SAAS,UAAU,GAAG,WAAW,GAAG,kBAAkB,CAAC,CAAC;AAC1G;AAEO,SAAS,mBAAmB,SAAS,IAAI,SAAS,gBAAgB;AAEvE,MAAI,YAAY;AAChB,QAAM,gBAAgB,QAAQ,MAAM,GAAG;AACvC,eAAa,GAAG,cAAc,CAAC,CAAC,IAAI,cAAc,CAAC,CAAC;AACpD,eAAa,KAAK,OAAO;AACzB,MAAI,MAAM,OAAO,IAAI;AACnB,iBAAa,GAAG,EAAE;AAAA,EACpB,OAAO;AACL,iBAAa;AAAA,EACf;AACA,eAAa,GAAG,OAAO;AACvB,MAAI,gBAAgB;AAClB,iBAAa,KAAK,cAAc;AAAA,EAClC,OAAO;AACL,iBAAa;AAAA,EACf;AACA,eAAa;AACb,SAAO;AACT;AAGA,SAAS,YAAY;AACnB,MAAI,SAAS,QAAQ;AACnB,YAAQ,QAAQ,UAAU;AAAA,MACxB,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO,QAAQ;AAAA,IACnB;AAAA,EACF,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAGA,SAAS,oBAAoB;AAC3B,MAAI,SAAS,QAAQ;AACnB,WAAO,QAAQ,SAAS;AAAA,EAC1B;AACA,SAAO;AACT;AACA,SAAS,aAAa;AACpB,MAAI,SAAS,QAAQ;AACnB,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEO,SAAS,eAAe,GAAG;AAChC,MAAI,EAAE,OAAO;AACX,WAAO,EAAE;AAAA,EACX,WAAW,EAAE,SAAS;AACpB,WAAO,EAAE;AAAA,EACX;AACA,SAAO,GAAG,CAAC;AACb;AAEO,SAAS,gBAAgB;AAE9B,MAAI,OAAO,eAAe,aAAa;AACrC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,aAAa;AAC/B,WAAO;AAAA,EACT;AACA,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AACA,QAAM,IAAI,MAAM,uBAAuB;AACzC;;;AC9OO,IAAM,kBAAN,cAA8B,WAAW;AAAA,EAC9C,YAAY,QAAQ;AAClB,UAAM;AACN,SAAK,UAAU;AAGf,QAAI,OAAO,UAAU,eAAe,SAAS,QAAQ;AAGnD,YAAM,cAAc,OAAO,wBAAwB,aAAa,0BAA0B;AAE1F,WAAK,OAAO,KAAK,YAAY,cAAc,GAAE,UAAW;AACxD,UAAI,OAAO,UAAU,aAAa;AAChC,aAAK,aAAa,YAAY,YAAY;AAAA,MAC5C,OAAO;AAEL,aAAK,aAAa;AAAA,MACpB;AAGA,WAAK,aAAa,YAAY,cAAc,EAAE,KAAK,YAAY,KAAK,IAAI;AAAA,IAC1E,OAAO;AACL,WAAK,aAAa,MAAM,KAAK,cAAc,CAAC;AAAA,IAC9C;AACA,QAAI,OAAO,oBAAoB,aAAa;AAG1C,YAAM,cAAc,OAAO,wBAAwB,aAAa,0BAA0B;AAE1F,WAAK,uBAAuB,YAAY,kBAAkB;AAAA,IAC5D,OAAO;AACL,WAAK,uBAAuB;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA,EAEM,KAAK,SAAS;AAAA;AAElB,UAAI,QAAQ,eAAe,QAAQ,YAAY,SAAS;AACtD,cAAM,IAAI,WAAW;AAAA,MACvB;AACA,UAAI,CAAC,QAAQ,QAAQ;AACnB,cAAM,IAAI,MAAM,oBAAoB;AAAA,MACtC;AACA,UAAI,CAAC,QAAQ,KAAK;AAChB,cAAM,IAAI,MAAM,iBAAiB;AAAA,MACnC;AACA,YAAM,kBAAkB,IAAI,KAAK,qBAAqB;AACtD,UAAI;AAEJ,UAAI,QAAQ,aAAa;AACvB,gBAAQ,YAAY,UAAU,MAAM;AAClC,0BAAgB,MAAM;AACtB,kBAAQ,IAAI,WAAW;AAAA,QACzB;AAAA,MACF;AAGA,UAAI,YAAY;AAChB,UAAI,QAAQ,SAAS;AACnB,cAAM,YAAY,QAAQ;AAC1B,oBAAY,WAAW,MAAM;AAC3B,0BAAgB,MAAM;AACtB,eAAK,QAAQ,IAAI,SAAS,SAAS,4BAA4B;AAC/D,kBAAQ,IAAI,aAAa;AAAA,QAC3B,GAAG,SAAS;AAAA,MACd;AACA,UAAI,QAAQ,YAAY,IAAI;AAC1B,gBAAQ,UAAU;AAAA,MACpB;AACA,UAAI,QAAQ,SAAS;AAEnB,gBAAQ,UAAU,QAAQ,WAAW,CAAC;AACtC,YAAI,cAAc,QAAQ,OAAO,GAAG;AAClC,kBAAQ,QAAQ,cAAc,IAAI;AAAA,QACpC,OAAO;AACL,kBAAQ,QAAQ,cAAc,IAAI;AAAA,QACpC;AAAA,MACF;AACA,UAAI;AACJ,UAAI;AACF,mBAAW,MAAM,KAAK,WAAW,QAAQ,KAAK;AAAA,UAC5C,MAAM,QAAQ;AAAA,UACd,OAAO;AAAA,UACP,aAAa,QAAQ,oBAAoB,OAAO,YAAY;AAAA,UAC5D,SAAS;AAAA,YACP,oBAAoB;AAAA,aACjB,QAAQ;AAAA,UAEb,QAAQ,QAAQ;AAAA,UAChB,MAAM;AAAA,UACN,UAAU;AAAA,UACV,QAAQ,gBAAgB;AAAA,QAC1B,CAAC;AAAA,MACH,SAAS,GAAG;AACV,YAAI,OAAO;AACT,gBAAM;AAAA,QACR;AACA,aAAK,QAAQ,IAAI,SAAS,SAAS,4BAA4B,CAAC,GAAG;AACnE,cAAM;AAAA,MACR,UAAE;AACA,YAAI,WAAW;AACb,uBAAa,SAAS;AAAA,QACxB;AACA,YAAI,QAAQ,aAAa;AACvB,kBAAQ,YAAY,UAAU;AAAA,QAChC;AAAA,MACF;AACA,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,eAAe,MAAM,mBAAmB,UAAU,MAAM;AAC9D,cAAM,IAAI,UAAU,gBAAgB,SAAS,YAAY,SAAS,MAAM;AAAA,MAC1E;AACA,YAAM,UAAU,mBAAmB,UAAU,QAAQ,YAAY;AACjE,YAAM,UAAU,MAAM;AACtB,aAAO,IAAI,aAAa,SAAS,QAAQ,SAAS,YAAY,OAAO;AAAA,IACvE;AAAA;AAAA,EACA,gBAAgB,KAAK;AACnB,QAAI,UAAU;AACd,QAAI,SAAS,UAAU,KAAK,MAAM;AAEhC,WAAK,KAAK,WAAW,KAAK,CAAC,GAAG,MAAM,UAAU,EAAE,KAAK,IAAI,CAAC;AAAA,IAC5D;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,mBAAmB,UAAU,cAAc;AAClD,MAAI;AACJ,UAAQ,cAAc;AAAA,IACpB,KAAK;AACH,gBAAU,SAAS,YAAY;AAC/B;AAAA,IACF,KAAK;AACH,gBAAU,SAAS,KAAK;AACxB;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,YAAM,IAAI,MAAM,GAAG,YAAY,oBAAoB;AAAA,IACrD;AACE,gBAAU,SAAS,KAAK;AACxB;AAAA,EACJ;AACA,SAAO;AACT;;;AC9IO,IAAM,gBAAN,cAA4B,WAAW;AAAA,EAC5C,YAAY,QAAQ;AAClB,UAAM;AACN,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA,EAEA,KAAK,SAAS;AAEZ,QAAI,QAAQ,eAAe,QAAQ,YAAY,SAAS;AACtD,aAAO,QAAQ,OAAO,IAAI,WAAW,CAAC;AAAA,IACxC;AACA,QAAI,CAAC,QAAQ,QAAQ;AACnB,aAAO,QAAQ,OAAO,IAAI,MAAM,oBAAoB,CAAC;AAAA,IACvD;AACA,QAAI,CAAC,QAAQ,KAAK;AAChB,aAAO,QAAQ,OAAO,IAAI,MAAM,iBAAiB,CAAC;AAAA,IACpD;AACA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,MAAM,IAAI,eAAe;AAC/B,UAAI,KAAK,QAAQ,QAAQ,QAAQ,KAAK,IAAI;AAC1C,UAAI,kBAAkB,QAAQ,oBAAoB,SAAY,OAAO,QAAQ;AAC7E,UAAI,iBAAiB,oBAAoB,gBAAgB;AACzD,UAAI,QAAQ,YAAY,IAAI;AAC1B,gBAAQ,UAAU;AAAA,MACpB;AACA,UAAI,QAAQ,SAAS;AAEnB,YAAI,cAAc,QAAQ,OAAO,GAAG;AAClC,cAAI,iBAAiB,gBAAgB,0BAA0B;AAAA,QACjE,OAAO;AACL,cAAI,iBAAiB,gBAAgB,0BAA0B;AAAA,QACjE;AAAA,MACF;AACA,YAAM,UAAU,QAAQ;AACxB,UAAI,SAAS;AACX,eAAO,KAAK,OAAO,EAAE,QAAQ,YAAU;AACrC,cAAI,iBAAiB,QAAQ,QAAQ,MAAM,CAAC;AAAA,QAC9C,CAAC;AAAA,MACH;AACA,UAAI,QAAQ,cAAc;AACxB,YAAI,eAAe,QAAQ;AAAA,MAC7B;AACA,UAAI,QAAQ,aAAa;AACvB,gBAAQ,YAAY,UAAU,MAAM;AAClC,cAAI,MAAM;AACV,iBAAO,IAAI,WAAW,CAAC;AAAA,QACzB;AAAA,MACF;AACA,UAAI,QAAQ,SAAS;AACnB,YAAI,UAAU,QAAQ;AAAA,MACxB;AACA,UAAI,SAAS,MAAM;AACjB,YAAI,QAAQ,aAAa;AACvB,kBAAQ,YAAY,UAAU;AAAA,QAChC;AACA,YAAI,IAAI,UAAU,OAAO,IAAI,SAAS,KAAK;AACzC,kBAAQ,IAAI,aAAa,IAAI,QAAQ,IAAI,YAAY,IAAI,YAAY,IAAI,YAAY,CAAC;AAAA,QACxF,OAAO;AACL,iBAAO,IAAI,UAAU,IAAI,YAAY,IAAI,gBAAgB,IAAI,YAAY,IAAI,MAAM,CAAC;AAAA,QACtF;AAAA,MACF;AACA,UAAI,UAAU,MAAM;AAClB,aAAK,QAAQ,IAAI,SAAS,SAAS,4BAA4B,IAAI,MAAM,KAAK,IAAI,UAAU,GAAG;AAC/F,eAAO,IAAI,UAAU,IAAI,YAAY,IAAI,MAAM,CAAC;AAAA,MAClD;AACA,UAAI,YAAY,MAAM;AACpB,aAAK,QAAQ,IAAI,SAAS,SAAS,4BAA4B;AAC/D,eAAO,IAAI,aAAa,CAAC;AAAA,MAC3B;AACA,UAAI,KAAK,QAAQ,OAAO;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;;;ACtEO,IAAM,oBAAN,cAAgC,WAAW;AAAA;AAAA,EAEhD,YAAY,QAAQ;AAClB,UAAM;AACN,QAAI,OAAO,UAAU,eAAe,SAAS,QAAQ;AACnD,WAAK,cAAc,IAAI,gBAAgB,MAAM;AAAA,IAC/C,WAAW,OAAO,mBAAmB,aAAa;AAChD,WAAK,cAAc,IAAI,cAAc,MAAM;AAAA,IAC7C,OAAO;AACL,YAAM,IAAI,MAAM,6BAA6B;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA,EAEA,KAAK,SAAS;AAEZ,QAAI,QAAQ,eAAe,QAAQ,YAAY,SAAS;AACtD,aAAO,QAAQ,OAAO,IAAI,WAAW,CAAC;AAAA,IACxC;AACA,QAAI,CAAC,QAAQ,QAAQ;AACnB,aAAO,QAAQ,OAAO,IAAI,MAAM,oBAAoB,CAAC;AAAA,IACvD;AACA,QAAI,CAAC,QAAQ,KAAK;AAChB,aAAO,QAAQ,OAAO,IAAI,MAAM,iBAAiB,CAAC;AAAA,IACpD;AACA,WAAO,KAAK,YAAY,KAAK,OAAO;AAAA,EACtC;AAAA,EACA,gBAAgB,KAAK;AACnB,WAAO,KAAK,YAAY,gBAAgB,GAAG;AAAA,EAC7C;AACF;;;ACjCO,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EAC7B,OAAO,MAAM,QAAQ;AACnB,WAAO,GAAG,MAAM,GAAG,mBAAkB,eAAe;AAAA,EACtD;AAAA,EACA,OAAO,MAAM,OAAO;AAClB,QAAI,MAAM,MAAM,SAAS,CAAC,MAAM,mBAAkB,iBAAiB;AACjE,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AACA,UAAM,WAAW,MAAM,MAAM,mBAAkB,eAAe;AAC9D,aAAS,IAAI;AACb,WAAO;AAAA,EACT;AACF;AACA,kBAAkB,sBAAsB;AACxC,kBAAkB,kBAAkB,OAAO,aAAa,kBAAkB,mBAAmB;;;ACbtF,IAAM,oBAAN,MAAwB;AAAA;AAAA,EAE7B,sBAAsB,kBAAkB;AACtC,WAAO,kBAAkB,MAAM,KAAK,UAAU,gBAAgB,CAAC;AAAA,EACjE;AAAA,EACA,uBAAuB,MAAM;AAC3B,QAAI;AACJ,QAAI;AACJ,QAAI,cAAc,IAAI,GAAG;AAEvB,YAAM,aAAa,IAAI,WAAW,IAAI;AACtC,YAAM,iBAAiB,WAAW,QAAQ,kBAAkB,mBAAmB;AAC/E,UAAI,mBAAmB,IAAI;AACzB,cAAM,IAAI,MAAM,wBAAwB;AAAA,MAC1C;AAGA,YAAM,iBAAiB,iBAAiB;AACxC,oBAAc,OAAO,aAAa,MAAM,MAAM,MAAM,UAAU,MAAM,KAAK,WAAW,MAAM,GAAG,cAAc,CAAC,CAAC;AAC7G,sBAAgB,WAAW,aAAa,iBAAiB,WAAW,MAAM,cAAc,EAAE,SAAS;AAAA,IACrG,OAAO;AACL,YAAM,WAAW;AACjB,YAAM,iBAAiB,SAAS,QAAQ,kBAAkB,eAAe;AACzE,UAAI,mBAAmB,IAAI;AACzB,cAAM,IAAI,MAAM,wBAAwB;AAAA,MAC1C;AAGA,YAAM,iBAAiB,iBAAiB;AACxC,oBAAc,SAAS,UAAU,GAAG,cAAc;AAClD,sBAAgB,SAAS,SAAS,iBAAiB,SAAS,UAAU,cAAc,IAAI;AAAA,IAC1F;AAEA,UAAM,WAAW,kBAAkB,MAAM,WAAW;AACpD,UAAM,WAAW,KAAK,MAAM,SAAS,CAAC,CAAC;AACvC,QAAI,SAAS,MAAM;AACjB,YAAM,IAAI,MAAM,gDAAgD;AAAA,IAClE;AACA,UAAM,kBAAkB;AAGxB,WAAO,CAAC,eAAe,eAAe;AAAA,EACxC;AACF;;;AC7CO,IAAI;AAAA,CACV,SAAUC,cAAa;AAEtB,EAAAA,aAAYA,aAAY,YAAY,IAAI,CAAC,IAAI;AAE7C,EAAAA,aAAYA,aAAY,YAAY,IAAI,CAAC,IAAI;AAE7C,EAAAA,aAAYA,aAAY,YAAY,IAAI,CAAC,IAAI;AAE7C,EAAAA,aAAYA,aAAY,kBAAkB,IAAI,CAAC,IAAI;AAEnD,EAAAA,aAAYA,aAAY,kBAAkB,IAAI,CAAC,IAAI;AAEnD,EAAAA,aAAYA,aAAY,MAAM,IAAI,CAAC,IAAI;AAEvC,EAAAA,aAAYA,aAAY,OAAO,IAAI,CAAC,IAAI;AACxC,EAAAA,aAAYA,aAAY,KAAK,IAAI,CAAC,IAAI;AACtC,EAAAA,aAAYA,aAAY,UAAU,IAAI,CAAC,IAAI;AAC7C,GAAG,gBAAgB,cAAc,CAAC,EAAE;;;ACjB7B,IAAM,UAAN,MAAc;AAAA,EACnB,cAAc;AACZ,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,KAAK,MAAM;AACT,eAAW,YAAY,KAAK,WAAW;AACrC,eAAS,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AAAA,EACA,MAAM,KAAK;AACT,eAAW,YAAY,KAAK,WAAW;AACrC,UAAI,SAAS,OAAO;AAClB,iBAAS,MAAM,GAAG;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,eAAW,YAAY,KAAK,WAAW;AACrC,UAAI,SAAS,UAAU;AACrB,iBAAS,SAAS;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,UAAU;AAClB,SAAK,UAAU,KAAK,QAAQ;AAC5B,WAAO,IAAI,oBAAoB,MAAM,QAAQ;AAAA,EAC/C;AACF;;;AC1BO,IAAM,gBAAN,MAAoB;AAAA,EACzB,YAAY,UAAU,YAAY,YAAY;AAC5C,SAAK,cAAc;AACnB,SAAK,YAAY,CAAC;AAClB,SAAK,qBAAqB;AAC1B,SAAK,0BAA0B;AAE/B,SAAK,2BAA2B;AAChC,SAAK,4BAA4B;AACjC,SAAK,qBAAqB;AAC1B,SAAK,uBAAuB;AAC5B,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,cAAc;AAAA,EACrB;AAAA,EACM,MAAM,SAAS;AAAA;AACnB,YAAM,oBAAoB,KAAK,UAAU,aAAa,OAAO;AAC7D,UAAI,sBAAsB,QAAQ,QAAQ;AAE1C,UAAI,KAAK,qBAAqB,OAAO,GAAG;AACtC,aAAK;AACL,YAAI,8BAA8B,MAAM;AAAA,QAAC;AACzC,YAAI,8BAA8B,MAAM;AAAA,QAAC;AACzC,YAAI,cAAc,iBAAiB,GAAG;AACpC,eAAK,sBAAsB,kBAAkB;AAAA,QAC/C,OAAO;AACL,eAAK,sBAAsB,kBAAkB;AAAA,QAC/C;AACA,YAAI,KAAK,sBAAsB,KAAK,aAAa;AAC/C,gCAAsB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACrD,0CAA8B;AAC9B,0CAA8B;AAAA,UAChC,CAAC;AAAA,QACH;AACA,aAAK,UAAU,KAAK,IAAI,aAAa,mBAAmB,KAAK,oBAAoB,6BAA6B,2BAA2B,CAAC;AAAA,MAC5I;AACA,UAAI;AAKF,YAAI,CAAC,KAAK,sBAAsB;AAC9B,gBAAM,KAAK,YAAY,KAAK,iBAAiB;AAAA,QAC/C;AAAA,MACF,QAAQ;AACN,aAAK,cAAc;AAAA,MACrB;AACA,YAAM;AAAA,IACR;AAAA;AAAA,EACA,KAAK,YAAY;AACf,QAAI,qBAAqB;AAEzB,aAAS,QAAQ,GAAG,QAAQ,KAAK,UAAU,QAAQ,SAAS;AAC1D,YAAM,UAAU,KAAK,UAAU,KAAK;AACpC,UAAI,QAAQ,OAAO,WAAW,YAAY;AACxC,6BAAqB;AACrB,YAAI,cAAc,QAAQ,QAAQ,GAAG;AACnC,eAAK,sBAAsB,QAAQ,SAAS;AAAA,QAC9C,OAAO;AACL,eAAK,sBAAsB,QAAQ,SAAS;AAAA,QAC9C;AAEA,gBAAQ,UAAU;AAAA,MACpB,WAAW,KAAK,qBAAqB,KAAK,aAAa;AAErD,gBAAQ,UAAU;AAAA,MACpB,OAAO;AACL;AAAA,MACF;AAAA,IACF;AACA,QAAI,uBAAuB,IAAI;AAE7B,WAAK,YAAY,KAAK,UAAU,MAAM,qBAAqB,CAAC;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,sBAAsB,SAAS;AAC7B,QAAI,KAAK,yBAAyB;AAChC,UAAI,QAAQ,SAAS,YAAY,UAAU;AACzC,eAAO;AAAA,MACT,OAAO;AACL,aAAK,0BAA0B;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,CAAC,KAAK,qBAAqB,OAAO,GAAG;AACvC,aAAO;AAAA,IACT;AACA,UAAM,YAAY,KAAK;AACvB,SAAK;AACL,QAAI,aAAa,KAAK,2BAA2B;AAC/C,UAAI,cAAc,KAAK,2BAA2B;AAGhD,aAAK,UAAU;AAAA,MACjB;AAEA,aAAO;AAAA,IACT;AACA,SAAK,4BAA4B;AAGjC,SAAK,UAAU;AACf,WAAO;AAAA,EACT;AAAA,EACA,eAAe,SAAS;AACtB,QAAI,QAAQ,aAAa,KAAK,0BAA0B;AAEtD,WAAK,YAAY,KAAK,IAAI,MAAM,6DAA6D,CAAC;AAC9F;AAAA,IACF;AACA,SAAK,2BAA2B,QAAQ;AAAA,EAC1C;AAAA,EACA,gBAAgB;AACd,SAAK,uBAAuB;AAC5B,SAAK,0BAA0B;AAAA,EACjC;AAAA,EACM,UAAU;AAAA;AACd,YAAM,aAAa,KAAK,UAAU,WAAW,IAAI,KAAK,UAAU,CAAC,EAAE,MAAM,KAAK,qBAAqB;AACnG,YAAM,KAAK,YAAY,KAAK,KAAK,UAAU,aAAa;AAAA,QACtD,MAAM,YAAY;AAAA,QAClB;AAAA,MACF,CAAC,CAAC;AAGF,YAAM,WAAW,KAAK;AACtB,iBAAW,WAAW,UAAU;AAC9B,cAAM,KAAK,YAAY,KAAK,QAAQ,QAAQ;AAAA,MAC9C;AACA,WAAK,uBAAuB;AAAA,IAC9B;AAAA;AAAA,EACA,SAAS,OAAO;AACd,cAAU,QAAQ,UAAU,SAAS,QAAQ,QAAQ,IAAI,MAAM,gCAAgC;AAE/F,eAAW,WAAW,KAAK,WAAW;AACpC,cAAQ,UAAU,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,qBAAqB,SAAS;AAM5B,YAAQ,QAAQ,MAAM;AAAA,MACpB,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AACf,eAAO;AAAA,MACT,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AAAA,MACjB,KAAK,YAAY;AACf,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,oBAAoB,QAAW;AACtC,WAAK,kBAAkB,WAAW,MAAY;AAC5C,YAAI;AACF,cAAI,CAAC,KAAK,sBAAsB;AAC9B,kBAAM,KAAK,YAAY,KAAK,KAAK,UAAU,aAAa;AAAA,cACtD,MAAM,YAAY;AAAA,cAClB,YAAY,KAAK;AAAA,YACnB,CAAC,CAAC;AAAA,UACJ;AAAA,QAEF,QAAQ;AAAA,QAAC;AACT,qBAAa,KAAK,eAAe;AACjC,aAAK,kBAAkB;AAAA,MAEzB,IAAG,GAAI;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAY,SAAS,IAAI,UAAU,UAAU;AAC3C,SAAK,WAAW;AAChB,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA,EACnB;AACF;;;ACpLA,IAAM,wBAAwB,KAAK;AACnC,IAAM,8BAA8B,KAAK;AACzC,IAAM,yCAAyC;AAExC,IAAI;AAAA,CACV,SAAUC,qBAAoB;AAE7B,EAAAA,oBAAmB,cAAc,IAAI;AAErC,EAAAA,oBAAmB,YAAY,IAAI;AAEnC,EAAAA,oBAAmB,WAAW,IAAI;AAElC,EAAAA,oBAAmB,eAAe,IAAI;AAEtC,EAAAA,oBAAmB,cAAc,IAAI;AACvC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAE3C,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB,OAAO,OAAO,YAAY,QAAQ,UAAU,iBAAiB,6BAA6B,iCAAiC,6BAA6B;AACtJ,WAAO,IAAI,eAAc,YAAY,QAAQ,UAAU,iBAAiB,6BAA6B,iCAAiC,2BAA2B;AAAA,EACnK;AAAA,EACA,YAAY,YAAY,QAAQ,UAAU,iBAAiB,6BAA6B,iCAAiC,6BAA6B;AACpJ,SAAK,iBAAiB;AACtB,SAAK,uBAAuB,MAAM;AAChC,WAAK,QAAQ,IAAI,SAAS,SAAS,uNAAuN;AAAA,IAC5P;AACA,QAAI,WAAW,YAAY,YAAY;AACvC,QAAI,WAAW,QAAQ,QAAQ;AAC/B,QAAI,WAAW,UAAU,UAAU;AACnC,SAAK,8BAA8B,gCAAgC,QAAQ,gCAAgC,SAAS,8BAA8B;AAClJ,SAAK,kCAAkC,oCAAoC,QAAQ,oCAAoC,SAAS,kCAAkC;AAClK,SAAK,+BAA+B,gCAAgC,QAAQ,gCAAgC,SAAS,8BAA8B;AACnJ,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,mBAAmB;AACxB,SAAK,qBAAqB,IAAI,kBAAkB;AAChD,SAAK,WAAW,YAAY,UAAQ,KAAK,qBAAqB,IAAI;AAClE,SAAK,WAAW,UAAU,WAAS,KAAK,kBAAkB,KAAK;AAC/D,SAAK,aAAa,CAAC;AACnB,SAAK,WAAW,CAAC;AACjB,SAAK,mBAAmB,CAAC;AACzB,SAAK,yBAAyB,CAAC;AAC/B,SAAK,wBAAwB,CAAC;AAC9B,SAAK,gBAAgB;AACrB,SAAK,6BAA6B;AAClC,SAAK,mBAAmB,mBAAmB;AAC3C,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB,KAAK,UAAU,aAAa;AAAA,MACpD,MAAM,YAAY;AAAA,IACpB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,IAAI,QAAQ;AACV,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,eAAe;AACjB,WAAO,KAAK,aAAa,KAAK,WAAW,gBAAgB,OAAO;AAAA,EAClE;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,WAAW,WAAW;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ,KAAK;AACf,QAAI,KAAK,qBAAqB,mBAAmB,gBAAgB,KAAK,qBAAqB,mBAAmB,cAAc;AAC1H,YAAM,IAAI,MAAM,wFAAwF;AAAA,IAC1G;AACA,QAAI,CAAC,KAAK;AACR,YAAM,IAAI,MAAM,4CAA4C;AAAA,IAC9D;AACA,SAAK,WAAW,UAAU;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,SAAK,gBAAgB,KAAK,2BAA2B;AACrD,WAAO,KAAK;AAAA,EACd;AAAA,EACM,6BAA6B;AAAA;AACjC,UAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC7D,eAAO,QAAQ,OAAO,IAAI,MAAM,uEAAuE,CAAC;AAAA,MAC1G;AACA,WAAK,mBAAmB,mBAAmB;AAC3C,WAAK,QAAQ,IAAI,SAAS,OAAO,yBAAyB;AAC1D,UAAI;AACF,cAAM,KAAK,eAAe;AAC1B,YAAI,SAAS,WAAW;AAEtB,iBAAO,SAAS,iBAAiB,UAAU,KAAK,oBAAoB;AAAA,QACtE;AACA,aAAK,mBAAmB,mBAAmB;AAC3C,aAAK,qBAAqB;AAC1B,aAAK,QAAQ,IAAI,SAAS,OAAO,uCAAuC;AAAA,MAC1E,SAAS,GAAG;AACV,aAAK,mBAAmB,mBAAmB;AAC3C,aAAK,QAAQ,IAAI,SAAS,OAAO,gEAAgE,CAAC,IAAI;AACtG,eAAO,QAAQ,OAAO,CAAC;AAAA,MACzB;AAAA,IACF;AAAA;AAAA,EACM,iBAAiB;AAAA;AACrB,WAAK,wBAAwB;AAC7B,WAAK,6BAA6B;AAElC,YAAM,mBAAmB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACxD,aAAK,qBAAqB;AAC1B,aAAK,qBAAqB;AAAA,MAC5B,CAAC;AACD,YAAM,KAAK,WAAW,MAAM,KAAK,UAAU,cAAc;AACzD,UAAI;AACF,YAAI,UAAU,KAAK,UAAU;AAC7B,YAAI,CAAC,KAAK,WAAW,SAAS,WAAW;AAGvC,oBAAU;AAAA,QACZ;AACA,cAAM,mBAAmB;AAAA,UACvB,UAAU,KAAK,UAAU;AAAA,UACzB;AAAA,QACF;AACA,aAAK,QAAQ,IAAI,SAAS,OAAO,4BAA4B;AAC7D,cAAM,KAAK,aAAa,KAAK,mBAAmB,sBAAsB,gBAAgB,CAAC;AACvF,aAAK,QAAQ,IAAI,SAAS,aAAa,sBAAsB,KAAK,UAAU,IAAI,IAAI;AAEpF,aAAK,gBAAgB;AACrB,aAAK,oBAAoB;AACzB,aAAK,wBAAwB;AAC7B,cAAM;AAIN,YAAI,KAAK,uBAAuB;AAK9B,gBAAM,KAAK;AAAA,QACb;AACA,cAAM,uBAAuB,KAAK,WAAW,SAAS,aAAa;AACnE,YAAI,sBAAsB;AACxB,eAAK,iBAAiB,IAAI,cAAc,KAAK,WAAW,KAAK,YAAY,KAAK,4BAA4B;AAC1G,eAAK,WAAW,SAAS,eAAe,KAAK,eAAe,cAAc,KAAK,KAAK,cAAc;AAClG,eAAK,WAAW,SAAS,SAAS,MAAM;AACtC,gBAAI,KAAK,gBAAgB;AACvB,qBAAO,KAAK,eAAe,QAAQ;AAAA,YACrC;AAAA,UACF;AAAA,QACF;AACA,YAAI,CAAC,KAAK,WAAW,SAAS,mBAAmB;AAC/C,gBAAM,KAAK,aAAa,KAAK,kBAAkB;AAAA,QACjD;AAAA,MACF,SAAS,GAAG;AACV,aAAK,QAAQ,IAAI,SAAS,OAAO,oCAAoC,CAAC,2CAA2C;AACjH,aAAK,gBAAgB;AACrB,aAAK,kBAAkB;AAGvB,cAAM,KAAK,WAAW,KAAK,CAAC;AAC5B,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,OAAO;AAAA;AAEX,YAAM,eAAe,KAAK;AAC1B,WAAK,WAAW,SAAS,YAAY;AACrC,WAAK,eAAe,KAAK,cAAc;AACvC,YAAM,KAAK;AACX,UAAI;AAEF,cAAM;AAAA,MACR,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AAAA;AAAA,EACA,cAAc,OAAO;AACnB,QAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC7D,WAAK,QAAQ,IAAI,SAAS,OAAO,8BAA8B,KAAK,4DAA4D;AAChI,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,QAAI,KAAK,qBAAqB,mBAAmB,eAAe;AAC9D,WAAK,QAAQ,IAAI,SAAS,OAAO,+BAA+B,KAAK,yEAAyE;AAC9I,aAAO,KAAK;AAAA,IACd;AACA,UAAM,QAAQ,KAAK;AACnB,SAAK,mBAAmB,mBAAmB;AAC3C,SAAK,QAAQ,IAAI,SAAS,OAAO,yBAAyB;AAC1D,QAAI,KAAK,uBAAuB;AAI9B,WAAK,QAAQ,IAAI,SAAS,OAAO,+DAA+D;AAChG,mBAAa,KAAK,qBAAqB;AACvC,WAAK,wBAAwB;AAC7B,WAAK,eAAe;AACpB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,QAAI,UAAU,mBAAmB,WAAW;AAE1C,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,wBAAwB,SAAS,IAAI,WAAW,qEAAqE;AAI1H,WAAO,KAAK,WAAW,KAAK,KAAK;AAAA,EACnC;AAAA,EACM,oBAAoB;AAAA;AACxB,UAAI;AACF,cAAM,KAAK,kBAAkB,KAAK,oBAAoB,CAAC;AAAA,MACzD,QAAQ;AAAA,MAER;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,eAAe,MAAM;AAC1B,UAAM,CAAC,SAAS,SAAS,IAAI,KAAK,wBAAwB,IAAI;AAC9D,UAAM,uBAAuB,KAAK,wBAAwB,YAAY,MAAM,SAAS;AAErF,QAAI;AACJ,UAAM,UAAU,IAAI,QAAQ;AAC5B,YAAQ,iBAAiB,MAAM;AAC7B,YAAM,mBAAmB,KAAK,wBAAwB,qBAAqB,YAAY;AACvF,aAAO,KAAK,WAAW,qBAAqB,YAAY;AACxD,aAAO,aAAa,KAAK,MAAM;AAC7B,eAAO,KAAK,kBAAkB,gBAAgB;AAAA,MAChD,CAAC;AAAA,IACH;AACA,SAAK,WAAW,qBAAqB,YAAY,IAAI,CAAC,iBAAiB,UAAU;AAC/E,UAAI,OAAO;AACT,gBAAQ,MAAM,KAAK;AACnB;AAAA,MACF,WAAW,iBAAiB;AAE1B,YAAI,gBAAgB,SAAS,YAAY,YAAY;AACnD,cAAI,gBAAgB,OAAO;AACzB,oBAAQ,MAAM,IAAI,MAAM,gBAAgB,KAAK,CAAC;AAAA,UAChD,OAAO;AACL,oBAAQ,SAAS;AAAA,UACnB;AAAA,QACF,OAAO;AACL,kBAAQ,KAAK,gBAAgB,IAAI;AAAA,QACnC;AAAA,MACF;AAAA,IACF;AACA,mBAAe,KAAK,kBAAkB,oBAAoB,EAAE,MAAM,OAAK;AACrE,cAAQ,MAAM,CAAC;AACf,aAAO,KAAK,WAAW,qBAAqB,YAAY;AAAA,IAC1D,CAAC;AACD,SAAK,eAAe,SAAS,YAAY;AACzC,WAAO;AAAA,EACT;AAAA,EACA,aAAa,SAAS;AACpB,SAAK,wBAAwB;AAC7B,WAAO,KAAK,WAAW,KAAK,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,SAAS;AACzB,QAAI,KAAK,gBAAgB;AACvB,aAAO,KAAK,eAAe,MAAM,OAAO;AAAA,IAC1C,OAAO;AACL,aAAO,KAAK,aAAa,KAAK,UAAU,aAAa,OAAO,CAAC;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,KAAK,eAAe,MAAM;AACxB,UAAM,CAAC,SAAS,SAAS,IAAI,KAAK,wBAAwB,IAAI;AAC9D,UAAM,cAAc,KAAK,kBAAkB,KAAK,kBAAkB,YAAY,MAAM,MAAM,SAAS,CAAC;AACpG,SAAK,eAAe,SAAS,WAAW;AACxC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,eAAe,MAAM;AAC1B,UAAM,CAAC,SAAS,SAAS,IAAI,KAAK,wBAAwB,IAAI;AAC9D,UAAM,uBAAuB,KAAK,kBAAkB,YAAY,MAAM,OAAO,SAAS;AACtF,UAAM,IAAI,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEzC,WAAK,WAAW,qBAAqB,YAAY,IAAI,CAAC,iBAAiB,UAAU;AAC/E,YAAI,OAAO;AACT,iBAAO,KAAK;AACZ;AAAA,QACF,WAAW,iBAAiB;AAE1B,cAAI,gBAAgB,SAAS,YAAY,YAAY;AACnD,gBAAI,gBAAgB,OAAO;AACzB,qBAAO,IAAI,MAAM,gBAAgB,KAAK,CAAC;AAAA,YACzC,OAAO;AACL,sBAAQ,gBAAgB,MAAM;AAAA,YAChC;AAAA,UACF,OAAO;AACL,mBAAO,IAAI,MAAM,4BAA4B,gBAAgB,IAAI,EAAE,CAAC;AAAA,UACtE;AAAA,QACF;AAAA,MACF;AACA,YAAM,eAAe,KAAK,kBAAkB,oBAAoB,EAAE,MAAM,OAAK;AAC3E,eAAO,CAAC;AAER,eAAO,KAAK,WAAW,qBAAqB,YAAY;AAAA,MAC1D,CAAC;AACD,WAAK,eAAe,SAAS,YAAY;AAAA,IAC3C,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,GAAG,YAAY,WAAW;AACxB,QAAI,CAAC,cAAc,CAAC,WAAW;AAC7B;AAAA,IACF;AACA,iBAAa,WAAW,YAAY;AACpC,QAAI,CAAC,KAAK,SAAS,UAAU,GAAG;AAC9B,WAAK,SAAS,UAAU,IAAI,CAAC;AAAA,IAC/B;AAEA,QAAI,KAAK,SAAS,UAAU,EAAE,QAAQ,SAAS,MAAM,IAAI;AACvD;AAAA,IACF;AACA,SAAK,SAAS,UAAU,EAAE,KAAK,SAAS;AAAA,EAC1C;AAAA,EACA,IAAI,YAAY,QAAQ;AACtB,QAAI,CAAC,YAAY;AACf;AAAA,IACF;AACA,iBAAa,WAAW,YAAY;AACpC,UAAM,WAAW,KAAK,SAAS,UAAU;AACzC,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AACA,QAAI,QAAQ;AACV,YAAM,YAAY,SAAS,QAAQ,MAAM;AACzC,UAAI,cAAc,IAAI;AACpB,iBAAS,OAAO,WAAW,CAAC;AAC5B,YAAI,SAAS,WAAW,GAAG;AACzB,iBAAO,KAAK,SAAS,UAAU;AAAA,QACjC;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,KAAK,SAAS,UAAU;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,UAAU;AAChB,QAAI,UAAU;AACZ,WAAK,iBAAiB,KAAK,QAAQ;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,UAAU;AACvB,QAAI,UAAU;AACZ,WAAK,uBAAuB,KAAK,QAAQ;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,UAAU;AACtB,QAAI,UAAU;AACZ,WAAK,sBAAsB,KAAK,QAAQ;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,qBAAqB,MAAM;AACzB,SAAK,gBAAgB;AACrB,QAAI,CAAC,KAAK,4BAA4B;AACpC,aAAO,KAAK,0BAA0B,IAAI;AAC1C,WAAK,6BAA6B;AAAA,IACpC;AAEA,QAAI,MAAM;AAER,YAAM,WAAW,KAAK,UAAU,cAAc,MAAM,KAAK,OAAO;AAChE,iBAAW,WAAW,UAAU;AAC9B,YAAI,KAAK,kBAAkB,CAAC,KAAK,eAAe,sBAAsB,OAAO,GAAG;AAE9E;AAAA,QACF;AACA,gBAAQ,QAAQ,MAAM;AAAA,UACpB,KAAK,YAAY;AACf,iBAAK,oBAAoB,OAAO,EAAE,MAAM,OAAK;AAC3C,mBAAK,QAAQ,IAAI,SAAS,OAAO,qCAAqC,eAAe,CAAC,CAAC,EAAE;AAAA,YAC3F,CAAC;AACD;AAAA,UACF,KAAK,YAAY;AAAA,UACjB,KAAK,YAAY,YACf;AACE,kBAAM,WAAW,KAAK,WAAW,QAAQ,YAAY;AACrD,gBAAI,UAAU;AACZ,kBAAI,QAAQ,SAAS,YAAY,YAAY;AAC3C,uBAAO,KAAK,WAAW,QAAQ,YAAY;AAAA,cAC7C;AACA,kBAAI;AACF,yBAAS,OAAO;AAAA,cAClB,SAAS,GAAG;AACV,qBAAK,QAAQ,IAAI,SAAS,OAAO,gCAAgC,eAAe,CAAC,CAAC,EAAE;AAAA,cACtF;AAAA,YACF;AACA;AAAA,UACF;AAAA,UACF,KAAK,YAAY;AAEf;AAAA,UACF,KAAK,YAAY,OACf;AACE,iBAAK,QAAQ,IAAI,SAAS,aAAa,qCAAqC;AAC5E,kBAAM,QAAQ,QAAQ,QAAQ,IAAI,MAAM,wCAAwC,QAAQ,KAAK,IAAI;AACjG,gBAAI,QAAQ,mBAAmB,MAAM;AAInC,mBAAK,WAAW,KAAK,KAAK;AAAA,YAC5B,OAAO;AAEL,mBAAK,eAAe,KAAK,cAAc,KAAK;AAAA,YAC9C;AACA;AAAA,UACF;AAAA,UACF,KAAK,YAAY;AACf,gBAAI,KAAK,gBAAgB;AACvB,mBAAK,eAAe,KAAK,OAAO;AAAA,YAClC;AACA;AAAA,UACF,KAAK,YAAY;AACf,gBAAI,KAAK,gBAAgB;AACvB,mBAAK,eAAe,eAAe,OAAO;AAAA,YAC5C;AACA;AAAA,UACF;AACE,iBAAK,QAAQ,IAAI,SAAS,SAAS,yBAAyB,QAAQ,IAAI,GAAG;AAC3E;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AACA,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,0BAA0B,MAAM;AAC9B,QAAI;AACJ,QAAI;AACJ,QAAI;AACF,OAAC,eAAe,eAAe,IAAI,KAAK,mBAAmB,uBAAuB,IAAI;AAAA,IACxF,SAAS,GAAG;AACV,YAAM,UAAU,uCAAuC;AACvD,WAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AACxC,YAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,WAAK,mBAAmB,KAAK;AAC7B,YAAM;AAAA,IACR;AACA,QAAI,gBAAgB,OAAO;AACzB,YAAM,UAAU,sCAAsC,gBAAgB;AACtE,WAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AACxC,YAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,WAAK,mBAAmB,KAAK;AAC7B,YAAM;AAAA,IACR,OAAO;AACL,WAAK,QAAQ,IAAI,SAAS,OAAO,4BAA4B;AAAA,IAC/D;AACA,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAAA,EACA,0BAA0B;AACxB,QAAI,KAAK,WAAW,SAAS,mBAAmB;AAC9C;AAAA,IACF;AAGA,SAAK,kBAAiB,oBAAI,KAAK,GAAE,QAAQ,IAAI,KAAK;AAClD,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,sBAAsB;AACpB,QAAI,CAAC,KAAK,WAAW,YAAY,CAAC,KAAK,WAAW,SAAS,mBAAmB;AAE5E,WAAK,iBAAiB,WAAW,MAAM,KAAK,cAAc,GAAG,KAAK,2BAA2B;AAE7F,UAAI,KAAK,sBAAsB,QAAW;AACxC,YAAI,WAAW,KAAK,kBAAiB,oBAAI,KAAK,GAAE,QAAQ;AACxD,YAAI,WAAW,GAAG;AAChB,qBAAW;AAAA,QACb;AAEA,aAAK,oBAAoB,WAAW,MAAY;AAC9C,cAAI,KAAK,qBAAqB,mBAAmB,WAAW;AAC1D,gBAAI;AACF,oBAAM,KAAK,aAAa,KAAK,kBAAkB;AAAA,YACjD,QAAQ;AAGN,mBAAK,kBAAkB;AAAA,YACzB;AAAA,UACF;AAAA,QACF,IAAG,QAAQ;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB;AAId,SAAK,WAAW,KAAK,IAAI,MAAM,qEAAqE,CAAC;AAAA,EACvG;AAAA,EACM,oBAAoB,mBAAmB;AAAA;AAC3C,YAAM,aAAa,kBAAkB,OAAO,YAAY;AACxD,YAAM,UAAU,KAAK,SAAS,UAAU;AACxC,UAAI,CAAC,SAAS;AACZ,aAAK,QAAQ,IAAI,SAAS,SAAS,mCAAmC,UAAU,UAAU;AAE1F,YAAI,kBAAkB,cAAc;AAClC,eAAK,QAAQ,IAAI,SAAS,SAAS,wBAAwB,UAAU,+BAA+B,kBAAkB,YAAY,IAAI;AACtI,gBAAM,KAAK,kBAAkB,KAAK,yBAAyB,kBAAkB,cAAc,mCAAmC,IAAI,CAAC;AAAA,QACrI;AACA;AAAA,MACF;AAEA,YAAM,cAAc,QAAQ,MAAM;AAElC,YAAM,kBAAkB,kBAAkB,eAAe,OAAO;AAEhE,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,iBAAW,KAAK,aAAa;AAC3B,YAAI;AACF,gBAAM,UAAU;AAChB,gBAAM,MAAM,EAAE,MAAM,MAAM,kBAAkB,SAAS;AACrD,cAAI,mBAAmB,OAAO,SAAS;AACrC,iBAAK,QAAQ,IAAI,SAAS,OAAO,kCAAkC,UAAU,6BAA6B;AAC1G,gCAAoB,KAAK,yBAAyB,kBAAkB,cAAc,qCAAqC,IAAI;AAAA,UAC7H;AAEA,sBAAY;AAAA,QACd,SAAS,GAAG;AACV,sBAAY;AACZ,eAAK,QAAQ,IAAI,SAAS,OAAO,8BAA8B,UAAU,kBAAkB,CAAC,IAAI;AAAA,QAClG;AAAA,MACF;AACA,UAAI,mBAAmB;AACrB,cAAM,KAAK,kBAAkB,iBAAiB;AAAA,MAChD,WAAW,iBAAiB;AAE1B,YAAI,WAAW;AACb,8BAAoB,KAAK,yBAAyB,kBAAkB,cAAc,GAAG,SAAS,IAAI,IAAI;AAAA,QACxG,WAAW,QAAQ,QAAW;AAC5B,8BAAoB,KAAK,yBAAyB,kBAAkB,cAAc,MAAM,GAAG;AAAA,QAC7F,OAAO;AACL,eAAK,QAAQ,IAAI,SAAS,SAAS,wBAAwB,UAAU,+BAA+B,kBAAkB,YAAY,IAAI;AAEtI,8BAAoB,KAAK,yBAAyB,kBAAkB,cAAc,mCAAmC,IAAI;AAAA,QAC3H;AACA,cAAM,KAAK,kBAAkB,iBAAiB;AAAA,MAChD,OAAO;AACL,YAAI,KAAK;AACP,eAAK,QAAQ,IAAI,SAAS,OAAO,qBAAqB,UAAU,gDAAgD;AAAA,QAClH;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,QAAQ,IAAI,SAAS,OAAO,kCAAkC,KAAK,2BAA2B,KAAK,gBAAgB,GAAG;AAE3H,SAAK,wBAAwB,KAAK,yBAAyB,SAAS,IAAI,WAAW,+EAA+E;AAGlK,QAAI,KAAK,oBAAoB;AAC3B,WAAK,mBAAmB;AAAA,IAC1B;AACA,SAAK,0BAA0B,SAAS,IAAI,MAAM,oEAAoE,CAAC;AACvH,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,QAAI,KAAK,qBAAqB,mBAAmB,eAAe;AAC9D,WAAK,eAAe,KAAK;AAAA,IAC3B,WAAW,KAAK,qBAAqB,mBAAmB,aAAa,KAAK,kBAAkB;AAE1F,WAAK,WAAW,KAAK;AAAA,IACvB,WAAW,KAAK,qBAAqB,mBAAmB,WAAW;AACjE,WAAK,eAAe,KAAK;AAAA,IAC3B;AAAA,EAMF;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,KAAK,oBAAoB;AAC3B,WAAK,mBAAmB,mBAAmB;AAC3C,WAAK,qBAAqB;AAC1B,UAAI,KAAK,gBAAgB;AACvB,aAAK,eAAe,SAAS,UAAU,QAAQ,UAAU,SAAS,QAAQ,IAAI,MAAM,oBAAoB,CAAC;AACzG,aAAK,iBAAiB;AAAA,MACxB;AACA,UAAI,SAAS,WAAW;AACtB,eAAO,SAAS,oBAAoB,UAAU,KAAK,oBAAoB;AAAA,MACzE;AACA,UAAI;AACF,aAAK,iBAAiB,QAAQ,OAAK,EAAE,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC;AAAA,MAC3D,SAAS,GAAG;AACV,aAAK,QAAQ,IAAI,SAAS,OAAO,0CAA0C,KAAK,kBAAkB,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AAAA,EACF;AAAA,EACM,WAAW,OAAO;AAAA;AACtB,YAAM,qBAAqB,KAAK,IAAI;AACpC,UAAI,4BAA4B;AAChC,UAAI,aAAa,UAAU,SAAY,QAAQ,IAAI,MAAM,iDAAiD;AAC1G,UAAI,iBAAiB,KAAK,mBAAmB,6BAA6B,GAAG,UAAU;AACvF,UAAI,mBAAmB,MAAM;AAC3B,aAAK,QAAQ,IAAI,SAAS,OAAO,oGAAoG;AACrI,aAAK,eAAe,KAAK;AACzB;AAAA,MACF;AACA,WAAK,mBAAmB,mBAAmB;AAC3C,UAAI,OAAO;AACT,aAAK,QAAQ,IAAI,SAAS,aAAa,6CAA6C,KAAK,IAAI;AAAA,MAC/F,OAAO;AACL,aAAK,QAAQ,IAAI,SAAS,aAAa,0BAA0B;AAAA,MACnE;AACA,UAAI,KAAK,uBAAuB,WAAW,GAAG;AAC5C,YAAI;AACF,eAAK,uBAAuB,QAAQ,OAAK,EAAE,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC;AAAA,QACjE,SAAS,GAAG;AACV,eAAK,QAAQ,IAAI,SAAS,OAAO,iDAAiD,KAAK,kBAAkB,CAAC,IAAI;AAAA,QAChH;AAEA,YAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC7D,eAAK,QAAQ,IAAI,SAAS,OAAO,uFAAuF;AACxH;AAAA,QACF;AAAA,MACF;AACA,aAAO,mBAAmB,MAAM;AAC9B,aAAK,QAAQ,IAAI,SAAS,aAAa,4BAA4B,yBAAyB,kBAAkB,cAAc,MAAM;AAClI,cAAM,IAAI,QAAQ,aAAW;AAC3B,eAAK,wBAAwB,WAAW,SAAS,cAAc;AAAA,QACjE,CAAC;AACD,aAAK,wBAAwB;AAC7B,YAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC7D,eAAK,QAAQ,IAAI,SAAS,OAAO,mFAAmF;AACpH;AAAA,QACF;AACA,YAAI;AACF,gBAAM,KAAK,eAAe;AAC1B,eAAK,mBAAmB,mBAAmB;AAC3C,eAAK,QAAQ,IAAI,SAAS,aAAa,yCAAyC;AAChF,cAAI,KAAK,sBAAsB,WAAW,GAAG;AAC3C,gBAAI;AACF,mBAAK,sBAAsB,QAAQ,OAAK,EAAE,MAAM,MAAM,CAAC,KAAK,WAAW,YAAY,CAAC,CAAC;AAAA,YACvF,SAAS,GAAG;AACV,mBAAK,QAAQ,IAAI,SAAS,OAAO,uDAAuD,KAAK,WAAW,YAAY,kBAAkB,CAAC,IAAI;AAAA,YAC7I;AAAA,UACF;AACA;AAAA,QACF,SAAS,GAAG;AACV,eAAK,QAAQ,IAAI,SAAS,aAAa,8CAA8C,CAAC,IAAI;AAC1F,cAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC7D,iBAAK,QAAQ,IAAI,SAAS,OAAO,4BAA4B,KAAK,gBAAgB,4EAA4E;AAE9J,gBAAI,KAAK,qBAAqB,mBAAmB,eAAe;AAC9D,mBAAK,eAAe;AAAA,YACtB;AACA;AAAA,UACF;AACA,uBAAa,aAAa,QAAQ,IAAI,IAAI,MAAM,EAAE,SAAS,CAAC;AAC5D,2BAAiB,KAAK,mBAAmB,6BAA6B,KAAK,IAAI,IAAI,oBAAoB,UAAU;AAAA,QACnH;AAAA,MACF;AACA,WAAK,QAAQ,IAAI,SAAS,aAAa,+CAA+C,KAAK,IAAI,IAAI,kBAAkB,WAAW,yBAAyB,6CAA6C;AACtM,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA,EACA,mBAAmB,oBAAoB,qBAAqB,aAAa;AACvE,QAAI;AACF,aAAO,KAAK,iBAAiB,6BAA6B;AAAA,QACxD;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,SAAS,GAAG;AACV,WAAK,QAAQ,IAAI,SAAS,OAAO,6CAA6C,kBAAkB,KAAK,mBAAmB,kBAAkB,CAAC,IAAI;AAC/I,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B,OAAO;AAC/B,UAAM,YAAY,KAAK;AACvB,SAAK,aAAa,CAAC;AACnB,WAAO,KAAK,SAAS,EAAE,QAAQ,SAAO;AACpC,YAAM,WAAW,UAAU,GAAG;AAC9B,UAAI;AACF,iBAAS,MAAM,KAAK;AAAA,MACtB,SAAS,GAAG;AACV,aAAK,QAAQ,IAAI,SAAS,OAAO,wCAAwC,KAAK,kBAAkB,eAAe,CAAC,CAAC,EAAE;AAAA,MACrH;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,mBAAmB;AAC1B,mBAAa,KAAK,iBAAiB;AACnC,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,gBAAgB;AACvB,mBAAa,KAAK,cAAc;AAAA,IAClC;AAAA,EACF;AAAA,EACA,kBAAkB,YAAY,MAAM,aAAa,WAAW;AAC1D,QAAI,aAAa;AACf,UAAI,UAAU,WAAW,GAAG;AAC1B,eAAO;AAAA,UACL,WAAW;AAAA,UACX;AAAA,UACA,QAAQ;AAAA,UACR,MAAM,YAAY;AAAA,QACpB;AAAA,MACF,OAAO;AACL,eAAO;AAAA,UACL,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,MAAM,YAAY;AAAA,QACpB;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,eAAe,KAAK;AAC1B,WAAK;AACL,UAAI,UAAU,WAAW,GAAG;AAC1B,eAAO;AAAA,UACL,WAAW;AAAA,UACX,cAAc,aAAa,SAAS;AAAA,UACpC;AAAA,UACA,QAAQ;AAAA,UACR,MAAM,YAAY;AAAA,QACpB;AAAA,MACF,OAAO;AACL,eAAO;AAAA,UACL,WAAW;AAAA,UACX,cAAc,aAAa,SAAS;AAAA,UACpC,QAAQ;AAAA,UACR,MAAM,YAAY;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,SAAS,cAAc;AACpC,QAAI,QAAQ,WAAW,GAAG;AACxB;AAAA,IACF;AAEA,QAAI,CAAC,cAAc;AACjB,qBAAe,QAAQ,QAAQ;AAAA,IACjC;AAGA,eAAW,YAAY,SAAS;AAC9B,cAAQ,QAAQ,EAAE,UAAU;AAAA,QAC1B,UAAU,MAAM;AACd,yBAAe,aAAa,KAAK,MAAM,KAAK,kBAAkB,KAAK,yBAAyB,QAAQ,CAAC,CAAC;AAAA,QACxG;AAAA,QACA,OAAO,SAAO;AACZ,cAAI;AACJ,cAAI,eAAe,OAAO;AACxB,sBAAU,IAAI;AAAA,UAChB,WAAW,OAAO,IAAI,UAAU;AAC9B,sBAAU,IAAI,SAAS;AAAA,UACzB,OAAO;AACL,sBAAU;AAAA,UACZ;AACA,yBAAe,aAAa,KAAK,MAAM,KAAK,kBAAkB,KAAK,yBAAyB,UAAU,OAAO,CAAC,CAAC;AAAA,QACjH;AAAA,QACA,MAAM,UAAQ;AACZ,yBAAe,aAAa,KAAK,MAAM,KAAK,kBAAkB,KAAK,yBAAyB,UAAU,IAAI,CAAC,CAAC;AAAA,QAC9G;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,wBAAwB,MAAM;AAC5B,UAAM,UAAU,CAAC;AACjB,UAAM,YAAY,CAAC;AACnB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAM,WAAW,KAAK,CAAC;AACvB,UAAI,KAAK,cAAc,QAAQ,GAAG;AAChC,cAAM,WAAW,KAAK;AACtB,aAAK;AAEL,gBAAQ,QAAQ,IAAI;AACpB,kBAAU,KAAK,SAAS,SAAS,CAAC;AAElC,aAAK,OAAO,GAAG,CAAC;AAAA,MAClB;AAAA,IACF;AACA,WAAO,CAAC,SAAS,SAAS;AAAA,EAC5B;AAAA,EACA,cAAc,KAAK;AAEjB,WAAO,OAAO,IAAI,aAAa,OAAO,IAAI,cAAc;AAAA,EAC1D;AAAA,EACA,wBAAwB,YAAY,MAAM,WAAW;AACnD,UAAM,eAAe,KAAK;AAC1B,SAAK;AACL,QAAI,UAAU,WAAW,GAAG;AAC1B,aAAO;AAAA,QACL,WAAW;AAAA,QACX,cAAc,aAAa,SAAS;AAAA,QACpC;AAAA,QACA,QAAQ;AAAA,QACR,MAAM,YAAY;AAAA,MACpB;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,WAAW;AAAA,QACX,cAAc,aAAa,SAAS;AAAA,QACpC,QAAQ;AAAA,QACR,MAAM,YAAY;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAAA,EACA,wBAAwB,IAAI;AAC1B,WAAO;AAAA,MACL,cAAc;AAAA,MACd,MAAM,YAAY;AAAA,IACpB;AAAA,EACF;AAAA,EACA,yBAAyB,IAAI,MAAM;AACjC,WAAO;AAAA,MACL,cAAc;AAAA,MACd;AAAA,MACA,MAAM,YAAY;AAAA,IACpB;AAAA,EACF;AAAA,EACA,yBAAyB,IAAI,OAAO,QAAQ;AAC1C,QAAI,OAAO;AACT,aAAO;AAAA,QACL;AAAA,QACA,cAAc;AAAA,QACd,MAAM,YAAY;AAAA,MACpB;AAAA,IACF;AACA,WAAO;AAAA,MACL,cAAc;AAAA,MACd;AAAA,MACA,MAAM,YAAY;AAAA,IACpB;AAAA,EACF;AAAA,EACA,sBAAsB;AACpB,WAAO;AAAA,MACL,MAAM,YAAY;AAAA,IACpB;AAAA,EACF;AACF;;;ACn4BA,IAAM,uCAAuC,CAAC,GAAG,KAAM,KAAO,KAAO,IAAI;AAElE,IAAM,yBAAN,MAA6B;AAAA,EAClC,YAAY,aAAa;AACvB,SAAK,eAAe,gBAAgB,SAAY,CAAC,GAAG,aAAa,IAAI,IAAI;AAAA,EAC3E;AAAA,EACA,6BAA6B,cAAc;AACzC,WAAO,KAAK,aAAa,aAAa,kBAAkB;AAAA,EAC1D;AACF;;;ACVO,IAAM,cAAN,MAAkB;AAAC;AAC1B,YAAY,gBAAgB;AAC5B,YAAY,SAAS;;;ACCd,IAAM,wBAAN,cAAoC,WAAW;AAAA,EACpD,YAAY,aAAa,oBAAoB;AAC3C,UAAM;AACN,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACM,KAAK,SAAS;AAAA;AAClB,UAAI,aAAa;AACjB,UAAI,KAAK,wBAAwB,CAAC,KAAK,gBAAgB,QAAQ,OAAO,QAAQ,IAAI,QAAQ,aAAa,IAAI,IAAI;AAE7G,qBAAa;AACb,aAAK,eAAe,MAAM,KAAK,oBAAoB;AAAA,MACrD;AACA,WAAK,wBAAwB,OAAO;AACpC,YAAM,WAAW,MAAM,KAAK,aAAa,KAAK,OAAO;AACrD,UAAI,cAAc,SAAS,eAAe,OAAO,KAAK,qBAAqB;AACzE,aAAK,eAAe,MAAM,KAAK,oBAAoB;AACnD,aAAK,wBAAwB,OAAO;AACpC,eAAO,MAAM,KAAK,aAAa,KAAK,OAAO;AAAA,MAC7C;AACA,aAAO;AAAA,IACT;AAAA;AAAA,EACA,wBAAwB,SAAS;AAC/B,QAAI,CAAC,QAAQ,SAAS;AACpB,cAAQ,UAAU,CAAC;AAAA,IACrB;AACA,QAAI,KAAK,cAAc;AACrB,cAAQ,QAAQ,YAAY,aAAa,IAAI,UAAU,KAAK,YAAY;AAAA,IAC1E,WAES,KAAK,qBAAqB;AACjC,UAAI,QAAQ,QAAQ,YAAY,aAAa,GAAG;AAC9C,eAAO,QAAQ,QAAQ,YAAY,aAAa;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB,KAAK;AACnB,WAAO,KAAK,aAAa,gBAAgB,GAAG;AAAA,EAC9C;AACF;;;ACxCO,IAAI;AAAA,CACV,SAAUC,oBAAmB;AAE5B,EAAAA,mBAAkBA,mBAAkB,MAAM,IAAI,CAAC,IAAI;AAEnD,EAAAA,mBAAkBA,mBAAkB,YAAY,IAAI,CAAC,IAAI;AAEzD,EAAAA,mBAAkBA,mBAAkB,kBAAkB,IAAI,CAAC,IAAI;AAE/D,EAAAA,mBAAkBA,mBAAkB,aAAa,IAAI,CAAC,IAAI;AAC5D,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEzC,IAAI;AAAA,CACV,SAAUC,iBAAgB;AAEzB,EAAAA,gBAAeA,gBAAe,MAAM,IAAI,CAAC,IAAI;AAE7C,EAAAA,gBAAeA,gBAAe,QAAQ,IAAI,CAAC,IAAI;AACjD,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;;;ACfnC,IAAMC,mBAAN,MAAsB;AAAA,EAC3B,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,QAAQ;AACN,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa;AAClB,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,SAAS;AACX,WAAO;AAAA,EACT;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AACF;;;ACjBO,IAAM,uBAAN,MAA2B;AAAA;AAAA,EAEhC,IAAI,cAAc;AAChB,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA,EACA,YAAY,YAAY,QAAQ,SAAS;AACvC,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,aAAa,IAAIC,iBAAgB;AACtC,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,EACjB;AAAA,EACM,QAAQ,KAAK,gBAAgB;AAAA;AACjC,UAAI,WAAW,KAAK,KAAK;AACzB,UAAI,WAAW,gBAAgB,gBAAgB;AAC/C,UAAI,KAAK,gBAAgB,gBAAgB,gBAAgB;AACzD,WAAK,OAAO;AACZ,WAAK,QAAQ,IAAI,SAAS,OAAO,qCAAqC;AAEtE,UAAI,mBAAmB,eAAe,UAAU,OAAO,mBAAmB,eAAe,OAAO,IAAI,eAAe,EAAE,iBAAiB,UAAU;AAC9I,cAAM,IAAI,MAAM,4FAA4F;AAAA,MAC9G;AACA,YAAM,CAAC,MAAM,KAAK,IAAI,mBAAmB;AACzC,YAAM,UAAU;AAAA,QACd,CAAC,IAAI,GAAG;AAAA,SACL,KAAK,SAAS;AAEnB,YAAM,cAAc;AAAA,QAClB,aAAa,KAAK,WAAW;AAAA,QAC7B;AAAA,QACA,SAAS;AAAA,QACT,iBAAiB,KAAK,SAAS;AAAA,MACjC;AACA,UAAI,mBAAmB,eAAe,QAAQ;AAC5C,oBAAY,eAAe;AAAA,MAC7B;AAGA,YAAM,UAAU,GAAG,GAAG,MAAM,KAAK,IAAI,CAAC;AACtC,WAAK,QAAQ,IAAI,SAAS,OAAO,oCAAoC,OAAO,GAAG;AAC/E,YAAM,WAAW,MAAM,KAAK,YAAY,IAAI,SAAS,WAAW;AAChE,UAAI,SAAS,eAAe,KAAK;AAC/B,aAAK,QAAQ,IAAI,SAAS,OAAO,qDAAqD,SAAS,UAAU,GAAG;AAE5G,aAAK,cAAc,IAAI,UAAU,SAAS,cAAc,IAAI,SAAS,UAAU;AAC/E,aAAK,WAAW;AAAA,MAClB,OAAO;AACL,aAAK,WAAW;AAAA,MAClB;AACA,WAAK,aAAa,KAAK,MAAM,KAAK,MAAM,WAAW;AAAA,IACrD;AAAA;AAAA,EACM,MAAM,KAAK,aAAa;AAAA;AAC5B,UAAI;AACF,eAAO,KAAK,UAAU;AACpB,cAAI;AACF,kBAAM,UAAU,GAAG,GAAG,MAAM,KAAK,IAAI,CAAC;AACtC,iBAAK,QAAQ,IAAI,SAAS,OAAO,oCAAoC,OAAO,GAAG;AAC/E,kBAAM,WAAW,MAAM,KAAK,YAAY,IAAI,SAAS,WAAW;AAChE,gBAAI,SAAS,eAAe,KAAK;AAC/B,mBAAK,QAAQ,IAAI,SAAS,aAAa,oDAAoD;AAC3F,mBAAK,WAAW;AAAA,YAClB,WAAW,SAAS,eAAe,KAAK;AACtC,mBAAK,QAAQ,IAAI,SAAS,OAAO,qDAAqD,SAAS,UAAU,GAAG;AAE5G,mBAAK,cAAc,IAAI,UAAU,SAAS,cAAc,IAAI,SAAS,UAAU;AAC/E,mBAAK,WAAW;AAAA,YAClB,OAAO;AAEL,kBAAI,SAAS,SAAS;AACpB,qBAAK,QAAQ,IAAI,SAAS,OAAO,0CAA0C,cAAc,SAAS,SAAS,KAAK,SAAS,iBAAiB,CAAC,GAAG;AAC9I,oBAAI,KAAK,WAAW;AAClB,uBAAK,UAAU,SAAS,OAAO;AAAA,gBACjC;AAAA,cACF,OAAO;AAEL,qBAAK,QAAQ,IAAI,SAAS,OAAO,oDAAoD;AAAA,cACvF;AAAA,YACF;AAAA,UACF,SAAS,GAAG;AACV,gBAAI,CAAC,KAAK,UAAU;AAElB,mBAAK,QAAQ,IAAI,SAAS,OAAO,wDAAwD,EAAE,OAAO,EAAE;AAAA,YACtG,OAAO;AACL,kBAAI,aAAa,cAAc;AAE7B,qBAAK,QAAQ,IAAI,SAAS,OAAO,oDAAoD;AAAA,cACvF,OAAO;AAEL,qBAAK,cAAc;AACnB,qBAAK,WAAW;AAAA,cAClB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF,UAAE;AACA,aAAK,QAAQ,IAAI,SAAS,OAAO,2CAA2C;AAG5E,YAAI,CAAC,KAAK,aAAa;AACrB,eAAK,cAAc;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EACM,KAAK,MAAM;AAAA;AACf,UAAI,CAAC,KAAK,UAAU;AAClB,eAAO,QAAQ,OAAO,IAAI,MAAM,8CAA8C,CAAC;AAAA,MACjF;AACA,aAAO,YAAY,KAAK,SAAS,eAAe,KAAK,aAAa,KAAK,MAAM,MAAM,KAAK,QAAQ;AAAA,IAClG;AAAA;AAAA,EACM,OAAO;AAAA;AACX,WAAK,QAAQ,IAAI,SAAS,OAAO,2CAA2C;AAE5E,WAAK,WAAW;AAChB,WAAK,WAAW,MAAM;AACtB,UAAI;AACF,cAAM,KAAK;AAEX,aAAK,QAAQ,IAAI,SAAS,OAAO,qDAAqD,KAAK,IAAI,GAAG;AAClG,cAAM,UAAU,CAAC;AACjB,cAAM,CAAC,MAAM,KAAK,IAAI,mBAAmB;AACzC,gBAAQ,IAAI,IAAI;AAChB,cAAM,gBAAgB;AAAA,UACpB,SAAS,kCACJ,UACA,KAAK,SAAS;AAAA,UAEnB,SAAS,KAAK,SAAS;AAAA,UACvB,iBAAiB,KAAK,SAAS;AAAA,QACjC;AACA,YAAI;AACJ,YAAI;AACF,gBAAM,KAAK,YAAY,OAAO,KAAK,MAAM,aAAa;AAAA,QACxD,SAAS,KAAK;AACZ,kBAAQ;AAAA,QACV;AACA,YAAI,OAAO;AACT,cAAI,iBAAiB,WAAW;AAC9B,gBAAI,MAAM,eAAe,KAAK;AAC5B,mBAAK,QAAQ,IAAI,SAAS,OAAO,oFAAoF;AAAA,YACvH,OAAO;AACL,mBAAK,QAAQ,IAAI,SAAS,OAAO,2DAA2D,KAAK,EAAE;AAAA,YACrG;AAAA,UACF;AAAA,QACF,OAAO;AACL,eAAK,QAAQ,IAAI,SAAS,OAAO,kDAAkD;AAAA,QACrF;AAAA,MACF,UAAE;AACA,aAAK,QAAQ,IAAI,SAAS,OAAO,wCAAwC;AAGzE,aAAK,cAAc;AAAA,MACrB;AAAA,IACF;AAAA;AAAA,EACA,gBAAgB;AACd,QAAI,KAAK,SAAS;AAChB,UAAI,aAAa;AACjB,UAAI,KAAK,aAAa;AACpB,sBAAc,aAAa,KAAK;AAAA,MAClC;AACA,WAAK,QAAQ,IAAI,SAAS,OAAO,UAAU;AAC3C,WAAK,QAAQ,KAAK,WAAW;AAAA,IAC/B;AAAA,EACF;AACF;;;ACxKO,IAAM,4BAAN,MAAgC;AAAA,EACrC,YAAY,YAAY,aAAa,QAAQ,SAAS;AACpD,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,EACjB;AAAA,EACM,QAAQ,KAAK,gBAAgB;AAAA;AACjC,UAAI,WAAW,KAAK,KAAK;AACzB,UAAI,WAAW,gBAAgB,gBAAgB;AAC/C,UAAI,KAAK,gBAAgB,gBAAgB,gBAAgB;AACzD,WAAK,QAAQ,IAAI,SAAS,OAAO,6BAA6B;AAE9D,WAAK,OAAO;AACZ,UAAI,KAAK,cAAc;AACrB,gBAAQ,IAAI,QAAQ,GAAG,IAAI,IAAI,MAAM,OAAO,gBAAgB,mBAAmB,KAAK,YAAY,CAAC;AAAA,MACnG;AACA,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAI,SAAS;AACb,YAAI,mBAAmB,eAAe,MAAM;AAC1C,iBAAO,IAAI,MAAM,2EAA2E,CAAC;AAC7F;AAAA,QACF;AACA,YAAI;AACJ,YAAI,SAAS,aAAa,SAAS,aAAa;AAC9C,wBAAc,IAAI,KAAK,SAAS,YAAY,KAAK;AAAA,YAC/C,iBAAiB,KAAK,SAAS;AAAA,UACjC,CAAC;AAAA,QACH,OAAO;AAEL,gBAAM,UAAU,KAAK,YAAY,gBAAgB,GAAG;AACpD,gBAAM,UAAU,CAAC;AACjB,kBAAQ,SAAS;AACjB,gBAAM,CAAC,MAAM,KAAK,IAAI,mBAAmB;AACzC,kBAAQ,IAAI,IAAI;AAChB,wBAAc,IAAI,KAAK,SAAS,YAAY,KAAK;AAAA,YAC/C,iBAAiB,KAAK,SAAS;AAAA,YAC/B,SAAS,kCACJ,UACA,KAAK,SAAS;AAAA,UAErB,CAAC;AAAA,QACH;AACA,YAAI;AACF,sBAAY,YAAY,OAAK;AAC3B,gBAAI,KAAK,WAAW;AAClB,kBAAI;AACF,qBAAK,QAAQ,IAAI,SAAS,OAAO,kCAAkC,cAAc,EAAE,MAAM,KAAK,SAAS,iBAAiB,CAAC,GAAG;AAC5H,qBAAK,UAAU,EAAE,IAAI;AAAA,cACvB,SAAS,OAAO;AACd,qBAAK,OAAO,KAAK;AACjB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,sBAAY,UAAU,OAAK;AAEzB,gBAAI,QAAQ;AACV,mBAAK,OAAO;AAAA,YACd,OAAO;AACL,qBAAO,IAAI,MAAM,8PAAwQ,CAAC;AAAA,YAC5R;AAAA,UACF;AACA,sBAAY,SAAS,MAAM;AACzB,iBAAK,QAAQ,IAAI,SAAS,aAAa,oBAAoB,KAAK,IAAI,EAAE;AACtE,iBAAK,eAAe;AACpB,qBAAS;AACT,oBAAQ;AAAA,UACV;AAAA,QACF,SAAS,GAAG;AACV,iBAAO,CAAC;AACR;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,EACM,KAAK,MAAM;AAAA;AACf,UAAI,CAAC,KAAK,cAAc;AACtB,eAAO,QAAQ,OAAO,IAAI,MAAM,8CAA8C,CAAC;AAAA,MACjF;AACA,aAAO,YAAY,KAAK,SAAS,OAAO,KAAK,aAAa,KAAK,MAAM,MAAM,KAAK,QAAQ;AAAA,IAC1F;AAAA;AAAA,EACA,OAAO;AACL,SAAK,OAAO;AACZ,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA,EACA,OAAO,GAAG;AACR,QAAI,KAAK,cAAc;AACrB,WAAK,aAAa,MAAM;AACxB,WAAK,eAAe;AACpB,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,CAAC;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACF;;;AChGO,IAAM,qBAAN,MAAyB;AAAA,EAC9B,YAAY,YAAY,oBAAoB,QAAQ,mBAAmB,sBAAsB,SAAS;AACpG,SAAK,UAAU;AACf,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAC1B,SAAK,wBAAwB;AAC7B,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,WAAW;AAAA,EAClB;AAAA,EACM,QAAQ,KAAK,gBAAgB;AAAA;AACjC,UAAI,WAAW,KAAK,KAAK;AACzB,UAAI,WAAW,gBAAgB,gBAAgB;AAC/C,UAAI,KAAK,gBAAgB,gBAAgB,gBAAgB;AACzD,WAAK,QAAQ,IAAI,SAAS,OAAO,oCAAoC;AACrE,UAAI;AACJ,UAAI,KAAK,qBAAqB;AAC5B,gBAAQ,MAAM,KAAK,oBAAoB;AAAA,MACzC;AACA,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,cAAM,IAAI,QAAQ,SAAS,IAAI;AAC/B,YAAI;AACJ,cAAM,UAAU,KAAK,YAAY,gBAAgB,GAAG;AACpD,YAAI,SAAS;AACb,YAAI,SAAS,UAAU,SAAS,eAAe;AAC7C,gBAAM,UAAU,CAAC;AACjB,gBAAM,CAAC,MAAM,KAAK,IAAI,mBAAmB;AACzC,kBAAQ,IAAI,IAAI;AAChB,cAAI,OAAO;AACT,oBAAQ,YAAY,aAAa,IAAI,UAAU,KAAK;AAAA,UACtD;AACA,cAAI,SAAS;AACX,oBAAQ,YAAY,MAAM,IAAI;AAAA,UAChC;AAEA,sBAAY,IAAI,KAAK,sBAAsB,KAAK,QAAW;AAAA,YACzD,SAAS,kCACJ,UACA,KAAK;AAAA,UAEZ,CAAC;AAAA,QACH,OAAO;AACL,cAAI,OAAO;AACT,oBAAQ,IAAI,QAAQ,GAAG,IAAI,IAAI,MAAM,OAAO,gBAAgB,mBAAmB,KAAK,CAAC;AAAA,UACvF;AAAA,QACF;AACA,YAAI,CAAC,WAAW;AAEd,sBAAY,IAAI,KAAK,sBAAsB,GAAG;AAAA,QAChD;AACA,YAAI,mBAAmB,eAAe,QAAQ;AAC5C,oBAAU,aAAa;AAAA,QACzB;AACA,kBAAU,SAAS,YAAU;AAC3B,eAAK,QAAQ,IAAI,SAAS,aAAa,0BAA0B,GAAG,GAAG;AACvE,eAAK,aAAa;AAClB,mBAAS;AACT,kBAAQ;AAAA,QACV;AACA,kBAAU,UAAU,WAAS;AAC3B,cAAI,QAAQ;AAEZ,cAAI,OAAO,eAAe,eAAe,iBAAiB,YAAY;AACpE,oBAAQ,MAAM;AAAA,UAChB,OAAO;AACL,oBAAQ;AAAA,UACV;AACA,eAAK,QAAQ,IAAI,SAAS,aAAa,0BAA0B,KAAK,GAAG;AAAA,QAC3E;AACA,kBAAU,YAAY,aAAW;AAC/B,eAAK,QAAQ,IAAI,SAAS,OAAO,yCAAyC,cAAc,QAAQ,MAAM,KAAK,kBAAkB,CAAC,GAAG;AACjI,cAAI,KAAK,WAAW;AAClB,gBAAI;AACF,mBAAK,UAAU,QAAQ,IAAI;AAAA,YAC7B,SAAS,OAAO;AACd,mBAAK,OAAO,KAAK;AACjB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,kBAAU,UAAU,WAAS;AAG3B,cAAI,QAAQ;AACV,iBAAK,OAAO,KAAK;AAAA,UACnB,OAAO;AACL,gBAAI,QAAQ;AAEZ,gBAAI,OAAO,eAAe,eAAe,iBAAiB,YAAY;AACpE,sBAAQ,MAAM;AAAA,YAChB,OAAO;AACL,sBAAQ;AAAA,YACV;AACA,mBAAO,IAAI,MAAM,KAAK,CAAC;AAAA,UACzB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,EACA,KAAK,MAAM;AACT,QAAI,KAAK,cAAc,KAAK,WAAW,eAAe,KAAK,sBAAsB,MAAM;AACrF,WAAK,QAAQ,IAAI,SAAS,OAAO,wCAAwC,cAAc,MAAM,KAAK,kBAAkB,CAAC,GAAG;AACxH,WAAK,WAAW,KAAK,IAAI;AACzB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,WAAO,QAAQ,OAAO,oCAAoC;AAAA,EAC5D;AAAA,EACA,OAAO;AACL,QAAI,KAAK,YAAY;AAGnB,WAAK,OAAO,MAAS;AAAA,IACvB;AACA,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA,EACA,OAAO,OAAO;AAEZ,QAAI,KAAK,YAAY;AAEnB,WAAK,WAAW,UAAU,MAAM;AAAA,MAAC;AACjC,WAAK,WAAW,YAAY,MAAM;AAAA,MAAC;AACnC,WAAK,WAAW,UAAU,MAAM;AAAA,MAAC;AACjC,WAAK,WAAW,MAAM;AACtB,WAAK,aAAa;AAAA,IACpB;AACA,SAAK,QAAQ,IAAI,SAAS,OAAO,uCAAuC;AACxE,QAAI,KAAK,SAAS;AAChB,UAAI,KAAK,cAAc,KAAK,MAAM,MAAM,aAAa,SAAS,MAAM,SAAS,MAAO;AAClF,aAAK,QAAQ,IAAI,MAAM,sCAAsC,MAAM,IAAI,KAAK,MAAM,UAAU,iBAAiB,IAAI,CAAC;AAAA,MACpH,WAAW,iBAAiB,OAAO;AACjC,aAAK,QAAQ,KAAK;AAAA,MACpB,OAAO;AACL,aAAK,QAAQ;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,SAAS,OAAO,MAAM,aAAa,aAAa,OAAO,MAAM,SAAS;AAAA,EAC/E;AACF;;;ACvIA,IAAM,gBAAgB;AAEf,IAAM,iBAAN,MAAqB;AAAA,EAC1B,YAAY,KAAK,UAAU,CAAC,GAAG;AAC7B,SAAK,uBAAuB,MAAM;AAAA,IAAC;AACnC,SAAK,WAAW,CAAC;AACjB,SAAK,oBAAoB;AACzB,QAAI,WAAW,KAAK,KAAK;AACzB,SAAK,UAAU,aAAa,QAAQ,MAAM;AAC1C,SAAK,UAAU,KAAK,YAAY,GAAG;AACnC,cAAU,WAAW,CAAC;AACtB,YAAQ,oBAAoB,QAAQ,sBAAsB,SAAY,QAAQ,QAAQ;AACtF,QAAI,OAAO,QAAQ,oBAAoB,aAAa,QAAQ,oBAAoB,QAAW;AACzF,cAAQ,kBAAkB,QAAQ,oBAAoB,SAAY,OAAO,QAAQ;AAAA,IACnF,OAAO;AACL,YAAM,IAAI,MAAM,iEAAiE;AAAA,IACnF;AACA,YAAQ,UAAU,QAAQ,YAAY,SAAY,MAAM,MAAO,QAAQ;AACvE,QAAI,kBAAkB;AACtB,QAAI,oBAAoB;AACxB,QAAI,SAAS,UAAU,OAAO,cAAY,aAAa;AAGrD,YAAM,cAAc,OAAO,wBAAwB,aAAa,0BAA0B;AAC1F,wBAAkB,YAAY,IAAI;AAClC,0BAAoB,YAAY,aAAa;AAAA,IAC/C;AACA,QAAI,CAAC,SAAS,UAAU,OAAO,cAAc,eAAe,CAAC,QAAQ,WAAW;AAC9E,cAAQ,YAAY;AAAA,IACtB,WAAW,SAAS,UAAU,CAAC,QAAQ,WAAW;AAChD,UAAI,iBAAiB;AACnB,gBAAQ,YAAY;AAAA,MACtB;AAAA,IACF;AACA,QAAI,CAAC,SAAS,UAAU,OAAO,gBAAgB,eAAe,CAAC,QAAQ,aAAa;AAClF,cAAQ,cAAc;AAAA,IACxB,WAAW,SAAS,UAAU,CAAC,QAAQ,aAAa;AAClD,UAAI,OAAO,sBAAsB,aAAa;AAC5C,gBAAQ,cAAc;AAAA,MACxB;AAAA,IACF;AACA,SAAK,cAAc,IAAI,sBAAsB,QAAQ,cAAc,IAAI,kBAAkB,KAAK,OAAO,GAAG,QAAQ,kBAAkB;AAClI,SAAK,mBAAmB;AACxB,SAAK,qBAAqB;AAC1B,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,EACjB;AAAA,EACM,MAAM,gBAAgB;AAAA;AAC1B,uBAAiB,kBAAkB,eAAe;AAClD,UAAI,KAAK,gBAAgB,gBAAgB,gBAAgB;AACzD,WAAK,QAAQ,IAAI,SAAS,OAAO,6CAA6C,eAAe,cAAc,CAAC,IAAI;AAChH,UAAI,KAAK,qBAAqB,gBAAmD;AAC/E,eAAO,QAAQ,OAAO,IAAI,MAAM,yEAAyE,CAAC;AAAA,MAC5G;AACA,WAAK,mBAAmB;AACxB,WAAK,wBAAwB,KAAK,eAAe,cAAc;AAC/D,YAAM,KAAK;AAEX,UAAI,KAAK,qBAAqB,iBAAqD;AAEjF,cAAM,UAAU;AAChB,aAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AAExC,cAAM,KAAK;AACX,eAAO,QAAQ,OAAO,IAAI,WAAW,OAAO,CAAC;AAAA,MAC/C,WAAW,KAAK,qBAAqB,aAA6C;AAEhF,cAAM,UAAU;AAChB,aAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AACxC,eAAO,QAAQ,OAAO,IAAI,WAAW,OAAO,CAAC;AAAA,MAC/C;AACA,WAAK,qBAAqB;AAAA,IAC5B;AAAA;AAAA,EACA,KAAK,MAAM;AACT,QAAI,KAAK,qBAAqB,aAA6C;AACzE,aAAO,QAAQ,OAAO,IAAI,MAAM,qEAAqE,CAAC;AAAA,IACxG;AACA,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,aAAa,IAAI,mBAAmB,KAAK,SAAS;AAAA,IACzD;AAEA,WAAO,KAAK,WAAW,KAAK,IAAI;AAAA,EAClC;AAAA,EACM,KAAK,OAAO;AAAA;AAChB,UAAI,KAAK,qBAAqB,gBAAmD;AAC/E,aAAK,QAAQ,IAAI,SAAS,OAAO,+BAA+B,KAAK,wEAAwE;AAC7I,eAAO,QAAQ,QAAQ;AAAA,MACzB;AACA,UAAI,KAAK,qBAAqB,iBAAqD;AACjF,aAAK,QAAQ,IAAI,SAAS,OAAO,+BAA+B,KAAK,yEAAyE;AAC9I,eAAO,KAAK;AAAA,MACd;AACA,WAAK,mBAAmB;AACxB,WAAK,eAAe,IAAI,QAAQ,aAAW;AAEzC,aAAK,uBAAuB;AAAA,MAC9B,CAAC;AAED,YAAM,KAAK,cAAc,KAAK;AAC9B,YAAM,KAAK;AAAA,IACb;AAAA;AAAA,EACM,cAAc,OAAO;AAAA;AAIzB,WAAK,aAAa;AAClB,UAAI;AACF,cAAM,KAAK;AAAA,MACb,SAAS,GAAG;AAAA,MAEZ;AAIA,UAAI,KAAK,WAAW;AAClB,YAAI;AACF,gBAAM,KAAK,UAAU,KAAK;AAAA,QAC5B,SAAS,GAAG;AACV,eAAK,QAAQ,IAAI,SAAS,OAAO,gDAAgD,CAAC,IAAI;AACtF,eAAK,gBAAgB;AAAA,QACvB;AACA,aAAK,YAAY;AAAA,MACnB,OAAO;AACL,aAAK,QAAQ,IAAI,SAAS,OAAO,wFAAwF;AAAA,MAC3H;AAAA,IACF;AAAA;AAAA,EACM,eAAe,gBAAgB;AAAA;AAGnC,UAAI,MAAM,KAAK;AACf,WAAK,sBAAsB,KAAK,SAAS;AACzC,WAAK,YAAY,sBAAsB,KAAK;AAC5C,UAAI;AACF,YAAI,KAAK,SAAS,iBAAiB;AACjC,cAAI,KAAK,SAAS,cAAc,kBAAkB,YAAY;AAE5D,iBAAK,YAAY,KAAK,oBAAoB,kBAAkB,UAAU;AAGtE,kBAAM,KAAK,gBAAgB,KAAK,cAAc;AAAA,UAChD,OAAO;AACL,kBAAM,IAAI,MAAM,8EAA8E;AAAA,UAChG;AAAA,QACF,OAAO;AACL,cAAI,oBAAoB;AACxB,cAAI,YAAY;AAChB,aAAG;AACD,gCAAoB,MAAM,KAAK,wBAAwB,GAAG;AAE1D,gBAAI,KAAK,qBAAqB,mBAAuD,KAAK,qBAAqB,gBAAmD;AAChK,oBAAM,IAAI,WAAW,gDAAgD;AAAA,YACvE;AACA,gBAAI,kBAAkB,OAAO;AAC3B,oBAAM,IAAI,MAAM,kBAAkB,KAAK;AAAA,YACzC;AACA,gBAAI,kBAAkB,iBAAiB;AACrC,oBAAM,IAAI,MAAM,8LAA8L;AAAA,YAChN;AACA,gBAAI,kBAAkB,KAAK;AACzB,oBAAM,kBAAkB;AAAA,YAC1B;AACA,gBAAI,kBAAkB,aAAa;AAGjC,oBAAM,cAAc,kBAAkB;AACtC,mBAAK,sBAAsB,MAAM;AAEjC,mBAAK,YAAY,eAAe;AAChC,mBAAK,YAAY,sBAAsB;AAAA,YACzC;AACA;AAAA,UACF,SAAS,kBAAkB,OAAO,YAAY;AAC9C,cAAI,cAAc,iBAAiB,kBAAkB,KAAK;AACxD,kBAAM,IAAI,MAAM,uCAAuC;AAAA,UACzD;AACA,gBAAM,KAAK,iBAAiB,KAAK,KAAK,SAAS,WAAW,mBAAmB,cAAc;AAAA,QAC7F;AACA,YAAI,KAAK,qBAAqB,sBAAsB;AAClD,eAAK,SAAS,oBAAoB;AAAA,QACpC;AACA,YAAI,KAAK,qBAAqB,cAA+C;AAG3E,eAAK,QAAQ,IAAI,SAAS,OAAO,4CAA4C;AAC7E,eAAK,mBAAmB;AAAA,QAC1B;AAAA,MAIF,SAAS,GAAG;AACV,aAAK,QAAQ,IAAI,SAAS,OAAO,qCAAqC,CAAC;AACvE,aAAK,mBAAmB;AACxB,aAAK,YAAY;AAEjB,aAAK,qBAAqB;AAC1B,eAAO,QAAQ,OAAO,CAAC;AAAA,MACzB;AAAA,IACF;AAAA;AAAA,EACM,wBAAwB,KAAK;AAAA;AACjC,YAAM,UAAU,CAAC;AACjB,YAAM,CAAC,MAAM,KAAK,IAAI,mBAAmB;AACzC,cAAQ,IAAI,IAAI;AAChB,YAAM,eAAe,KAAK,qBAAqB,GAAG;AAClD,WAAK,QAAQ,IAAI,SAAS,OAAO,gCAAgC,YAAY,GAAG;AAChF,UAAI;AACF,cAAM,WAAW,MAAM,KAAK,YAAY,KAAK,cAAc;AAAA,UACzD,SAAS;AAAA,UACT,SAAS,kCACJ,UACA,KAAK,SAAS;AAAA,UAEnB,SAAS,KAAK,SAAS;AAAA,UACvB,iBAAiB,KAAK,SAAS;AAAA,QACjC,CAAC;AACD,YAAI,SAAS,eAAe,KAAK;AAC/B,iBAAO,QAAQ,OAAO,IAAI,MAAM,mDAAmD,SAAS,UAAU,GAAG,CAAC;AAAA,QAC5G;AACA,cAAM,oBAAoB,KAAK,MAAM,SAAS,OAAO;AACrD,YAAI,CAAC,kBAAkB,oBAAoB,kBAAkB,mBAAmB,GAAG;AAGjF,4BAAkB,kBAAkB,kBAAkB;AAAA,QACxD;AACA,YAAI,kBAAkB,wBAAwB,KAAK,SAAS,0BAA0B,MAAM;AAC1F,iBAAO,QAAQ,OAAO,IAAI,iCAAiC,gEAAgE,CAAC;AAAA,QAC9H;AACA,eAAO;AAAA,MACT,SAAS,GAAG;AACV,YAAI,eAAe,qDAAqD;AACxE,YAAI,aAAa,WAAW;AAC1B,cAAI,EAAE,eAAe,KAAK;AACxB,2BAAe,eAAe;AAAA,UAChC;AAAA,QACF;AACA,aAAK,QAAQ,IAAI,SAAS,OAAO,YAAY;AAC7C,eAAO,QAAQ,OAAO,IAAI,iCAAiC,YAAY,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA;AAAA,EACA,kBAAkB,KAAK,iBAAiB;AACtC,QAAI,CAAC,iBAAiB;AACpB,aAAO;AAAA,IACT;AACA,WAAO,OAAO,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAO,MAAM,eAAe;AAAA,EAC5E;AAAA,EACM,iBAAiB,KAAK,oBAAoB,mBAAmB,yBAAyB;AAAA;AAC1F,UAAI,aAAa,KAAK,kBAAkB,KAAK,kBAAkB,eAAe;AAC9E,UAAI,KAAK,cAAc,kBAAkB,GAAG;AAC1C,aAAK,QAAQ,IAAI,SAAS,OAAO,yEAAyE;AAC1G,aAAK,YAAY;AACjB,cAAM,KAAK,gBAAgB,YAAY,uBAAuB;AAC9D,aAAK,eAAe,kBAAkB;AACtC;AAAA,MACF;AACA,YAAM,sBAAsB,CAAC;AAC7B,YAAM,aAAa,kBAAkB,uBAAuB,CAAC;AAC7D,UAAI,YAAY;AAChB,iBAAW,YAAY,YAAY;AACjC,cAAM,mBAAmB,KAAK,yBAAyB,UAAU,oBAAoB,0BAA0B,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU,0BAA0B,IAAI;AAC7M,YAAI,4BAA4B,OAAO;AAErC,8BAAoB,KAAK,GAAG,SAAS,SAAS,UAAU;AACxD,8BAAoB,KAAK,gBAAgB;AAAA,QAC3C,WAAW,KAAK,cAAc,gBAAgB,GAAG;AAC/C,eAAK,YAAY;AACjB,cAAI,CAAC,WAAW;AACd,gBAAI;AACF,0BAAY,MAAM,KAAK,wBAAwB,GAAG;AAAA,YACpD,SAAS,IAAI;AACX,qBAAO,QAAQ,OAAO,EAAE;AAAA,YAC1B;AACA,yBAAa,KAAK,kBAAkB,KAAK,UAAU,eAAe;AAAA,UACpE;AACA,cAAI;AACF,kBAAM,KAAK,gBAAgB,YAAY,uBAAuB;AAC9D,iBAAK,eAAe,UAAU;AAC9B;AAAA,UACF,SAAS,IAAI;AACX,iBAAK,QAAQ,IAAI,SAAS,OAAO,kCAAkC,SAAS,SAAS,MAAM,EAAE,EAAE;AAC/F,wBAAY;AACZ,gCAAoB,KAAK,IAAI,4BAA4B,GAAG,SAAS,SAAS,YAAY,EAAE,IAAI,kBAAkB,SAAS,SAAS,CAAC,CAAC;AACtI,gBAAI,KAAK,qBAAqB,cAA+C;AAC3E,oBAAM,UAAU;AAChB,mBAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AACxC,qBAAO,QAAQ,OAAO,IAAI,WAAW,OAAO,CAAC;AAAA,YAC/C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,oBAAoB,SAAS,GAAG;AAClC,eAAO,QAAQ,OAAO,IAAI,gBAAgB,yEAAyE,oBAAoB,KAAK,GAAG,CAAC,IAAI,mBAAmB,CAAC;AAAA,MAC1K;AACA,aAAO,QAAQ,OAAO,IAAI,MAAM,6EAA6E,CAAC;AAAA,IAChH;AAAA;AAAA,EACA,oBAAoB,WAAW;AAC7B,YAAQ,WAAW;AAAA,MACjB,KAAK,kBAAkB;AACrB,YAAI,CAAC,KAAK,SAAS,WAAW;AAC5B,gBAAM,IAAI,MAAM,mDAAmD;AAAA,QACrE;AACA,eAAO,IAAI,mBAAmB,KAAK,aAAa,KAAK,qBAAqB,KAAK,SAAS,KAAK,SAAS,mBAAmB,KAAK,SAAS,WAAW,KAAK,SAAS,WAAW,CAAC,CAAC;AAAA,MAC/K,KAAK,kBAAkB;AACrB,YAAI,CAAC,KAAK,SAAS,aAAa;AAC9B,gBAAM,IAAI,MAAM,qDAAqD;AAAA,QACvE;AACA,eAAO,IAAI,0BAA0B,KAAK,aAAa,KAAK,YAAY,cAAc,KAAK,SAAS,KAAK,QAAQ;AAAA,MACnH,KAAK,kBAAkB;AACrB,eAAO,IAAI,qBAAqB,KAAK,aAAa,KAAK,SAAS,KAAK,QAAQ;AAAA,MAC/E;AACE,cAAM,IAAI,MAAM,sBAAsB,SAAS,GAAG;AAAA,IACtD;AAAA,EACF;AAAA,EACA,gBAAgB,KAAK,gBAAgB;AACnC,SAAK,UAAU,YAAY,KAAK;AAChC,QAAI,KAAK,SAAS,WAAW;AAC3B,WAAK,UAAU,UAAU,CAAM,MAAK;AAClC,YAAI,WAAW;AACf,YAAI,KAAK,SAAS,WAAW;AAC3B,cAAI;AACF,iBAAK,SAAS,aAAa;AAC3B,kBAAM,KAAK,UAAU,QAAQ,KAAK,cAAc;AAChD,kBAAM,KAAK,SAAS,OAAO;AAAA,UAC7B,QAAQ;AACN,uBAAW;AAAA,UACb;AAAA,QACF,OAAO;AACL,eAAK,gBAAgB,CAAC;AACtB;AAAA,QACF;AACA,YAAI,UAAU;AACZ,eAAK,gBAAgB,CAAC;AAAA,QACxB;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,UAAU,UAAU,OAAK,KAAK,gBAAgB,CAAC;AAAA,IACtD;AACA,WAAO,KAAK,UAAU,QAAQ,KAAK,cAAc;AAAA,EACnD;AAAA,EACA,yBAAyB,UAAU,oBAAoB,yBAAyB,sBAAsB;AACpG,UAAM,YAAY,kBAAkB,SAAS,SAAS;AACtD,QAAI,cAAc,QAAQ,cAAc,QAAW;AACjD,WAAK,QAAQ,IAAI,SAAS,OAAO,uBAAuB,SAAS,SAAS,+CAA+C;AACzH,aAAO,IAAI,MAAM,uBAAuB,SAAS,SAAS,+CAA+C;AAAA,IAC3G,OAAO;AACL,UAAI,iBAAiB,oBAAoB,SAAS,GAAG;AACnD,cAAM,kBAAkB,SAAS,gBAAgB,IAAI,OAAK,eAAe,CAAC,CAAC;AAC3E,YAAI,gBAAgB,QAAQ,uBAAuB,KAAK,GAAG;AACzD,cAAI,cAAc,kBAAkB,cAAc,CAAC,KAAK,SAAS,aAAa,cAAc,kBAAkB,oBAAoB,CAAC,KAAK,SAAS,aAAa;AAC5J,iBAAK,QAAQ,IAAI,SAAS,OAAO,uBAAuB,kBAAkB,SAAS,CAAC,qDAAqD;AACzI,mBAAO,IAAI,0BAA0B,IAAI,kBAAkB,SAAS,CAAC,2CAA2C,SAAS;AAAA,UAC3H,OAAO;AACL,iBAAK,QAAQ,IAAI,SAAS,OAAO,wBAAwB,kBAAkB,SAAS,CAAC,IAAI;AACzF,gBAAI;AACF,mBAAK,SAAS,YAAY,cAAc,kBAAkB,aAAa,uBAAuB;AAC9F,qBAAO,KAAK,oBAAoB,SAAS;AAAA,YAC3C,SAAS,IAAI;AACX,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF,OAAO;AACL,eAAK,QAAQ,IAAI,SAAS,OAAO,uBAAuB,kBAAkB,SAAS,CAAC,gEAAgE,eAAe,uBAAuB,CAAC,IAAI;AAC/L,iBAAO,IAAI,MAAM,IAAI,kBAAkB,SAAS,CAAC,sBAAsB,eAAe,uBAAuB,CAAC,GAAG;AAAA,QACnH;AAAA,MACF,OAAO;AACL,aAAK,QAAQ,IAAI,SAAS,OAAO,uBAAuB,kBAAkB,SAAS,CAAC,0CAA0C;AAC9H,eAAO,IAAI,uBAAuB,IAAI,kBAAkB,SAAS,CAAC,gCAAgC,SAAS;AAAA,MAC7G;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,WAAW;AACvB,WAAO,aAAa,OAAO,cAAc,YAAY,aAAa;AAAA,EACpE;AAAA,EACA,gBAAgB,OAAO;AACrB,SAAK,QAAQ,IAAI,SAAS,OAAO,iCAAiC,KAAK,2BAA2B,KAAK,gBAAgB,GAAG;AAC1H,SAAK,YAAY;AAEjB,YAAQ,KAAK,cAAc;AAC3B,SAAK,aAAa;AAClB,QAAI,KAAK,qBAAqB,gBAAmD;AAC/E,WAAK,QAAQ,IAAI,SAAS,OAAO,yCAAyC,KAAK,4EAA4E;AAC3J;AAAA,IACF;AACA,QAAI,KAAK,qBAAqB,cAA+C;AAC3E,WAAK,QAAQ,IAAI,SAAS,SAAS,yCAAyC,KAAK,wEAAwE;AACzJ,YAAM,IAAI,MAAM,iCAAiC,KAAK,qEAAqE;AAAA,IAC7H;AACA,QAAI,KAAK,qBAAqB,iBAAqD;AAGjF,WAAK,qBAAqB;AAAA,IAC5B;AACA,QAAI,OAAO;AACT,WAAK,QAAQ,IAAI,SAAS,OAAO,uCAAuC,KAAK,IAAI;AAAA,IACnF,OAAO;AACL,WAAK,QAAQ,IAAI,SAAS,aAAa,0BAA0B;AAAA,IACnE;AACA,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,KAAK,EAAE,MAAM,OAAK;AAChC,aAAK,QAAQ,IAAI,SAAS,OAAO,0CAA0C,CAAC,IAAI;AAAA,MAClF,CAAC;AACD,WAAK,aAAa;AAAA,IACpB;AACA,SAAK,eAAe;AACpB,SAAK,mBAAmB;AACxB,QAAI,KAAK,oBAAoB;AAC3B,WAAK,qBAAqB;AAC1B,UAAI;AACF,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,KAAK;AAAA,QACpB;AAAA,MACF,SAAS,GAAG;AACV,aAAK,QAAQ,IAAI,SAAS,OAAO,0BAA0B,KAAK,kBAAkB,CAAC,IAAI;AAAA,MACzF;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY,KAAK;AAEf,QAAI,IAAI,YAAY,YAAY,CAAC,MAAM,KAAK,IAAI,YAAY,WAAW,CAAC,MAAM,GAAG;AAC/E,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,WAAW;AACvB,YAAM,IAAI,MAAM,mBAAmB,GAAG,IAAI;AAAA,IAC5C;AAMA,UAAM,OAAO,OAAO,SAAS,cAAc,GAAG;AAC9C,SAAK,OAAO;AACZ,SAAK,QAAQ,IAAI,SAAS,aAAa,gBAAgB,GAAG,SAAS,KAAK,IAAI,IAAI;AAChF,WAAO,KAAK;AAAA,EACd;AAAA,EACA,qBAAqB,KAAK;AACxB,UAAM,eAAe,IAAI,IAAI,GAAG;AAChC,QAAI,aAAa,SAAS,SAAS,GAAG,GAAG;AACvC,mBAAa,YAAY;AAAA,IAC3B,OAAO;AACL,mBAAa,YAAY;AAAA,IAC3B;AACA,UAAM,eAAe,IAAI,gBAAgB,aAAa,YAAY;AAClE,QAAI,CAAC,aAAa,IAAI,kBAAkB,GAAG;AACzC,mBAAa,OAAO,oBAAoB,KAAK,kBAAkB,SAAS,CAAC;AAAA,IAC3E;AACA,QAAI,aAAa,IAAI,sBAAsB,GAAG;AAC5C,UAAI,aAAa,IAAI,sBAAsB,MAAM,QAAQ;AACvD,aAAK,SAAS,wBAAwB;AAAA,MACxC;AAAA,IACF,WAAW,KAAK,SAAS,0BAA0B,MAAM;AACvD,mBAAa,OAAO,wBAAwB,MAAM;AAAA,IACpD;AACA,iBAAa,SAAS,aAAa,SAAS;AAC5C,WAAO,aAAa,SAAS;AAAA,EAC/B;AACF;AACA,SAAS,iBAAiB,oBAAoB,iBAAiB;AAC7D,SAAO,CAAC,uBAAuB,kBAAkB,wBAAwB;AAC3E;AAEO,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EAC9B,YAAY,YAAY;AACtB,SAAK,aAAa;AAClB,SAAK,UAAU,CAAC;AAChB,SAAK,aAAa;AAClB,SAAK,oBAAoB,IAAI,cAAc;AAC3C,SAAK,mBAAmB,IAAI,cAAc;AAC1C,SAAK,mBAAmB,KAAK,UAAU;AAAA,EACzC;AAAA,EACA,KAAK,MAAM;AACT,SAAK,YAAY,IAAI;AACrB,QAAI,CAAC,KAAK,kBAAkB;AAC1B,WAAK,mBAAmB,IAAI,cAAc;AAAA,IAC5C;AACA,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA,EACA,OAAO;AACL,SAAK,aAAa;AAClB,SAAK,kBAAkB,QAAQ;AAC/B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,MAAM;AAChB,QAAI,KAAK,QAAQ,UAAU,OAAO,KAAK,QAAQ,CAAC,MAAM,OAAO,MAAM;AACjE,YAAM,IAAI,MAAM,+BAA+B,OAAO,KAAK,OAAO,oBAAoB,OAAO,IAAI,EAAE;AAAA,IACrG;AACA,SAAK,QAAQ,KAAK,IAAI;AACtB,SAAK,kBAAkB,QAAQ;AAAA,EACjC;AAAA,EACM,YAAY;AAAA;AAChB,aAAO,MAAM;AACX,cAAM,KAAK,kBAAkB;AAC7B,YAAI,CAAC,KAAK,YAAY;AACpB,cAAI,KAAK,kBAAkB;AACzB,iBAAK,iBAAiB,OAAO,qBAAqB;AAAA,UACpD;AACA;AAAA,QACF;AACA,aAAK,oBAAoB,IAAI,cAAc;AAC3C,cAAM,kBAAkB,KAAK;AAC7B,aAAK,mBAAmB;AACxB,cAAM,OAAO,OAAO,KAAK,QAAQ,CAAC,MAAM,WAAW,KAAK,QAAQ,KAAK,EAAE,IAAI,oBAAmB,eAAe,KAAK,OAAO;AACzH,aAAK,QAAQ,SAAS;AACtB,YAAI;AACF,gBAAM,KAAK,WAAW,KAAK,IAAI;AAC/B,0BAAgB,QAAQ;AAAA,QAC1B,SAAS,OAAO;AACd,0BAAgB,OAAO,KAAK;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EACA,OAAO,eAAe,cAAc;AAClC,UAAM,cAAc,aAAa,IAAI,OAAK,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC;AAC9E,UAAM,SAAS,IAAI,WAAW,WAAW;AACzC,QAAI,SAAS;AACb,eAAW,QAAQ,cAAc;AAC/B,aAAO,IAAI,IAAI,WAAW,IAAI,GAAG,MAAM;AACvC,gBAAU,KAAK;AAAA,IACjB;AACA,WAAO,OAAO;AAAA,EAChB;AACF;AACA,IAAM,gBAAN,MAAoB;AAAA,EAClB,cAAc;AACZ,SAAK,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW,CAAC,KAAK,WAAW,KAAK,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC;AAAA,EACtG;AAAA,EACA,UAAU;AACR,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO,QAAQ;AACb,SAAK,UAAU,MAAM;AAAA,EACvB;AACF;;;ACthBA,IAAM,yBAAyB;AAExB,IAAM,kBAAN,MAAsB;AAAA,EAC3B,cAAc;AAEZ,SAAK,OAAO;AAEZ,SAAK,UAAU;AAEf,SAAK,iBAAiB,eAAe;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,OAAO,QAAQ;AAE3B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,IAAI,MAAM,yDAAyD;AAAA,IAC3E;AACA,QAAI,CAAC,OAAO;AACV,aAAO,CAAC;AAAA,IACV;AACA,QAAI,WAAW,MAAM;AACnB,eAAS,WAAW;AAAA,IACtB;AAEA,UAAM,WAAW,kBAAkB,MAAM,KAAK;AAC9C,UAAM,cAAc,CAAC;AACrB,eAAW,WAAW,UAAU;AAC9B,YAAM,gBAAgB,KAAK,MAAM,OAAO;AACxC,UAAI,OAAO,cAAc,SAAS,UAAU;AAC1C,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACpC;AACA,cAAQ,cAAc,MAAM;AAAA,QAC1B,KAAK,YAAY;AACf,eAAK,qBAAqB,aAAa;AACvC;AAAA,QACF,KAAK,YAAY;AACf,eAAK,qBAAqB,aAAa;AACvC;AAAA,QACF,KAAK,YAAY;AACf,eAAK,qBAAqB,aAAa;AACvC;AAAA,QACF,KAAK,YAAY;AAEf;AAAA,QACF,KAAK,YAAY;AAEf;AAAA,QACF,KAAK,YAAY;AACf,eAAK,cAAc,aAAa;AAChC;AAAA,QACF,KAAK,YAAY;AACf,eAAK,mBAAmB,aAAa;AACrC;AAAA,QACF;AAEE,iBAAO,IAAI,SAAS,aAAa,2BAA2B,cAAc,OAAO,YAAY;AAC7F;AAAA,MACJ;AACA,kBAAY,KAAK,aAAa;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,SAAS;AACpB,WAAO,kBAAkB,MAAM,KAAK,UAAU,OAAO,CAAC;AAAA,EACxD;AAAA,EACA,qBAAqB,SAAS;AAC5B,SAAK,sBAAsB,QAAQ,QAAQ,yCAAyC;AACpF,QAAI,QAAQ,iBAAiB,QAAW;AACtC,WAAK,sBAAsB,QAAQ,cAAc,yCAAyC;AAAA,IAC5F;AAAA,EACF;AAAA,EACA,qBAAqB,SAAS;AAC5B,SAAK,sBAAsB,QAAQ,cAAc,yCAAyC;AAC1F,QAAI,QAAQ,SAAS,QAAW;AAC9B,YAAM,IAAI,MAAM,yCAAyC;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,qBAAqB,SAAS;AAC5B,QAAI,QAAQ,UAAU,QAAQ,OAAO;AACnC,YAAM,IAAI,MAAM,yCAAyC;AAAA,IAC3D;AACA,QAAI,CAAC,QAAQ,UAAU,QAAQ,OAAO;AACpC,WAAK,sBAAsB,QAAQ,OAAO,yCAAyC;AAAA,IACrF;AACA,SAAK,sBAAsB,QAAQ,cAAc,yCAAyC;AAAA,EAC5F;AAAA,EACA,cAAc,SAAS;AACrB,QAAI,OAAO,QAAQ,eAAe,UAAU;AAC1C,YAAM,IAAI,MAAM,qCAAqC;AAAA,IACvD;AAAA,EACF;AAAA,EACA,mBAAmB,SAAS;AAC1B,QAAI,OAAO,QAAQ,eAAe,UAAU;AAC1C,YAAM,IAAI,MAAM,0CAA0C;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,sBAAsB,OAAO,cAAc;AACzC,QAAI,OAAO,UAAU,YAAY,UAAU,IAAI;AAC7C,YAAM,IAAI,MAAM,YAAY;AAAA,IAC9B;AAAA,EACF;AACF;;;AC5GA,IAAM,sBAAsB;AAAA,EAC1B,OAAO,SAAS;AAAA,EAChB,OAAO,SAAS;AAAA,EAChB,MAAM,SAAS;AAAA,EACf,aAAa,SAAS;AAAA,EACtB,MAAM,SAAS;AAAA,EACf,SAAS,SAAS;AAAA,EAClB,OAAO,SAAS;AAAA,EAChB,UAAU,SAAS;AAAA,EACnB,MAAM,SAAS;AACjB;AACA,SAAS,cAAc,MAAM;AAI3B,QAAM,UAAU,oBAAoB,KAAK,YAAY,CAAC;AACtD,MAAI,OAAO,YAAY,aAAa;AAClC,WAAO;AAAA,EACT,OAAO;AACL,UAAM,IAAI,MAAM,sBAAsB,IAAI,EAAE;AAAA,EAC9C;AACF;AAEO,IAAM,uBAAN,MAA2B;AAAA,EAChC,iBAAiB,SAAS;AACxB,QAAI,WAAW,SAAS,SAAS;AACjC,QAAI,SAAS,OAAO,GAAG;AACrB,WAAK,SAAS;AAAA,IAChB,WAAW,OAAO,YAAY,UAAU;AACtC,YAAM,WAAW,cAAc,OAAO;AACtC,WAAK,SAAS,IAAI,cAAc,QAAQ;AAAA,IAC1C,OAAO;AACL,WAAK,SAAS,IAAI,cAAc,OAAO;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,KAAK,wBAAwB;AACnC,QAAI,WAAW,KAAK,KAAK;AACzB,QAAI,WAAW,KAAK,KAAK;AACzB,SAAK,MAAM;AAGX,QAAI,OAAO,2BAA2B,UAAU;AAC9C,WAAK,wBAAwB,kCACxB,KAAK,wBACL;AAAA,IAEP,OAAO;AACL,WAAK,wBAAwB,iCACxB,KAAK,wBADmB;AAAA,QAE3B,WAAW;AAAA,MACb;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,UAAU;AACxB,QAAI,WAAW,UAAU,UAAU;AACnC,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA,EACA,uBAAuB,8BAA8B;AACnD,QAAI,KAAK,iBAAiB;AACxB,YAAM,IAAI,MAAM,yCAAyC;AAAA,IAC3D;AACA,QAAI,CAAC,8BAA8B;AACjC,WAAK,kBAAkB,IAAI,uBAAuB;AAAA,IACpD,WAAW,MAAM,QAAQ,4BAA4B,GAAG;AACtD,WAAK,kBAAkB,IAAI,uBAAuB,4BAA4B;AAAA,IAChF,OAAO;AACL,WAAK,kBAAkB;AAAA,IACzB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,cAAc;AAC9B,QAAI,WAAW,cAAc,cAAc;AAC3C,SAAK,+BAA+B;AACpC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,cAAc;AAClC,QAAI,WAAW,cAAc,cAAc;AAC3C,SAAK,mCAAmC;AACxC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,SAAS;AAC7B,QAAI,KAAK,0BAA0B,QAAW;AAC5C,WAAK,wBAAwB,CAAC;AAAA,IAChC;AACA,SAAK,sBAAsB,wBAAwB;AACnD,SAAK,+BAA+B,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAC9F,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AAGN,UAAM,wBAAwB,KAAK,yBAAyB,CAAC;AAE7D,QAAI,sBAAsB,WAAW,QAAW;AAE9C,4BAAsB,SAAS,KAAK;AAAA,IACtC;AAEA,QAAI,CAAC,KAAK,KAAK;AACb,YAAM,IAAI,MAAM,0FAA0F;AAAA,IAC5G;AACA,UAAM,aAAa,IAAI,eAAe,KAAK,KAAK,qBAAqB;AACrE,WAAO,cAAc,OAAO,YAAY,KAAK,UAAU,WAAW,UAAU,KAAK,YAAY,IAAI,gBAAgB,GAAG,KAAK,iBAAiB,KAAK,8BAA8B,KAAK,kCAAkC,KAAK,4BAA4B;AAAA,EACvP;AACF;AACA,SAAS,SAAS,QAAQ;AACxB,SAAO,OAAO,QAAQ;AACxB;", "names": ["LogLevel", "MessageType", "HubConnectionState", "HttpTransportType", "TransferFormat", "AbortController", "AbortController"]}