# Debug complet des produits et données
$baseUrl = "http://localhost:5014/api"

Write-Host "=== DEBUG COMPLET PRODUITS ET DONNÉES ===" -ForegroundColor Red

# 1. Vérifier tous les produits
Write-Host "`n🔍 1. VÉRIFICATION PRODUITS..." -ForegroundColor Yellow
try {
    $produits = Invoke-RestMethod -Uri "$baseUrl/Produits" -Method Get
    Write-Host "✅ $($produits.Count) produits trouvés" -ForegroundColor Green
    
    if ($produits.Count -gt 0) {
        Write-Host "`n📋 TOUS LES PRODUITS:" -ForegroundColor Cyan
        foreach ($p in $produits) {
            Write-Host "  - ID: $($p.id) | Nom: $($p.nom) | Fournisseur: $($p.fournisseurId) | Prix: $($p.prixVenteHT) TND | Stock: $($p.stock)" -ForegroundColor White
        }
        
        # Grouper par fournisseur
        Write-Host "`n📊 PRODUITS PAR FOURNISSEUR:" -ForegroundColor Cyan
        $produits | Group-Object fournisseurId | Sort-Object Name | ForEach-Object {
            Write-Host "  - Fournisseur ID $($_.Name): $($_.Count) produits" -ForegroundColor White
        }
    } else {
        Write-Host "❌ AUCUN PRODUIT DANS LA BASE !" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Erreur récupération produits: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Vérifier les fournisseurs
Write-Host "`n🔍 2. VÉRIFICATION FOURNISSEURS..." -ForegroundColor Yellow
try {
    $fournisseurs = Invoke-RestMethod -Uri "$baseUrl/Fournisseurs" -Method Get
    Write-Host "✅ $($fournisseurs.Count) fournisseurs trouvés" -ForegroundColor Green
    
    Write-Host "`n📋 FOURNISSEURS ACTIFS:" -ForegroundColor Cyan
    $fournisseurs | Where-Object { $_.estActif } | Select-Object -First 5 | ForEach-Object {
        Write-Host "  - ID: $($_.id) | Email: $($_.email) | Nom: $($_.nom) $($_.prenom)" -ForegroundColor White
    }
} catch {
    Write-Host "❌ Erreur fournisseurs: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Vérifier les promotions
Write-Host "`n🔍 3. VÉRIFICATION PROMOTIONS..." -ForegroundColor Yellow
try {
    $promotions = Invoke-RestMethod -Uri "$baseUrl/Promotions" -Method Get
    Write-Host "✅ $($promotions.Count) promotions trouvées" -ForegroundColor Green
    
    if ($promotions.Count -gt 0) {
        Write-Host "`n📋 PROMOTIONS:" -ForegroundColor Cyan
        $promotions | Select-Object -First 3 | ForEach-Object {
            Write-Host "  - ID: $($_.id) | Type: $($_.type) | Remise: $($_.pourcentageRemise)% | Active: $($_.estActive)" -ForegroundColor White
        }
    }
} catch {
    Write-Host "❌ Erreur promotions: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. Test endpoints spécifiques
Write-Host "`n🔍 4. TEST ENDPOINTS SPÉCIFIQUES..." -ForegroundColor Yellow

# Test produits par fournisseur
$fournisseurTest = 11
Write-Host "Test produits fournisseur $fournisseurTest..." -ForegroundColor Gray
try {
    $produitsFournisseur = Invoke-RestMethod -Uri "$baseUrl/Fournisseurs/$fournisseurTest/produits" -Method Get
    Write-Host "✅ $($produitsFournisseur.Count) produits pour fournisseur $fournisseurTest" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# Test promotions actives
Write-Host "Test promotions actives..." -ForegroundColor Gray
try {
    $promotionsActives = Invoke-RestMethod -Uri "$baseUrl/Promotions/active" -Method Get
    Write-Host "✅ $($promotionsActives.Count) promotions actives" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. Créer des données de test si nécessaire
Write-Host "`n🔍 5. CRÉATION DONNÉES DE TEST..." -ForegroundColor Yellow

if ($produits.Count -eq 0) {
    Write-Host "Création de produits de test..." -ForegroundColor Yellow
    
    $produitsTest = @(
        @{
            nom = "Lunettes Test Dashboard 1"
            description = "Produit de test pour dashboard"
            prixVenteHT = 150.00
            stock = 20
            fournisseurId = 11
            categorieId = 1
            marqueId = 1
            formeId = 1
            reference = "TEST001"
        },
        @{
            nom = "Lunettes Test Dashboard 2"
            description = "Produit de test pour dashboard"
            prixVenteHT = 200.00
            stock = 15
            fournisseurId = 11
            categorieId = 1
            marqueId = 1
            formeId = 1
            reference = "TEST002"
        }
    )
    
    $headers = @{"Content-Type" = "application/json"}
    
    foreach ($produit in $produitsTest) {
        try {
            $produitJson = $produit | ConvertTo-Json
            $newProduit = Invoke-RestMethod -Uri "$baseUrl/Produits" -Method Post -Headers $headers -Body $produitJson
            Write-Host "✅ Produit créé: $($produit.nom)" -ForegroundColor Green
        } catch {
            Write-Host "❌ Erreur création: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "`n=== DIAGNOSTIC ===" -ForegroundColor Red
Write-Host "1. Vérifiez les résultats ci-dessus" -ForegroundColor White
Write-Host "2. Si pas de produits, ils doivent être créés" -ForegroundColor White
Write-Host "3. Si erreurs HTTP, vérifiez le backend" -ForegroundColor White
Write-Host "4. Si données OK, le problème est dans le frontend" -ForegroundColor White

Write-Host "`n=== FIN DEBUG ===" -ForegroundColor Red
