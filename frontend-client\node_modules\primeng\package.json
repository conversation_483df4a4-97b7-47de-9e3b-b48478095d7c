{"name": "primeng", "version": "19.1.3", "author": "PrimeTek Informatics", "description": "PrimeNG is an open source UI library for Angular featuring a rich set of 80+ components, a theme designer, various theme alternatives such as Material, Bootstrap, Tailwind, premium templates and professional support. In addition, it integrates with PrimeBlock, which has 370+ ready to use UI blocks to build spectacular applications in no time.", "homepage": "https://primeng.org/", "license": "SEE LICENSE IN LICENSE.md", "repository": {"type": "git", "url": "https://github.com/primefaces/primeng.git", "directory": "packages/primeng"}, "bugs": {"url": "https://github.com/primefaces/primeng/issues"}, "keywords": ["primeng", "angular", "ui library", "component library", "material", "bootstrap", "fluent", "tailwind"], "publishConfig": {"access": "public"}, "peerDependencies": {"@angular/animations": "^19.0.0", "@angular/cdk": "^19.0.0", "@angular/common": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/router": "^19.0.0", "@angular/platform-browser": "^19.0.0", "rxjs": "^6.0.0 || ^7.8.1"}, "dependencies": {"@primeuix/styled": "^0.3.2", "@primeuix/utils": "^0.3.2", "tslib": "^2.3.0"}, "module": "fesm2022/primeng.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/primeng.mjs"}, "./accordion": {"types": "./accordion/index.d.ts", "default": "./fesm2022/primeng-accordion.mjs"}, "./animateonscroll": {"types": "./animateonscroll/index.d.ts", "default": "./fesm2022/primeng-animateonscroll.mjs"}, "./autocomplete": {"types": "./autocomplete/index.d.ts", "default": "./fesm2022/primeng-autocomplete.mjs"}, "./api": {"types": "./api/index.d.ts", "default": "./fesm2022/primeng-api.mjs"}, "./avatargroup": {"types": "./avatargroup/index.d.ts", "default": "./fesm2022/primeng-avatargroup.mjs"}, "./autofocus": {"types": "./autofocus/index.d.ts", "default": "./fesm2022/primeng-autofocus.mjs"}, "./avatar": {"types": "./avatar/index.d.ts", "default": "./fesm2022/primeng-avatar.mjs"}, "./badge": {"types": "./badge/index.d.ts", "default": "./fesm2022/primeng-badge.mjs"}, "./base": {"types": "./base/index.d.ts", "default": "./fesm2022/primeng-base.mjs"}, "./basecomponent": {"types": "./basecomponent/index.d.ts", "default": "./fesm2022/primeng-basecomponent.mjs"}, "./blockui": {"types": "./blockui/index.d.ts", "default": "./fesm2022/primeng-blockui.mjs"}, "./breadcrumb": {"types": "./breadcrumb/index.d.ts", "default": "./fesm2022/primeng-breadcrumb.mjs"}, "./button": {"types": "./button/index.d.ts", "default": "./fesm2022/primeng-button.mjs"}, "./buttongroup": {"types": "./buttongroup/index.d.ts", "default": "./fesm2022/primeng-buttongroup.mjs"}, "./calendar": {"types": "./calendar/index.d.ts", "default": "./fesm2022/primeng-calendar.mjs"}, "./card": {"types": "./card/index.d.ts", "default": "./fesm2022/primeng-card.mjs"}, "./carousel": {"types": "./carousel/index.d.ts", "default": "./fesm2022/primeng-carousel.mjs"}, "./cascadeselect": {"types": "./cascadeselect/index.d.ts", "default": "./fesm2022/primeng-cascadeselect.mjs"}, "./chart": {"types": "./chart/index.d.ts", "default": "./fesm2022/primeng-chart.mjs"}, "./checkbox": {"types": "./checkbox/index.d.ts", "default": "./fesm2022/primeng-checkbox.mjs"}, "./chip": {"types": "./chip/index.d.ts", "default": "./fesm2022/primeng-chip.mjs"}, "./colorpicker": {"types": "./colorpicker/index.d.ts", "default": "./fesm2022/primeng-colorpicker.mjs"}, "./chips": {"types": "./chips/index.d.ts", "default": "./fesm2022/primeng-chips.mjs"}, "./config": {"types": "./config/index.d.ts", "default": "./fesm2022/primeng-config.mjs"}, "./confirmdialog": {"types": "./confirmdialog/index.d.ts", "default": "./fesm2022/primeng-confirmdialog.mjs"}, "./confirmpopup": {"types": "./confirmpopup/index.d.ts", "default": "./fesm2022/primeng-confirmpopup.mjs"}, "./contextmenu": {"types": "./contextmenu/index.d.ts", "default": "./fesm2022/primeng-contextmenu.mjs"}, "./dataview": {"types": "./dataview/index.d.ts", "default": "./fesm2022/primeng-dataview.mjs"}, "./datepicker": {"types": "./datepicker/index.d.ts", "default": "./fesm2022/primeng-datepicker.mjs"}, "./defer": {"types": "./defer/index.d.ts", "default": "./fesm2022/primeng-defer.mjs"}, "./dialog": {"types": "./dialog/index.d.ts", "default": "./fesm2022/primeng-dialog.mjs"}, "./divider": {"types": "./divider/index.d.ts", "default": "./fesm2022/primeng-divider.mjs"}, "./dock": {"types": "./dock/index.d.ts", "default": "./fesm2022/primeng-dock.mjs"}, "./dom": {"types": "./dom/index.d.ts", "default": "./fesm2022/primeng-dom.mjs"}, "./dragdrop": {"types": "./dragdrop/index.d.ts", "default": "./fesm2022/primeng-dragdrop.mjs"}, "./drawer": {"types": "./drawer/index.d.ts", "default": "./fesm2022/primeng-drawer.mjs"}, "./dropdown": {"types": "./dropdown/index.d.ts", "default": "./fesm2022/primeng-dropdown.mjs"}, "./dynamicdialog": {"types": "./dynamicdialog/index.d.ts", "default": "./fesm2022/primeng-dynamicdialog.mjs"}, "./editor": {"types": "./editor/index.d.ts", "default": "./fesm2022/primeng-editor.mjs"}, "./fileupload": {"types": "./fileupload/index.d.ts", "default": "./fesm2022/primeng-fileupload.mjs"}, "./fieldset": {"types": "./fieldset/index.d.ts", "default": "./fesm2022/primeng-fieldset.mjs"}, "./floatlabel": {"types": "./floatlabel/index.d.ts", "default": "./fesm2022/primeng-floatlabel.mjs"}, "./fluid": {"types": "./fluid/index.d.ts", "default": "./fesm2022/primeng-fluid.mjs"}, "./focustrap": {"types": "./focustrap/index.d.ts", "default": "./fesm2022/primeng-focustrap.mjs"}, "./galleria": {"types": "./galleria/index.d.ts", "default": "./fesm2022/primeng-galleria.mjs"}, "./iconfield": {"types": "./iconfield/index.d.ts", "default": "./fesm2022/primeng-iconfield.mjs"}, "./icons": {"types": "./icons/index.d.ts", "default": "./fesm2022/primeng-icons.mjs"}, "./iftalabel": {"types": "./iftalabel/index.d.ts", "default": "./fesm2022/primeng-iftalabel.mjs"}, "./imagecompare": {"types": "./imagecompare/index.d.ts", "default": "./fesm2022/primeng-imagecompare.mjs"}, "./image": {"types": "./image/index.d.ts", "default": "./fesm2022/primeng-image.mjs"}, "./inputgroup": {"types": "./inputgroup/index.d.ts", "default": "./fesm2022/primeng-inputgroup.mjs"}, "./inplace": {"types": "./inplace/index.d.ts", "default": "./fesm2022/primeng-inplace.mjs"}, "./inputgroupaddon": {"types": "./inputgroupaddon/index.d.ts", "default": "./fesm2022/primeng-inputgroupaddon.mjs"}, "./inputicon": {"types": "./inputicon/index.d.ts", "default": "./fesm2022/primeng-inputicon.mjs"}, "./inputmask": {"types": "./inputmask/index.d.ts", "default": "./fesm2022/primeng-inputmask.mjs"}, "./inputnumber": {"types": "./inputnumber/index.d.ts", "default": "./fesm2022/primeng-inputnumber.mjs"}, "./inputswitch": {"types": "./inputswitch/index.d.ts", "default": "./fesm2022/primeng-inputswitch.mjs"}, "./inputotp": {"types": "./inputotp/index.d.ts", "default": "./fesm2022/primeng-inputotp.mjs"}, "./inputtext": {"types": "./inputtext/index.d.ts", "default": "./fesm2022/primeng-inputtext.mjs"}, "./inputtextarea": {"types": "./inputtextarea/index.d.ts", "default": "./fesm2022/primeng-inputtextarea.mjs"}, "./keyfilter": {"types": "./keyfilter/index.d.ts", "default": "./fesm2022/primeng-keyfilter.mjs"}, "./knob": {"types": "./knob/index.d.ts", "default": "./fesm2022/primeng-knob.mjs"}, "./listbox": {"types": "./listbox/index.d.ts", "default": "./fesm2022/primeng-listbox.mjs"}, "./megamenu": {"types": "./megamenu/index.d.ts", "default": "./fesm2022/primeng-megamenu.mjs"}, "./menu": {"types": "./menu/index.d.ts", "default": "./fesm2022/primeng-menu.mjs"}, "./menubar": {"types": "./menubar/index.d.ts", "default": "./fesm2022/primeng-menubar.mjs"}, "./message": {"types": "./message/index.d.ts", "default": "./fesm2022/primeng-message.mjs"}, "./messages": {"types": "./messages/index.d.ts", "default": "./fesm2022/primeng-messages.mjs"}, "./metergroup": {"types": "./metergroup/index.d.ts", "default": "./fesm2022/primeng-metergroup.mjs"}, "./multiselect": {"types": "./multiselect/index.d.ts", "default": "./fesm2022/primeng-multiselect.mjs"}, "./orderlist": {"types": "./orderlist/index.d.ts", "default": "./fesm2022/primeng-orderlist.mjs"}, "./overlay": {"types": "./overlay/index.d.ts", "default": "./fesm2022/primeng-overlay.mjs"}, "./organizationchart": {"types": "./organizationchart/index.d.ts", "default": "./fesm2022/primeng-organizationchart.mjs"}, "./overlaybadge": {"types": "./overlaybadge/index.d.ts", "default": "./fesm2022/primeng-overlaybadge.mjs"}, "./overlaypanel": {"types": "./overlaypanel/index.d.ts", "default": "./fesm2022/primeng-overlaypanel.mjs"}, "./paginator": {"types": "./paginator/index.d.ts", "default": "./fesm2022/primeng-paginator.mjs"}, "./panel": {"types": "./panel/index.d.ts", "default": "./fesm2022/primeng-panel.mjs"}, "./panelmenu": {"types": "./panelmenu/index.d.ts", "default": "./fesm2022/primeng-panelmenu.mjs"}, "./password": {"types": "./password/index.d.ts", "default": "./fesm2022/primeng-password.mjs"}, "./popover": {"types": "./popover/index.d.ts", "default": "./fesm2022/primeng-popover.mjs"}, "./picklist": {"types": "./picklist/index.d.ts", "default": "./fesm2022/primeng-picklist.mjs"}, "./progressbar": {"types": "./progressbar/index.d.ts", "default": "./fesm2022/primeng-progressbar.mjs"}, "./progressspinner": {"types": "./progressspinner/index.d.ts", "default": "./fesm2022/primeng-progressspinner.mjs"}, "./radiobutton": {"types": "./radiobutton/index.d.ts", "default": "./fesm2022/primeng-radiobutton.mjs"}, "./rating": {"types": "./rating/index.d.ts", "default": "./fesm2022/primeng-rating.mjs"}, "./ripple": {"types": "./ripple/index.d.ts", "default": "./fesm2022/primeng-ripple.mjs"}, "./scroller": {"types": "./scroller/index.d.ts", "default": "./fesm2022/primeng-scroller.mjs"}, "./scrollpanel": {"types": "./scrollpanel/index.d.ts", "default": "./fesm2022/primeng-scrollpanel.mjs"}, "./scrolltop": {"types": "./scrolltop/index.d.ts", "default": "./fesm2022/primeng-scrolltop.mjs"}, "./select": {"types": "./select/index.d.ts", "default": "./fesm2022/primeng-select.mjs"}, "./selectbutton": {"types": "./selectbutton/index.d.ts", "default": "./fesm2022/primeng-selectbutton.mjs"}, "./sidebar": {"types": "./sidebar/index.d.ts", "default": "./fesm2022/primeng-sidebar.mjs"}, "./skeleton": {"types": "./skeleton/index.d.ts", "default": "./fesm2022/primeng-skeleton.mjs"}, "./slider": {"types": "./slider/index.d.ts", "default": "./fesm2022/primeng-slider.mjs"}, "./speeddial": {"types": "./speeddial/index.d.ts", "default": "./fesm2022/primeng-speeddial.mjs"}, "./splitbutton": {"types": "./splitbutton/index.d.ts", "default": "./fesm2022/primeng-splitbutton.mjs"}, "./splitter": {"types": "./splitter/index.d.ts", "default": "./fesm2022/primeng-splitter.mjs"}, "./stepper": {"types": "./stepper/index.d.ts", "default": "./fesm2022/primeng-stepper.mjs"}, "./steps": {"types": "./steps/index.d.ts", "default": "./fesm2022/primeng-steps.mjs"}, "./styleclass": {"types": "./styleclass/index.d.ts", "default": "./fesm2022/primeng-styleclass.mjs"}, "./table": {"types": "./table/index.d.ts", "default": "./fesm2022/primeng-table.mjs"}, "./tabmenu": {"types": "./tabmenu/index.d.ts", "default": "./fesm2022/primeng-tabmenu.mjs"}, "./tabs": {"types": "./tabs/index.d.ts", "default": "./fesm2022/primeng-tabs.mjs"}, "./tabview": {"types": "./tabview/index.d.ts", "default": "./fesm2022/primeng-tabview.mjs"}, "./tag": {"types": "./tag/index.d.ts", "default": "./fesm2022/primeng-tag.mjs"}, "./terminal": {"types": "./terminal/index.d.ts", "default": "./fesm2022/primeng-terminal.mjs"}, "./textarea": {"types": "./textarea/index.d.ts", "default": "./fesm2022/primeng-textarea.mjs"}, "./tieredmenu": {"types": "./tieredmenu/index.d.ts", "default": "./fesm2022/primeng-tieredmenu.mjs"}, "./timeline": {"types": "./timeline/index.d.ts", "default": "./fesm2022/primeng-timeline.mjs"}, "./toast": {"types": "./toast/index.d.ts", "default": "./fesm2022/primeng-toast.mjs"}, "./togglebutton": {"types": "./togglebutton/index.d.ts", "default": "./fesm2022/primeng-togglebutton.mjs"}, "./toggleswitch": {"types": "./toggleswitch/index.d.ts", "default": "./fesm2022/primeng-toggleswitch.mjs"}, "./toolbar": {"types": "./toolbar/index.d.ts", "default": "./fesm2022/primeng-toolbar.mjs"}, "./tooltip": {"types": "./tooltip/index.d.ts", "default": "./fesm2022/primeng-tooltip.mjs"}, "./tree": {"types": "./tree/index.d.ts", "default": "./fesm2022/primeng-tree.mjs"}, "./treeselect": {"types": "./treeselect/index.d.ts", "default": "./fesm2022/primeng-treeselect.mjs"}, "./usestyle": {"types": "./usestyle/index.d.ts", "default": "./fesm2022/primeng-usestyle.mjs"}, "./ts-helpers": {"types": "./ts-helpers/index.d.ts", "default": "./fesm2022/primeng-ts-helpers.mjs"}, "./utils": {"types": "./utils/index.d.ts", "default": "./fesm2022/primeng-utils.mjs"}, "./treetable": {"types": "./treetable/index.d.ts", "default": "./fesm2022/primeng-treetable.mjs"}, "./icons/angledoubledown": {"types": "./icons/angledoubledown/index.d.ts", "default": "./fesm2022/primeng-icons-angledoubledown.mjs"}, "./icons/angledoubleleft": {"types": "./icons/angledoubleleft/index.d.ts", "default": "./fesm2022/primeng-icons-angledoubleleft.mjs"}, "./icons/angledoubleright": {"types": "./icons/angledoubleright/index.d.ts", "default": "./fesm2022/primeng-icons-angledoubleright.mjs"}, "./icons/angledoubleup": {"types": "./icons/angledoubleup/index.d.ts", "default": "./fesm2022/primeng-icons-angledoubleup.mjs"}, "./icons/angledown": {"types": "./icons/angledown/index.d.ts", "default": "./fesm2022/primeng-icons-angledown.mjs"}, "./icons/angleleft": {"types": "./icons/angleleft/index.d.ts", "default": "./fesm2022/primeng-icons-angleleft.mjs"}, "./icons/angleup": {"types": "./icons/angleup/index.d.ts", "default": "./fesm2022/primeng-icons-angleup.mjs"}, "./icons/arrowdownleft": {"types": "./icons/arrowdownleft/index.d.ts", "default": "./fesm2022/primeng-icons-arrowdownleft.mjs"}, "./icons/arrowdownright": {"types": "./icons/arrowdownright/index.d.ts", "default": "./fesm2022/primeng-icons-arrowdownright.mjs"}, "./icons/angleright": {"types": "./icons/angleright/index.d.ts", "default": "./fesm2022/primeng-icons-angleright.mjs"}, "./icons/arrowdown": {"types": "./icons/arrowdown/index.d.ts", "default": "./fesm2022/primeng-icons-arrowdown.mjs"}, "./icons/arrowright": {"types": "./icons/arrowright/index.d.ts", "default": "./fesm2022/primeng-icons-arrowright.mjs"}, "./icons/arrowup": {"types": "./icons/arrowup/index.d.ts", "default": "./fesm2022/primeng-icons-arrowup.mjs"}, "./icons/arrowleft": {"types": "./icons/arrowleft/index.d.ts", "default": "./fesm2022/primeng-icons-arrowleft.mjs"}, "./icons/bars": {"types": "./icons/bars/index.d.ts", "default": "./fesm2022/primeng-icons-bars.mjs"}, "./icons/baseicon": {"types": "./icons/baseicon/index.d.ts", "default": "./fesm2022/primeng-icons-baseicon.mjs"}, "./icons/blank": {"types": "./icons/blank/index.d.ts", "default": "./fesm2022/primeng-icons-blank.mjs"}, "./icons/ban": {"types": "./icons/ban/index.d.ts", "default": "./fesm2022/primeng-icons-ban.mjs"}, "./icons/calendar": {"types": "./icons/calendar/index.d.ts", "default": "./fesm2022/primeng-icons-calendar.mjs"}, "./icons/caretleft": {"types": "./icons/caretleft/index.d.ts", "default": "./fesm2022/primeng-icons-caretleft.mjs"}, "./icons/caretright": {"types": "./icons/caretright/index.d.ts", "default": "./fesm2022/primeng-icons-caretright.mjs"}, "./icons/check": {"types": "./icons/check/index.d.ts", "default": "./fesm2022/primeng-icons-check.mjs"}, "./icons/chevrondown": {"types": "./icons/chevrondown/index.d.ts", "default": "./fesm2022/primeng-icons-chevrondown.mjs"}, "./icons/chevronright": {"types": "./icons/chevronright/index.d.ts", "default": "./fesm2022/primeng-icons-chevronright.mjs"}, "./icons/chevronleft": {"types": "./icons/chevronleft/index.d.ts", "default": "./fesm2022/primeng-icons-chevronleft.mjs"}, "./icons/exclamationtriangle": {"types": "./icons/exclamationtriangle/index.d.ts", "default": "./fesm2022/primeng-icons-exclamationtriangle.mjs"}, "./icons/chevronup": {"types": "./icons/chevronup/index.d.ts", "default": "./fesm2022/primeng-icons-chevronup.mjs"}, "./icons/eyeslash": {"types": "./icons/eyeslash/index.d.ts", "default": "./fesm2022/primeng-icons-eyeslash.mjs"}, "./icons/eye": {"types": "./icons/eye/index.d.ts", "default": "./fesm2022/primeng-icons-eye.mjs"}, "./icons/home": {"types": "./icons/home/<USER>", "default": "./fesm2022/primeng-icons-home.mjs"}, "./icons/infocircle": {"types": "./icons/infocircle/index.d.ts", "default": "./fesm2022/primeng-icons-infocircle.mjs"}, "./icons/filterslash": {"types": "./icons/filterslash/index.d.ts", "default": "./fesm2022/primeng-icons-filterslash.mjs"}, "./icons/filter": {"types": "./icons/filter/index.d.ts", "default": "./fesm2022/primeng-icons-filter.mjs"}, "./icons/minus": {"types": "./icons/minus/index.d.ts", "default": "./fesm2022/primeng-icons-minus.mjs"}, "./icons/pencil": {"types": "./icons/pencil/index.d.ts", "default": "./fesm2022/primeng-icons-pencil.mjs"}, "./icons/search": {"types": "./icons/search/index.d.ts", "default": "./fesm2022/primeng-icons-search.mjs"}, "./icons/refresh": {"types": "./icons/refresh/index.d.ts", "default": "./fesm2022/primeng-icons-refresh.mjs"}, "./icons/searchminus": {"types": "./icons/searchminus/index.d.ts", "default": "./fesm2022/primeng-icons-searchminus.mjs"}, "./icons/sortalt": {"types": "./icons/sortalt/index.d.ts", "default": "./fesm2022/primeng-icons-sortalt.mjs"}, "./icons/plus": {"types": "./icons/plus/index.d.ts", "default": "./fesm2022/primeng-icons-plus.mjs"}, "./icons/searchplus": {"types": "./icons/searchplus/index.d.ts", "default": "./fesm2022/primeng-icons-searchplus.mjs"}, "./icons/sortamountdown": {"types": "./icons/sortamountdown/index.d.ts", "default": "./fesm2022/primeng-icons-sortamountdown.mjs"}, "./icons/spinner": {"types": "./icons/spinner/index.d.ts", "default": "./fesm2022/primeng-icons-spinner.mjs"}, "./icons/sortamountupalt": {"types": "./icons/sortamountupalt/index.d.ts", "default": "./fesm2022/primeng-icons-sortamountupalt.mjs"}, "./icons/starfill": {"types": "./icons/starfill/index.d.ts", "default": "./fesm2022/primeng-icons-starfill.mjs"}, "./icons/thlarge": {"types": "./icons/thlarge/index.d.ts", "default": "./fesm2022/primeng-icons-thlarge.mjs"}, "./icons/times": {"types": "./icons/times/index.d.ts", "default": "./fesm2022/primeng-icons-times.mjs"}, "./icons/timescircle": {"types": "./icons/timescircle/index.d.ts", "default": "./fesm2022/primeng-icons-timescircle.mjs"}, "./icons/trash": {"types": "./icons/trash/index.d.ts", "default": "./fesm2022/primeng-icons-trash.mjs"}, "./icons/star": {"types": "./icons/star/index.d.ts", "default": "./fesm2022/primeng-icons-star.mjs"}, "./icons/undo": {"types": "./icons/undo/index.d.ts", "default": "./fesm2022/primeng-icons-undo.mjs"}, "./icons/upload": {"types": "./icons/upload/index.d.ts", "default": "./fesm2022/primeng-icons-upload.mjs"}, "./icons/windowminimize": {"types": "./icons/windowminimize/index.d.ts", "default": "./fesm2022/primeng-icons-windowminimize.mjs"}, "./icons/windowmaximize": {"types": "./icons/windowmaximize/index.d.ts", "default": "./fesm2022/primeng-icons-windowmaximize.mjs"}}, "sideEffects": false}