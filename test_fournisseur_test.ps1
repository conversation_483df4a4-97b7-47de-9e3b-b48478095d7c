# Test avec le fournisseur de test spécifique
$baseUrl = "http://localhost:5014/api"

Write-Host "=== TEST FOURNISSEUR DE TEST ===" -ForegroundColor Cyan

# Test de connexion avec le fournisseur de test
Write-Host "`n1. Test de <NAME_EMAIL>..." -ForegroundColor Yellow

# Essayer plusieurs mots de passe possibles pour ce fournisseur
$motsDePasse = @("Test123!", "123456", "password", "Password123", "admin", "test", "Test123", "testfournisseur")

foreach ($mdp in $motsDePasse) {
    Write-Host "`nTest avec mot de passe: $mdp" -ForegroundColor Cyan
    
    $loginData = @{
        email = "<EMAIL>"
        motDePasse = $mdp
    } | ConvertTo-Json
    
    $headers = @{"Content-Type" = "application/json"}
    
    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
        Write-Host "✅ CONNEXION RÉUSSIE avec le mot de passe: $mdp" -ForegroundColor Green
        Write-Host "Token reçu: $($loginResponse.token.Substring(0, 50))..." -ForegroundColor White
        Write-Host "Utilisateur: $($loginResponse.utilisateur.nom) $($loginResponse.utilisateur.prenom)" -ForegroundColor White
        Write-Host "Role: $($loginResponse.utilisateur.role)" -ForegroundColor White
        Write-Host "ID Fournisseur: $($loginResponse.utilisateur.id)" -ForegroundColor White
        
        # Test de l'endpoint my-promotions avec ce token
        Write-Host "`n2. Test de l'endpoint my-promotions..." -ForegroundColor Yellow
        $headers["Authorization"] = "Bearer $($loginResponse.token)"
        
        try {
            $myPromotions = Invoke-RestMethod -Uri "$baseUrl/promotions/my-promotions" -Method Get -Headers $headers
            Write-Host "✅ My-promotions récupérées: $($myPromotions.Count)" -ForegroundColor Green
            
            if ($myPromotions.Count -gt 0) {
                foreach ($promo in $myPromotions) {
                    $status = if ($promo.estValide) { "✅ Active" } else { "❌ Inactive" }
                    Write-Host "  - $($promo.nomAffichage) ($($promo.type)) - $($promo.pourcentageRemise)% - $status" -ForegroundColor White
                    if ($promo.codePromo) {
                        Write-Host "    Code: $($promo.codePromo)" -ForegroundColor Gray
                    }
                }
            } else {
                Write-Host "Aucune promotion trouvée pour ce fournisseur" -ForegroundColor Yellow
                
                # Créer une promotion de test
                Write-Host "`n3. Création d'une promotion de test..." -ForegroundColor Yellow
                
                $promoData = @{
                    type = "CodePromo"
                    pourcentageRemise = 15
                    dateDebut = (Get-Date).ToString("yyyy-MM-dd")
                    dateFin = (Get-Date).AddDays(30).ToString("yyyy-MM-dd")
                    codePromo = "TESTFOURNISSEUR15"
                    nomAffichage = "Promotion Test Fournisseur"
                    description = "Promotion de test créée pour le fournisseur de test"
                    appliquerSurHT = $false
                } | ConvertTo-Json
                
                try {
                    $newPromo = Invoke-RestMethod -Uri "$baseUrl/promotions" -Method Post -Headers $headers -Body $promoData
                    Write-Host "✅ Promotion créée !" -ForegroundColor Green
                    Write-Host "ID: $($newPromo.id) - Nom: $($newPromo.nomAffichage)" -ForegroundColor White
                    Write-Host "Code: $($newPromo.codePromo)" -ForegroundColor White
                    
                    # Vérifier à nouveau my-promotions
                    Write-Host "`n4. Vérification finale my-promotions..." -ForegroundColor Yellow
                    $finalPromotions = Invoke-RestMethod -Uri "$baseUrl/promotions/my-promotions" -Method Get -Headers $headers
                    Write-Host "✅ Promotions finales: $($finalPromotions.Count)" -ForegroundColor Green
                    
                    foreach ($promo in $finalPromotions) {
                        $status = if ($promo.estValide) { "✅ Active" } else { "❌ Inactive" }
                        Write-Host "  - $($promo.nomAffichage) ($($promo.type)) - $($promo.pourcentageRemise)% - $status" -ForegroundColor White
                        if ($promo.codePromo) {
                            Write-Host "    Code: $($promo.codePromo)" -ForegroundColor Gray
                        }
                    }
                    
                } catch {
                    Write-Host "❌ Erreur création promotion: $($_.Exception.Message)" -ForegroundColor Red
                    Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
                }
            }
            
        } catch {
            Write-Host "❌ Erreur my-promotions: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
            Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
        }
        
        break # Sortir de la boucle si connexion réussie
        
    } catch {
        if ($_.Exception.Response.StatusCode -eq 401) {
            Write-Host "❌ Mot de passe incorrect" -ForegroundColor Red
        } else {
            Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
    }
}

Write-Host "`n=== FIN DU TEST ===" -ForegroundColor Cyan
