import { Component, inject } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { catchError, finalize, of, tap } from 'rxjs';
import { AuthService } from '../auth.service';
import { matchValidator } from '../auth.validators';
import { RegisterDto } from 'src/app/shared/models/register.dto';
import { ClientDto } from 'src/app/models/ClientDto';

@Component({
  selector: 'app-register',
  standalone: false,
  templateUrl: './register.component.html',
  styleUrl: './register.component.scss',
})
export class RegisterComponent {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private snackBar = inject(MatSnackBar);
  private router = inject(Router);
  minDate = new Date(1900, 0, 1);
  maxDate = new Date();
  maxBirthDate = new Date(new Date().getFullYear() - 13, 11, 31);

  registerForm = this.fb.group(
    {
      nom: ['', [Validators.required]],
      prenom: ['', [Validators.required]],
      username: ['', [Validators.required, Validators.minLength(3)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: ['', [Validators.required]],
      dateNaissance: [null as Date | null, [Validators.required]],
      motDePasse: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
    },
    {
      validators: matchValidator('motDePasse', 'confirmPassword'),
    }
  );

  isLoading = false;

  onSubmit() {
    console.log('🚀 Début de l\'inscription');
    console.log('📝 Formulaire valide:', this.registerForm.valid);

    if (this.registerForm.invalid) {
      console.log('❌ Formulaire invalide, arrêt');
      Object.keys(this.registerForm.controls).forEach(key => {
        const control = this.registerForm.get(key);
        if (control && control.invalid) {
          console.log(`❌ Champ ${key} invalide:`, control.errors);
        }
      });
      return;
    }

    this.isLoading = true;
    const { confirmPassword, ...userData } = this.registerForm.value;

    console.log('📊 Données du formulaire:', userData);

    const registerDto: RegisterDto = {
      nom: userData.nom!,
      prenom: userData.prenom!,
      email: userData.email!,
      phoneNumber: userData.phoneNumber!,
      dateNaissance: new Date(userData.dateNaissance!),
      password: userData.motDePasse!,
      estActif: true,
    };

    console.log('📤 DTO à envoyer:', registerDto);
    this.authService
      .register(registerDto)
      .pipe(
        tap((client) => {
          console.log('✅ Inscription réussie:', client);
          this.snackBar.open(
            'Inscription réussie ! Bienvenue !',
            'Fermer',
            { duration: 5000, panelClass: ['success-snackbar'] }
          );
          this.router.navigate(['/user/profile']);
        }),
        catchError((error) => {
          console.error('❌ Erreur inscription complète:', error);
          console.error('❌ Status:', error.status);
          console.error('❌ Message:', error.message);
          console.error('❌ Error body:', error.error);
          console.error('❌ Error headers:', error.headers);
          console.error('❌ Error URL:', error.url);

          // Essayons de parser le body s'il existe
          if (error.error) {
            try {
              console.error('❌ Error body parsed:', JSON.stringify(error.error, null, 2));
            } catch (e) {
              console.error('❌ Error body (raw):', error.error);
            }
          }

          let errorMessage = 'Erreur lors de l\'inscription. Veuillez réessayer.';

          if (error.status === 400) {
            errorMessage = 'Données invalides. Vérifiez vos informations.';
          } else if (error.status === 409) {
            errorMessage = 'Un compte avec cet email existe déjà.';
          } else if (error.status === 0) {
            errorMessage = 'Impossible de contacter le serveur.';
          }

          this.snackBar.open(errorMessage, 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar'],
          });
          return of(null);
        }),
        finalize(() => (this.isLoading = false)) 
      )
      .subscribe();
  }
}
