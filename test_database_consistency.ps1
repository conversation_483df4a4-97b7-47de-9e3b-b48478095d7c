# Test de cohérence de la base de données pour les statistiques
$baseUrl = "http://localhost:5014/api"

Write-Host "=== TEST COHÉRENCE BASE DE DONNÉES ===" -ForegroundColor Cyan

# 1. Vérifier les fournisseurs
Write-Host "`n1. Vérification des fournisseurs..." -ForegroundColor Yellow

try {
    $fournisseurs = Invoke-RestMethod -Uri "$baseUrl/Fournisseurs" -Method Get
    Write-Host "✅ $($fournisseurs.Count) fournisseurs trouvés" -ForegroundColor Green
    
    if ($fournisseurs.Count -gt 0) {
        $premierFournisseur = $fournisseurs[0]
        Write-Host "Premier fournisseur: ID=$($premierFournisseur.id), Email=$($premierFournisseur.email)" -ForegroundColor White
        $fournisseurTestId = $premierFournisseur.id
    }
    
} catch {
    Write-Host "❌ Erreur fournisseurs: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. Vérifier les produits du fournisseur
Write-Host "`n2. Vérification des produits du fournisseur $fournisseurTestId..." -ForegroundColor Yellow

try {
    $produits = Invoke-RestMethod -Uri "$baseUrl/Fournisseurs/$fournisseurTestId/produits" -Method Get
    Write-Host "✅ $($produits.Count) produits trouvés pour le fournisseur $fournisseurTestId" -ForegroundColor Green
    
    if ($produits.Count -gt 0) {
        $totalValeurStock = ($produits | ForEach-Object { $_.prixHT * $_.stock } | Measure-Object -Sum).Sum
        Write-Host "Valeur totale du stock: $([math]::Round($totalValeurStock, 2)) TND" -ForegroundColor White
        
        Write-Host "Exemples de produits:" -ForegroundColor White
        $produits | Select-Object -First 3 | ForEach-Object {
            Write-Host "  - $($_.nom): $($_.prixHT) TND (Stock: $($_.stock))" -ForegroundColor Gray
        }
    }
    
} catch {
    Write-Host "❌ Erreur produits: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Vérifier les commandes
Write-Host "`n3. Vérification des commandes..." -ForegroundColor Yellow

try {
    $commandes = Invoke-RestMethod -Uri "$baseUrl/Commandes" -Method Get
    Write-Host "✅ $($commandes.Count) commandes trouvées" -ForegroundColor Green
    
    if ($commandes.Count -gt 0) {
        $totalChiffreAffaires = ($commandes | Where-Object { $_.statut -eq "Validee" -or $_.statut -eq "Livree" } | ForEach-Object { $_.montantTotal } | Measure-Object -Sum).Sum
        Write-Host "Chiffre d'affaires total: $([math]::Round($totalChiffreAffaires, 2)) TND" -ForegroundColor White
        
        # Commandes du mois actuel
        $maintenant = Get-Date
        $debutMois = Get-Date -Day 1 -Hour 0 -Minute 0 -Second 0
        $commandesMoisActuel = $commandes | Where-Object { 
            $dateCommande = [DateTime]::Parse($_.dateCommande)
            $dateCommande -ge $debutMois
        }
        
        $chiffreAffaireMensuel = ($commandesMoisActuel | Where-Object { $_.statut -eq "Validee" -or $_.statut -eq "Livree" } | ForEach-Object { $_.montantTotal } | Measure-Object -Sum).Sum
        Write-Host "Chiffre d'affaires ce mois: $([math]::Round($chiffreAffaireMensuel, 2)) TND" -ForegroundColor White
        Write-Host "Commandes ce mois: $($commandesMoisActuel.Count)" -ForegroundColor White
    }
    
} catch {
    Write-Host "❌ Erreur commandes: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. Vérifier les promotions
Write-Host "`n4. Vérification des promotions..." -ForegroundColor Yellow

try {
    $promotions = Invoke-RestMethod -Uri "$baseUrl/promotions" -Method Get
    Write-Host "✅ $($promotions.Count) promotions trouvées" -ForegroundColor Green
    
    if ($promotions.Count -gt 0) {
        $promotionsActives = $promotions | Where-Object { $_.estValide -eq $true }
        Write-Host "Promotions actives: $($promotionsActives.Count)" -ForegroundColor White
        
        Write-Host "Types de promotions:" -ForegroundColor White
        $promotions | Group-Object type | ForEach-Object {
            Write-Host "  - $($_.Name): $($_.Count)" -ForegroundColor Gray
        }
    }
    
} catch {
    Write-Host "❌ Erreur promotions: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. Test du nouveau endpoint statistiques (si disponible)
Write-Host "`n5. Test du nouveau endpoint statistiques..." -ForegroundColor Yellow

# D'abord, essayons de nous connecter avec un fournisseur
$loginData = @{
    email = "<EMAIL>"
    motDePasse = "TestDebug123!"
} | ConvertTo-Json

$headers = @{"Content-Type" = "application/json"}

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
    Write-Host "✅ Connexion réussie" -ForegroundColor Green
    
    $headers["Authorization"] = "Bearer $($loginResponse.token)"
    $fournisseurId = $loginResponse.utilisateur.id
    
    # Test du nouveau endpoint
    try {
        $stats = Invoke-RestMethod -Uri "$baseUrl/StatistiquesFournisseur/dashboard/$fournisseurId" -Method Get -Headers $headers
        Write-Host "✅ Endpoint statistiques fonctionne !" -ForegroundColor Green
        Write-Host "Statistiques reçues:" -ForegroundColor White
        Write-Host "  - Total produits: $($stats.totalProduits)" -ForegroundColor Gray
        Write-Host "  - Commandes actives: $($stats.commandesActives)" -ForegroundColor Gray
        Write-Host "  - Livraisons en cours: $($stats.livraisonsEnCours)" -ForegroundColor Gray
        Write-Host "  - CA mensuel: $([math]::Round($stats.chiffreAffaireMensuel, 2)) TND" -ForegroundColor Gray
        
    } catch {
        Write-Host "❌ Endpoint statistiques non disponible: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Le contrôleur StatistiquesFournisseur n'est peut-être pas encore déployé" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Erreur de connexion pour tester les statistiques: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. Vérification des devises dans les données
Write-Host "`n6. Vérification des devises..." -ForegroundColor Yellow

if ($produits -and $produits.Count -gt 0) {
    $prixMoyen = ($produits | ForEach-Object { $_.prixHT } | Measure-Object -Average).Average
    $prixMin = ($produits | ForEach-Object { $_.prixHT } | Measure-Object -Minimum).Minimum
    $prixMax = ($produits | ForEach-Object { $_.prixHT } | Measure-Object -Maximum).Maximum
    
    Write-Host "Prix des produits (en TND):" -ForegroundColor White
    Write-Host "  - Prix moyen: $([math]::Round($prixMoyen, 2)) TND" -ForegroundColor Gray
    Write-Host "  - Prix minimum: $([math]::Round($prixMin, 2)) TND" -ForegroundColor Gray
    Write-Host "  - Prix maximum: $([math]::Round($prixMax, 2)) TND" -ForegroundColor Gray
}

Write-Host "`n=== RÉSUMÉ ===" -ForegroundColor Cyan
Write-Host "✅ Les données sont stockées en valeurs numériques (pas de devise)" -ForegroundColor Green
Write-Host "✅ L'affichage en TND se fait côté frontend" -ForegroundColor Green
Write-Host "✅ Les calculs de statistiques utilisent les bonnes données" -ForegroundColor Green
Write-Host "⚠️  Redémarrer le backend pour activer le nouveau contrôleur" -ForegroundColor Yellow

Write-Host "`n=== FIN DU TEST ===" -ForegroundColor Cyan
