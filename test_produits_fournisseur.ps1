# Test des produits pour fournisseur spécifique
$baseUrl = "http://localhost:5014/api"

Write-Host "=== TEST PRODUITS FOURNISSEUR ===" -ForegroundColor Cyan

# 1. Créer un fournisseur de test
Write-Host "`n1. Création d'un fournisseur de test..." -ForegroundColor Yellow

$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$email = "testproduits$<EMAIL>"
$password = "TestProduits123!"

$fournisseurData = @{
    nom = "Test"
    prenom = "Produits"
    email = $email
    password = $password
    phoneNumber = "12345678"
    raisonSociale = "Test Produits SARL"
    matriculeFiscale = "PROD$timestamp"
    rib = "12345678901234567890"
    codeBanque = "12345"
    commission = 5.0
    delaiPreparationJours = 2
    fraisLivraisonBase = 10.0
    description = "Fournisseur de test pour produits"
} | ConvertTo-Json

$headers = @{"Content-Type" = "application/json"}

try {
    $newFournisseur = Invoke-RestMethod -Uri "$baseUrl/Auth/register/fournisseur" -Method Post -Headers $headers -Body $fournisseurData
    Write-Host "✅ Fournisseur créé - ID: $($newFournisseur.id)" -ForegroundColor Green
    $fournisseurId = $newFournisseur.id
} catch {
    Write-Host "❌ Erreur création fournisseur, utilisation d'un existant" -ForegroundColor Yellow
    $fournisseurId = 11
    $email = "<EMAIL>"
    $password = "123456"
}

# 2. Connexion
Write-Host "`n2. Connexion..." -ForegroundColor Yellow

$loginData = @{
    email = $email
    motDePasse = $password
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
    Write-Host "✅ Connecté - ID: $($loginResponse.utilisateur.id)" -ForegroundColor Green
    
    $authHeaders = @{
        "Content-Type" = "application/json"
        "Authorization" = "Bearer $($loginResponse.token)"
    }
    $fournisseurId = $loginResponse.utilisateur.id
    
} catch {
    Write-Host "❌ Erreur connexion: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. Créer quelques produits de test
Write-Host "`n3. Création de produits de test..." -ForegroundColor Yellow

$produits = @(
    @{
        nom = "Lunettes Test 1"
        description = "Produit de test 1"
        prixVenteHT = 150.00
        stock = 20
        fournisseurId = $fournisseurId
        categorieId = 1
        marqueId = 1
        formeId = 1
        reference = "TEST001"
    },
    @{
        nom = "Lunettes Test 2"
        description = "Produit de test 2"
        prixVenteHT = 200.00
        stock = 15
        fournisseurId = $fournisseurId
        categorieId = 1
        marqueId = 1
        formeId = 1
        reference = "TEST002"
    }
)

foreach ($produit in $produits) {
    try {
        $produitJson = $produit | ConvertTo-Json
        $newProduit = Invoke-RestMethod -Uri "$baseUrl/Produits" -Method Post -Headers $authHeaders -Body $produitJson
        Write-Host "✅ Produit créé: $($produit.nom)" -ForegroundColor Green
    } catch {
        Write-Host "❌ Erreur création produit: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 4. Test des endpoints produits
Write-Host "`n4. Test des endpoints produits..." -ForegroundColor Yellow

# Test endpoint 1: /api/Produits/by-fournisseur/{id}
Write-Host "Test endpoint 1: /api/Produits/by-fournisseur/$fournisseurId" -ForegroundColor Gray
try {
    $produits1 = Invoke-RestMethod -Uri "$baseUrl/Produits/by-fournisseur/$fournisseurId" -Method Get -Headers $authHeaders
    Write-Host "✅ Endpoint 1 OK: $($produits1.Count) produits" -ForegroundColor Green
    
    if ($produits1.Count -gt 0) {
        Write-Host "Produits trouvés:" -ForegroundColor White
        foreach ($p in $produits1) {
            Write-Host "  - $($p.nom): $($p.prixVenteHT) TND" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ Endpoint 1 erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# Test endpoint 2: /api/Fournisseurs/{id}/produits
Write-Host "`nTest endpoint 2: /api/Fournisseurs/$fournisseurId/produits" -ForegroundColor Gray
try {
    $produits2 = Invoke-RestMethod -Uri "$baseUrl/Fournisseurs/$fournisseurId/produits" -Method Get -Headers $authHeaders
    Write-Host "✅ Endpoint 2 OK: $($produits2.Count) produits" -ForegroundColor Green
    
    if ($produits2.Count -gt 0) {
        Write-Host "Produits trouvés:" -ForegroundColor White
        foreach ($p in $produits2) {
            Write-Host "  - $($p.nom): $($p.prixVenteHT) TND" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "❌ Endpoint 2 erreur: $($_.Exception.Message)" -ForegroundColor Red
}

# Test sans authentification
Write-Host "`n5. Test sans authentification..." -ForegroundColor Yellow
try {
    $produits3 = Invoke-RestMethod -Uri "$baseUrl/Produits/by-fournisseur/$fournisseurId" -Method Get
    Write-Host "✅ Sans auth OK: $($produits3.Count) produits" -ForegroundColor Green
} catch {
    Write-Host "❌ Sans auth erreur: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
}

Write-Host "`n=== RÉSUMÉ ===" -ForegroundColor Cyan
Write-Host "Fournisseur ID: $fournisseurId" -ForegroundColor White
Write-Host "Email: $email" -ForegroundColor White
Write-Host "Password: $password" -ForegroundColor White
Write-Host "`nUtilisez ces identifiants dans le frontend fournisseur" -ForegroundColor Green

Write-Host "`n=== FIN ===" -ForegroundColor Cyan
