﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.AuthDTO;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.Services.Implementations;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _AuthService;

        public AuthController(IAuthService AuthService)
        {
            _AuthService = AuthService;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginDto dto)
        {
            try
            {
                var response = await _AuthService.AuthentifierAsync(dto);
                return Ok(response);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new { Message = ex.Message });
            }
        }
        [HttpPost("register/client")]
        public async Task<IActionResult> RegisterClient([FromBody] ClientCreateDto dto)
        {
            try
            {
                var client = await _AuthService.CreerClientAsync(dto);
                var token = await _AuthService.GenerateJwtTokenAsync(dto.Email);

                return Ok(new {
                    User = client,
                    Token = token
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }
        [HttpPost("register/fournisseur")]
        public async Task<IActionResult> RegisterFournisseur([FromBody] FournisseurCreateDto dto)
        {
            try
            {
                var result = await _AuthService.CreerFournisseurAsync(dto);
                return CreatedAtAction(nameof(RegisterFournisseur), new { id = result.Id }, result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpPost("register/admin")]
        public async Task<IActionResult> RegisterAdmin([FromBody] AdminCreateDto dto)
        {
            try
            {
                var result = await _AuthService.CreerAdminAsync(dto);
                return CreatedAtAction(nameof(RegisterAdmin), new { id = result.Id }, result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }

        [HttpPost("init-admin")]
        public async Task<IActionResult> InitializeDefaultAdmin()
        {
            try
            {
                var result = await _AuthService.InitialiserAdminParDefautAsync();
                return Ok(new { Message = "Admin par défaut initialisé", Admin = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = ex.Message });
            }
        }
    }
}
