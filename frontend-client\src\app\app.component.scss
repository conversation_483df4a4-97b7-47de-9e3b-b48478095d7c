/* ===== COMPOSANT RACINE MODERNE ===== */
:host {
  display: block;
  height: 100vh;
  background-color: var(--background-color);
  color: var(--text-color);
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
  transition: background-color 0.5s ease, color 0.5s ease;
  overflow-x: hidden;
}

/* === CONTENU PRINCIPAL === */
.main-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 80px);
  position: relative;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

/* === TYPOGRAPHIE AMÉLIORÉE === */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-color);
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 1.5rem;

  &.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
}

h2 {
  font-size: 2rem;

  @media (max-width: 768px) {
    font-size: 1.75rem;
  }
}

h3 {
  font-size: 1.75rem;

  @media (max-width: 768px) {
    font-size: 1.5rem;
  }
}

/* === BOUTONS MODERNES === */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: 2px solid transparent;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }

  // Bouton primaire avec dégradé
  &.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-color);

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }

  // Bouton secondaire
  &.btn-secondary {
    background: transparent;
    color: var(--secondary-color);
    border-color: var(--secondary-color);

    &:hover:not(:disabled) {
      background: var(--secondary-color);
      color: white;
      transform: translateY(-1px);
    }
  }

  // Bouton succès
  &.btn-success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);

    &:hover:not(:disabled) {
      background: #219a52;
      transform: translateY(-1px);
      box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3);
    }
  }

  // Bouton danger
  &.btn-danger {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);

    &:hover:not(:disabled) {
      background: #c0392b;
      transform: translateY(-1px);
      box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
    }
  }

  // Bouton outline
  &.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);

    &:hover:not(:disabled) {
      background: var(--primary-color);
      color: white;
    }
  }

  // Tailles
  &.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }

  &.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
  }
}

/* === LIENS AMÉLIORÉS === */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    color: var(--primary-color-hover);

    &.link-underline::after {
      width: 100%;
    }
  }

  &.link-underline {
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 0;
      height: 2px;
      background: var(--primary-color);
      transition: width 0.3s ease;
    }
  }
}

/* === CARTES MODERNES === */
.card {
  background: var(--card-background-color);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  box-shadow: var(--card-shadow);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    background: var(--card-background-color-hover);
  }

  &.card-featured {
    border: 2px solid var(--primary-color);

    &::before {
      height: 6px;
      background: var(--gradient-primary);
    }
  }

  .card-header {
    margin: -1.5rem -1.5rem 1.5rem -1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, transparent 100%);
    border-bottom: 1px solid var(--border-color);

    h1, h2, h3, h4, h5, h6 {
      margin: 0;
      color: var(--text-color);
    }
  }

  .card-footer {
    margin: 1.5rem -1.5rem -1.5rem -1.5rem;
    padding: 1rem 1.5rem;
    background: rgba(52, 152, 219, 0.05);
    border-top: 1px solid var(--border-color);
  }
}

/* === FORMULAIRES === */
.form-group {
  margin-bottom: 1.5rem;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.875rem;
  }
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: 0.5rem;
  background: var(--card-background-color);
  color: var(--text-color);
  font-size: 1rem;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  }

  &:hover {
    border-color: var(--primary-color-hover);
  }

  &::placeholder {
    color: var(--secondary-color);
    opacity: 0.7;
  }
}

/* === ALERTES === */
.alert {
  padding: 1rem 1.25rem;
  border-radius: 0.5rem;
  border: 1px solid transparent;
  margin-bottom: 1rem;

  &.alert-success {
    background: rgba(39, 174, 96, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
  }

  &.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    border-color: var(--error-color);
    color: var(--error-color);
  }

  &.alert-warning {
    background: rgba(230, 126, 34, 0.1);
    border-color: var(--accent-color);
    color: var(--accent-color);
  }

  &.alert-info {
    background: rgba(52, 152, 219, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
  }
}

/* === BADGES === */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 50px;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.badge-primary {
    background: rgba(52, 152, 219, 0.2);
    color: var(--primary-color);
  }

  &.badge-success {
    background: rgba(39, 174, 96, 0.2);
    color: var(--success-color);
  }

  &.badge-danger {
    background: rgba(231, 76, 60, 0.2);
    color: var(--error-color);
  }

  &.badge-warning {
    background: rgba(230, 126, 34, 0.2);
    color: var(--accent-color);
  }
}

/* === ANIMATIONS === */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

/* === UTILITAIRES === */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }
.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

/* === RESPONSIVE === */
@media (max-width: 1200px) {
  .main-content {
    max-width: 100%;
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .card {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
}
  