﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Exceptions;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class ClientsController : ControllerBase
    {
        private readonly IClientService _service;

        public ClientsController(IClientService service)
        {
            _service = service;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<ClientDto>>> GetAll()
        {
            return Ok(await _service.GetAllAsync());
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ClientDto>> GetById(int id)
        {
            try
            {
                var client = await _service.GetByIdAsync(id);
                return Ok(client);
            }
            catch (NotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
        [HttpPut("{id}/adresses")]
        public async Task<IActionResult> UpdateAdresses(int id, [FromBody] List<AdresseDto> adresses)
        {
            try
            {
                await _service.UpdateAdressesAsync(id, adresses);
                return NoContent();
            }
            catch (NotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpPost]
        [Consumes("multipart/form-data")]
        public async Task<ActionResult<ClientDto>> Create([FromForm] ClientCreateDto dto,
            [FromForm] string? adresseRue = null,
            [FromForm] string? adresseVille = null,
            [FromForm] string? adresseCodePostal = null,
            [FromForm] string? adressePays = null,
            [FromForm] bool adresseEstPrincipale = true)
        {
            // Création de l'adresse si les champs sont fournis
            List<AdresseCreateDto>? adresses = null;
            if (!string.IsNullOrEmpty(adresseRue) && !string.IsNullOrEmpty(adresseVille))
            {
                adresses = new List<AdresseCreateDto>
                {
                    new AdresseCreateDto
                    {
                        Rue = adresseRue,
                        Ville = adresseVille,
                        CodePostal = adresseCodePostal ?? "",
                        Pays = adressePays ?? "Tunisie",
                        EstPrincipale = adresseEstPrincipale,
                        EntityId = 0 // Sera ignoré lors de la création
                    }
                };
            }

            // Debug: Afficher les données reçues
            Console.WriteLine($"🔍 ClientCreateDto reçu:");
            Console.WriteLine($"  - Email: {dto.Email}");
            Console.WriteLine($"  - Nom: {dto.Nom}");
            Console.WriteLine($"  - Adresse Rue: {adresseRue}");
            Console.WriteLine($"  - Adresse Ville: {adresseVille}");
            Console.WriteLine($"  - Adresses count: {adresses?.Count ?? 0}");

            if (adresses != null)
            {
                for (int i = 0; i < adresses.Count; i++)
                {
                    var adresse = adresses[i];
                    Console.WriteLine($"  - Adresse[{i}]: {adresse.Rue}, {adresse.Ville}, {adresse.CodePostal}");
                }
            }

            var client = await _service.CreateAsync(dto, adresses);
            return CreatedAtAction(nameof(GetById), new { id = client.Id }, client);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, ClientUpdateDto dto)
        {
            await _service.UpdateAsync(id, dto);
            return NoContent();
        }

        [HttpPut("{id}/profile")]
        public async Task<IActionResult> UpdateProfile(int id, ClientProfileUpdateDto dto)
        {
            try
            {
                await _service.UpdateProfileAsync(id, dto);
                return NoContent();
            }
            catch (NotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _service.DeleteAsync(id);
            return NoContent();
        }

        
        [HttpGet("{id}/commandes")]
        public async Task<ActionResult<IEnumerable<CommandeDto>>> GetCommandes(int id)
        {
            return Ok(await _service.GetCommandesByClientAsync(id));
        }
    }
}
