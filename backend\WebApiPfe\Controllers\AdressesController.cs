﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AdressesController : ControllerBase
    {
        private readonly IAdresseService _service;

        public AdressesController(IAdresseService service)
        {
            _service = service;
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<AdresseDto>> Get(int id)
        {
            var adresse = await _service.GetByIdAsync(id);
            return adresse != null ? Ok(adresse) : NotFound();
        }

        [HttpGet("entity/{entityId}")]
        public async Task<ActionResult<IEnumerable<AdresseDto>>> GetByEntity(int entityId)
        {
            return Ok(await _service.GetByEntityAsync(entityId));
        }

        [HttpPost]
        public async Task<ActionResult<AdresseDto>> Post(AdresseCreateDto dto)
        {
            var adresse = await _service.CreateAsync(dto);
            return CreatedAtAction(nameof(Get), new { id = adresse.Id }, adresse);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Put(int id, AdresseUpdateDto dto)
        {
            await _service.UpdateAsync(id, dto);
            return NoContent();
        }

        [HttpPatch("{id}/principale/{entityId}")]
        public async Task<IActionResult> SetPrincipale(int id, int entityId)
        {
            await _service.SetPrincipaleAsync(id, entityId);
            return NoContent();
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _service.DeleteAsync(id);
            return NoContent();
        }
    }
}
