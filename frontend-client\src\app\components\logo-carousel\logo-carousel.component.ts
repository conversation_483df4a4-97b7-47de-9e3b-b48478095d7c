import {
  ChangeDetectorRef,
  Component,
  HostListener,
  ViewChild,
} from '@angular/core';
import { debounceTime, Subject, Subscription, timer } from 'rxjs';
import { MarqueService } from '../../services/marque.service';
import { CarouselComponent } from 'ngx-bootstrap/carousel';
import { Router } from '@angular/router';
import { MarqueDto } from 'src/app/models/MarqueDto';

@Component({
  selector: 'demo-carousel-multilist-from-index',
  standalone: false,
  providers:[MarqueService],
  templateUrl: './logo-carousel.component.html',
  styleUrls: ['./logo-carousel.component.scss'],
})
export class LogoCarouselComponent {
  @ViewChild('carousel') carousel!: CarouselComponent;
  itemsPerSlide: number = 0;
  interval = 3000;
  singleSlideOffset = true;
  slides: MarqueDto[] = [];
  currentIndex = 0;
  subscription!: Subscription;
  private resizeSubject = new Subject<void>();

  constructor(
    private cdr: ChangeDetectorRef,
    private marqueService: MarqueService,
    private router: Router
  ) {
    this.updateItemsPerSlide();
    this.resizeSubject.pipe(debounceTime(100)).subscribe(() => {
      this.updateItemsPerSlide();
      this.resetCarousel();
      this.cdr.detectChanges();
    });
  }
  navigateToBrand(brandId: number): void {
    console.log('Navigating to brand with ID:', brandId);
    this.router.navigate(['/products/marque', brandId]);
  }
  ngOnInit() {
    this.marqueService.getAll().subscribe({
      next: (marques) => {
        this.slides = marques;
        console.log('✅ Marques chargées:', marques.length);
        this.cdr.detectChanges();

        // Démarrer le carrousel après le chargement des données
        setTimeout(() => {
          if (this.carousel && this.slides.length > this.itemsPerSlide) {
            this.carousel.play();
          }
        }, 1000);
      },
      error: (err) => {
        console.error('❌ Erreur lors de la récupération des marques:', err);
      }
    });

    // Timer pour le défilement automatique
    this.subscription = timer(this.interval, this.interval).subscribe(() => {
      if (this.slides.length > this.itemsPerSlide && this.carousel) {
        this.currentIndex = (this.currentIndex + 1) % Math.max(1, this.slides.length - this.itemsPerSlide + 1);
        this.carousel.selectSlide(this.currentIndex);
      }
    });
  }


  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(_event: Event) {
    console.log('Window resized:', window.innerWidth);
    this.resizeSubject.next();
  }

  private updateItemsPerSlide() {
    const logoWidth = 80;
    const margin = 20;
    const paddingPercentage = 0.12;
    const totalMargin = window.innerWidth * paddingPercentage;
    const containerWidth = window.innerWidth - totalMargin;

    this.itemsPerSlide = Math.floor(containerWidth / (logoWidth + margin));
    this.itemsPerSlide = Math.max(this.itemsPerSlide, 1);

    console.log('Items per slide updated to:', this.itemsPerSlide);
  }

  private resetCarousel() {
    if (this.carousel) {
      this.carousel.pause();
      this.carousel.selectSlide(this.currentIndex);
      this.carousel.play();
      this.carousel.selectSlide((this.currentIndex + 1) % this.slides.length);
      this.carousel.selectSlide(this.currentIndex);
    }
  }
}