<div class="product-container" *ngIf="product">
  <div class="product-content">
    <div class="product-images-complex">
      <div
        class="thumbnails-vertical"
        [ngClass]="{ 'thumbnails-scroll': (product.images.length || 0) > 4 }"
      >
        <img
          *ngFor="let img of product.images"
          [src]="imageUrlService.getProduitImageUrl(img.imageUrl)"
          alt="{{ product.nom }} - {{ img.ordre }}"
          class="thumbnail"
          [class.active-thumbnail]="imageUrlService.getProduitImageUrl(img.imageUrl) === selectedImage"
          (click)="onThumbnailClick(img.imageUrl)"
        />
      </div>

      <div class="main-image-wrapper">
        <button class="favorite-btn" (click)="toggleFavorite()">
          <mat-icon [ngClass]="{ 'favorite-active': isFavorite }"
            >favorite</mat-icon
          >
        </button>

        <img
          class="main-image"
          [src]="selectedImage || imageUrlService.getPlaceholderUrl()"
          alt="{{ product.nom }} - {{
            selectedImageObject?.ordre || 'image principale'
          }}"
        />
      </div>
    </div>
    <div class="product-details">
      <div class="title-badges-wrapper">
        <h1 class="product-title">{{ product.nom }}</h1>
        <div class="badges-wrapper-rate">
          <div class="badges-wrapper">
            <div class="product-promo-badge" *ngIf="product.estEnPromo">
              PROMO
            </div>
            <div class="product-new-badge" *ngIf="product.estNouveau">
              NOUVEAU
            </div>
          </div>
          <!-- Note moyenne en étoiles -->
          <div
            *ngIf="product.noteMoyenne != null"
            class="rating-section flex items-center"
          >
            <mat-icon
              *ngFor="let star of [1, 2, 3, 4, 5]"
              aria-label="Évaluation"
              [class.filled]="star <= product.noteMoyenne"
            >
              {{ star <= product.noteMoyenne ? "star" : "star_border" }}
            </mat-icon>
            <span class="review-count text-xs ml-1"
              >({{ nombreAvis || 0 }})</span
            >
          </div>
        </div>
      </div>

      <div class="price-section">
        <div class="current-price">
          {{ product.prixFinalTTC }} DT
          <span class="vat-label">TVA incluse</span>
        </div>
        <div class="outlet-price" *ngIf="product.prixApresOutlet">
          Ancien Prix :
          <span class="price-strike">{{ product.prixApresOutlet }} DT</span>
          <span class="discount-badge">-{{ product.tauxRemiseTotale }}%</span>
        </div>
        <div class="original-price" *ngIf="product.prixOriginalTTC">
          Prix Original :
          <span class="price-strike">{{ product.prixOriginalTTC }} DT</span>
          <span class="discount-badge">-{{ product.tauxRemiseOutlet }}%</span>
        </div>
        <div class="tva-rate" *ngIf="product.tauxTVA">
          Taux de TVA : {{ product.tauxTVA.taux }}%
        </div>
      </div>

      <h1 class="product-description">{{ product.description }}</h1>

      <div class="stock-info">
        <span class="stock-label">Stock :</span>
        <span class="stock-quantity">
          {{ product.stock }} ({{ product.isInStock ? "En stock" : "Rupture" }})
        </span>
      </div>
      <div class="product-attributes">
        <div class="attribute" *ngIf="product.marque">
          <span class="attribute-label">Marque:</span>
          <img
            *ngIf="product.marque.logo"
            [src]="product.marque.logo"
            alt="product.marque.nom"
            class="attribute-image"
          />
        </div>
        <div class="attribute" *ngIf="product.forme">
          <span class="attribute-label">Forme:</span>
          <span class="attribute-value">{{ product.forme.nom }}</span>
          <!-- <img
            *ngIf="product.forme.imageUrl"
            [src]="product.forme.imageUrl"
            alt="forme"
            class="attribute-image"
          /> -->
        </div>
        <div class="attribute" *ngIf="product.sousCategorie">
          <span class="attribute-label">Catégorie:</span>
          <span class="attribute-value">{{ product.sousCategorie.nom }}</span>
        </div>
      </div>
      <button class="add-to-cart-btn" (click)="ajouterAuPanier()">
        Ajouter au panier
        <mat-icon aria-hidden="true" class="icon">add_shopping_cart</mat-icon>
      </button>
    </div>
  </div>
  <div class="delivery-info">
    <div class="delivery-left">
      <div class="delivery-text">
        <span class="delivery-by">
          <mat-icon aria-hidden="true" class="icon">local_shipping</mat-icon>
          Vendu et envoyé par {{ product.fournisseur?.raisonSociale }}
        </span>
        <span class="delivery-date">
          <mat-icon aria-hidden="true" class="icon">schedule</mat-icon>
          Préparation : {{ product.fournisseur?.delaiPreparationJours }} jours
        </span>
        <span class="delivery-fees">
          <mat-icon aria-hidden="true" class="icon">attach_money</mat-icon>
          Frais de livraison : {{ product.fournisseur?.fraisLivraisonBase }} DT
        </span>
      </div>
      <div class="free-return">
        <mat-icon aria-hidden="true" class="icon">replay</mat-icon>
        Retour gratuit
      </div>
    </div>
    <div class="delivery-right" *ngIf="product.fournisseur">
      <img
        *ngIf="product.fournisseur.logoFile"
        [src]="imageUrlService.getFournisseurLogoUrl(product.fournisseur.logoFile)"
        alt="Logo Fournisseur"
        class="fournisseur-logo"
      />
      <div class="fournisseur-info">
        <div class="fournisseur-raison">
          {{ product.fournisseur.raisonSociale }}
        </div>
        <div class="fournisseur-email">{{ product.fournisseur.email }}</div>
        <div class="fournisseur-adresse" *ngIf="product.fournisseur">
          <mat-icon class="address-icon">location_on</mat-icon>
          <span *ngIf="fournisseurAdressePrincipale; else noAddress">
            {{ fournisseurAdressePrincipale.rue }}, {{ fournisseurAdressePrincipale.ville }} {{ fournisseurAdressePrincipale.codePostal }}
          </span>
          <ng-template #noAddress>
            <span class="no-address">Adresse non disponible</span>
          </ng-template>
        </div>


        <div
          class="fournisseur-description"
          *ngIf="product.fournisseur.description"
        >
          {{ product.fournisseur.description }}
        </div>
      </div>
    </div>
  </div>
  <div class="avis-section">
    <div class="avis-left">
      <div class="avis-title">Commentaires des clients ({{ tousLesAvis.length }})</div>

      <div class="avis-distribution">
        <div class="avis-row" *ngFor="let score of [5, 4, 3, 2, 1]">
          <span class="star-label">
            <ng-container *ngFor="let star of [1, 2, 3, 4, 5]">
              <mat-icon [class.filled]="star <= score">
                {{ star <= score ? "star" : "star_border" }}
              </mat-icon>
            </ng-container>
          </span>
          <span class="avis-count">{{ repartitionAvis[score] || 0 }} avis</span>
        </div>
      </div>

      <a class="voir-commentaires" (click)="toggleAffichageAvis()">
        {{ afficherTousLesAvis ? 'Masquer les commentaires' : 'Voir tous les commentaires (' + tousLesAvis.length + ')' }}
        <mat-icon>{{ afficherTousLesAvis ? 'expand_less' : 'expand_more' }}</mat-icon>
      </a>

      <!-- Section d'affichage de tous les avis - directement sous le bouton -->
      <div class="tous-les-avis-section" *ngIf="afficherTousLesAvis && tousLesAvis.length > 0">
        <div class="avis-list-container">
          <h3>Tous les avis clients ({{ tousLesAvis.length }})</h3>

          <div class="avis-cards-grid">
            <div *ngFor="let avis of tousLesAvis; let i = index" class="avis-card-inline">
              <div class="avis-card-header-inline">
                <div class="client-info-inline">
                  <div class="client-avatar-inline">
                    {{ (avis.client?.prenom || 'A').charAt(0).toUpperCase() }}
                  </div>
                  <div class="client-details-inline">
                    <div class="client-name-inline">
                      {{ avis.client?.prenom }} {{ avis.client?.nom }}
                    </div>
                    <div class="avis-date-inline">
                      {{ avis.datePublication | date:'dd/MM/yyyy' }}
                    </div>
                  </div>
                </div>

                <div class="avis-rating-inline">
                  <div class="stars-container-inline">
                    <ng-container *ngFor="let star of [1, 2, 3, 4, 5]">
                      <mat-icon [class.filled]="star <= avis.note" class="star-icon-inline">
                        {{ star <= avis.note ? "star" : "star_border" }}
                      </mat-icon>
                    </ng-container>
                  </div>
                  <span class="note-value-inline">{{ avis.note }}/5</span>
                </div>
              </div>

              <div class="avis-content-inline" *ngIf="avis.commentaire">
                <p>{{ avis.commentaire }}</p>
              </div>

              <div class="avis-content-inline no-comment-inline" *ngIf="!avis.commentaire">
                <p><em>Aucun commentaire écrit</em></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="avis-right">
      <div class="moyenne-note">Moyenne : {{ moyenneNote.toFixed(1) }} / 5</div>
      <div class="moyenne-stars">
        <ng-container *ngFor="let star of [1, 2, 3, 4, 5]">
          <mat-icon [class.filled]="star <= moyenneNote">
            {{ star <= moyenneNote ? "star" : "star_border" }}
          </mat-icon>
        </ng-container>
      </div>
      <div class="ajout-avis-form">
        <div class="ajout-avis-title">
          {{ monAvisExistant ? 'Modifier mon commentaire' : 'Ajouter un commentaire' }}
        </div>
        <div class="ajout-note">
          Note :
          <ng-container *ngFor="let star of [1, 2, 3, 4, 5]">
            <mat-icon
              [class.filled]="star <= nouvelleNote"
              (click)="choisirNote(star)"
              style="cursor: pointer"
            >
              {{ star <= nouvelleNote ? "star" : "star_border" }}
            </mat-icon>
          </ng-container>
        </div>
        <div class="ajout-commentaire">
          Commentaire :
          <textarea
            [(ngModel)]="nouveauCommentaire"
            rows="3"
            placeholder="Écrivez votre avis ici..."
          ></textarea>
        </div>
        <button class="p-button p-button-primary" (click)="envoyerAvis()">
          {{ monAvisExistant ? 'Modifier mon avis' : 'Envoyer la note' }}
        </button>
      </div>
    </div>
  </div>

  <section
    class="collection-fournisseur"
    *ngIf="produitsFournisseur && produitsFournisseur.length > 0"
  >
    <h2 class="text-xl font-bold mb-4">
      Collection de {{ product.fournisseur?.raisonSociale }}
    </h2>
    <p-carousel
      [value]="produitsFournisseur"
      [numVisible]="5"
      [numScroll]="5"
      [circular]="true"
      [responsiveOptions]="responsiveOptions"
      autoplayInterval="3000"
    >
      <ng-template let-produit pTemplate="item">
        <div
          class="card border border-surface-200 dark:border-surface-700 rounded m-2"
          (click)="goToProductDetails(produit.id)"
          style="cursor: pointer"
        >
          <div class="card-image relative">
            <img
              [src]="getMainImage(produit)"
              [alt]="produit.nom"
              class="w-full"
              loading="lazy"
            />

            <div class="product-badges">
              <span *ngIf="produit.estNouveau" class="badge-new">Nouveau</span>
              <span *ngIf="produit.tauxRemiseTotale > 0" class="badge-discount">
                -{{ produit.tauxRemiseTotale }}%
              </span>
            </div>
          </div>

          <div class="card-content p-4">
            <h3
              class="mb-2 font-medium cursor-pointer text-primary-600 hover:underline"
            >
              {{ produit.nom }}
            </h3>
            <div class="product-info-right">
              <p class="product-brand">
                {{ produit.marque?.name || "Marque inconnue" }}
              </p>
              <p class="product-fournisseur">
                {{
                  produit.fournisseur?.raisonSociale || "Fournisseur inconnu"
                }}
              </p>
              <div
                *ngIf="produit.noteMoyenne != null"
                class="rating-section flex items-center"
              >
                <mat-icon
                  *ngFor="let star of [1, 2, 3, 4, 5]"
                  aria-label="Évaluation"
                  [class.filled]="star <= produit.noteMoyenne"
                >
                  {{ star <= produit.noteMoyenne ? "star" : "star_border" }}
                </mat-icon>
                <span class="review-count text-xs ml-1">
                  ({{ produit.nombreAvis || 0 }})
                </span>
              </div>
            </div>
            <p class="truncate mb-4">{{ produit.description }}</p>

            <div class="price-section">
              <div class="price-stack">
                <div class="price-final">{{ produit.prixFinalTTC }} DT</div>
                <div
                  class="price-intermediate"
                  *ngIf="
                    produit.prixApresOutlet &&
                    produit.prixApresOutlet !== produit.prixFinalTTC
                  "
                >
                  Ancien prix :
                  <span class="amount">{{ produit.prixApresOutlet }} DT</span>
                </div>
                <div
                  class="price-original"
                  *ngIf="
                    produit.prixOriginalTTC &&
                    produit.prixOriginalTTC !== produit.prixApresOutlet
                  "
                >
                  Prix d'origine :
                  <span class="amount">{{ produit.prixOriginalTTC }} DT</span>
                </div>
              </div>
            </div>

            <span class="button-container flex">
              <p-button
                [icon]="isFavorite ? 'pi pi-heart-fill' : 'pi pi-heart'"
                [styleClass]="isFavorite ? 'p-button-danger' : 'p-button-secondary p-button-outlined'"
                aria-label="Ajouter aux favoris"
                (click)="toggleFavorite()"
              ></p-button>

              <p-button
                icon="pi pi-shopping-cart"
                styleClass="p-button-primary ml-2"
                aria-label="Ajouter au panier"
                (click)="ajouterAuPanier()"
              ></p-button>
            </span>
          </div>
        </div>
      </ng-template>
    </p-carousel>
  </section>
  <section
    class="collection-marque"
    *ngIf="produitsMarque && produitsMarque.length > 0"
  >
    <h2 class="text-xl font-bold mb-4">
      Collection de {{ product.marque?.name || "Marque inconnue" }}
    </h2>
    <p-carousel
      [value]="produitsMarque"
      [numVisible]="5"
      [numScroll]="5"
      [circular]="true"
      [responsiveOptions]="responsiveOptions"
      autoplayInterval="3000"
    >
      <ng-template let-produit pTemplate="item">
        <div
          class="card border border-surface-200 dark:border-surface-700 rounded m-2"
          (click)="goToProductDetails(produit.id)"
          style="cursor: pointer"
        >
          <div class="card-image relative">
            <img
              [src]="getMainImage(produit)"
              [alt]="produit.nom"
              class="w-full"
              loading="lazy"
            />

            <div class="product-badges">
              <span *ngIf="produit.estNouveau" class="badge-new">Nouveau</span>
              <span *ngIf="produit.tauxRemiseTotale > 0" class="badge-discount">
                -{{ produit.tauxRemiseTotale }}%
              </span>
            </div>
          </div>

          <div class="card-content p-4">
            <h3
              class="mb-2 font-medium cursor-pointer text-primary-600 hover:underline"
            >
              {{ produit.nom }}
            </h3>
            <div class="product-info-right">
              <p class="product-brand">
                {{ produit.marque?.name || "Marque inconnue" }}
              </p>
              <p class="product-fournisseur">
                {{
                  produit.fournisseur?.raisonSociale || "Fournisseur inconnu"
                }}
              </p>
              <div
                *ngIf="produit.noteMoyenne != null"
                class="rating-section flex items-center"
              >
                <mat-icon
                  *ngFor="let star of [1, 2, 3, 4, 5]"
                  aria-label="Évaluation"
                  [class.filled]="star <= produit.noteMoyenne"
                >
                  {{ star <= produit.noteMoyenne ? "star" : "star_border" }}
                </mat-icon>
                <span class="review-count text-xs ml-1">
                  ({{ produit.nombreAvis || 0 }})
                </span>
              </div>
            </div>
            <p class="truncate mb-4">{{ produit.description }}</p>

            <div class="price-section">
              <div class="price-stack">
                <div class="price-final">{{ produit.prixFinalTTC }} DT</div>
                <div
                  class="price-intermediate"
                  *ngIf="
                    produit.prixApresOutlet &&
                    produit.prixApresOutlet !== produit.prixFinalTTC
                  "
                >
                  Ancien prix :
                  <span class="amount">{{ produit.prixApresOutlet }} DT</span>
                </div>
                <div
                  class="price-original"
                  *ngIf="
                    produit.prixOriginalTTC &&
                    produit.prixOriginalTTC !== produit.prixApresOutlet
                  "
                >
                  Prix d'origine :
                  <span class="amount">{{ produit.prixOriginalTTC }} DT</span>
                </div>
              </div>
            </div>

            <span class="button-container flex">
              <p-button
                [icon]="isFavorite ? 'pi pi-heart-fill' : 'pi pi-heart'"
                [styleClass]="isFavorite ? 'p-button-danger' : 'p-button-secondary p-button-outlined'"
                aria-label="Ajouter aux favoris"
                (click)="toggleFavorite()"
              ></p-button>

              <p-button
                icon="pi pi-shopping-cart"
                styleClass="p-button-primary ml-2"
                aria-label="Ajouter au panier"
                (click)="ajouterAuPanier()"
              ></p-button>
            </span>
          </div>
        </div>
      </ng-template>
    </p-carousel>
  </section>
  <section
    class="collection-similaire"
    *ngIf="produitsSimilaire && produitsSimilaire.length > 0"
  >
    <h2 class="text-xl font-bold mb-4">
      Collection de {{ product.sousCategorie?.nom || "Sous Categorie inconnue" }}
    </h2>
    <p-carousel
      [value]="produitsSimilaire"
      [numVisible]="5"
      [numScroll]="5"
      [circular]="true"
      [responsiveOptions]="responsiveOptions"
      autoplayInterval="3000"
    >
      <ng-template let-produit pTemplate="item">
        <div
          class="card border border-surface-200 dark:border-surface-700 rounded m-2"
          (click)="goToProductDetails(produit.id)"
          style="cursor: pointer"
        >
          <div class="card-image relative">
            <img
              [src]="getMainImage(produit)"
              [alt]="produit.nom"
              class="w-full"
              loading="lazy"
            />

            <div class="product-badges">
              <span *ngIf="produit.estNouveau" class="badge-new">Nouveau</span>
              <span *ngIf="produit.tauxRemiseTotale > 0" class="badge-discount">
                -{{ produit.tauxRemiseTotale }}%
              </span>
            </div>
          </div>

          <div class="card-content p-4">
            <h3
              class="mb-2 font-medium cursor-pointer text-primary-600 hover:underline"
            >
              {{ produit.nom }}
            </h3>
            <div class="product-info-right">
              <p class="product-brand">
                {{ produit.marque?.name || "Marque inconnue" }}
              </p>
              <p class="product-fournisseur">
                {{
                  produit.fournisseur?.raisonSociale || "Fournisseur inconnu"
                }}
              </p>
              <div
                *ngIf="produit.noteMoyenne != null"
                class="rating-section flex items-center"
              >
                <mat-icon
                  *ngFor="let star of [1, 2, 3, 4, 5]"
                  aria-label="Évaluation"
                  [class.filled]="star <= produit.noteMoyenne"
                >
                  {{ star <= produit.noteMoyenne ? "star" : "star_border" }}
                </mat-icon>
                <span class="review-count text-xs ml-1">
                  ({{ produit.nombreAvis || 0 }})
                </span>
              </div>
            </div>
            <p class="truncate mb-4">{{ produit.description }}</p>

            <div class="price-section">
              <div class="price-stack">
                <div class="price-final">{{ produit.prixFinalTTC }} DT</div>
                <div
                  class="price-intermediate"
                  *ngIf="
                    produit.prixApresOutlet &&
                    produit.prixApresOutlet !== produit.prixFinalTTC
                  "
                >
                  Ancien prix :
                  <span class="amount">{{ produit.prixApresOutlet }} DT</span>
                </div>
                <div
                  class="price-original"
                  *ngIf="
                    produit.prixOriginalTTC &&
                    produit.prixOriginalTTC !== produit.prixApresOutlet
                  "
                >
                  Prix d'origine :
                  <span class="amount">{{ produit.prixOriginalTTC }} DT</span>
                </div>
              </div>
            </div>

            <span class="button-container flex">
              <p-button
                [icon]="isFavorite ? 'pi pi-heart-fill' : 'pi pi-heart'"
                [styleClass]="isFavorite ? 'p-button-danger' : 'p-button-secondary p-button-outlined'"
                aria-label="Ajouter aux favoris"
                (click)="toggleFavorite()"
              ></p-button>

              <p-button
                icon="pi pi-shopping-cart"
                styleClass="p-button-primary ml-2"
                aria-label="Ajouter au panier"
                (click)="ajouterAuPanier()"
              ></p-button>
            </span>
          </div>
        </div>
      </ng-template>
    </p-carousel>
  </section>
</div>


