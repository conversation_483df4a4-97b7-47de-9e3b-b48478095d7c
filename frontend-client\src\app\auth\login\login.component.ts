import { Component, inject } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { catchError, finalize, of, tap } from 'rxjs';
import { AuthService } from '../auth.service';

@Component({
  selector: 'app-login',
  standalone: false,
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private snackBar = inject(MatSnackBar);
  private router = inject(Router);

  loginForm = this.fb.group({
    email: ['', [Validators.required, Validators.email]],
    password: ['', [Validators.required, Validators.minLength(6)]],
  });

  isLoading = false;

  onSubmit() {
    if (this.loginForm.invalid) return;

    this.isLoading = true;
    const loginData = {
      email: this.loginForm.value.email!,
      motDePasse: this.loginForm.value.password!,
    };
    this.authService
      .login(loginData)
      .pipe(
        tap(() => {
          const redirectUrl = localStorage.getItem('redirectAfterLogin');
          if (redirectUrl) {
            this.router.navigate([redirectUrl]);
            localStorage.removeItem('redirectAfterLogin');
          } else {
            this.router.navigate(['/home']);
          }
        }),
        catchError((error) => {
          this.snackBar.open(
            error.message || 'Échec de la connexion',
            'Fermer',
            { duration: 5000, panelClass: ['error-snackbar'] }
          );
          return of(null);
        }),
        finalize(() => (this.isLoading = false))
      )
      .subscribe();
  }
}
