import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { CartService } from '../../../services/cart.service';
import { AuthService } from '../../../auth/auth.service';
import { ClientService } from '../../../services/client.service';
import { CommandeService } from '../../../services/commande.service';
import { ProduitService } from '../../../services/produit.service';
import { ImageUrlService } from '../../../services/image-url.service';
import { PanierDto } from '../../../models/PanierDto';
import { ItemPanierDto } from '../../../models/ItemPanierDto';
import { AdresseDto } from '../../../models/AdresseDto';
import { ProduitCard } from '../../../models/ProduitCard';
import { CreateCommandeDto, CreateDetailsCommandeDto } from '../../../models/CommandeDto';
import { FraisLivraisonResponseDto } from '../../../models/FraisLivraisonDto';
import { forkJoin, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

export interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  icon: string;
  available: boolean;
}

@Component({
  selector: 'app-final-confirmation',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './final-confirmation.component.html',
  styleUrls: ['./final-confirmation.component.scss']
})
export class FinalConfirmationComponent implements OnInit {
  cart: PanierDto | null = null;
  cartItemsWithDetails: Array<ItemPanierDto & { produitDetails?: ProduitCard }> = [];
  selectedAddress: AdresseDto | null = null;
  selectedPaymentMethod: PaymentMethod | null = null;
  loading = false;
  errorMessage = '';
  isProcessingOrder = false;
  fraisLivraisonDetails: FraisLivraisonResponseDto | null = null;

  paymentMethods: PaymentMethod[] = [
    {
      id: 'card',
      name: 'Carte bancaire',
      description: 'Visa, Mastercard, American Express',
      icon: '💳',
      available: true
    },
    {
      id: 'paypal',
      name: 'PayPal',
      description: 'Paiement sécurisé avec PayPal',
      icon: '🅿️',
      available: true
    },
    {
      id: 'cash',
      name: 'Paiement à la livraison',
      description: 'Payez en espèces lors de la réception',
      icon: '💵',
      available: true
    }
  ];

  constructor(
    private cartService: CartService,
    private authService: AuthService,
    private clientService: ClientService,
    private commandeService: CommandeService,
    private produitService: ProduitService,
    public imageUrlService: ImageUrlService,
    private router: Router
  ) {
    console.log('🏗️ FinalConfirmationComponent constructor - fraisLivraisonDetails initial:', this.fraisLivraisonDetails);
  }

  ngOnInit(): void {
    console.log('🚀 FinalConfirmationComponent ngOnInit - fraisLivraisonDetails initial:', this.fraisLivraisonDetails);
    this.loadOrderData();
  }

  loadOrderData(): void {
    console.log('📊 loadOrderData appelée');
    // Vérifier que toutes les étapes précédentes ont été complétées
    const selectedAddressId = sessionStorage.getItem('selectedAddressId');
    const selectedPaymentMethodId = sessionStorage.getItem('selectedPaymentMethod');

    console.log('📊 selectedAddressId:', selectedAddressId);
    console.log('📊 selectedPaymentMethodId:', selectedPaymentMethodId);

    if (!selectedAddressId || !selectedPaymentMethodId) {
      console.log('❌ Redirection vers checkout car données manquantes');
      this.router.navigate(['/user/checkout']);
      return;
    }

    this.loading = true;

    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      this.router.navigate(['/auth/login']);
      return;
    }

    // Charger le panier
    this.cartService.getCart(currentUser.id).subscribe({
      next: (cart) => {
        this.cart = cart;
        if (!cart || cart.items.length === 0) {
          this.router.navigate(['/user/cart']);
          return;
        }
        this.loadProductDetails();
        this.loadAddressAndPaymentMethod(parseInt(selectedAddressId), selectedPaymentMethodId);
      },
      error: (err) => {
        console.error('Erreur lors du chargement du panier:', err);
        this.errorMessage = 'Erreur lors du chargement du panier';
        this.loading = false;
      }
    });
  }

  loadAddressAndPaymentMethod(addressId: number, paymentMethodId: string): void {
    console.log('🔄 loadAddressAndPaymentMethod appelée avec:', { addressId, paymentMethodId });

    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      this.router.navigate(['/auth/login']);
      return;
    }

    this.clientService.getById(currentUser.id).subscribe({
      next: (client) => {
        this.selectedAddress = client.adresses?.find(addr => addr.id === addressId) || null;
        this.selectedPaymentMethod = this.paymentMethods.find(pm => pm.id === paymentMethodId) || null;

        console.log('📍 selectedAddress:', this.selectedAddress);
        console.log('💳 selectedPaymentMethod:', this.selectedPaymentMethod);

        if (!this.selectedAddress || !this.selectedPaymentMethod) {
          console.log('❌ Redirection vers checkout car adresse ou méthode de paiement manquante');
          this.router.navigate(['/user/checkout']);
          return;
        }

        // Calculer les frais de livraison
        console.log('🚚 Appel de calculerFraisLivraison()');
        this.calculerFraisLivraison();

        this.loading = false;
      },
      error: (err) => {
        console.error('Erreur lors du chargement des données:', err);
        this.errorMessage = 'Erreur lors du chargement des données';
        this.loading = false;
      }
    });
  }

  confirmerCommande(): void {
    const clientId = this.authService.getCurrentUser()?.id;

    if (!clientId || !this.cart || !this.selectedAddress) {
      this.errorMessage = 'Données manquantes pour créer la commande';
      return;
    }

    this.isProcessingOrder = true;
    this.errorMessage = '';

    // Préparer les données de la commande
    const detailsCommandes: CreateDetailsCommandeDto[] = this.cart.items.map(item => ({
      produitId: item.produitId,
      quantite: item.quantite
    }));

    const nouvelleCommande: CreateCommandeDto = {
      clientId: clientId,
      detailsCommandes: detailsCommandes
    };

    // Créer la commande
    this.commandeService.creerCommande(nouvelleCommande).subscribe({
      next: (commande) => {
        console.log('Commande créée avec succès:', commande);

        // Vider le panier
        if (this.cart) {
          this.cartService.clearCart(this.cart.id).subscribe();
        }

        // Nettoyer le sessionStorage
        sessionStorage.removeItem('selectedAddressId');
        sessionStorage.removeItem('selectedPaymentMethod');

        // Rediriger vers la page des commandes avec un message de succès
        this.router.navigate(['/user/orders'], {
          queryParams: { 
            success: 'true', 
            orderId: commande.id,
            total: commande.montantTotal 
          }
        });
      },
      error: (err: any) => {
        console.error('Erreur lors de la création de la commande:', err);
        this.isProcessingOrder = false;

        let errorMessage = 'Erreur lors de la création de la commande.';
        if (err.error?.message) {
          errorMessage = err.error.message;
        } else if (err.message) {
          errorMessage = err.message;
        }

        this.errorMessage = errorMessage;
      }
    });
  }

  retourPaiement(): void {
    this.router.navigate(['/user/checkout/payment']);
  }

  modifierAdresse(): void {
    this.router.navigate(['/user/checkout/address']);
  }

  modifierPaiement(): void {
    this.router.navigate(['/user/checkout/payment']);
  }

  loadProductDetails(): void {
    if (!this.cart) return;

    // Récupérer les détails de chaque produit
    const produitRequests = this.cart.items.map(item =>
      this.produitService.getById(item.produitId).pipe(
        map(produit => ({ ...item, produitDetails: produit })),
        catchError(error => {
          console.error(`Erreur lors de la récupération du produit ${item.produitId}:`, error);
          return of({ ...item, produitDetails: undefined });
        })
      )
    );

    forkJoin(produitRequests).subscribe({
      next: (itemsWithDetails) => {
        this.cartItemsWithDetails = itemsWithDetails;
      },
      error: (err) => {
        console.error('Erreur lors du chargement des détails des produits:', err);
        this.cartItemsWithDetails = this.cart!.items.map(item => ({ ...item, produitDetails: undefined }));
      }
    });
  }

  // Obtenir l'image d'un produit
  getProductImage(item: ItemPanierDto & { produitDetails?: ProduitCard }): string {
    // Si on a les détails du produit avec des images
    if (item.produitDetails?.images && item.produitDetails.images.length > 0) {
      const mainImage = item.produitDetails.images.find(img => img.isMain);
      const imageUrl = mainImage?.imageUrl || item.produitDetails.images[0]?.imageUrl;
      if (imageUrl) {
        return this.imageUrlService.getProduitImageUrl(imageUrl);
      }
    }

    // Si on a l'imageUrl de base du produit
    if (item.produitDetails?.imageUrl) {
      return this.imageUrlService.getProduitImageUrl(item.produitDetails.imageUrl);
    }

    return this.imageUrlService.getPlaceholderUrl();
  }

  // Gestion d'erreur d'image
  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) {
      img.src = this.imageUrlService.getPlaceholderUrl();
    }
  }

  calculerFraisLivraison(): void {
    console.log('🚚 calculerFraisLivraison appelée');
    console.log('🛒 cart:', this.cart);
    console.log('📍 selectedAddress:', this.selectedAddress);

    if (!this.cart || !this.selectedAddress) {
      console.log('❌ Conditions non remplies pour calculer les frais');
      return;
    }

    const produitIds = this.cart.items.map(item => item.produitId);
    console.log('📦 produitIds:', produitIds);

    this.commandeService.calculerFraisLivraison(this.selectedAddress.id, produitIds).subscribe({
      next: (response) => {
        console.log('✅ Réponse frais de livraison:', response);
        this.fraisLivraisonDetails = response;
      },
      error: (err) => {
        console.error('❌ Erreur lors du calcul des frais de livraison:', err);
        // En cas d'erreur, on garde les frais à null
        this.fraisLivraisonDetails = null;
      }
    });
  }

  getSousTotal(): number {
    return this.cartItemsWithDetails.reduce((total, item) => total + (item.prixUnitaire * item.quantite), 0) || 0;
  }

  getTotal(): number {
    return this.getSousTotal() + (this.fraisLivraisonDetails?.fraisLivraisonTotal || 0);
  }

  getTotalItems(): number {
    return this.cartItemsWithDetails.reduce((total, item) => total + item.quantite, 0) || 0;
  }
}
