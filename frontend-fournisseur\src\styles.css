/* Styles globaux pour l'application fournisseur */

/* Import des variables CSS */
@import './styles/variables.css';

/* Import des composants */
@import './styles/components.css';

/* Styles de base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: var(--font-family-sans);
  background-color: var(--gray-50);
  color: var(--gray-900);
  line-height: var(--line-height-normal);
}

/* Styles pour les liens */
a {
  color: var(--primary-600);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

/* Styles pour les titres */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-4);
  color: var(--gray-900);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

/* Styles pour les paragraphes */
p {
  margin-bottom: var(--spacing-4);
  color: var(--gray-700);
}

/* Styles pour les listes */
ul, ol {
  margin-bottom: var(--spacing-4);
  padding-left: var(--spacing-6);
}

li {
  margin-bottom: var(--spacing-1);
}

/* Styles pour les images */
img {
  max-width: 100%;
  height: auto;
}

/* Styles pour les tableaux */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-6);
}

th, td {
  padding: var(--spacing-3);
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

th {
  background-color: var(--gray-100);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
}

/* Styles pour les formulaires */
input, textarea, select {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  transition: var(--transition-fast);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Styles pour les boutons */
button {
  cursor: pointer;
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Classes utilitaires */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-2);
  }

  h1 { font-size: var(--font-size-2xl); }
  h2 { font-size: var(--font-size-xl); }
  h3 { font-size: var(--font-size-lg); }
}

/* Très petits écrans (< 480px) */
@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-1);
  }

  h1 { font-size: var(--font-size-xl); }
  h2 { font-size: var(--font-size-lg); }
  h3 { font-size: var(--font-size-base); }
  h4 { font-size: var(--font-size-sm); }

  input, textarea, select {
    padding: var(--spacing-2);
    font-size: var(--font-size-sm);
    min-height: 44px; /* Amélioration tactile */
  }

  table {
    font-size: var(--font-size-sm);
  }

  th, td {
    padding: var(--spacing-2);
  }
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

/* Focus visible pour l'accessibilité */
*:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}
