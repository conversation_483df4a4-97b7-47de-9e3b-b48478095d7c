import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { environment } from '../../environments/environment';
import { 
  Depot, 
  StockDepot, 
  ProduitDepot, 
  StockDepotUpdate, 
  StockDepotBulkUpdate,
  MouvementStockDepot,
  TransfertStock,
  CreateDepotDto,
  UpdateDepotDto,
  FiltreStockDepot,
  StatistiquesDepot,
  TypeMouvementDepot
} from '../models/depot.model';

@Injectable({
  providedIn: 'root'
})
export class DepotService {
  private readonly API_URL = `${environment.apiUrl}/depots`;
  private readonly STOCK_API_URL = `${environment.apiUrl}/stock-depots`;
  
  // Subjects pour la réactivité
  private depotsSubject = new BehaviorSubject<Depot[]>([]);
  private stocksDepotsSubject = new BehaviorSubject<StockDepot[]>([]);
  
  public depots$ = this.depotsSubject.asObservable();
  public stocksDepots$ = this.stocksDepotsSubject.asObservable();

  constructor(private http: HttpClient) {
    this.initializeMockData();
  }

  // ==================== GESTION DES DÉPÔTS ====================

  /**
   * Obtenir tous les dépôts d'un fournisseur
   */
  getDepotsByFournisseur(fournisseurId: number): Observable<Depot[]> {
    // Filtrer les dépôts par fournisseurId
    return new Observable(observer => {
      this.depots$.subscribe(depots => {
        const depotsFiltered = depots.filter(d => d.fournisseurId === fournisseurId);
        observer.next(depotsFiltered);
      });
    });
  }

  /**
   * Obtenir un dépôt par ID
   */
  getDepotById(id: number): Observable<Depot> {
    return new Observable(observer => {
      this.depots$.subscribe(depots => {
        const depot = depots.find(d => d.id === id);
        if (depot) {
          observer.next(depot);
        } else {
          observer.error('Dépôt non trouvé');
        }
      });
    });
  }

  /**
   * Créer un nouveau dépôt
   */
  createDepot(depot: CreateDepotDto): Observable<Depot> {
    return new Observable(observer => {
      const currentDepots = this.depotsSubject.value;
      const newDepot: Depot = {
        id: Date.now(),
        ...depot,
        estActif: true,
        estPrincipal: depot.estPrincipal || false,
        dateCreation: new Date()
      };
      
      const updatedDepots = [...currentDepots, newDepot];
      this.depotsSubject.next(updatedDepots);
      this.saveMockData();
      
      observer.next(newDepot);
      observer.complete();
    });
  }

  /**
   * Mettre à jour un dépôt
   */
  updateDepot(id: number, depot: UpdateDepotDto): Observable<void> {
    return new Observable(observer => {
      const currentDepots = this.depotsSubject.value;
      const index = currentDepots.findIndex(d => d.id === id);
      
      if (index !== -1) {
        currentDepots[index] = { ...currentDepots[index], ...depot };
        this.depotsSubject.next([...currentDepots]);
        this.saveMockData();
        observer.next();
      } else {
        observer.error('Dépôt non trouvé');
      }
      observer.complete();
    });
  }

  // ==================== GESTION DES STOCKS PAR DÉPÔT ====================

  /**
   * Obtenir les stocks d'un dépôt
   */
  getStocksByDepot(depotId: number): Observable<StockDepot[]> {
    return new Observable(observer => {
      this.stocksDepots$.subscribe(stocks => {
        const stocksDepot = stocks.filter(s => s.depotId === depotId);
        observer.next(stocksDepot);
      });
    });
  }

  /**
   * Obtenir les stocks d'un produit dans tous les dépôts
   */
  getStocksByProduit(produitId: number): Observable<StockDepot[]> {
    return new Observable(observer => {
      this.stocksDepots$.subscribe(stocks => {
        const stocksProduit = stocks.filter(s => s.produitId === produitId);
        observer.next(stocksProduit);
      });
    });
  }

  /**
   * Obtenir tous les produits avec leurs stocks par dépôt
   */
  getProduitsAvecStocksDepots(fournisseurId: number, filtre?: FiltreStockDepot): Observable<ProduitDepot[]> {
    return new Observable(observer => {
      // Vérifier si on a des données sauvegardées
      const savedProduits = localStorage.getItem('mock-produits-depot');
      let produits: ProduitDepot[];

      if (savedProduits) {
        produits = JSON.parse(savedProduits);
      } else {
        // Générer et sauvegarder des données de test
        produits = this.generateMockProduitsDepots();
        localStorage.setItem('mock-produits-depot', JSON.stringify(produits));
      }

      // Appliquer les filtres si nécessaire
      if (filtre) {
        if (filtre.rupture) {
          produits = produits.filter(p =>
            p.stocksDepots.some(s => s.quantite === 0)
          );
        }
        if (filtre.stockFaible) {
          produits = produits.filter(p =>
            p.stocksDepots.some(s => s.quantite > 0 && s.quantite <= 5)
          );
        }
        if (filtre.seuilAlerte) {
          produits = produits.filter(p =>
            p.stocksDepots.some(s => s.quantite <= s.seuilAlerte)
          );
        }
      }

      observer.next(produits);
      observer.complete();
    });
  }

  /**
   * Mettre à jour le stock d'un produit dans un dépôt
   */
  updateStockDepot(stockUpdate: StockDepotUpdate): Observable<void> {
    return new Observable(observer => {
      const currentStocks = this.stocksDepotsSubject.value;
      const index = currentStocks.findIndex(s => s.id === stockUpdate.id);
      
      if (index !== -1) {
        const oldQuantite = currentStocks[index].quantite;
        currentStocks[index] = {
          ...currentStocks[index],
          quantite: stockUpdate.quantite,
          quantiteDisponible: stockUpdate.quantite - currentStocks[index].quantiteReservee,
          dateModification: new Date(),
          seuilAlerte: stockUpdate.seuilAlerte || currentStocks[index].seuilAlerte,
          emplacementPhysique: stockUpdate.emplacementPhysique || currentStocks[index].emplacementPhysique
        };
        
        this.stocksDepotsSubject.next([...currentStocks]);
        
        // Enregistrer le mouvement
        this.enregistrerMouvement({
          stockDepotId: stockUpdate.id,
          produitId: currentStocks[index].produitId,
          depotId: currentStocks[index].depotId,
          type: stockUpdate.quantite > oldQuantite ? 'entree' : 'sortie',
          quantite: Math.abs(stockUpdate.quantite - oldQuantite),
          quantiteAvant: oldQuantite,
          quantiteApres: stockUpdate.quantite,
          motif: stockUpdate.motif,
          utilisateur: 'Fournisseur',
          dateCreation: new Date()
        });
        
        this.saveMockData();
        observer.next();
      } else {
        observer.error('Stock non trouvé');
      }
      observer.complete();
    });
  }

  /**
   * Mise à jour en lot des stocks d'un dépôt
   */
  updateStocksBulk(bulkUpdate: StockDepotBulkUpdate): Observable<void> {
    return new Observable(observer => {
      const promises = bulkUpdate.updates.map(update => {
        const stockUpdate: StockDepotUpdate = {
          id: 0, // Sera trouvé par produitId et depotId
          quantite: update.quantite,
          motif: update.motif
        };
        
        // Trouver le stock correspondant
        const currentStocks = this.stocksDepotsSubject.value;
        const stock = currentStocks.find(s => 
          s.produitId === update.produitId && s.depotId === bulkUpdate.depotId
        );
        
        if (stock) {
          stockUpdate.id = stock.id;
          return this.updateStockDepot(stockUpdate).toPromise();
        }
        return Promise.resolve();
      });
      
      Promise.all(promises).then(() => {
        observer.next();
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Transférer du stock entre dépôts
   */
  transfererStock(transfert: TransfertStock): Observable<void> {
    return new Observable(observer => {
      // Logique de transfert entre dépôts
      // Pour l'instant, simuler le transfert
      observer.next();
      observer.complete();
    });
  }

  // ==================== STATISTIQUES ====================

  /**
   * Obtenir les statistiques d'un dépôt
   */
  getStatistiquesDepot(depotId: number): Observable<StatistiquesDepot> {
    return new Observable(observer => {
      // Obtenir les produits avec leurs stocks
      const savedProduits = localStorage.getItem('mock-produits-depot');
      let produits: ProduitDepot[] = [];

      if (savedProduits) {
        produits = JSON.parse(savedProduits);
      } else {
        produits = this.generateMockProduitsDepots();
        localStorage.setItem('mock-produits-depot', JSON.stringify(produits));
      }

      // Filtrer les stocks pour ce dépôt et calculer les statistiques
      const stocksDepot: StockDepot[] = [];
      let valeurTotale = 0;

      produits.forEach(produit => {
        const stockDuDepot = produit.stocksDepots.find(s => s.depotId === depotId);
        if (stockDuDepot) {
          stocksDepot.push(stockDuDepot);
          valeurTotale += stockDuDepot.quantite * produit.prixAchatHT;
        }
      });

      const stats: StatistiquesDepot = {
        totalProduits: stocksDepot.length,
        totalQuantite: stocksDepot.reduce((sum, s) => sum + s.quantite, 0),
        valeurStock: valeurTotale,
        produitsEnRupture: stocksDepot.filter(s => s.quantite === 0).length,
        produitsStockFaible: stocksDepot.filter(s => s.quantite > 0 && s.quantite <= 5).length,
        produitsAlerte: stocksDepot.filter(s => s.quantite <= s.seuilAlerte).length,
        dernierMouvement: new Date()
      };

      observer.next(stats);
      observer.complete();
    });
  }

  // ==================== MÉTHODES PRIVÉES ====================

  private enregistrerMouvement(mouvement: Omit<MouvementStockDepot, 'id'>): void {
    // Enregistrer le mouvement dans le localStorage ou via API
    const mouvements = JSON.parse(localStorage.getItem('mouvements-stock-depot') || '[]');
    const nouveauMouvement = {
      id: Date.now(),
      ...mouvement
    };
    mouvements.unshift(nouveauMouvement);
    localStorage.setItem('mouvements-stock-depot', JSON.stringify(mouvements.slice(0, 100))); // Garder les 100 derniers
  }

  private initializeMockData(): void {
    const savedDepots = localStorage.getItem('mock-depots');
    const savedStocks = localStorage.getItem('mock-stocks-depots');

    // Vérifier si les données sauvegardées correspondent au fournisseur connecté
    const currentUser = JSON.parse(localStorage.getItem('current_user') || '{}');
    const currentFournisseurId = currentUser.id || 2070;

    if (savedDepots && savedStocks) {
      const depots = JSON.parse(savedDepots);
      // Vérifier si les dépôts correspondent au fournisseur connecté
      if (depots.length > 0 && depots[0].fournisseurId === currentFournisseurId) {
        this.depotsSubject.next(depots);
        this.stocksDepotsSubject.next(JSON.parse(savedStocks));
        return;
      }
    }

    // Générer de nouvelles données pour le fournisseur connecté
    this.generateMockData();
  }

  /**
   * Réinitialiser les données mock (pour le développement)
   */
  resetMockData(): void {
    console.log('🔄 Réinitialisation des données mock...');
    localStorage.removeItem('mock-depots');
    localStorage.removeItem('mock-stocks-depots');
    localStorage.removeItem('mock-produits-depot');
    this.generateMockData();
  }

  /**
   * Forcer la régénération des données pour le fournisseur connecté
   */
  refreshDataForCurrentUser(): void {
    console.log('🔄 Actualisation des données pour le fournisseur connecté...');
    this.resetMockData();
  }

  private saveMockData(): void {
    localStorage.setItem('mock-depots', JSON.stringify(this.depotsSubject.value));
    localStorage.setItem('mock-stocks-depots', JSON.stringify(this.stocksDepotsSubject.value));
  }

  private generateMockData(): void {
    // Obtenir l'ID du fournisseur connecté depuis localStorage
    const currentUser = JSON.parse(localStorage.getItem('current_user') || '{}');
    const fournisseurId = currentUser.id || 2070; // Fallback vers l'ID par défaut

    console.log('🏪 Génération des dépôts pour le fournisseur ID:', fournisseurId);

    // Générer des dépôts mock pour le fournisseur connecté
    const mockDepots: Depot[] = [
      {
        id: 1,
        nom: 'Dépôt Principal',
        adresse: '123 Rue de la Logistique',
        ville: 'Tunis',
        codePostal: '1000',
        pays: 'Tunisie',
        fournisseurId: fournisseurId,
        estActif: true,
        estPrincipal: true,
        dateCreation: new Date('2024-01-01'),
        description: 'Dépôt principal de stockage',
        capaciteMax: 10000,
        responsable: 'Ahmed Ben Ali',
        telephone: '+216 71 123 456',
        email: '<EMAIL>'
      },
      {
        id: 2,
        nom: 'Dépôt Secondaire',
        adresse: '456 Avenue du Commerce',
        ville: 'Sfax',
        codePostal: '3000',
        pays: 'Tunisie',
        fournisseurId: fournisseurId,
        estActif: true,
        estPrincipal: false,
        dateCreation: new Date('2024-02-01'),
        description: 'Dépôt secondaire pour la région Sud',
        capaciteMax: 5000,
        responsable: 'Fatma Trabelsi',
        telephone: '+216 74 987 654',
        email: '<EMAIL>'
      }
    ];

    // Générer aussi les données de produits avec stocks
    const produits = this.generateMockProduitsDepots();
    localStorage.setItem('mock-produits-depot', JSON.stringify(produits));

    this.depotsSubject.next(mockDepots);
    this.saveMockData();
  }

  private generateMockProduitsDepots(): ProduitDepot[] {
    // Générer des produits avec stocks par dépôt
    return [
      {
        id: 1,
        nom: 'Lunettes Gucci GG0001',
        referenceOriginal: 'GG0001',
        referenceFournisseur: 'FEKI-GG0001',
        imagePrincipaleUrl: '/assets/images/products/lunettes-gucci.jpg',
        prixAchatHT: 450.00,
        prixVenteHT: 555.00,
        stock: 38,
        stocksDepots: [
          {
            id: 1,
            produitId: 1,
            depotId: 1,
            quantite: 20,
            quantiteReservee: 0,
            quantiteDisponible: 20,
            seuilAlerte: 5,
            emplacementPhysique: 'A1-B2-C3',
            dateModification: new Date()
          },
          {
            id: 2,
            produitId: 1,
            depotId: 2,
            quantite: 18,
            quantiteReservee: 0,
            quantiteDisponible: 18,
            seuilAlerte: 3,
            emplacementPhysique: 'B2-A1-D4',
            dateModification: new Date()
          }
        ]
      },
      {
        id: 2,
        nom: 'Monture Ray-Ban RB2140',
        referenceOriginal: 'RB2140',
        referenceFournisseur: 'FEKI-RB2140',
        imagePrincipaleUrl: '/assets/images/products/lunettes-rayban.jpg',
        prixAchatHT: 120.00,
        prixVenteHT: 180.00,
        stock: 25,
        stocksDepots: [
          {
            id: 3,
            produitId: 2,
            depotId: 1,
            quantite: 15,
            quantiteReservee: 2,
            quantiteDisponible: 13,
            seuilAlerte: 8,
            emplacementPhysique: 'C3-D4-E5',
            dateModification: new Date()
          },
          {
            id: 4,
            produitId: 2,
            depotId: 2,
            quantite: 10,
            quantiteReservee: 0,
            quantiteDisponible: 10,
            seuilAlerte: 5,
            emplacementPhysique: 'A1-C3-F6',
            dateModification: new Date()
          }
        ]
      },
      {
        id: 3,
        nom: 'Verres Progressifs Zeiss',
        referenceOriginal: 'ZE-PROG-001',
        referenceFournisseur: 'FEKI-ZE001',
        imagePrincipaleUrl: '/assets/images/products/verres-zeiss.jpg',
        prixAchatHT: 200.00,
        prixVenteHT: 350.00,
        stock: 45,
        stocksDepots: [
          {
            id: 5,
            produitId: 3,
            depotId: 1,
            quantite: 30,
            quantiteReservee: 5,
            quantiteDisponible: 25,
            seuilAlerte: 10,
            emplacementPhysique: 'E5-F6-G7',
            dateModification: new Date()
          },
          {
            id: 6,
            produitId: 3,
            depotId: 2,
            quantite: 15,
            quantiteReservee: 0,
            quantiteDisponible: 15,
            seuilAlerte: 8,
            emplacementPhysique: 'F6-G7-H8',
            dateModification: new Date()
          }
        ]
      },
      {
        id: 4,
        nom: 'Lunettes Oakley Holbrook',
        referenceOriginal: 'OO9102',
        referenceFournisseur: 'FEKI-OO9102',
        imagePrincipaleUrl: '/assets/images/products/lunettes-oakley.jpg',
        prixAchatHT: 80.00,
        prixVenteHT: 140.00,
        stock: 12,
        stocksDepots: [
          {
            id: 7,
            produitId: 4,
            depotId: 1,
            quantite: 8,
            quantiteReservee: 1,
            quantiteDisponible: 7,
            seuilAlerte: 5,
            emplacementPhysique: 'G7-H8-I9',
            dateModification: new Date()
          },
          {
            id: 8,
            produitId: 4,
            depotId: 2,
            quantite: 4,
            quantiteReservee: 0,
            quantiteDisponible: 4,
            seuilAlerte: 3,
            emplacementPhysique: 'H8-I9-J10',
            dateModification: new Date()
          }
        ]
      }
    ];
  }
}
