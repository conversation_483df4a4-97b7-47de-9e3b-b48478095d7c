﻿using System.ComponentModel.DataAnnotations;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.DTOs.CreateDTOs
{
    public class CreatePromotionDto : IProduitsApplicablesDto, IValidatableObject
    {
        [Required(ErrorMessage = "Le type de promotion est obligatoire")]
        public TypePromotion Type { get; set; }

        [Required(ErrorMessage = "Le pourcentage de remise est obligatoire")]
        [Range(0.01, 100, ErrorMessage = "La remise doit être entre 0.01% et 100%")]
        public decimal PourcentageRemise { get; set; }

        [Required(ErrorMessage = "La date de début est obligatoire")]
        public DateTime DateDebut { get; set; }
        public DateTime? DateFin { get; set; }

        [StringLength(20, MinimumLength = 4, ErrorMessage = "Le code promo doit contenir entre 4 et 20 caractères")]
        [RegularExpression(@"^[A-Z0-9]+$", ErrorMessage = "Seuls les majuscules et chiffres sont autorisés")]
        public string? CodePromo { get; set; }

        // Produits applicables
        public int? CategorieId { get; set; }
        public int? SousCategorieId { get; set; }
        public int? MarqueId { get; set; }
        public int? FournisseurId { get; set; }
        public int? FormeId { get; set; }
        public List<int>? ProduitsApplicablesIds { get; set; }
        public bool AppliquerSurHT { get; set; } = true;

        // Propriété calculée
        public int DureeJours => DateFin.HasValue ? (int)((DateFin.Value - DateDebut).TotalDays) : 0;

        // Validation conditionnelle
        public IEnumerable<ValidationResult> Validate(ValidationContext context)
        {
            if (Type == TypePromotion.CodePromo && string.IsNullOrEmpty(CodePromo))
            {
                yield return new ValidationResult(
                    "Le code promo est obligatoire pour ce type de promotion",
                    new[] { nameof(CodePromo) });
            }

            if (!DateFin.HasValue)
            {
                yield return new ValidationResult(
                    "La date de fin est obligatoire",
                    new[] { nameof(DateFin) });
            }
            else if (DateFin.Value <= DateDebut)
            {
                yield return new ValidationResult(
                    "La date de fin doit être postérieure à la date de début",
                    new[] { nameof(DateFin) });
            }

            if (PourcentageRemise <= 0 || PourcentageRemise > 100)
            {
                yield return new ValidationResult(
                    "La remise doit être comprise entre 0.01 et 100%",
                    new[] { nameof(PourcentageRemise) });
            }
        }
    }
}
