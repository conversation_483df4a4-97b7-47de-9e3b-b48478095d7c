# Test de l'endpoint my-promotions avec authentification
$baseUrl = "http://localhost:5014/api"

Write-Host "=== TEST MY-PROMOTIONS AVEC AUTHENTIFICATION ===" -ForegroundColor Cyan

# 1. Connexion d'un fournisseur
Write-Host "`n1. Connexion du fournisseur..." -ForegroundColor Yellow

$loginData = @{
    email = "<EMAIL>"
    motDePasse = "123456"
} | ConvertTo-Json

$headers = @{"Content-Type" = "application/json"}

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
    Write-Host "✅ Connexion réussie !" -ForegroundColor Green
    Write-Host "Fournisseur: $($loginResponse.utilisateur.nom) $($loginResponse.utilisateur.prenom)" -ForegroundColor White
    Write-Host "Role: $($loginResponse.utilisateur.roleDiscriminator)" -ForegroundColor White
    
    # Ajouter le token aux headers
    $headers["Authorization"] = "Bearer $($loginResponse.token)"
    
} catch {
    Write-Host "❌ Erreur de connexion: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
    exit 1
}

# 2. Test de l'endpoint my-promotions
Write-Host "`n2. Test de l'endpoint my-promotions..." -ForegroundColor Yellow

try {
    $myPromotions = Invoke-RestMethod -Uri "$baseUrl/promotions/my-promotions" -Method Get -Headers $headers
    Write-Host "✅ My-promotions récupérées: $($myPromotions.Count)" -ForegroundColor Green
    
    if ($myPromotions.Count -gt 0) {
        Write-Host "Mes promotions:" -ForegroundColor White
        foreach ($promo in $myPromotions) {
            $status = if ($promo.estValide) { "✅ Active" } else { "❌ Inactive" }
            Write-Host "  - $($promo.nomAffichage) ($($promo.type)) - $($promo.pourcentageRemise)% - $status" -ForegroundColor Gray
            if ($promo.codePromo) {
                Write-Host "    Code: $($promo.codePromo)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "Aucune promotion trouvée pour ce fournisseur" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Erreur my-promotions: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 3. Créer une promotion de test
Write-Host "`n3. Création d'une promotion de test..." -ForegroundColor Yellow

$promoData = @{
    type = "CodePromo"
    pourcentageRemise = 25
    dateDebut = (Get-Date).ToString("yyyy-MM-dd")
    dateFin = (Get-Date).AddDays(30).ToString("yyyy-MM-dd")
    codePromo = "TEST25"
    nomAffichage = "Promotion Test Fournisseur"
    description = "Promotion de test créée via API"
    appliquerSurHT = $false
} | ConvertTo-Json

try {
    $newPromo = Invoke-RestMethod -Uri "$baseUrl/promotions" -Method Post -Headers $headers -Body $promoData
    Write-Host "✅ Promotion créée !" -ForegroundColor Green
    Write-Host "ID: $($newPromo.id) - Nom: $($newPromo.nomAffichage)" -ForegroundColor White
    
} catch {
    Write-Host "❌ Erreur création promotion: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 4. Vérifier à nouveau my-promotions
Write-Host "`n4. Vérification finale my-promotions..." -ForegroundColor Yellow

try {
    $finalPromotions = Invoke-RestMethod -Uri "$baseUrl/promotions/my-promotions" -Method Get -Headers $headers
    Write-Host "✅ Promotions finales: $($finalPromotions.Count)" -ForegroundColor Green
    
    foreach ($promo in $finalPromotions) {
        $status = if ($promo.estValide) { "✅ Active" } else { "❌ Inactive" }
        Write-Host "  - $($promo.nomAffichage) ($($promo.type)) - $($promo.pourcentageRemise)% - $status" -ForegroundColor White
        if ($promo.codePromo) {
            Write-Host "    Code: $($promo.codePromo)" -ForegroundColor Gray
        }
    }
    
} catch {
    Write-Host "❌ Erreur vérification finale: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== FIN DU TEST ===" -ForegroundColor Cyan
