import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { NotificationService } from '../../services/notification.service';

@Component({
  selector: 'app-test-validation',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="test-validation-container">
      <h2>🧪 Test de Validation des Fournisseurs</h2>
      
      <div class="test-section">
        <h3>📝 Test d'inscription avec notification</h3>
        <button 
          class="btn btn-primary" 
          (click)="testInscriptionNotification()"
          [disabled]="isLoading">
          {{ isLoading ? 'Test en cours...' : 'Tester Inscription + Notification' }}
        </button>
      </div>

      <div class="test-section">
        <h3>🔐 Test de connexion avec validation</h3>
        <div class="form-group">
          <label>Email de test :</label>
          <input 
            type="email" 
            [(ngModel)]="testEmail" 
            class="form-control"
            placeholder="<EMAIL>">
        </div>
        <div class="form-group">
          <label>Mot de passe :</label>
          <input 
            type="password" 
            [(ngModel)]="testPassword" 
            class="form-control"
            placeholder="password123">
        </div>
        <button 
          class="btn btn-secondary" 
          (click)="testConnexionValidation()"
          [disabled]="isLoading">
          {{ isLoading ? 'Test en cours...' : 'Tester Connexion' }}
        </button>
      </div>

      <div class="test-section">
        <h3>📧 Test de notification aux admins</h3>
        <div class="form-group">
          <label>Message de test :</label>
          <textarea 
            [(ngModel)]="testMessage" 
            class="form-control"
            rows="3"
            placeholder="Message de test pour les admins..."></textarea>
        </div>
        <button 
          class="btn btn-info" 
          (click)="testNotificationAdmins()"
          [disabled]="isLoading">
          {{ isLoading ? 'Envoi en cours...' : 'Envoyer Notification Test' }}
        </button>
      </div>

      <!-- Résultats des tests -->
      <div class="results-section" *ngIf="testResults.length > 0">
        <h3>📊 Résultats des Tests</h3>
        <div class="result-item" 
             *ngFor="let result of testResults" 
             [ngClass]="{'success': result.success, 'error': !result.success}">
          <strong>{{ result.test }}</strong>: {{ result.message }}
          <small class="timestamp">{{ result.timestamp | date:'HH:mm:ss' }}</small>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .test-validation-container {
      max-width: 800px;
      margin: 20px auto;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .test-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      background: #f9f9f9;
    }

    .test-section h3 {
      margin-top: 0;
      color: #333;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 600;
      color: #555;
    }

    .form-control {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      transition: background-color 0.3s;
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .btn-primary {
      background-color: #007bff;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background-color: #0056b3;
    }

    .btn-secondary {
      background-color: #6c757d;
      color: white;
    }

    .btn-secondary:hover:not(:disabled) {
      background-color: #545b62;
    }

    .btn-info {
      background-color: #17a2b8;
      color: white;
    }

    .btn-info:hover:not(:disabled) {
      background-color: #117a8b;
    }

    .results-section {
      margin-top: 30px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 6px;
    }

    .result-item {
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 4px;
      position: relative;
    }

    .result-item.success {
      background-color: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
    }

    .result-item.error {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
    }

    .timestamp {
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 12px;
      opacity: 0.7;
    }
  `]
})
export class TestValidationComponent implements OnInit {
  isLoading = false;
  testEmail = '<EMAIL>';
  testPassword = 'password123';
  testMessage = '🧪 Test de notification depuis le composant de test - Nouveau fournisseur inscrit !';
  testResults: Array<{test: string, message: string, success: boolean, timestamp: Date}> = [];

  constructor(
    private authService: AuthService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.addResult('Initialisation', 'Composant de test chargé avec succès', true);
  }

  testInscriptionNotification(): void {
    this.isLoading = true;
    this.addResult('Inscription + Notification', 'Test en cours...', true);
    
    // Simuler une inscription réussie
    setTimeout(() => {
      this.addResult('Inscription', 'Simulation d\'inscription réussie', true);
      
      // Tester l'envoi de notification
      this.testNotificationAdmins();
    }, 1000);
  }

  testConnexionValidation(): void {
    if (!this.testEmail || !this.testPassword) {
      this.addResult('Connexion', 'Veuillez remplir l\'email et le mot de passe', false);
      return;
    }

    this.isLoading = true;
    this.addResult('Connexion', 'Test de connexion en cours...', true);

    this.authService.login({
      email: this.testEmail,
      motDePasse: this.testPassword
    }).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.addResult('Connexion', 'Connexion réussie !', true);
      },
      error: (error) => {
        this.isLoading = false;
        if (error.message === 'VALIDATION_PENDING') {
          this.addResult('Connexion', 'Test réussi : Compte en attente de validation détecté', true);
        } else if (error.message === 'VALIDATION_REJECTED') {
          this.addResult('Connexion', 'Test réussi : Compte rejeté détecté', true);
        } else if (error.message === 'ACCOUNT_SUSPENDED') {
          this.addResult('Connexion', 'Test réussi : Compte suspendu détecté', true);
        } else {
          this.addResult('Connexion', `Erreur : ${error.error?.message || error.message}`, false);
        }
      }
    });
  }

  testNotificationAdmins(): void {
    if (!this.testMessage.trim()) {
      this.addResult('Notification', 'Veuillez saisir un message de test', false);
      return;
    }

    this.isLoading = true;
    this.addResult('Notification', 'Envoi de notification aux admins...', true);

    this.notificationService.notifierTousLesAdmins(this.testMessage).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.addResult('Notification', 'Notification envoyée avec succès aux admins !', true);
      },
      error: (error) => {
        this.isLoading = false;
        this.addResult('Notification', `Erreur lors de l'envoi : ${error.error?.message || error.message}`, false);
      }
    });
  }

  private addResult(test: string, message: string, success: boolean): void {
    this.testResults.unshift({
      test,
      message,
      success,
      timestamp: new Date()
    });

    // Garder seulement les 10 derniers résultats
    if (this.testResults.length > 10) {
      this.testResults = this.testResults.slice(0, 10);
    }
  }
}
