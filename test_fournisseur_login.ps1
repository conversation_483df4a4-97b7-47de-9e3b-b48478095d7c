# Test de connexion fournisseur avec accès libre
Write-Host "🧪 Test de connexion fournisseur avec accès libre" -ForegroundColor Green

# Configuration
$baseUrl = "http://localhost:5014"
$loginUrl = "$baseUrl/api/Auth/login"

# Données de test pour un fournisseur existant
$loginData = @{
    Email = "<EMAIL>"
    MotDePasse = "Test123!"
} | ConvertTo-Json

Write-Host "📧 Test de connexion avec: <EMAIL>" -ForegroundColor Yellow

try {
    # Test de connexion
    $response = Invoke-RestMethod -Uri $loginUrl -Method POST -Body $loginData -ContentType "application/json"
    
    Write-Host "✅ Connexion réussie !" -ForegroundColor Green
    Write-Host "🔑 Token reçu: $($response.token.Substring(0, 50))..." -ForegroundColor Cyan
    Write-Host "👤 Utilisateur: $($response.utilisateur.nom) $($response.utilisateur.prenom)" -ForegroundColor Cyan
    Write-Host "🏪 Raison sociale: $($response.utilisateur.raisonSociale)" -ForegroundColor Cyan
    Write-Host "📊 Statut validation: $($response.utilisateur.statutValidation)" -ForegroundColor Cyan
    
    if ($response.utilisateur.statutValidation -eq 1) {
        Write-Host "🎉 SUCCÈS: Le fournisseur peut se connecter sans validation !" -ForegroundColor Green
    } else {
        Write-Host "❌ ÉCHEC: Le fournisseur nécessite encore une validation" -ForegroundColor Red
    }
    
} catch {
    $errorDetails = $_.Exception.Response
    if ($errorDetails) {
        $reader = New-Object System.IO.StreamReader($errorDetails.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "❌ Erreur de connexion:" -ForegroundColor Red
        Write-Host $responseBody -ForegroundColor Red
    } else {
        Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🔍 Test terminé" -ForegroundColor Blue
