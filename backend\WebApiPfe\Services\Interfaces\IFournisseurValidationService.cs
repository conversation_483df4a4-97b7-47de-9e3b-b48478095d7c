using WebApiPfe.DTOs.Admin;

namespace WebApiPfe.Services.Interfaces
{
    public interface IFournisseurValidationService
    {
        Task<List<FournisseurValidationDto>> GetFournisseursEnAttenteAsync();
        Task<List<FournisseurValidationDto>> GetTousFournisseursAvecStatutAsync();
        Task<FournisseurValidationDto?> GetFournisseurValidationByIdAsync(int id);
        Task<bool> ValiderFournisseurAsync(int fournisseurId, int adminId, bool accepter, string? commentaire = null);
        Task<bool> SuspendreReactiverFournisseurAsync(int fournisseurId, int adminId, bool suspendre, string? commentaire = null);
    }
}
