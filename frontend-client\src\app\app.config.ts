import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { providePrimeNG } from 'primeng/config';
import Aura from '@primeng/themes/aura';
import { provideRouter, Routes } from '@angular/router';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { authInterceptor } from './auth/auth.interceptor';
import { BrowserModule, provideClientHydration } from '@angular/platform-browser';
import { provideAnimations } from '@angular/platform-browser/animations';

const routes: Routes = [
  { path: '', loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent)
  },
  { path: 'auth', loadComponent: () => import('./auth/auth.component').then(m => m.AuthComponent) }
];

export const appConfig: ApplicationConfig = {
    providers: [
        provideAnimations(),
        provideAnimationsAsync(),
        provideClientHydration(),
        providePrimeNG({
            theme: {
                preset: Aura,
                options: {
                    darkModeSelector: '.my-app-dark'
                }
            }
        }),
        provideRouter(routes),
        provideHttpClient( withInterceptors([authInterceptor])),
        importProvidersFrom(BrowserModule)
    ]
};
