import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './pages/home/<USER>';
import { ProductDetailsComponent } from './pages/product-details/product-details.component';
import { ProductsByCategoryComponent } from './components/products-by-category/products-by-category.component';
import { ProductsBySubcategoryComponent } from './components/products-by-subcategory/products-by-subcategory.component';
import { ProductsByFormComponent } from './components/products-by-form/products-by-form.component';
import { ProductsSearchComponent } from './components/products-search/products-search.component';
import { ProductsByMarqueComponent } from './components/products-by-marque/products-by-marque.component';
import { ProductsByFournisseurComponent } from './components/Products-By-fournisseur/products-by-fournisseur.component';
import { MarquesAllComponent } from './components/marques-all/marques-all.component';
import { FournisseursAllComponent } from './components/fournisseurs-all/fournisseurs-all.component';

const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'marques', component: MarquesAllComponent },
  { path: 'fournisseurs', component: FournisseursAllComponent },
  { path: 'products/category/:id', component: ProductsByCategoryComponent },
  { path: 'products/subcategory/:id',component: ProductsBySubcategoryComponent,},
  { path: 'products/marque/:id', component: ProductsByMarqueComponent },
  { path: 'products/fournisseur/:id', component: ProductsByFournisseurComponent },
  { path: 'products/form/:id', component: ProductsByFormComponent },
  { path: 'products/search', component: ProductsSearchComponent },
  { path: 'products/:id', component: ProductDetailsComponent },
  { path: 'auth', loadChildren: () => import('./auth/auth.module').then(m => m.AuthModule) },
  { path: 'user', loadChildren: () => import('./user/user.module').then(m => m.UserModule) },  
  { path: '**', redirectTo: '', pathMatch: 'full' },
];
@NgModule({
  declarations: [],
  imports: [CommonModule, RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
