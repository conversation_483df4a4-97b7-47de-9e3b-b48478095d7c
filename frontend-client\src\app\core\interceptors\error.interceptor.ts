import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar'; 

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  constructor(
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  intercept(
    request: HttpRequest<unknown>,
    next: HttpHandler
  ): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        let errorMessage = 'Une erreur inconnue est survenue';
        
        switch (true) {
          case error.status === 0:
            errorMessage = 'Serveur inaccessible (erreur réseau)';
            break;
            
          case error.status >= 400 && error.status < 500:
            errorMessage = this.handleClientError(error);
            break;
            
          case error.status >= 500:
            errorMessage = 'Erreur serveur (500+)';
            break;
        }

        this.snackBar.open(errorMessage, 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });

        if (error.status === 401) {
          this.router.navigate(['/auth/login']);
        }

        return throwError(() => error);
      })
    );
  }

  private handleClientError(error: HttpErrorResponse): string {
    if (error.error?.message) {
      return error.error.message;
    }
    const errors: { [key: number]: string } = {
      400: 'Requête incorrecte',
      403: 'Accès refusé',
      404: 'Ressource introuvable'
    };
    
    return errors[error.status] || error.message;
  }
}
