using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers.Admin
{
    [ApiController]
    [Route("api/admin/[controller]")]
    [Authorize(Roles = "Admin")]
    public class FournisseurValidationController : ControllerBase
    {
        private readonly IFournisseurValidationService _validationService;
        private readonly ILogger<FournisseurValidationController> _logger;

        public FournisseurValidationController(
            IFournisseurValidationService validationService,
            ILogger<FournisseurValidationController> logger)
        {
            _validationService = validationService;
            _logger = logger;
        }

        [HttpGet("en-attente")]
        public async Task<ActionResult<List<FournisseurValidationDto>>> GetFournisseursEnAttente()
        {
            try
            {
                var fournisseurs = await _validationService.GetFournisseursEnAttenteAsync();
                return Ok(fournisseurs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des fournisseurs en attente");
                return StatusCode(500, "Erreur interne");
            }
        }

        [HttpGet("tous")]
        public async Task<ActionResult<List<FournisseurValidationDto>>> GetTousFournisseurs()
        {
            try
            {
                var fournisseurs = await _validationService.GetTousFournisseursAvecStatutAsync();
                return Ok(fournisseurs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de tous les fournisseurs");
                return StatusCode(500, "Erreur interne");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<FournisseurValidationDto>> GetFournisseurById(int id)
        {
            try
            {
                var fournisseur = await _validationService.GetFournisseurValidationByIdAsync(id);
                if (fournisseur == null)
                    return NotFound("Fournisseur introuvable");

                return Ok(fournisseur);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération du fournisseur {id}");
                return StatusCode(500, "Erreur interne");
            }
        }

        [HttpPost("valider")]
        public async Task<IActionResult> ValiderFournisseur([FromBody] ValiderFournisseurRequest request)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var adminId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                
                var success = await _validationService.ValiderFournisseurAsync(
                    request.FournisseurId, 
                    adminId, 
                    request.Accepter, 
                    request.Commentaire);

                if (!success)
                    return NotFound("Fournisseur introuvable");

                var action = request.Accepter ? "validé" : "rejeté";
                return Ok(new { Message = $"Fournisseur {action} avec succès" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la validation du fournisseur {request.FournisseurId}");
                return StatusCode(500, "Erreur interne");
            }
        }

        [HttpPost("suspendre")]
        public async Task<IActionResult> SuspendreReactiverFournisseur([FromBody] SuspendreReactiverRequest request)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var adminId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                
                var success = await _validationService.SuspendreReactiverFournisseurAsync(
                    request.FournisseurId, 
                    adminId, 
                    request.Suspendre, 
                    request.Commentaire);

                if (!success)
                    return NotFound("Fournisseur introuvable");

                var action = request.Suspendre ? "suspendu" : "réactivé";
                return Ok(new { Message = $"Fournisseur {action} avec succès" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la suspension/réactivation du fournisseur {request.FournisseurId}");
                return StatusCode(500, "Erreur interne");
            }
        }
    }

    public class SuspendreReactiverRequest
    {
        public int FournisseurId { get; set; }
        public bool Suspendre { get; set; } // true = suspendre, false = réactiver
        public string? Commentaire { get; set; }
    }
}
