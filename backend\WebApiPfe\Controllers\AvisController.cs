﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Models.Entity;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AvisController : ControllerBase
    {
        private readonly IAvisService _avisService;
        private readonly IClientService _clientService;


        public AvisController(IAvisService avisService, IClientService clientService)
        {
            _avisService = avisService;
            _clientService = clientService;
        }
        [HttpGet("produit/{produitId}")]
        public async Task<ActionResult<List<AvisDto>>> GetAvisByProduit(int produitId)
        {
            var avis = await _avisService.GetAvisByProduitAsync(produitId);
            return Ok(avis);
        }

        [HttpGet("{produitId}/mon-avis")]
        [Authorize]
        public async Task<ActionResult<AvisDto?>> GetMonAvis(int produitId, [FromServices] ICurrentUserService currentUserService)
        {
            try
            {
                var clientId = await currentUserService.GetCurrentClientId();
                var monAvis = await _avisService.GetAvisClientProduitAsync(clientId, produitId);
                return Ok(monAvis);
            }
            catch (Exception ex)
            {
                return BadRequest($"Erreur lors de la récupération de l'avis: {ex.Message}");
            }
        }

        [HttpGet("{produitId}/{id}")]
        public async Task<ActionResult<AvisDto>> GetAvis(int produitId, int id)
        {
            var avis = await _avisService.GetByIdAsync(produitId, id);
            return avis != null ? Ok(avis) : NotFound();
        }
        [HttpPost("{produitId}")]
        public async Task<ActionResult<AvisDto>> AjouterAvis(
            int produitId,
            [FromBody] AvisCreateDto avisDto,
            [FromServices] ICurrentUserService currentUserService)
        {
            try
            {
                var clientId = await currentUserService.GetCurrentClientId();
                if (clientId == 0)
                    return Unauthorized("Client non identifié");

                avisDto.ProduitId = produitId;

                var result = await _avisService.AjouterAvisAsync(avisDto, clientId);
                return CreatedAtAction(
                    nameof(GetAvis),
                    new { produitId = result.ProduitId, id = result.Id },
                    result);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(ex.Message);
            }
        }

        [HttpGet("{produitId}/moyenne")]
        public async Task<ActionResult<double>> GetMoyenne(int produitId)
        {
            return Ok(await _avisService.GetNoteMoyenneAsync(produitId));
        }
    }
}
