using AutoMapper;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Entity;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Services.Implementations
{
    public class FournisseurValidationService : IFournisseurValidationService
    {
        private readonly AppDbContext _context;
        private readonly IMapper _mapper;
        private readonly INotificationService _notificationService;

        public FournisseurValidationService(
            AppDbContext context, 
            IMapper mapper,
            INotificationService notificationService)
        {
            _context = context;
            _mapper = mapper;
            _notificationService = notificationService;
        }

        public async Task<List<FournisseurValidationDto>> GetFournisseursEnAttenteAsync()
        {
            var fournisseurs = await _context.Users
                .OfType<Fournisseur>()
                .Where(f => f.StatutValidation == StatutValidationFournisseur.EnAttente)
                .Include(f => f.Adresses)
                .OrderBy(f => f.DateInscription)
                .ToListAsync();

            return _mapper.Map<List<FournisseurValidationDto>>(fournisseurs);
        }

        public async Task<List<FournisseurValidationDto>> GetTousFournisseursAvecStatutAsync()
        {
            var fournisseurs = await _context.Users
                .OfType<Fournisseur>()
                .Include(f => f.Adresses)
                .OrderByDescending(f => f.DateInscription)
                .ToListAsync();

            return _mapper.Map<List<FournisseurValidationDto>>(fournisseurs);
        }

        public async Task<FournisseurValidationDto?> GetFournisseurValidationByIdAsync(int id)
        {
            var fournisseur = await _context.Users
                .OfType<Fournisseur>()
                .Include(f => f.Adresses)
                .FirstOrDefaultAsync(f => f.Id == id);

            return fournisseur != null ? _mapper.Map<FournisseurValidationDto>(fournisseur) : null;
        }

        public async Task<bool> ValiderFournisseurAsync(int fournisseurId, int adminId, bool accepter, string? commentaire = null)
        {
            var fournisseur = await _context.Users
                .OfType<Fournisseur>()
                .FirstOrDefaultAsync(f => f.Id == fournisseurId);

            if (fournisseur == null)
                return false;

            // Mettre à jour le statut
            fournisseur.StatutValidation = accepter ? StatutValidationFournisseur.Valide : StatutValidationFournisseur.Rejete;
            fournisseur.DateValidation = DateTime.UtcNow;
            fournisseur.ValidePar = adminId;
            fournisseur.CommentaireValidation = commentaire;
            fournisseur.EstActif = accepter; // Activer/désactiver le compte

            await _context.SaveChangesAsync();

            // Envoyer une notification au fournisseur
            var message = accepter 
                ? $"🎉 Félicitations ! Votre compte fournisseur '{fournisseur.RaisonSociale}' a été validé par l'administration. Vous pouvez maintenant accéder à toutes les fonctionnalités."
                : $"❌ Votre demande d'inscription en tant que fournisseur '{fournisseur.RaisonSociale}' a été rejetée. Raison : {commentaire ?? "Non spécifiée"}";

            await _notificationService.CreateAsync(new DTOs.CreateDTOs.CreateNotificationDto
            {
                Contenu = message,
                UtilisateurId = fournisseurId
            });

            return true;
        }

        public async Task<bool> SuspendreReactiverFournisseurAsync(int fournisseurId, int adminId, bool suspendre, string? commentaire = null)
        {
            var fournisseur = await _context.Users
                .OfType<Fournisseur>()
                .FirstOrDefaultAsync(f => f.Id == fournisseurId);

            if (fournisseur == null)
                return false;

            // Mettre à jour le statut
            fournisseur.StatutValidation = suspendre ? StatutValidationFournisseur.Suspendu : StatutValidationFournisseur.Valide;
            fournisseur.DateValidation = DateTime.UtcNow;
            fournisseur.ValidePar = adminId;
            fournisseur.CommentaireValidation = commentaire;
            fournisseur.EstActif = !suspendre;

            await _context.SaveChangesAsync();

            // Envoyer une notification au fournisseur
            var message = suspendre 
                ? $"⚠️ Votre compte fournisseur '{fournisseur.RaisonSociale}' a été suspendu. Raison : {commentaire ?? "Non spécifiée"}"
                : $"✅ Votre compte fournisseur '{fournisseur.RaisonSociale}' a été réactivé.";

            await _notificationService.CreateAsync(new DTOs.CreateDTOs.CreateNotificationDto
            {
                Contenu = message,
                UtilisateurId = fournisseurId
            });

            return true;
        }
    }
}
