/*  */import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { delay, map, tap } from 'rxjs/operators';
import { ADMIN_CONFIG, ADMIN_USER_INFO } from '../config/admin.config';
import { AdminUser, AdminRole, LoginRequest, OTPRequest, LoginResponse, Permission, PermissionAction } from '../models/admin.model';

@Injectable({
  providedIn: 'root'
})
export class AdminAuthService {
  private currentUserSubject = new BehaviorSubject<AdminUser | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  private sessionTokenSubject = new BehaviorSubject<string | null>(null);

  public currentUser$ = this.currentUserSubject.asObservable();
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor() {
    // Vérifier si l'utilisateur est déjà connecté
    this.checkStoredAuth();
  }

  /**
   * Connexion avec email et mot de passe
   */
  login(credentials: LoginRequest): Observable<LoginResponse> {
    return of(null).pipe(
      delay(1000), // Simulation délai réseau
      map(() => {
        // Validation avec les credentials de configuration OptiLet
        const user = this.validateAdminCredentials(credentials.email, credentials.password);

        if (!user) {
          throw new Error('Nom d\'utilisateur ou mot de passe incorrect');
        }

        const sessionToken = this.generateSessionToken();
        this.sessionTokenSubject.next(sessionToken);

        // Si 2FA activé, demander OTP
        if (user.twoFactorEnabled) {
          return {
            success: true,
            requiresOTP: true,
            sessionToken: sessionToken,
            message: 'Code OTP envoyé par email'
          };
        }

        // Connexion directe si pas de 2FA
        return this.completeLogin(user, sessionToken);
      })
    );
  }

  /**
   * Vérification du code OTP
   */
  verifyOTP(otpRequest: OTPRequest): Observable<LoginResponse> {
    return of(null).pipe(
      delay(500),
      map(() => {
        // Simulation validation OTP (code: 123456)
        if (otpRequest.otpCode !== '123456') {
          throw new Error('Code OTP invalide');
        }

        const user = this.findUserByEmail(otpRequest.email);
        if (!user) {
          throw new Error('Utilisateur non trouvé');
        }

        return this.completeLogin(user, otpRequest.sessionToken);
      })
    );
  }

  /**
   * Finaliser la connexion
   */
  private completeLogin(user: AdminUser, sessionToken: string): LoginResponse {
    const accessToken = this.generateAccessToken();
    const refreshToken = this.generateRefreshToken();

    // Mettre à jour la dernière connexion
    user.lastLogin = new Date();

    // Stocker les informations de session
    this.storeAuthData(user, accessToken, refreshToken, sessionToken);

    // Mettre à jour les subjects
    this.currentUserSubject.next(user);
    this.isAuthenticatedSubject.next(true);

    return {
      success: true,
      requiresOTP: false,
      accessToken: accessToken,
      refreshToken: refreshToken,
      user: user,
      expiresIn: user.sessionTimeout,
      message: 'Connexion réussie'
    };
  }

  /**
   * Déconnexion
   */
  logout(): Observable<boolean> {
    return of(true).pipe(
      delay(300),
      tap(() => {
        this.clearAuthData();
        this.currentUserSubject.next(null);
        this.isAuthenticatedSubject.next(false);
        this.sessionTokenSubject.next(null);
      })
    );
  }

  /**
   * Rafraîchir le token
   */
  refreshToken(): Observable<string> {
    const refreshToken = localStorage.getItem('admin_refresh_token');
    if (!refreshToken) {
      return throwError(() => new Error('Aucun refresh token'));
    }

    return of(this.generateAccessToken()).pipe(
      delay(300),
      tap(newToken => {
        localStorage.setItem('admin_access_token', newToken);
      })
    );
  }

  /**
   * Vérifier les permissions
   */
  hasPermission(resource: string, action: PermissionAction): boolean {
    const user = this.currentUserSubject.value;
    if (!user) return false;

    return user.permissions.some(p => 
      p.resource === resource && p.action === action
    );
  }

  /**
   * Vérifier le rôle
   */
  hasRole(role: AdminRole): boolean {
    const user = this.currentUserSubject.value;
    return user?.role === role || false;
  }

  /**
   * Vérifier si super admin
   */
  isSuperAdmin(): boolean {
    return this.hasRole(AdminRole.SUPER_ADMIN);
  }

  /**
   * Obtenir l'utilisateur actuel
   */
  getCurrentUser(): AdminUser | null {
    return this.currentUserSubject.value;
  }

  /**
   * Vérifier l'authentification stockée
   */
  private checkStoredAuth(): void {
    const token = localStorage.getItem('admin_access_token');
    const userStr = localStorage.getItem('admin_user');

    if (token && userStr) {
      try {
        const user = JSON.parse(userStr) as AdminUser;
        this.currentUserSubject.next(user);
        this.isAuthenticatedSubject.next(true);
      } catch (error) {
        this.clearAuthData();
      }
    }
  }

  /**
   * Stocker les données d'authentification
   */
  private storeAuthData(user: AdminUser, accessToken: string, refreshToken: string, sessionToken: string): void {
    localStorage.setItem('admin_access_token', accessToken);
    localStorage.setItem('admin_refresh_token', refreshToken);
    localStorage.setItem('admin_session_token', sessionToken);
    localStorage.setItem('admin_user', JSON.stringify(user));
  }

  /**
   * Effacer les données d'authentification
   */
  private clearAuthData(): void {
    localStorage.removeItem('admin_access_token');
    localStorage.removeItem('admin_refresh_token');
    localStorage.removeItem('admin_session_token');
    localStorage.removeItem('admin_user');
  }

  /**
   * Générer un token de session
   */
  private generateSessionToken(): string {
    return 'session_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }

  /**
   * Générer un access token
   */
  private generateAccessToken(): string {
    return 'access_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }

  /**
   * Générer un refresh token
   */
  private generateRefreshToken(): string {
    return 'refresh_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }

  /**
   * Obtenir toutes les permissions (Super Admin)
   */
  private getAllPermissions(): Permission[] {
    return [
      { id: 1, name: 'Créer utilisateur', resource: 'users', action: PermissionAction.CREATE, description: 'Créer de nouveaux utilisateurs' },
      { id: 2, name: 'Lire utilisateur', resource: 'users', action: PermissionAction.READ, description: 'Consulter les utilisateurs' },
      { id: 3, name: 'Modifier utilisateur', resource: 'users', action: PermissionAction.UPDATE, description: 'Modifier les utilisateurs' },
      { id: 4, name: 'Supprimer utilisateur', resource: 'users', action: PermissionAction.DELETE, description: 'Supprimer les utilisateurs' },
      { id: 5, name: 'Approuver produit', resource: 'products', action: PermissionAction.APPROVE, description: 'Approuver les produits' },
      { id: 6, name: 'Exporter données', resource: 'data', action: PermissionAction.EXPORT, description: 'Exporter les données' },
      { id: 7, name: 'Consulter audit', resource: 'audit', action: PermissionAction.AUDIT, description: 'Consulter les logs d\'audit' }
    ];
  }

  /**
   * Obtenir les permissions modérateur
   */
  private getModeratorPermissions(): Permission[] {
    return [
      { id: 2, name: 'Lire utilisateur', resource: 'users', action: PermissionAction.READ, description: 'Consulter les utilisateurs' },
      { id: 5, name: 'Approuver produit', resource: 'products', action: PermissionAction.APPROVE, description: 'Approuver les produits' },
      { id: 6, name: 'Exporter données', resource: 'data', action: PermissionAction.EXPORT, description: 'Exporter les données' }
    ];
  }

  /**
   * Vérifier les credentials admin (username/password depuis la configuration)
   */
  private validateAdminCredentials(email: string, password: string): AdminUser | undefined {
    // Vérifier si c'est l'admin OptiLet avec les credentials de configuration
    // Le champ 'email' du formulaire contient en fait le username
    if (email === ADMIN_CONFIG.username && password === ADMIN_CONFIG.password) {
      return {
        id: ADMIN_USER_INFO.id,
        email: ADMIN_USER_INFO.email,
        nom: ADMIN_USER_INFO.nom,
        prenom: ADMIN_USER_INFO.prenom,
        role: AdminRole.SUPER_ADMIN,
        permissions: this.getAllPermissions(),
        isActive: ADMIN_USER_INFO.isActive,
        lastLogin: new Date(),
        createdAt: ADMIN_USER_INFO.createdAt,
        updatedAt: new Date(),
        profileImage: ADMIN_USER_INFO.profileImage,
        phoneNumber: ADMIN_USER_INFO.phoneNumber,
        department: ADMIN_USER_INFO.department,
        twoFactorEnabled: ADMIN_USER_INFO.twoFactorEnabled,
        sessionTimeout: ADMIN_USER_INFO.sessionTimeout
      };
    }

    return undefined;
  }

  /**
   * Trouver un utilisateur par son email (pour compatibilité avec OTP)
   */
  private findUserByEmail(email: string): AdminUser | undefined {
    // Pour l'admin OptiLet, on utilise l'email de configuration
    if (email === ADMIN_USER_INFO.email) {
      return {
        id: ADMIN_USER_INFO.id,
        email: ADMIN_USER_INFO.email,
        nom: ADMIN_USER_INFO.nom,
        prenom: ADMIN_USER_INFO.prenom,
        role: AdminRole.SUPER_ADMIN,
        permissions: this.getAllPermissions(),
        isActive: ADMIN_USER_INFO.isActive,
        lastLogin: new Date(),
        createdAt: ADMIN_USER_INFO.createdAt,
        updatedAt: new Date(),
        profileImage: ADMIN_USER_INFO.profileImage,
        phoneNumber: ADMIN_USER_INFO.phoneNumber,
        department: ADMIN_USER_INFO.department,
        twoFactorEnabled: ADMIN_USER_INFO.twoFactorEnabled,
        sessionTimeout: ADMIN_USER_INFO.sessionTimeout
      };
    }

    return undefined;
  }
}
