import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import {
  BehaviorSubject,
  catchError,
  map,
  Observable,
  of,
  Subject,
  tap,
} from 'rxjs';
import { LoginDto } from '../shared/models/login.dto';
import { AuthResponseDto } from '../shared/models/auth-response.dto';
import { RegisterDto } from '../shared/models/register.dto';
import { ClientDto } from '../models/ClientDto';
import { JwtHelperService } from '@auth0/angular-jwt';
import { UtilisateurReadDto } from '../models/UtilisateurReadDto';
import { RegisterResponseDto } from '../models/RegisterResponseDto';
import { AdresseDto } from '../models/AdresseDto';
import { TokenStorageService } from '../shared/services/token-storage.service';
import { ClientService } from '../services/client.service';

@Injectable({ providedIn: 'root' })
export class AuthService {
  private readonly API_URL = `${environment.apiUrl}/auth`;
  public currentUser$ = new BehaviorSubject<ClientDto | null>(null);
  private logoutEvent$ = new Subject<void>();
  isLoggedIn(): boolean {
    return !!this.tokenService.getToken();
  }
  constructor(
    private http: HttpClient,
    private jwtHelper: JwtHelperService,
    private tokenService: TokenStorageService,
    private clientService: ClientService
  ) {
    this.initializeUserFromStorage();
  }

  initializeUserFromStorage(): void {
    console.log('🚀 AuthService.initializeUserFromStorage() appelé');

    // Debug localStorage
    this.debugLocalStorage();

    // Migration des anciennes clés si nécessaire
    this.migrateOldTokens();

    const token = this.tokenService.getToken();
    console.log('Token récupéré depuis TokenStorageService:', token ? 'Présent' : 'Absent');

    if (!token) {
      console.log('❌ Aucun token trouvé, arrêt de l\'initialisation');
      return;
    }

    console.log('Token value (50 premiers caractères):', token.substring(0, 50) + '...');

    try {
      console.log('🔍 Vérification de l\'expiration du token...');
      if (this.jwtHelper.isTokenExpired(token)) {
        console.log('❌ Token expiré');
        throw new Error('Token expiré');
      }
      console.log('✅ Token valide, décodage...');

      const user = this.decodeToken(token);
      console.log('👤 Utilisateur décodé:', user);

      console.log('📝 Mise à jour du BehaviorSubject currentUser$...');
      this.currentUser$.next(user);
      console.log('✅ BehaviorSubject mis à jour, valeur actuelle:', this.currentUser$.value);

      console.log('🔄 Chargement des données complètes du client...');
      this.loadFullClientData();
    } catch (e) {
      console.error("❌ Erreur d'initialisation:", e);
      console.log('🧹 Nettoyage des tokens...');
      this.tokenService.clearAll();
    }
  }

  onLogout(): Observable<void> {
    return this.logoutEvent$.asObservable();
  }

  logout(): void {
    console.log('🚪 Déconnexion en cours...');
    this.tokenService.clearAll();
    this.currentUser$.next(null);
    this.logoutEvent$.next();
    console.log('✅ Déconnexion terminée');
  }
  private decodeToken(token: string): ClientDto | null {
    if (!token) return null;

    try {
      const decoded = this.jwtHelper.decodeToken(token);
      if (!decoded) return null;

      console.log('Decoded Token:', decoded);
      return {
        id: Number(decoded.sub),
        nom: decoded.nom ?? '',
        prenom: decoded.prenom ?? '',
        email: decoded.email ?? '',
        phoneNumber: decoded.phoneNumber ?? '',
        role: decoded.role ?? 'Client',
        dateInscription: new Date(decoded.dateInscription),
        dateNaissance: new Date(decoded.dateNaissance),
        derniereConnexion: decoded.derniereConnexion
          ? new Date(decoded.derniereConnexion)
          : null,
        adresses: [],
        estActif: decoded.estActif ?? true,
        nombreCommandes: 0,
        adresseLivraison: undefined,
      };
    } catch (e) {
      console.error('Erreur de décodage:', e);
      return null;
    }
  }

  private getAdresseLivraison(adresses: AdresseDto[]): AdresseDto | undefined {
    if (!adresses || !Array.isArray(adresses)) {
      return undefined;
    }
    const adressesValides = adresses.filter(
      (a): a is AdresseDto =>
        typeof a === 'object' && a !== null && 'rue' in a && 'ville' in a
    );
    return adressesValides.find((a) => a.estPrincipale) || adressesValides[0];
  }

  login(credentials: LoginDto): Observable<AuthResponseDto | null> {
    return this.http
      .post<AuthResponseDto>(`${this.API_URL}/login`, credentials)
      .pipe(
        tap((response) => {
          console.log('Réponse complète du serveur:', response);
          if (!response?.token) {
            throw new Error('Token manquant dans la réponse du serveur');
          }
          this.storeAuthData(response);
          if (this.isAuthenticated()) {
            console.log('Token reçu:', response.token);
            console.log('Utilisateur connecté avec succès');
          }
        }),
        catchError((err) => {
          console.error('Échec de la connexion:', err);
          return of(null);
        })
      );
  }

  private storeAuthData(response: AuthResponseDto): void {
    if (!response.token) {
      console.error('Token non défini dans:', response);
      throw new Error('Impossible de sauvegarder un token undefined');
    }
    this.tokenService.saveToken(response.token);
    if (response.refreshToken) {
      this.tokenService.saveRefreshToken(response.refreshToken);
    }
    const user = this.decodeToken(response.token);
    this.currentUser$.next(user);
  }

  register(userData: RegisterDto): Observable<ClientDto> {
    console.log('🌐 AuthService - URL complète:', `${this.API_URL}/register/client`);
    console.log('🌐 AuthService - Données envoyées:', userData);

    return this.http
      .post<RegisterResponseDto>(`${this.API_URL}/register/client`, userData)
      .pipe(
        tap((response) => {
          console.log('✅ AuthService - Réponse reçue:', response);
          // Stocker le token
          this.tokenService.saveToken(response.token);
          // Mettre à jour l'utilisateur courant
          this.currentUser$.next(response.user);
        }),
        // Retourner seulement l'utilisateur pour maintenir la compatibilité
        map((response) => response.user),
        catchError((error) => {
          console.error('❌ AuthService - Erreur HTTP:', error);
          throw error;
        })
      );
  }

  // private storeAuthData(response: AuthResponseDto): void {
  //   localStorage.setItem('access_token', response.token);
  //   if (response.refreshToken) {
  //     localStorage.setItem('refresh_token', response.refreshToken);
  //   }
  //   const client = this.decodeToken(response.token);
  //   this.currentUser$.next(client);
  // }

  getCurrentUser(): ClientDto | null {
    const currentUser = this.currentUser$.value;
    console.log('🔍 AuthService.getCurrentUser() appelé');
    console.log('Valeur actuelle du BehaviorSubject:', currentUser);

    // Vérifier aussi le token
    const token = this.tokenService.getToken();
    console.log('Token présent dans localStorage:', token ? 'Oui' : 'Non');

    if (!currentUser && token) {
      console.log('⚠️ Token présent mais pas d\'utilisateur dans le BehaviorSubject');
      console.log('Tentative de décodage du token...');
      try {
        const decoded = this.decodeToken(token);
        console.log('Token décodé:', decoded);
      } catch (error) {
        console.error('Erreur lors du décodage du token:', error);
      }
    }

    return currentUser;
  }
  public loadFullClientData(): void {
    console.log('📊 AuthService.loadFullClientData() appelé');

    const token = this.tokenService.getToken();
    console.log('Token depuis TokenStorageService:', token ? 'Présent' : 'Absent');

    if (!token) {
      console.log('❌ Aucun token trouvé dans localStorage, arrêt du chargement');
      return;
    }

    const decoded = this.decodeToken(token);
    console.log('Token décodé pour loadFullClientData:', decoded);

    if (!decoded?.id) {
      console.log('❌ ID utilisateur manquant dans le token décodé');
      return;
    }

    console.log('🔄 Appel API pour récupérer les données complètes du client ID:', decoded.id);
    this.clientService.getById(decoded.id).subscribe({
      next: (client) => {
        console.log('✅ Données complètes du client reçues:', client);
        console.log('📝 Mise à jour du BehaviorSubject avec les données complètes...');
        this.currentUser$.next(client);
        console.log('✅ BehaviorSubject mis à jour avec données complètes, valeur actuelle:', this.currentUser$.value);
      },
      error: (err) => {
        console.error('❌ Erreur chargement client complet:', err);
        console.log('Détails de l\'erreur:', {
          status: err.status,
          message: err.message,
          error: err.error
        });
      },
    });
  }

  updateCurrentUser(user: ClientDto): void {
    this.currentUser$.next(user);
  }

  getToken(): string | null {
    return this.tokenService.getToken();
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    return !!token && !this.jwtHelper.isTokenExpired(token);
  }

  refreshToken(): Observable<AuthResponseDto> {
    const refreshToken = this.tokenService.getRefreshToken();
    return this.http
      .post<AuthResponseDto>(`${this.API_URL}/refresh-token`, { refreshToken })
      .pipe(tap((response) => this.storeAuthData(response)));
  }

  private debugLocalStorage(): void {
    console.log('🔍 Debug localStorage:');
    console.log('- pfe-auth (TokenStorageService):', localStorage.getItem('pfe-auth') ? 'Présent' : 'Absent');
    console.log('- auth_token (ancienne clé):', localStorage.getItem('auth_token') ? 'Présent' : 'Absent');
    console.log('- pfe-refresh-token:', localStorage.getItem('pfe-refresh-token') ? 'Présent' : 'Absent');
    console.log('- refresh_token (ancienne clé):', localStorage.getItem('refresh_token') ? 'Présent' : 'Absent');
    console.log('- pfe-user-context:', localStorage.getItem('pfe-user-context') ? 'Présent' : 'Absent');

    // Afficher toutes les clés localStorage pour debug
    console.log('📋 Toutes les clés localStorage:', Object.keys(localStorage));
  }

  private migrateOldTokens(): void {
    console.log('🔄 Vérification de la migration des anciens tokens...');

    // Migration du token principal
    const oldToken = localStorage.getItem('auth_token');
    const newToken = localStorage.getItem('pfe-auth');

    if (oldToken && !newToken) {
      console.log('📦 Migration du token principal: auth_token → pfe-auth');
      try {
        this.tokenService.saveToken(oldToken);
        localStorage.removeItem('auth_token');
        console.log('✅ Token principal migré avec succès');
      } catch (error) {
        console.error('❌ Erreur lors de la migration du token:', error);
        localStorage.removeItem('auth_token'); // Nettoyer le token invalide
      }
    }

    // Migration du refresh token
    const oldRefreshToken = localStorage.getItem('refresh_token');
    const newRefreshToken = localStorage.getItem('pfe-refresh-token');

    if (oldRefreshToken && !newRefreshToken) {
      console.log('📦 Migration du refresh token: refresh_token → pfe-refresh-token');
      this.tokenService.saveRefreshToken(oldRefreshToken);
      localStorage.removeItem('refresh_token');
      console.log('✅ Refresh token migré avec succès');
    }

    console.log('✅ Migration terminée');
  }
}
