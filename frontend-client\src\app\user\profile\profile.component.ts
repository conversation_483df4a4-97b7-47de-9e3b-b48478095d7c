import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/auth/auth.service';
import { ClientDto } from 'src/app/models/ClientDto';
import { ClientService } from 'src/app/services/client.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
  standalone: false,
})
export class ProfileComponent {
  user: ClientDto = {
    id: 0,
    nom: '',
    prenom: '',
    email: '',
    phoneNumber: '',
    role: 'Client',
    dateInscription: new Date(),
    dateNaissance: new Date(),
    adresses: [],
    adresseLivraison: {
      id: 0,
      rue: '',
      ville: '',
      codePostal: '',
      pays: 'Tunisie',
      estPrincipale: false,
    },
    estActif: true,
    nombreCommandes: 0,
  };
  constructor(
    private authService: AuthService,
    private clientService: ClientService,
    private router: Router
  ) {
    this.loadUserData();
  }
  logout() {
    this.authService.logout();
    this.router.navigate(['/login']);
  }

  loadUserData() {
    const currentUser = this.authService.getCurrentUser();
    if (currentUser && currentUser.id) {
      this.clientService.getById(currentUser.id).subscribe({
        next: (userFromApi) => {
          console.log('Données utilisateur reçues de l’API:', userFromApi);
          this.user = {
            ...userFromApi,
            adresseLivraison: userFromApi.adresseLivraison ?? {
              id: 0,
              rue: '',
              ville: '',
              codePostal: '',
              pays: 'Tunisie',
              estPrincipale: false,
            },
          };
          console.log(
            'Adresse livraison après mapping:',
            this.user.adresseLivraison
          );
        },
        error: (err) => {
          console.error('Erreur lors du chargement du profil', err);
        },
      });
    } else {
      console.warn('Utilisateur non connecté ou id manquant');
    }
  }

  updateProfile() {
    if (!this.user.id) return;

    const userToUpdate: ClientDto = {
      ...this.user,
      adresseLivraison: this.user.adresseLivraison ?? undefined,
    };

    this.clientService.updateClient(this.user.id, userToUpdate).subscribe({
      next: (updatedUser) => {
        this.user = updatedUser;
        this.authService.updateCurrentUser(updatedUser);
        this.authService.loadFullClientData();
        console.log('Profil mis à jour avec succès');
      },
      error: (err) => {
        console.error('Erreur de mise à jour', err);
      },
    });
  }
}
