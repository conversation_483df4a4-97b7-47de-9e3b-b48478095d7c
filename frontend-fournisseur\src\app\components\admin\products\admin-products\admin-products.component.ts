import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ProduitService } from '../../../../services/produit.service';
import { Produit } from '../../../../models';

@Component({
  selector: 'app-admin-products',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="admin-products">
      <div class="header">
        <h1>📦 Gestion des Produits</h1>
        <p>Gérer et valider les produits de la plateforme</p>
      </div>

      <!-- Statistiques -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">📦</div>
          <div class="stat-info">
            <div class="stat-number">{{ totalProduits }}</div>
            <div class="stat-label">Total Produits</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">✅</div>
          <div class="stat-info">
            <div class="stat-number">{{ produitsActifs }}</div>
            <div class="stat-label">Produits Actifs</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">⚠️</div>
          <div class="stat-info">
            <div class="stat-number">{{ produitsEnRupture }}</div>
            <div class="stat-label">En Rupture</div>
          </div>
        </div>
      </div>

      <!-- Filtres et recherche -->
      <div class="filters-section">
        <div class="search-box">
          <input
            type="text"
            [(ngModel)]="searchTerm"
            (input)="onSearch()"
            placeholder="Rechercher un produit..."
            class="search-input">
        </div>
        <div class="filter-buttons">
          <button
            class="filter-btn"
            [class.active]="selectedFilter === 'all'"
            (click)="setFilter('all')">
            Tous
          </button>
          <button
            class="filter-btn"
            [class.active]="selectedFilter === 'active'"
            (click)="setFilter('active')">
            Actifs
          </button>
          <button
            class="filter-btn"
            [class.active]="selectedFilter === 'rupture'"
            (click)="setFilter('rupture')">
            En Rupture
          </button>
        </div>
      </div>

      <!-- Loading -->
      <div *ngIf="loading" class="loading">
        <div class="spinner"></div>
        <p>Chargement des produits...</p>
      </div>

      <!-- Error -->
      <div *ngIf="error" class="error-message">
        <div class="error-icon">❌</div>
        <p>{{ error }}</p>
        <button (click)="loadProduits()" class="retry-btn">Réessayer</button>
      </div>

      <!-- Liste des produits -->
      <div *ngIf="!loading && !error" class="products-section">
        <div class="section-header">
          <h2>Liste des Produits ({{ filteredProduits.length }})</h2>
        </div>

        <div *ngIf="filteredProduits.length === 0" class="empty-state">
          <div class="empty-icon">📦</div>
          <h3>Aucun produit trouvé</h3>
          <p>Aucun produit ne correspond à vos critères de recherche.</p>
        </div>

        <div *ngIf="filteredProduits.length > 0" class="products-table">
          <div class="table-header">
            <div class="col-image">Image</div>
            <div class="col-name">Nom</div>
            <div class="col-reference">Référence</div>
            <div class="col-fournisseur">Fournisseur</div>
            <div class="col-stock">Stock</div>
            <div class="col-prix">Prix</div>
            <div class="col-status">Statut</div>
            <div class="col-actions">Actions</div>
          </div>

          <div *ngFor="let produit of paginatedProduits" class="table-row">
            <div class="col-image">
              <img
                [src]="getProductImage(produit)"
                [alt]="produit.nom"
                class="product-image"
                (error)="onImageError($event)">
            </div>
            <div class="col-name">
              <div class="product-name">{{ produit.nom }}</div>
              <div class="product-description">{{ produit.description | slice:0:50 }}...</div>
            </div>
            <div class="col-reference">{{ produit.referenceOriginal }}</div>
            <div class="col-fournisseur">{{ produit.fournisseur?.raisonSociale || produit.fournisseur?.nom || 'N/A' }}</div>
            <div class="col-stock">
              <span class="stock-badge" [class.low-stock]="produit.stock <= 5">
                {{ produit.stock }}
              </span>
            </div>
            <div class="col-prix">{{ produit.prixVenteHT | number:'1.2-2' }} TND</div>
            <div class="col-status">
              <span class="status-badge" [class.active]="produit.stock > 0" [class.inactive]="produit.stock === 0">
                {{ produit.stock > 0 ? 'En Stock' : 'Rupture' }}
              </span>
            </div>
            <div class="col-actions">
              <button class="action-btn view" (click)="viewProduct(produit)" title="Voir">👁️</button>
              <button class="action-btn edit" (click)="editProduct(produit)" title="Modifier">✏️</button>
              <button
                class="action-btn toggle"
                (click)="toggleProductStatus(produit)"
                [title]="produit.stock > 0 ? 'Marquer en rupture' : 'Remettre en stock'">
                {{ produit.stock > 0 ? '🔴' : '🟢' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div *ngIf="totalPages > 1" class="pagination">
          <button
            class="page-btn"
            [disabled]="currentPage === 1"
            (click)="goToPage(currentPage - 1)">
            ← Précédent
          </button>

          <span class="page-info">
            Page {{ currentPage }} sur {{ totalPages }}
          </span>

          <button
            class="page-btn"
            [disabled]="currentPage === totalPages"
            (click)="goToPage(currentPage + 1)">
            Suivant →
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .admin-products {
      padding: 1.5rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header {
      margin-bottom: 2rem;
    }

    .header h1 {
      color: #1e293b;
      margin-bottom: 0.5rem;
      font-size: 2rem;
      font-weight: 600;
    }

    .header p {
      color: #64748b;
      font-size: 1.1rem;
    }

    /* Statistiques */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      background: white;
      padding: 1.5rem;
      border-radius: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .stat-icon {
      font-size: 2.5rem;
      width: 60px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f1f5f9;
      border-radius: 12px;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: 700;
      color: #1e293b;
    }

    .stat-label {
      color: #64748b;
      font-size: 0.9rem;
    }

    /* Filtres */
    .filters-section {
      background: white;
      padding: 1.5rem;
      border-radius: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 2rem;
      display: flex;
      gap: 1rem;
      align-items: center;
      flex-wrap: wrap;
    }

    .search-box {
      flex: 1;
      min-width: 300px;
    }

    .search-input {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 1rem;
      transition: border-color 0.2s;
    }

    .search-input:focus {
      outline: none;
      border-color: #3b82f6;
    }

    .filter-buttons {
      display: flex;
      gap: 0.5rem;
    }

    .filter-btn {
      padding: 0.5rem 1rem;
      border: 2px solid #e2e8f0;
      background: white;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;
      font-weight: 500;
    }

    .filter-btn:hover {
      border-color: #3b82f6;
      color: #3b82f6;
    }

    .filter-btn.active {
      background: #3b82f6;
      border-color: #3b82f6;
      color: white;
    }

    /* Loading et erreurs */
    .loading, .error-message {
      text-align: center;
      padding: 3rem;
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f4f6;
      border-top: 4px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .error-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .retry-btn {
      background: #3b82f6;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 6px;
      cursor: pointer;
      margin-top: 1rem;
    }

    /* Section produits */
    .products-section {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .section-header {
      padding: 1.5rem;
      border-bottom: 1px solid #e2e8f0;
    }

    .section-header h2 {
      color: #1e293b;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 600;
    }

    .empty-state {
      text-align: center;
      padding: 3rem;
    }

    .empty-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    .empty-state h3 {
      color: #1e293b;
      margin-bottom: 0.5rem;
    }

    .empty-state p {
      color: #64748b;
    }

    /* Tableau des produits */
    .products-table {
      overflow-x: auto;
    }

    .table-header, .table-row {
      display: grid;
      grid-template-columns: 80px 2fr 150px 150px 100px 120px 100px 120px;
      gap: 1rem;
      padding: 1rem 1.5rem;
      align-items: center;
    }

    .table-header {
      background: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      font-weight: 600;
      color: #374151;
      font-size: 0.9rem;
    }

    .table-row {
      border-bottom: 1px solid #f1f5f9;
      transition: background-color 0.2s;
    }

    .table-row:hover {
      background: #f8fafc;
    }

    .product-image {
      width: 60px;
      height: 60px;
      object-fit: cover;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
    }

    .product-name {
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 0.25rem;
    }

    .product-description {
      color: #64748b;
      font-size: 0.85rem;
    }

    .stock-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      background: #dcfce7;
      color: #166534;
      font-weight: 600;
      font-size: 0.85rem;
    }

    .stock-badge.low-stock {
      background: #fef3c7;
      color: #92400e;
    }

    .status-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-weight: 600;
      font-size: 0.85rem;
    }

    .status-badge.active {
      background: #dcfce7;
      color: #166534;
    }

    .status-badge.inactive {
      background: #fee2e2;
      color: #dc2626;
    }

    .col-actions {
      display: flex;
      gap: 0.5rem;
    }

    .action-btn {
      width: 32px;
      height: 32px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1rem;
      transition: all 0.2s;
    }

    .action-btn.view {
      background: #dbeafe;
      color: #1d4ed8;
    }

    .action-btn.view:hover {
      background: #bfdbfe;
    }

    .action-btn.edit {
      background: #fef3c7;
      color: #d97706;
    }

    .action-btn.edit:hover {
      background: #fde68a;
    }

    .action-btn.toggle {
      background: #f3f4f6;
      color: #374151;
    }

    .action-btn.toggle:hover {
      background: #e5e7eb;
    }

    /* Pagination */
    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 1rem;
      padding: 1.5rem;
      border-top: 1px solid #e2e8f0;
    }

    .page-btn {
      padding: 0.5rem 1rem;
      border: 1px solid #d1d5db;
      background: white;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .page-btn:hover:not(:disabled) {
      background: #f9fafb;
      border-color: #9ca3af;
    }

    .page-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .page-info {
      color: #6b7280;
      font-weight: 500;
    }

    /* Responsive */
    @media (max-width: 1200px) {
      .table-header, .table-row {
        grid-template-columns: 60px 1fr 120px 100px 80px 100px 80px 100px;
        font-size: 0.85rem;
      }
    }

    @media (max-width: 768px) {
      .filters-section {
        flex-direction: column;
        align-items: stretch;
      }

      .search-box {
        min-width: auto;
      }

      .filter-buttons {
        justify-content: center;
      }

      .table-header, .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .table-header {
        display: none;
      }

      .table-row {
        display: block;
        padding: 1rem;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        margin-bottom: 1rem;
      }
    }
  `]
})
export class AdminProductsComponent implements OnInit {
  // Données
  produits: Produit[] = [];
  filteredProduits: Produit[] = [];
  paginatedProduits: Produit[] = [];

  // États
  loading = false;
  error: string | null = null;

  // Filtres et recherche
  searchTerm = '';
  selectedFilter = 'all';

  // Pagination
  currentPage = 1;
  itemsPerPage = 10;
  totalPages = 1;

  // Statistiques
  totalProduits = 0;
  produitsActifs = 0;
  produitsEnRupture = 0;

  constructor(private produitService: ProduitService) {}

  ngOnInit() {
    this.loadProduits();
  }

  loadProduits() {
    this.loading = true;
    this.error = null;

    this.produitService.getAll().subscribe({
      next: (produits) => {
        this.produits = produits;
        this.calculateStats();
        this.applyFilters();
        this.loading = false;
        console.log('✅ Produits chargés:', produits);
      },
      error: (error) => {
        this.error = 'Erreur lors du chargement des produits';
        this.loading = false;
        console.error('❌ Erreur chargement produits:', error);
      }
    });
  }

  calculateStats() {
    this.totalProduits = this.produits.length;
    this.produitsActifs = this.produits.filter(p => p.stock > 0).length;
    this.produitsEnRupture = this.produits.filter(p => p.stock <= 5).length;
  }

  onSearch() {
    this.currentPage = 1;
    this.applyFilters();
  }

  setFilter(filter: string) {
    this.selectedFilter = filter;
    this.currentPage = 1;
    this.applyFilters();
  }

  applyFilters() {
    let filtered = [...this.produits];

    // Filtre par recherche
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(p =>
        p.nom.toLowerCase().includes(term) ||
        p.referenceOriginal?.toLowerCase().includes(term) ||
        p.description?.toLowerCase().includes(term)
      );
    }

    // Filtre par statut
    switch (this.selectedFilter) {
      case 'active':
        filtered = filtered.filter(p => p.stock > 0);
        break;
      case 'rupture':
        filtered = filtered.filter(p => p.stock <= 5);
        break;
    }

    this.filteredProduits = filtered;
    this.updatePagination();
  }

  updatePagination() {
    this.totalPages = Math.ceil(this.filteredProduits.length / this.itemsPerPage);
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedProduits = this.filteredProduits.slice(startIndex, endIndex);
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();
    }
  }

  getProductImage(produit: Produit): string {
    if (produit.images && produit.images.length > 0) {
      return produit.images[0].imageUrl;
    }
    return '/assets/images/no-image.png';
  }

  onImageError(event: any) {
    event.target.src = '/assets/images/no-image.png';
  }

  viewProduct(produit: Produit) {
    console.log('👁️ Voir produit:', produit);
    // TODO: Implémenter la vue détaillée
  }

  editProduct(produit: Produit) {
    console.log('✏️ Modifier produit:', produit);
    // TODO: Implémenter l'édition
  }

  toggleProductStatus(produit: Produit) {
    console.log('🔄 Changer statut produit:', produit);
    // TODO: Implémenter le changement de statut
  }
}
