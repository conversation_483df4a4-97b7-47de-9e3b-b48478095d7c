import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { CommandeDto, CreateCommandeDto } from '../models/CommandeDto';
import { DetailsCommandeDto } from '../models/DetailsCommandeDto';
import { FraisLivraisonResponseDto } from '../models/FraisLivraisonDto';
import { BaseService } from './core/base.service';

@Injectable({ providedIn: 'root' })
export class CommandeService extends BaseService<CommandeDto> {
  constructor(http: HttpClient) {
    super(http, 'commandes');
  }

  // Créer une commande à partir du panier
  creerCommande(commande: CreateCommandeDto): Observable<CommandeDto> {
    // Retour à l'endpoint original
    return this.http.post<CommandeDto>(`${this.apiUrl}/${this.endpoint}`, commande);
  }

  // Finaliser une commande avec paiement (nouvelle méthode)
  finaliserCommande(
    commandeId: number,
    tokenPaiement: string
  ): Observable<boolean> {
    return this.http.post<boolean>(
      `${this.apiUrl}/${this.endpoint}/${commandeId}/finaliser`,
      { tokenPaiement }
    );
  }

  // Ancienne méthode pour compatibilité
  finalizeCommande(
    commandeId: number,
    tokenPaiement: string
  ): Observable<CommandeDto> {
    return this.http.post<CommandeDto>(
      `${this.apiUrl}/${this.endpoint}/${commandeId}/finalize`,
      { tokenPaiement }
    );
  }

  // Récupérer les commandes d'un client
  getByClient(clientId: number): Observable<CommandeDto[]> {
    return this.http.get<CommandeDto[]>(
      `${this.apiUrl}/${this.endpoint}/by-client/${clientId}`
    );
  }

  // Annuler une commande (nouvelle méthode)
  annulerCommande(commandeId: number): Observable<CommandeDto> {
    return this.http.patch<CommandeDto>(
      `${this.apiUrl}/${this.endpoint}/${commandeId}/annuler`,
      {}
    );
  }

  // Ancienne méthode pour compatibilité
  cancel(orderId: number): Observable<CommandeDto> {
    return this.annulerCommande(orderId);
  }

  // Récupérer les détails d'une commande
  getDetailsCommande(commandeId: number): Observable<DetailsCommandeDto[]> {
    return this.http.get<DetailsCommandeDto[]>(
      `${this.apiUrl}/detailscommande/commande/${commandeId}`
    );
  }

  // Calculer les frais de livraison pour une adresse et des produits
  calculerFraisLivraison(adresseId: number, produitIds: number[]): Observable<FraisLivraisonResponseDto> {
    return this.http.post<FraisLivraisonResponseDto>(
      `${this.apiUrl}/${this.endpoint}/calculer-frais-livraison`,
      { adresseId, produitIds }
    );
  }

  // Méthodes héritées de BaseService disponibles :
  // - getAll(): Observable<CommandeDto[]>
  // - getById(id: number): Observable<CommandeDto>
  // - create(item: CommandeDto): Observable<CommandeDto>
  // - update(id: number, item: CommandeDto): Observable<CommandeDto>
  // - delete(id: number): Observable<void>
}
