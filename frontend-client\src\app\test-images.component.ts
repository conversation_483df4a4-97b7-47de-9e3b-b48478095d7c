import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ImageUrlService } from './services/image-url.service';
import { ProduitService } from './services/produit.service';
import { FournisseurService } from './services/fournisseur.service';

@Component({
  selector: 'app-test-images',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div style="padding: 20px;">
      <h2>Test des Images</h2>
      
      <div style="margin-bottom: 30px;">
        <h3>Test des URLs d'images</h3>
        <div>
          <p><strong>URL de base API:</strong> {{ baseApiUrl }}</p>
          <p><strong>URL de base pour images:</strong> {{ baseImageUrl }}</p>
        </div>
      </div>

      <div style="margin-bottom: 30px;">
        <h3>Test des chemins d'images</h3>
        <div style="margin-bottom: 10px;">
          <strong>Chemin relatif:</strong> /uploads/produits/test.jpg<br>
          <strong>URL complète:</strong> {{ getTestImageUrl('/uploads/produits/test.jpg') }}
        </div>
        <div style="margin-bottom: 10px;">
          <strong>Chemin sans slash:</strong> uploads/logos/logo.png<br>
          <strong>URL complète:</strong> {{ getTestImageUrl('uploads/logos/logo.png') }}
        </div>
        <div style="margin-bottom: 10px;">
          <strong>URL complète existante:</strong> https://example.com/image.jpg<br>
          <strong>URL retournée:</strong> {{ getTestImageUrl('https://example.com/image.jpg') }}
        </div>
        <div style="margin-bottom: 10px;">
          <strong>Chemin null/undefined:</strong><br>
          <strong>URL placeholder:</strong> {{ getTestImageUrl(null) }}
        </div>
      </div>

      <div style="margin-bottom: 30px;" *ngIf="testProduits.length > 0">
        <h3>Images de Produits ({{ testProduits.length }} produits)</h3>
        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
          <div *ngFor="let produit of testProduits" style="border: 1px solid #ccc; padding: 10px; max-width: 200px;">
            <h4>{{ produit.nom }}</h4>
            <img 
              [src]="imageUrlService.getProduitImageUrl(produit.imageUrl)" 
              [alt]="produit.nom"
              style="width: 100%; height: 150px; object-fit: cover; border: 1px solid #ddd;"
              (error)="onImageError($event, produit.nom)"
              (load)="onImageLoad($event, produit.nom)"
            />
            <p style="font-size: 12px; margin-top: 5px;">
              <strong>URL originale:</strong> {{ produit.imageUrl }}<br>
              <strong>URL complète:</strong> {{ imageUrlService.getProduitImageUrl(produit.imageUrl) }}
            </p>
          </div>
        </div>
      </div>

      <div style="margin-bottom: 30px;" *ngIf="testFournisseurs.length > 0">
        <h3>Logos de Fournisseurs ({{ testFournisseurs.length }} fournisseurs)</h3>
        <div style="display: flex; flex-wrap: wrap; gap: 15px;">
          <div *ngFor="let fournisseur of testFournisseurs" style="border: 1px solid #ccc; padding: 10px; max-width: 200px;">
            <h4>{{ fournisseur.nom }} {{ fournisseur.prenom }}</h4>
            <img 
              [src]="imageUrlService.getFournisseurLogoUrl(fournisseur.logoFile)" 
              [alt]="fournisseur.nom + ' logo'"
              style="width: 100%; height: 100px; object-fit: cover; border: 1px solid #ddd;"
              (error)="onImageError($event, fournisseur.nom)"
              (load)="onImageLoad($event, fournisseur.nom)"
            />
            <p style="font-size: 12px; margin-top: 5px;">
              <strong>URL originale:</strong> {{ fournisseur.logoFile }}<br>
              <strong>URL complète:</strong> {{ imageUrlService.getFournisseurLogoUrl(fournisseur.logoFile) }}
            </p>
          </div>
        </div>
      </div>

      <div style="margin-top: 30px;">
        <h3>Logs des événements d'images</h3>
        <div style="background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;">
          <div *ngFor="let log of imageLogs" [style.color]="log.type === 'error' ? 'red' : 'green'">
            {{ log.timestamp }} - {{ log.type.toUpperCase() }}: {{ log.message }}
          </div>
        </div>
      </div>
    </div>
  `
})
export class TestImagesComponent implements OnInit {
  baseApiUrl = '';
  baseImageUrl = '';
  testProduits: any[] = [];
  testFournisseurs: any[] = [];
  imageLogs: Array<{timestamp: string, type: string, message: string}> = [];

  constructor(
    public imageUrlService: ImageUrlService,
    private produitService: ProduitService,
    private fournisseurService: FournisseurService
  ) {}

  ngOnInit() {
    // Récupérer les URLs de base
    this.baseApiUrl = this.imageUrlService['baseUrl'] + '/api';
    this.baseImageUrl = this.imageUrlService['baseUrl'];

    // Charger quelques produits pour tester
    this.produitService.getAll().subscribe({
      next: (produits) => {
        this.testProduits = produits.slice(0, 6); // Prendre les 6 premiers
        this.addLog('success', `${produits.length} produits chargés`);
      },
      error: (err) => {
        this.addLog('error', `Erreur chargement produits: ${err.message}`);
      }
    });

    // Charger quelques fournisseurs pour tester
    this.fournisseurService.getAll().subscribe({
      next: (fournisseurs) => {
        this.testFournisseurs = fournisseurs.slice(0, 6); // Prendre les 6 premiers
        this.addLog('success', `${fournisseurs.length} fournisseurs chargés`);
      },
      error: (err) => {
        this.addLog('error', `Erreur chargement fournisseurs: ${err.message}`);
      }
    });
  }

  getTestImageUrl(path: string | null): string {
    return this.imageUrlService.getFullImageUrl(path);
  }

  onImageLoad(event: Event, name: string) {
    const img = event.target as HTMLImageElement;
    this.addLog('success', `Image chargée pour ${name}: ${img.src}`);
  }

  onImageError(event: Event, name: string) {
    const img = event.target as HTMLImageElement;
    this.addLog('error', `Erreur chargement image pour ${name}: ${img.src}`);
    // Remplacer par l'image placeholder
    img.src = this.imageUrlService.getPlaceholderUrl();
  }

  private addLog(type: string, message: string) {
    const timestamp = new Date().toLocaleTimeString();
    this.imageLogs.unshift({ timestamp, type, message });
    // Garder seulement les 20 derniers logs
    if (this.imageLogs.length > 20) {
      this.imageLogs = this.imageLogs.slice(0, 20);
    }
  }
}
