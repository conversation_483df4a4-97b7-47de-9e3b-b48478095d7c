# Créer un fournisseur de test pour le dashboard
$baseUrl = "http://localhost:5014/api"

Write-Host "=== CRÉATION FOURNISSEUR TEST DASHBOARD ===" -ForegroundColor Cyan

# Données du fournisseur
$timestamp = Get-Date -Format "yyyyMMddHHmmss"
$email = "dashboard$<EMAIL>"
$password = "Dashboard123!"

Write-Host "`nCréation du fournisseur:" -ForegroundColor Yellow
Write-Host "Email: $email" -ForegroundColor White
Write-Host "Mot de passe: $password" -ForegroundColor White

$fournisseurData = @{
    nom = "Dashboard"
    prenom = "Test"
    email = $email
    password = $password
    phoneNumber = "12345678"
    raisonSociale = "Dashboard Test SARL"
    matriculeFiscale = "DASH$timestamp"
    rib = "12345678901234567890"
    codeBanque = "12345"
    commission = 5.0
    delaiPreparationJours = 2
    fraisLivraisonBase = 10.0
    description = "Fournisseur de test pour dashboard avec statistiques"
} | ConvertTo-Json

$headers = @{"Content-Type" = "application/json"}

try {
    Write-Host "`nCréation en cours..." -ForegroundColor Yellow
    $newFournisseur = Invoke-RestMethod -Uri "$baseUrl/Auth/register/fournisseur" -Method Post -Headers $headers -Body $fournisseurData
    Write-Host "✅ Fournisseur créé avec succès !" -ForegroundColor Green
    Write-Host "ID: $($newFournisseur.id)" -ForegroundColor White
    
    $fournisseurId = $newFournisseur.id
    
    # Test de connexion immédiat
    Write-Host "`nTest de connexion..." -ForegroundColor Yellow
    
    $loginData = @{
        email = $email
        motDePasse = $password
    } | ConvertTo-Json
    
    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
        Write-Host "✅ Connexion réussie !" -ForegroundColor Green
        Write-Host "Token reçu: Oui" -ForegroundColor White
        
        $authHeaders = @{
            "Content-Type" = "application/json"
            "Authorization" = "Bearer $($loginResponse.token)"
        }
        
        # Test des statistiques
        Write-Host "`nTest des statistiques..." -ForegroundColor Yellow
        
        try {
            $stats = Invoke-RestMethod -Uri "$baseUrl/StatistiquesFournisseur/dashboard/$fournisseurId" -Method Get -Headers $authHeaders
            Write-Host "✅ STATISTIQUES RÉCUPÉRÉES !" -ForegroundColor Green
            Write-Host "Total produits: $($stats.totalProduits)" -ForegroundColor White
            Write-Host "CA mensuel: $($stats.chiffreAffaireMensuel) TND" -ForegroundColor White
            Write-Host "Commandes actives: $($stats.commandesActives)" -ForegroundColor White
            
        } catch {
            Write-Host "❌ Erreur statistiques: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "❌ Erreur de connexion: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Erreur création fournisseur: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Détails: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    
    # Essayer avec un fournisseur existant
    Write-Host "`nEssai avec fournisseur existant..." -ForegroundColor Yellow
    
    # Utiliser le fournisseur de test existant
    $email = "<EMAIL>"
    $password = "123456"
    
    $loginData = @{
        email = $email
        motDePasse = $password
    } | ConvertTo-Json
    
    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
        Write-Host "✅ Connexion avec fournisseur existant réussie !" -ForegroundColor Green
        
        $fournisseurId = $loginResponse.utilisateur.id
        $authHeaders = @{
            "Content-Type" = "application/json"
            "Authorization" = "Bearer $($loginResponse.token)"
        }
        
        # Test des statistiques
        try {
            $stats = Invoke-RestMethod -Uri "$baseUrl/StatistiquesFournisseur/dashboard/$fournisseurId" -Method Get -Headers $authHeaders
            Write-Host "✅ STATISTIQUES OK !" -ForegroundColor Green
            Write-Host "Total produits: $($stats.totalProduits)" -ForegroundColor White
            Write-Host "CA mensuel: $($stats.chiffreAffaireMensuel) TND" -ForegroundColor White
            
        } catch {
            Write-Host "❌ Erreur stats: $($_.Exception.Message)" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "❌ Aucune connexion possible" -ForegroundColor Red
    }
}

Write-Host "`n=== INSTRUCTIONS POUR LE FRONTEND ===" -ForegroundColor Cyan
Write-Host "1. Ouvrez le frontend fournisseur: http://localhost:55520/" -ForegroundColor White
Write-Host "2. Connectez-vous avec:" -ForegroundColor White
if ($email -and $password) {
    Write-Host "   Email: $email" -ForegroundColor Green
    Write-Host "   Mot de passe: $password" -ForegroundColor Green
}
Write-Host "3. Allez sur le dashboard pour voir les statistiques en TND" -ForegroundColor White

Write-Host "`n=== FIN ===" -ForegroundColor Cyan
