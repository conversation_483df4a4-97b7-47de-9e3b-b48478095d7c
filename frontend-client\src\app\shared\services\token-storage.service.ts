import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class TokenStorageService {
  private readonly TOKEN_KEY = 'pfe-auth';
  private readonly USER_DATA_KEY = 'pfe-user-context';
  private readonly REFRESH_TOKEN_KEY = 'pfe-refresh-token';

  saveToken(token: string): void {
    if (token === undefined || token === null) {
        throw new Error('TokenStorageService: token est undefined/null');
    }
    if (typeof token !== 'string' || token.trim().length === 0) {
        throw new Error('TokenStorageService: token doit être une chaîne non vide');
    }
    if (!this.isValidToken(token)) {
       console.error('Token reçu:', token);
      throw new Error('Token invalide : structure incorrecte');
    }
    localStorage.setItem(this.TOKEN_KEY, token);
  }
  getToken(): string | null {
    const token = localStorage.getItem(this.TOKEN_KEY);
    return token && this.isValidToken(token) ? token : null;
  }

  clearToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
  }
  saveRefreshToken(token: string): void {
    localStorage.setItem(this.REFRESH_TOKEN_KEY, token);
  }

  getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }
  saveUserData(userData: unknown): void {
    localStorage.setItem(this.USER_DATA_KEY, JSON.stringify(userData));
  }

  getUserData<T>(): T | null {
    const data = localStorage.getItem(this.USER_DATA_KEY);
    return data ? JSON.parse(data) as T : null;
  }

  clearAll(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    localStorage.removeItem(this.USER_DATA_KEY);
  }

  private isValidToken(token: string): boolean {
    if (!token) return false;
    
    try {
        const parts = token.split('.');
        if (parts.length !== 3) return false;

        return parts.every(part => {
            try {
                atob(part.replace(/-/g, '+').replace(/_/g, '/'));
                return true;
            } catch {
                return false;
            }
        });
    } catch {
        return false;
    }
  }
}
