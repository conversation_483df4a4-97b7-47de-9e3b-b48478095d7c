import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { catchError, Observable, throwError } from 'rxjs';
import { SousCategorieDto } from '../models/SousCategorieDto';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SousCategorieService {
  private readonly API_URL = `${environment.apiUrl}/souscategories`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<SousCategorieDto[]> {
return this.http.get<SousCategorieDto[]>(`${this.API_URL}`).pipe(
    catchError(error => {
      console.error('Erreur lors de la récupération des sous-catégories', error);
      return throwError(() => new Error('Erreur serveur'));
    })
  );  }

  getById(id: number): Observable<SousCategorieDto> {
    return this.http.get<SousCategorieDto>(`${this.API_URL}/${id}`);
  }

  getByCategorie(categorieId: number): Observable<SousCategorieDto[]> {
    return this.http.get<SousCategorieDto[]>(`${this.API_URL}/by-categorie/${categorieId}`);
  }

  getProduitsCount(id: number): Observable<number> {
    return this.http.get<number>(`${this.API_URL}/${id}/produits-count`);
  }

  getForDropdown(categorieId: number): Observable<{ [key: number]: string }> {
    return this.http.get<{ [key: number]: string }>(`${this.API_URL}/dropdown/${categorieId}`);
  }

  create(sousCategorie: SousCategorieDto): Observable<SousCategorieDto> {
    return this.http.post<SousCategorieDto>(this.API_URL, sousCategorie);
  }

  update(sousCategorie: SousCategorieDto): Observable<void> {
    return this.http.put<void>(`${this.API_URL}/${sousCategorie.id}`, sousCategorie);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/${id}`);
  }
}
