import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { AvisDto } from '../models/AvisDto';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AvisService {
  private baseUrl = environment.apiUrl + '/avis';

  constructor(private http: HttpClient) {}

  ajouterAvis(produitId: number, avis: Partial<AvisDto>): Observable<AvisDto> {
    return this.http.post<AvisDto>(`${this.baseUrl}/${produitId}`, avis);
  }

  getMoyenne(produitId: number): Observable<number> {
    return this.http.get<number>(`${this.baseUrl}/${produitId}/moyenne`);
  }

  getAvis(produitId: number, avisId: number): Observable<AvisDto> {
    return this.http.get<AvisDto>(`${this.baseUrl}/${produitId}/${avisId}`);
  }
  getAvisByProduit(produitId: number): Observable<AvisDto[]> {
    return this.http.get<AvisDto[]>(`${this.baseUrl}/produit/${produitId}`);
  }

  // Vérifier si l'utilisateur connecté a déjà un avis pour ce produit
  getMonAvis(produitId: number): Observable<AvisDto | null> {
    return this.http.get<AvisDto | null>(`${this.baseUrl}/${produitId}/mon-avis`);
  }
}
