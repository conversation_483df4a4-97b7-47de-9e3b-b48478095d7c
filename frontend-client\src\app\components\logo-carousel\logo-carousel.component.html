<div class="brands-carousel">
  <carousel
    #carousel
    [itemsPerSlide]="itemsPerSlide"
    [singleSlideOffset]="singleSlideOffset"
    [startFromIndex]="currentIndex"
    [interval]="interval"
  >
    <slide *ngFor="let slide of slides; let index = index" class="brand-item">
      <img
        [src]="slide.logo"
        alt="image slide"
        class="brand-logo"
        (click)="navigateToBrand(slide.id)"
      />
    </slide>
  </carousel>
</div>