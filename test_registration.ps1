$body = @{
    email = "<EMAIL>"
    password = "TestPassword123!"
    nom = "Du<PERSON>"
    prenom = "<PERSON>"
    phoneNumber = "12345678"
    raisonSociale = "Test Optique SARL"
    matriculeFiscale = "1234567890123"
    description = "Magasin d'optique de test"
    rib = "12345678901234567890"
    codeBanque = "12345"
    commission = 5.0
    fraisLivraisonBase = 10.0
    delaiPreparationJours = 3
} | ConvertTo-Json

Write-Host "Inscription du fournisseur..."
try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5014/api/Auth/register/fournisseur' -Method POST -ContentType 'application/json' -Body $body
    Write-Host "Succès: $($response | ConvertTo-Json)"
} catch {
    Write-Host "Erreur: $($_.Exception.Message)"
    Write-Host "Détails: $($_.ErrorDetails.Message)"
}

Write-Host "`nVérification des notifications pour l'admin (ID=1)..."
try {
    $notifications = Invoke-RestMethod -Uri 'http://localhost:5014/api/notification/user/1' -Method GET
    Write-Host "Notifications trouvées: $($notifications.Count)"
    $notifications | ForEach-Object { Write-Host "- $($_.contenu)" }
} catch {
    Write-Host "Erreur lors de la récupération des notifications: $($_.Exception.Message)"
}
