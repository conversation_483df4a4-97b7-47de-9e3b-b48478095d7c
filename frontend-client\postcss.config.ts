import type { Plugin } from 'postcss';
import { PurgeCSS } from 'purgecss';

const config: { plugins: Plugin[] } = {
  plugins: [
    {
      postcssPlugin: 'purgecss',
      Once(root, { result }) {
        return new PurgeCSS().purge({
          content: [
            './src/**/*.html',
            './src/**/*.ts',
            './src/**/*.scss'
          ],
          css: [{ raw: root.toString() }],
          defaultExtractor: (content: string) => 
            content.match(/[\w-/:]+(?<!:)/g) || []
        }).then(purgeResult => {
          root.removeAll();
          root.append(purgeResult[0].css);
        });
      }
    } as Plugin
  ]
};

export default config;
