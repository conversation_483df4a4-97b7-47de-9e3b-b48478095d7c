{"name": "optic-marketplace", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.2", "@angular/cdk": "^19.2.17", "@angular/common": "^19.2.2", "@angular/compiler": "^19.1.6", "@angular/core": "^19.2.2", "@angular/forms": "^19.1.7", "@angular/material": "^19.2.17", "@angular/platform-browser": "^19.1.6", "@angular/platform-browser-dynamic": "^19.1.6", "@angular/router": "^19.2.2", "@auth0/angular-jwt": "^5.2.0", "@fortawesome/fontawesome-free": "^6.7.2", "@microsoft/signalr": "^8.0.7", "@primeng/themes": "^19.0.9", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "ngx-bootstrap": "^19.0.2", "optic-marketplace": "file:", "primeicons": "^7.0.0", "primeng": "^19.0.9", "rxjs": "~7.8.0", "sass": "^1.89.2", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.8", "@angular/cli": "^19.1.7", "@angular/compiler-cli": "^19.1.6", "@types/jasmine": "^5.1.6", "jasmine": "^5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "purgecss": "^7.0.2", "typescript": "^5.7.3"}}