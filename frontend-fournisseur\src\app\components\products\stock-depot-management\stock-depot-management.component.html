<div class="stock-depot-management">
  <!-- En-tête avec sélection du dépôt -->
  <div class="page-header">
    <div class="header-content">
      <h1>🏪 Gestion du Stock par Dépôt</h1>
      <p>Gérez rapidement les quantités disponibles dans vos différents dépôts</p>
    </div>
    
    <div class="depot-selector">
      <label for="depot-select">Dépôt :</label>
      <select
        id="depot-select"
        [(ngModel)]="selectedDepot"
        (ngModelChange)="onDepotChange()"
        class="form-control"
      >
        <option *ngFor="let depot of depots" [ngValue]="depot">
          {{ depot.nom }} {{ depot.estPrincipal ? '(Principal)' : '' }}
        </option>
      </select>

      <button
        class="btn btn-secondary refresh-btn"
        (click)="refreshData()"
        title="Actualiser les données"
      >
        🔄 Actualiser
      </button>
    </div>
  </div>

  <!-- Statistiques du dépôt -->
  <div class="stats-section" *ngIf="statistiques && selectedDepot">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">📦</div>
        <div class="stat-content">
          <div class="stat-value">{{ statistiques.totalProduits }}</div>
          <div class="stat-label">Produits</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <div class="stat-value">{{ statistiques.totalQuantite }}</div>
          <div class="stat-label">Unités en stock</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">💰</div>
        <div class="stat-content">
          <div class="stat-value">{{ formatPrice(statistiques.valeurStock) }}</div>
          <div class="stat-label">Valeur du stock</div>
        </div>
      </div>
      
      <div class="stat-card alert" *ngIf="statistiques.produitsEnRupture > 0">
        <div class="stat-icon">⚠️</div>
        <div class="stat-content">
          <div class="stat-value">{{ statistiques.produitsEnRupture }}</div>
          <div class="stat-label">En rupture</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Barre d'outils -->
  <div class="toolbar">
    <div class="search-section">
      <div class="search-box">
        <input
          type="text"
          [(ngModel)]="searchQuery"
          placeholder="Rechercher un produit..."
          class="form-control"
        />
        <span class="search-icon">🔍</span>
      </div>
      
      <div class="filters">
        <label class="filter-checkbox">
          <input type="checkbox" [(ngModel)]="filtre.rupture" />
          <span>Rupture de stock</span>
        </label>
        <label class="filter-checkbox">
          <input type="checkbox" [(ngModel)]="filtre.stockFaible" />
          <span>Stock faible</span>
        </label>
        <label class="filter-checkbox">
          <input type="checkbox" [(ngModel)]="filtre.seuilAlerte" />
          <span>Seuil d'alerte</span>
        </label>
      </div>
    </div>
    
    <div class="actions">
      <button
        class="btn btn-secondary"
        [class.active]="bulkEditMode"
        (click)="toggleBulkEditMode()"
      >
        {{ bulkEditMode ? '❌ Annuler édition' : '✏️ Édition rapide' }}
      </button>

      <button
        *ngIf="bulkEditMode && bulkUpdates.length > 0"
        class="btn btn-primary"
        (click)="saveBulkEdits()"
      >
        💾 Sauvegarder ({{ bulkUpdates.length }})
      </button>

      <button
        class="btn btn-warning"
        (click)="resetData()"
        title="Réinitialiser les données de test"
      >
        🔄 Reset Data
      </button>
    </div>
  </div>

  <!-- Message de chargement -->
  <div *ngIf="isLoading" class="loading">
    <div class="loading-spinner"></div>
    <p>Chargement des données...</p>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="error-message">
    <div class="error-icon">❌</div>
    <p>{{ error }}</p>
  </div>

  <!-- Table des stocks par produit -->
  <div class="stock-table" *ngIf="!isLoading && !error && selectedDepot">
    <div class="table-header">
      <div class="col-product">Produit</div>
      <div class="col-reference">Référence</div>
      <div class="col-stock">Stock Actuel</div>
      <div class="col-reserved">Réservé</div>
      <div class="col-available">Disponible</div>
      <div class="col-location">Emplacement</div>
      <div class="col-value">Valeur</div>
      <div class="col-status">Statut</div>
      <div class="col-actions">Actions</div>
    </div>
    
    <div *ngFor="let produit of getFilteredProduits(); let i = index" class="table-row">
      <ng-container *ngIf="getStockDepot(produit, selectedDepot.id) as stock">
        <!-- Informations produit -->
        <div class="col-product">
          <div class="product-info">
            <img [src]="getProductImageUrl(produit)" [alt]="produit.nom" />
            <div class="product-details">
              <h3>{{ produit.nom }}</h3>
              <p>{{ produit.referenceFournisseur || produit.referenceOriginal }}</p>
            </div>
          </div>
        </div>
        
        <div class="col-reference">
          <span class="reference-code">{{ produit.referenceOriginal }}</span>
        </div>
        
        <!-- Stock actuel (éditable) -->
        <div class="col-stock">
          <div *ngIf="!isInEditMode(stock.id) && !bulkEditMode" class="stock-display">
            <span class="stock-number" [ngClass]="getStockLevelClass(stock)">
              {{ stock.quantite }}
            </span>
            <span class="stock-unit">unités</span>
          </div>
          
          <!-- Mode édition individuel -->
          <div *ngIf="isInEditMode(stock.id)" class="stock-edit">
            <input
              type="number"
              [(ngModel)]="getEditMode(stock.id)!.nouvelleQuantite"
              class="form-control stock-input"
              min="0"
            />
          </div>
          
          <!-- Mode édition en lot -->
          <div *ngIf="bulkEditMode && !isInEditMode(stock.id)" class="stock-edit">
            <input
              type="number"
              [value]="stock.quantite"
              class="form-control stock-input"
              min="0"
              (input)="updateBulkEdit(produit.id, $event)"
            />
          </div>
        </div>
        
        <div class="col-reserved">
          <span class="reserved-quantity">{{ stock.quantiteReservee }}</span>
        </div>
        
        <div class="col-available">
          <span class="available-quantity">{{ stock.quantiteDisponible }}</span>
        </div>
        
        <div class="col-location">
          <span class="location-code">{{ stock.emplacementPhysique || '-' }}</span>
        </div>
        
        <div class="col-value">
          <span class="stock-value">{{ formatPrice(calculateStockValue(produit, stock)) }}</span>
        </div>
        
        <div class="col-status">
          <span class="status-badge" [ngClass]="getStockLevelClass(stock)">
            {{ getStockStatusText(stock) }}
          </span>
        </div>
        
        <!-- Actions -->
        <div class="col-actions">
          <!-- Actions mode normal -->
          <div *ngIf="!isInEditMode(stock.id) && !bulkEditMode" class="action-buttons">
            <button 
              class="btn btn-sm btn-primary"
              (click)="enterEditMode(stock)"
              title="Modifier le stock"
            >
              ✏️
            </button>
          </div>
          
          <!-- Actions mode édition individuel -->
          <div *ngIf="isInEditMode(stock.id)" class="edit-actions">
            <select 
              [(ngModel)]="getEditMode(stock.id)!.motif"
              class="form-control motif-select"
            >
              <option *ngFor="let motif of motifsStock" [value]="motif">{{ motif }}</option>
            </select>
            <button 
              class="btn btn-sm btn-success"
              (click)="saveStockEdit(stock.id)"
              title="Sauvegarder"
            >
              ✅
            </button>
            <button 
              class="btn btn-sm btn-secondary"
              (click)="exitEditMode(stock.id)"
              title="Annuler"
            >
              ❌
            </button>
          </div>
          
          <!-- Actions mode édition en lot -->
          <div *ngIf="bulkEditMode" class="bulk-edit-actions">
            <select 
              class="form-control motif-select"
              (change)="updateBulkEditMotif(produit.id, $event)"
            >
              <option value="">Motif...</option>
              <option *ngFor="let motif of motifsStock" [value]="motif">{{ motif }}</option>
            </select>
          </div>
        </div>
      </ng-container>
    </div>

    <!-- Message si aucun produit -->
    <div *ngIf="getFilteredProduits().length === 0" class="no-products">
      <div class="no-products-icon">📦</div>
      <h3>Aucun produit trouvé</h3>
      <p>Aucun produit ne correspond à vos critères dans ce dépôt.</p>
    </div>
  </div>
</div>
