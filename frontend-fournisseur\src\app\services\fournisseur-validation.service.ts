import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface FournisseurValidationDto {
  id: number;
  email: string;
  nom: string;
  prenom: string;
  phoneNumber: string;
  matriculeFiscale: string;
  raisonSociale: string;
  description?: string;
  rib: string;
  codeBanque: string;
  commission: number;
  delaiPreparationJours: number;
  fraisLivraisonBase: number;
  logoFile: string;
  statutValidation: StatutValidationFournisseur;
  dateInscription: Date;
  dateValidation?: Date;
  validePar?: number;
  commentaireValidation?: string;
  adresses: AdresseDto[];
}

export interface AdresseDto {
  id: number;
  rue: string;
  ville: string;
  codePostal: string;
  pays: string;
  estPrincipale: boolean;
}

export enum StatutValidationFournisseur {
  EnAttente = 0,
  Valide = 1,
  Rejete = 2,
  Suspendu = 3
}

export interface ValiderFournisseurRequest {
  fournisseurId: number;
  accepter: boolean;
  commentaire?: string;
}

@Injectable({
  providedIn: 'root'
})
export class FournisseurValidationService {
  private apiUrl = `${environment.apiUrl}/api/Admin/fournisseurs`;

  constructor(private http: HttpClient) {}

  /**
   * Récupérer tous les fournisseurs en attente de validation
   */
  getFournisseursEnAttente(): Observable<FournisseurValidationDto[]> {
    return this.http.get<FournisseurValidationDto[]>(`${this.apiUrl}/en-attente`);
  }

  /**
   * Récupérer tous les fournisseurs avec leur statut
   */
  getTousFournisseurs(): Observable<FournisseurValidationDto[]> {
    return this.http.get<FournisseurValidationDto[]>(`${this.apiUrl}`);
  }

  /**
   * Récupérer un fournisseur par son ID
   */
  getFournisseurById(id: number): Observable<FournisseurValidationDto> {
    return this.http.get<FournisseurValidationDto>(`${this.apiUrl}/${id}`);
  }

  /**
   * Valider ou rejeter un fournisseur
   */
  validerFournisseur(request: ValiderFournisseurRequest): Observable<{message: string}> {
    return this.http.post<{message: string}>(`${this.apiUrl}/valider`, request);
  }

  /**
   * Suspendre ou réactiver un fournisseur
   */
  suspendreReactiverFournisseur(fournisseurId: number, suspendre: boolean, commentaire?: string): Observable<{message: string}> {
    return this.http.post<{message: string}>(`${this.apiUrl}/suspendre`, {
      fournisseurId,
      suspendre,
      commentaire
    });
  }
}
