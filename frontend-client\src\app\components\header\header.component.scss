@use "sass:color";

// Variables globales
$mobile-breakpoint: 1400px;
$drawer-width: 400px;
$header-height: 70px;

// Styles de base communs
.main-navbar {
  background-color: var(--background-color) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0 1rem;
  .container {
    height: $header-height;
    max-width: 1280px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    //
    @media (max-width: $mobile-breakpoint) {
      justify-content: space-between !important;
      padding: 0 20px !important;
    }
    //
  }
  .brand {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    text-decoration: none;
    padding-left: 20px;
    @media (max-width: $mobile-breakpoint) {
      font-size: 20px;
      padding-left: 0;
    }
  }
}
// ============ VERSION DESKTOP ============
@media (min-width: ($mobile-breakpoint + 1)) {
  .desktop-nav {
    display: flex !important;
    align-items: center !important;
    gap: 2rem;
    width: 100%;
    .nav-bar {
      display: flex;
      gap: 1rem;
      position: relative;
      z-index: 1001;
      button {
        position: relative;
        z-index: 1002;
        font-size: 16px;
        color: var(--text-color);
        text-transform: capitalize;
        background-color: transparent;
        transition: color 0.2s ease, background-color 0.2s ease;
        border: none;
        padding: 8px 16px;
        transform: none;
        &:hover {
          color: var(--accent-color-hover) !important;
          background-color: rgba(var(--accent-color), 0.1) !important;
        }
      }
    }
    .search-container {
      width: 350px;
      transition: box-shadow 0.3s ease;

      &:focus-within {
        box-shadow: 0 2px 8px rgba(var(--accent-color), 0.1);
      }
    }
    .user-actions {
      display: flex;
      align-items: center;
      gap: 1rem;
      button {
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-color);
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.1);
          opacity: 0.8;
        }
        i {
          color: var(--text-color);
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          display: flex;
        }
      }
    }
  }
  .mobile-nav,
  .mobile-menu-container {
    display: none !important;
  }
}

// ============ VERSION MOBILE ============
@media (max-width: $mobile-breakpoint) {
  .main-navbar .container {
    justify-content: space-between;
    padding: 0 20px;
  }

  .mobile-nav {
    display: flex !important;
    align-items: center;
    gap: 1rem;
    justify-content: space-between !important;
    width: 100%;

    .mobile-header {
      display: flex;
      align-items: center;
      width: 100%;
      height: $header-height;

      .brand {
        display: flex;
        align-items: center;
        gap: 8px;

        span {
          font-size: 18px;
          font-weight: 700;
          color: var(--text-color);
        }
      }

      .mobile-actions {
        position: relative;
        display: flex;
        align-items: center;
        gap: 1rem;

        button {
          opacity: 1 !important;
          position: relative;
          display: flex;
          align-items: center;
          i {
            color: var(--text-color) !important;
            font-size: 1.5rem;
            transition: opacity 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          &:hover i {
            opacity: 0.8;
            transform: scale(1.1);
          }
        }
      }
    }

    .user-actions {
      display: flex;
      align-items: center !important;
      margin-left: auto;
      gap: 1rem;

      button {
        color: var(--text-color);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        i {
          font-size: 1.5rem;
        }

        &:hover {
          opacity: 0.8;
          transform: scale(1.1);
          transition: transform 0.2s ease, opacity 0.2s ease;
        }
      }
    }
  }

  .mobile-menu-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    flex-direction: row;
    .mobile-header {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      padding: 0 20px;
      height: $header-height;
      background: var(--background-color);
      width: 100%;
      .user-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-left: auto;
        padding: 0 16px;
        button {
          color: var(--text-color);
          display: flex;
          align-items: center;

          i {
            font-size: 1.5rem;
          }

          &:hover {
            opacity: 0.8;
            transform: scale(1.1);
          }
        }
      }
    }
    .mobile-menu-drawer {
      width: $drawer-width;
      background: var(--card-background-color);
      box-shadow: var(--card-shadow);
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow-y: auto;
      button {
        color: var(--text-color) !important;
        font-family: inherit;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 14px 16px;
        background: none;
        border: none;
        cursor: pointer;
        transition: color 0.2s ease, background-color 0.2s ease;

        &:hover {
          color: var(--accent-color-hover) !important;
          background-color: rgba(var(--accent-color), 0.1) !important;
        }
      }

      .nav-bar-mobile {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        padding: 0 1rem;
        text-align: center;
        button {
          font-size: 16px;
          color: var(--text-color);
          text-transform: capitalize;
          background-color: transparent;
          border: none;
          cursor: pointer;
          padding: 8px 16px;
          transition: color 0.2s ease, background-color 0.2s ease;
          text-align: left;
          width: 100%;
          border-radius: 4px;
          &:hover,
          &:focus {
            color: var(--accent-color-hover) !important;
            background-color: rgba(var(--accent-color), 0.1) !important;
          }
          &.active,
          &:active {
            color: var(--accent-color-hover) !important;
            background-color: rgba(var(--accent-color), 0.1) !important;
          }
        }
      }

      .mobile-menu-header {
        display: flex;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        flex-direction: row;
        height: $header-height;

        .title {
          justify-content: flex-start;
          font-size: 18px;
          font-weight: 600;
          margin-left: 25px;
          color: var(--text-color);
        }

        .close-btn {
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            margin: 0;
            padding: 0;
            font-size: 20px;
            color: var(--text-color);
          }
        }

        &:hover .close-btn i {
          transform: scale(1.1);
          opacity: 0.8;
        }
      }
    }

    .mobile-menu-backdrop {
      z-index: 999;
      backdrop-filter: blur(3px);
      transition: opacity 0.3s ease;
      opacity: 0;
      background: var(--overlay-color);

      &.active {
        opacity: 1;
      }
    }
  }

  .menu-button i {
    font-size: 24px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .search-icon {
    color: var(--text-color);
  }

  .desktop-nav {
    display: none !important;
  }
}
/* Cart icon */
.cart-icon {
  position: relative;

  i {
    font-size: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--error-color);
    color: var(--background-color);
    font-size: 10px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
/* Toggle switch */
.custom-toggle {
  background-color: var(--toggle-background-color);
  display: flex;
  align-items: center;

  i {
    color: var(--toggle-icon-color);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.7rem;
  }

  .mat-slide-toggle-bar,
  .mat-slide-toggle-thumb {
    transition: background-color 0.3s ease;
  }
}

.toggleswitch {
  display: inline-flex;
  align-items: center;
  cursor: pointer;

  .handle {
    background-color: var(--handle-background-color);
  }

  &.checked .handle {
    background-color: var(--handle-checked-background-color);
  }
}
/* Responsive display toggling */
@media (max-width: $mobile-breakpoint) {
  .desktop-nav {
    display: none !important;
  }
  .mobile-nav {
    display: flex !important;
  }
}

@media (min-width: ($mobile-breakpoint + 1)) {
  .mobile-nav {
    display: none !important;
  }
  .desktop-nav {
    display: flex !important;
  }
}