$mobile-breakpoint: 1400px;
$card-max-width: 900px;
$action-header-height: 180px;
$action-header-height-mobile: 120px;
$border-radius: 110px;

.login-container {
  display: flex;
  height: 90vh;
  justify-content: center;
  align-items: center;
  background-color: var(--background-color);
  padding: 1rem;
  transition: background-color 0.3s ease;

  @media (min-width: $mobile-breakpoint) {
    height: 90vh;
  }
  mat-card {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    max-width: $card-max-width;
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    background-color: var(--card-background-color);
    color: var(--text-color);
    transition: all 0.3s ease;
    overflow: hidden;
    padding: 0;

    .card-content-wrapper {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      flex: 1;

      @media (min-width: $mobile-breakpoint) {
        flex-direction: row;
      }
    }
    .form-section {
      padding: 2rem;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      min-height: $action-header-height-mobile;
      height: 100%;

      @media (min-width: $mobile-breakpoint) {
        height: 100%;
        width: 50%;
        display: flex;
        flex-direction: column;
        padding: 2rem 0;
      }

      mat-card-header {
        text-align: center;
        display: block;
        padding: 24px 0 0;
        margin-bottom: 2rem;

        mat-card-title {
          font-size: 1.8rem;
          font-weight: 500;
          color: var(--text-color);
        }
        @media (min-width: $mobile-breakpoint) {
          padding: auto;
        }
      }

      mat-card-content {
        padding: 0 30px 30px;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;

        @media (min-width: $mobile-breakpoint) {
          padding: 0 30px 30px;
          flex: 1;
          overflow-y: auto;
        }

        mat-form-field {
          width: 100%;
          margin-bottom: 1rem;

          ::ng-deep {
            .mat-form-field-outline {
              background-color: var(--card-background-color);
            }
            .mat-form-field-label {
              color: var(--secondary-color);
            }
            .mat-input-element {
              color: var(--text-color);
            }
          }
        }

        .forgot-password {
          display: block;
          text-align: right;
          margin: -10px 0 15px;
          color: var(--secondary-color);
          font-size: 0.85rem;
          text-decoration: none;
          transition: color 0.2s ease;

          &:hover {
            color: var(--text-color-hover);
          }
        }

        .submit-btn {
          width: 100%;
          padding: 10px;
          font-size: 1rem;
          margin-bottom: 1.5rem;
          background: var(--primary-color);
          color: var(--text-color);
          transition: background 0.3s ease;
          border: none;
          border-radius: 4px;
          cursor: pointer;

          &:hover {
            background: var(--primary-color-hover);
          }

          &:disabled {
            background: var(--secondary-color);
            cursor: not-allowed;
          }
        }
      }
    }

    .welcome-section {
      flex: 0 0 20%;
      min-height: unset;
      padding: 0;
      margin: 0;
      position: relative;
      width: 100%;
      order: -1;
      height: 20% !important;
      display: flex;
      align-items: center;
      text-align: center;
      flex-direction: column;
      justify-content: center;
      position: relative;
      border-bottom-left-radius: $border-radius;
      border-bottom-right-radius: $border-radius;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.3);
        z-index: 0;
      }

      @media (min-width: $mobile-breakpoint) {
        flex: 1 1 50%; 
        order: 1;
        width: 50%;
        flex: 1;
        height: 100% !important;
        min-height: unset;
        display: flex;
        border-top-left-radius: $border-radius;
        border-bottom-left-radius: $border-radius;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        flex-direction: column;
        overflow: hidden;
        justify-content: center;
        align-items: center;
      }

      .welcome-message {
        padding: 2rem;
        color: rgb(20, 32, 82);
        text-align: center;
        font-weight: 500;
        z-index: 2;
        position: relative;
        width: 100%;
        height: auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        mat-card-title {
          color: var(--primary-color-hover);
          width: 100%;
          margin: 0 0 1.5rem 0;
          font-size: 1.5rem;
          font-weight: 500;
          margin-bottom: 1rem;
          text-shadow: 0 2px 4px rgba(32, 49, 59, 0.5);

          @media (min-width: $mobile-breakpoint) {
            font-size: 2rem;
            margin-bottom: 1.5rem;
          }
        }

        .action-link {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          width: 100%;

          span {
            font-size: 0.9rem;
            opacity: 0.9;

            @media (min-width: $mobile-breakpoint) {
              font-size: 1.1rem;
            }
          }

          button {
            color: white !important;
            background-color: var(--accent-color);
            font-weight: 600;
            text-decoration: none;
            border-radius: 10px;
          }
        }
      }
    }
  }
}
