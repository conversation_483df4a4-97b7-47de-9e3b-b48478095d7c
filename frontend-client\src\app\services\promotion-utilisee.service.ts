import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { PromotionUtiliseeDto } from '../models/PromotionUtiliseeDto';

@Injectable({
  providedIn: 'root'
})
export class PromotionUtiliseeService {
  private readonly apiUrl = `${environment.apiUrl}/promotions-utilisees`;
  constructor(private http: HttpClient) { }
  applyPromotionToCommande(
    commandeId: number,
    codePromo: string
  ): Observable<PromotionUtiliseeDto> {
    return this.http.post<PromotionUtiliseeDto>(`${this.apiUrl}/apply`, {
      commandeId,
      code: codePromo,
    });
  }
  getPromotionsUsedByCommande(commandeId: number): Observable<PromotionUtiliseeDto[]> {
    return this.http.get<PromotionUtiliseeDto[]>(
      `${this.apiUrl}/by-commande/${commandeId}`
    );
  }
}
