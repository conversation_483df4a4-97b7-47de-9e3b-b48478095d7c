# Debug du dashboard - Vérification des produits et données
$baseUrl = "http://localhost:5014/api"

Write-Host "=== DEBUG DASHBOARD - PRODUITS ET DONNÉES ===" -ForegroundColor Red

# 1. Vérifier les produits dans la base
Write-Host "`n🔍 1. VÉRIFICATION DES PRODUITS..." -ForegroundColor Yellow

try {
    $produits = Invoke-RestMethod -Uri "$baseUrl/Produits" -Method Get
    Write-Host "✅ $($produits.Count) produits trouvés dans la base" -ForegroundColor Green
    
    if ($produits.Count -gt 0) {
        Write-Host "`n📋 PREMIERS PRODUITS:" -ForegroundColor Cyan
        $produits | Select-Object -First 5 | ForEach-Object {
            Write-Host "  - ID: $($_.id) | Nom: $($_.nom) | Fournisseur: $($_.fournisseurId) | Prix: $($_.prixVenteHT) TND | Stock: $($_.stock)" -ForegroundColor White
        }
        
        # Grouper par fournisseur
        Write-Host "`n📊 PRODUITS PAR FOURNISSEUR:" -ForegroundColor Cyan
        $produits | Group-Object fournisseurId | ForEach-Object {
            Write-Host "  - Fournisseur ID $($_.Name): $($_.Count) produits" -ForegroundColor White
        }
    } else {
        Write-Host "❌ AUCUN PRODUIT TROUVÉ !" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Erreur récupération produits: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Vérifier les commandes
Write-Host "`n🔍 2. VÉRIFICATION DES COMMANDES..." -ForegroundColor Yellow

try {
    $commandes = Invoke-RestMethod -Uri "$baseUrl/Commandes" -Method Get
    Write-Host "✅ $($commandes.Count) commandes trouvées" -ForegroundColor Green
    
    if ($commandes.Count -gt 0) {
        Write-Host "`n📋 PREMIÈRES COMMANDES:" -ForegroundColor Cyan
        $commandes | Select-Object -First 3 | ForEach-Object {
            Write-Host "  - ID: $($_.id) | Montant: $($_.montantTotal) TND | Statut: $($_.statut) | Date: $($_.dateCreation)" -ForegroundColor White
        }
    }
    
} catch {
    Write-Host "❌ Erreur récupération commandes: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Test avec un fournisseur spécifique
Write-Host "`n🔍 3. TEST AVEC FOURNISSEUR SPÉCIFIQUE..." -ForegroundColor Yellow

$fournisseurTest = 11  # Premier fournisseur de la liste

Write-Host "Test avec fournisseur ID: $fournisseurTest" -ForegroundColor White

# Vérifier les produits du fournisseur
try {
    $produitsFournisseur = Invoke-RestMethod -Uri "$baseUrl/Fournisseurs/$fournisseurTest/produits" -Method Get
    Write-Host "✅ $($produitsFournisseur.Count) produits pour le fournisseur $fournisseurTest" -ForegroundColor Green
    
    if ($produitsFournisseur.Count -gt 0) {
        Write-Host "`n📋 PRODUITS DU FOURNISSEUR $fournisseurTest :" -ForegroundColor Cyan
        $produitsFournisseur | ForEach-Object {
            Write-Host "  - $($_.nom): $($_.prixVenteHT) TND (Stock: $($_.stock))" -ForegroundColor White
        }
    }
    
} catch {
    Write-Host "❌ Erreur produits fournisseur: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. Test de connexion et statistiques
Write-Host "`n🔍 4. TEST CONNEXION ET STATISTIQUES..." -ForegroundColor Yellow

# Essayer avec différents fournisseurs
$emailsTest = @(
    "<EMAIL>",
    "<EMAIL>", 
    "<EMAIL>"
)

foreach ($email in $emailsTest) {
    Write-Host "`nTest connexion avec: $email" -ForegroundColor Gray
    
    $loginData = @{
        email = $email
        motDePasse = "123456"  # Mot de passe par défaut
    } | ConvertTo-Json
    
    $headers = @{"Content-Type" = "application/json"}
    
    try {
        $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
        Write-Host "✅ Connexion réussie - ID: $($loginResponse.utilisateur.id)" -ForegroundColor Green
        
        $headers["Authorization"] = "Bearer $($loginResponse.token)"
        $fournisseurId = $loginResponse.utilisateur.id
        
        # Test statistiques
        try {
            $stats = Invoke-RestMethod -Uri "$baseUrl/StatistiquesFournisseur/dashboard/$fournisseurId" -Method Get -Headers $headers
            Write-Host "✅ Statistiques récupérées !" -ForegroundColor Green
            Write-Host "  - Produits: $($stats.totalProduits)" -ForegroundColor White
            Write-Host "  - CA: $($stats.chiffreAffaireMensuel) TND" -ForegroundColor White
            
            break  # Sortir de la boucle si succès
            
        } catch {
            Write-Host "❌ Erreur statistiques: $($_.Exception.Message)" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "❌ Échec connexion avec $email" -ForegroundColor Red
    }
}

# 5. Vérification de l'endpoint dashboard
Write-Host "`n🔍 5. VÉRIFICATION ENDPOINT DASHBOARD..." -ForegroundColor Yellow

try {
    # Test direct de l'endpoint sans authentification pour voir s'il existe
    $response = Invoke-WebRequest -Uri "$baseUrl/StatistiquesFournisseur/dashboard/11" -Method Get -ErrorAction SilentlyContinue
    Write-Host "✅ Endpoint existe (même si 401)" -ForegroundColor Green
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "✅ Endpoint existe mais nécessite authentification" -ForegroundColor Green
    } elseif ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "❌ ENDPOINT N'EXISTE PAS !" -ForegroundColor Red
        Write-Host "Le contrôleur StatistiquesFournisseur n'est pas accessible" -ForegroundColor Red
    } else {
        Write-Host "❌ Erreur: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== DIAGNOSTIC ===" -ForegroundColor Red
Write-Host "1. Vérifiez si les produits existent dans la base" -ForegroundColor White
Write-Host "2. Vérifiez si le contrôleur StatistiquesFournisseur est bien déployé" -ForegroundColor White
Write-Host "3. Vérifiez l'authentification des fournisseurs" -ForegroundColor White
Write-Host "4. Vérifiez les routes du backend" -ForegroundColor White

Write-Host "`n=== FIN DEBUG ===" -ForegroundColor Red
