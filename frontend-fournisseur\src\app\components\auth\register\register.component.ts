import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../../services/auth.service';
import { NotificationService } from '../../../services/notification.service';

export interface RegisterFournisseurRequest {
  // Champs de UtilisateurCreateDto
  email: string;
  password: string;
  nom: string;
  prenom: string;
  phoneNumber: string;
  dateNaissance: string;

  // Champs spécifiques au fournisseur
  matriculeFiscale: string;
  raisonSociale: string;
  description?: string;
  rib: string;
  codeBanque: string;
  commission?: number;
  delaiPreparationJours?: number;
  estActif?: boolean;
  fraisLivraisonBase?: number;

  // Adresse de l'entreprise (selon AdresseCreateDto backend)
  rue: string;
  ville: string;
  codePostal: string;
  pays: string;

  logoFile: File;
}

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css']
})
export class RegisterComponent {
  registerForm: FormGroup;
  isLoading = false;
  errorMessage = '';
  successMessage = '';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private notificationService: NotificationService
  ) {
    this.registerForm = this.formBuilder.group({
      // Informations personnelles
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(8), Validators.maxLength(100)]],
      confirmPassword: ['', [Validators.required]],
      nom: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      prenom: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^\d{8,15}$/)]],
      dateNaissance: ['', [Validators.required]],

      // Informations entreprise
      matriculeFiscale: ['', [Validators.required, Validators.pattern(/^\d{8}$/)]],
      raisonSociale: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(200)]],
      description: [''],
      rib: ['', [Validators.required, Validators.pattern(/^\d{20}$/)]],
      codeBanque: ['', [Validators.required, Validators.pattern(/^\d{3}$/)]],
      commission: [0.75, [Validators.required, Validators.min(0.5), Validators.max(1)]],
      delaiPreparationJours: [2, [Validators.required, Validators.min(1), Validators.max(30)]],
      fraisLivraisonBase: [9.99, [Validators.required, Validators.min(0)]],

      // Adresse de l'entreprise (selon AdresseCreateDto backend)
      rue: ['', [Validators.required, Validators.minLength(5)]],
      ville: ['', [Validators.required, Validators.minLength(2)]],
      codePostal: ['', [Validators.required, Validators.pattern(/^\d{5}$/)]],
      pays: ['France', [Validators.required]],

      logoFile: [null] // Rendu optionnel car on ajoute un logo par défaut
    }, { validators: this.passwordMatchValidator });
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    return null;
  }

  validateFormData(formValue: any): boolean {
    // Validation du matricule fiscal (8 chiffres)
    if (!/^\d{8}$/.test(formValue.matriculeFiscale)) {
      this.errorMessage = 'Le matricule fiscal doit contenir exactement 8 chiffres.';
      return false;
    }

    // Validation du code banque (3 chiffres)
    if (!/^\d{3}$/.test(formValue.codeBanque)) {
      this.errorMessage = 'Le code banque doit contenir exactement 3 chiffres.';
      return false;
    }

    // Validation du RIB (20 chiffres et doit commencer par le code banque)
    if (!/^\d{20}$/.test(formValue.rib)) {
      this.errorMessage = 'Le RIB doit contenir exactement 20 chiffres.';
      return false;
    }

    if (!formValue.rib.startsWith(formValue.codeBanque)) {
      this.errorMessage = 'Le RIB doit commencer par le code banque.';
      return false;
    }

    // Validation du téléphone
    if (!/^\d{8,15}$/.test(formValue.phoneNumber)) {
      this.errorMessage = 'Le numéro de téléphone doit contenir entre 8 et 15 chiffres.';
      return false;
    }

    return true;
  }

  getFieldDisplayName(fieldName: string): string {
    const fieldNames: { [key: string]: string } = {
      'email': 'Email',
      'password': 'Mot de passe',
      'nom': 'Nom',
      'prenom': 'Prénom',
      'phoneNumber': 'Téléphone',
      'dateNaissance': 'Date de naissance',
      'matriculeFiscale': 'Matricule fiscal',
      'raisonSociale': 'Raison sociale',
      'rib': 'RIB',
      'codeBanque': 'Code banque',
      'description': 'Description',
      'commission': 'Commission',
      'delaiPreparationJours': 'Délai de préparation',
      'fraisLivraisonBase': 'Frais de livraison',
      'logoFile': 'Logo'
    };

    return fieldNames[fieldName] || fieldName;
  }

  onSubmit(): void {
    if (this.registerForm.valid) {
      this.isLoading = true;
      this.errorMessage = '';
      this.successMessage = '';

      const formValue = this.registerForm.value;

      // Validation côté client avant envoi
      if (!this.validateFormData(formValue)) {
        this.isLoading = false;
        return;
      }

      // Créer un FormData pour l'envoi avec fichier
      const formData = new FormData();

      // Ajouter tous les champs au FormData selon l'API (camelCase)
      formData.append('email', formValue.email);
      formData.append('password', formValue.password);
      formData.append('nom', formValue.nom);
      formData.append('prenom', formValue.prenom);
      formData.append('phoneNumber', formValue.phoneNumber);

      // Format de date ISO pour le backend
      if (formValue.dateNaissance) {
        const date = new Date(formValue.dateNaissance);
        formData.append('dateNaissance', date.toISOString());
      }

      formData.append('matriculeFiscale', formValue.matriculeFiscale);
      formData.append('raisonSociale', formValue.raisonSociale);
      formData.append('rib', formValue.rib); // Corrigé : le backend utilise camelCase
      formData.append('codeBanque', formValue.codeBanque);

      if (formValue.description) {
        formData.append('description', formValue.description);
      }

      formData.append('commission', formValue.commission?.toString() || '0.75');
      formData.append('delaiPreparationJours', formValue.delaiPreparationJours?.toString() || '2');
      formData.append('fraisLivraisonBase', formValue.fraisLivraisonBase?.toString() || '9.99');
      formData.append('estActif', 'true');

      // Ajouter les champs d'adresse (selon le format attendu par le backend)
      if (formValue.rue) {
        formData.append('adresseRue', formValue.rue);
      }
      if (formValue.ville) {
        formData.append('adresseVille', formValue.ville);
      }
      if (formValue.codePostal) {
        formData.append('adresseCodePostal', formValue.codePostal);
      }
      if (formValue.pays) {
        formData.append('adressePays', formValue.pays);
      }
      formData.append('adresseEstPrincipale', 'true'); // Première adresse = principale

      // Ajouter le fichier logo
      if (formValue.logoFile) {
        formData.append('logoFile', formValue.logoFile);
      } else {
        // Créer un fichier logo par défaut si aucun n'est sélectionné
        const defaultLogoBlob = new Blob([
          new Uint8Array([
            0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
            0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
            0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
            0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
            0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
            0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
          ])
        ], { type: 'image/png' });
        
        const defaultLogoFile = new File([defaultLogoBlob], 'default-logo.png', { type: 'image/png' });
        formData.append('logoFile', defaultLogoFile);
        console.log('📝 Logo par défaut ajouté car aucun logo n\'a été sélectionné');
      }

      // Debug: Afficher le contenu du FormData
      console.log('📋 FormData final:');
      for (let pair of formData.entries()) {
        const key = pair[0];
        const value = pair[1];
        if (value instanceof File) {
          console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
        } else {
          console.log(`${key}: ${value}`);
        }
      }

      this.authService.registerFournisseur(formData).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.successMessage = 'Inscription réussie ! Vous pouvez maintenant vous connecter.';

          // Envoyer une notification à tous les admins
          const raisonSociale = this.registerForm.get('raisonSociale')?.value;
          const email = this.registerForm.get('email')?.value;
          const matriculeFiscale = this.registerForm.get('matriculeFiscale')?.value;

          const contenuNotification = `🏪 Nouveau fournisseur inscrit : ${raisonSociale} (${email}). Matricule fiscal : ${matriculeFiscale}. Veuillez vérifier et valider le compte.`;

          this.notificationService.notifierTousLesAdmins(contenuNotification).subscribe({
            next: () => {
              console.log('✅ Notification envoyée aux admins');
            },
            error: (notifError) => {
              console.error('❌ Erreur lors de l\'envoi de la notification aux admins:', notifError);
              // Ne pas bloquer le processus d'inscription pour une erreur de notification
            }
          });

          // Rediriger vers la page de login après 2 secondes
          setTimeout(() => {
            this.router.navigate(['/login']);
          }, 2000);
        },
        error: (error) => {
          this.isLoading = false;
          
          // Gestion détaillée des erreurs
          if (error.status === 400) {
            console.error('❌ Erreur 400 - Détails:', error.error);
            
            // Essayer de parser les erreurs de validation
            if (error.error && typeof error.error === 'object') {
              if (error.error.errors) {
                // Format: { message: "Validation failed", errors: { field: ["error1", "error2"] } }
                const validationErrors = error.error.errors;
                let errorMessages: string[] = [];

                Object.keys(validationErrors).forEach(key => {
                  const fieldErrors = validationErrors[key];
                  if (Array.isArray(fieldErrors)) {
                    errorMessages.push(`${this.getFieldDisplayName(key)}: ${fieldErrors.join(', ')}`);
                  } else if (typeof fieldErrors === 'string') {
                    errorMessages.push(`${this.getFieldDisplayName(key)}: ${fieldErrors}`);
                  }
                });

                if (errorMessages.length > 0) {
                  this.errorMessage = 'Erreurs de validation:\n' + errorMessages.join('\n');
                } else {
                  this.errorMessage = error.error.message || 'Erreur de validation. Vérifiez vos données.';
                }
              } else {
                // Ancien format ou format différent
                const validationErrors = error.error;
                let errorMessages: string[] = [];

                Object.keys(validationErrors).forEach(key => {
                  const fieldErrors = validationErrors[key];
                  if (Array.isArray(fieldErrors)) {
                    errorMessages.push(`${this.getFieldDisplayName(key)}: ${fieldErrors.join(', ')}`);
                  } else if (typeof fieldErrors === 'string') {
                    errorMessages.push(`${this.getFieldDisplayName(key)}: ${fieldErrors}`);
                  }
                });

                if (errorMessages.length > 0) {
                  this.errorMessage = 'Erreurs de validation:\n' + errorMessages.join('\n');
                } else {
                  this.errorMessage = error.error?.message || 'Erreur de validation. Vérifiez vos données.';
                }
              }
            } else {
              this.errorMessage = error.error?.message || 'Erreur lors de l\'inscription. Veuillez réessayer.';
            }
          } else {
            this.errorMessage = error.error?.message || 'Erreur lors de l\'inscription. Veuillez réessayer.';
          }
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.registerForm.controls).forEach(key => {
      const control = this.registerForm.get(key);
      control?.markAsTouched();
    });
  }

  goToLogin(): void {
    this.router.navigate(['/login']);
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.registerForm.patchValue({
        logoFile: file
      });
    }
  }

  testBackendConnection(): void {
    console.log('🧪 Test de connectivité backend...');

    // Test simple avec les données du formulaire
    const formValue = this.registerForm.value;

    // Créer un objet de test sans fichier (avec noms corrects selon API camelCase)
    const testData = {
      email: formValue.email || '<EMAIL>',
      password: formValue.password || 'Test123!',
      nom: formValue.nom || 'TestNom',
      prenom: formValue.prenom || 'TestPrenom',
      phoneNumber: formValue.phoneNumber || '1234567890',
      dateNaissance: formValue.dateNaissance || '2025-06-17T06:50:12.478Z',
      matriculeFiscale: formValue.matriculeFiscale || '12345678',
      raisonSociale: formValue.raisonSociale || 'Test Company',
      rib: formValue.rib || '12312345678901234567', // RIB qui commence par le code banque 123
      codeBanque: formValue.codeBanque || '123',
      description: formValue.description || 'Test description',
      commission: 0.75,
      delaiPreparationJours: 2,
      fraisLivraisonBase: 9.99,
      estActif: true
    };

    console.log('🧪 Test data:', testData);

    // Test 1: FormData avec logo par défaut
    console.log('🧪 Test 1: FormData avec logo par défaut...');
    this.authService.registerFournisseurJSON(testData).subscribe({
      next: (response) => {
        console.log('✅ Test FormData réussi:', response);
        alert('✅ Test FormData réussi ! Le backend fonctionne.\nLe problème vient probablement du fichier logo ou des données.');

        // Si FormData fonctionne, tester avec les vraies données du formulaire
        this.testWithRealFormData();
      },
      error: (error) => {
        console.error('❌ Test FormData échoué:', error);

        if (error.status === 415) {
          alert('❌ Erreur 415 avec FormData.\nLe backend ne supporte peut-être pas cet endpoint ou il manque des headers.');
        } else if (error.status === 400) {
          alert('✅ Le backend répond (erreur 400).\nLe problème vient probablement des données ou de la validation.');
        } else {
          alert(`❌ Test FormData échoué: ${error.status} - ${error.statusText}\nVoir la console pour plus de détails.`);
        }
      }
    });
  }

  testWithRealFormData(): void {
    console.log('🧪 Test 2: Avec les vraies données du formulaire...');

    if (!this.registerForm.valid) {
      alert('❌ Le formulaire n\'est pas valide. Veuillez remplir tous les champs requis.');
      return;
    }

    const formValue = this.registerForm.value;
    const formData = new FormData();

    // Ajouter tous les champs au FormData
    formData.append('Email', formValue.email);
    formData.append('Password', formValue.password);
    formData.append('Nom', formValue.nom);
    formData.append('Prenom', formValue.prenom);
    formData.append('Telephone', formValue.phoneNumber);
    if (formValue.dateNaissance) {
      formData.append('DateNaissance', formValue.dateNaissance);
    }
    formData.append('MatriculeFiscale', formValue.matriculeFiscale);
    formData.append('RaisonSociale', formValue.raisonSociale);
    formData.append('Rib', formValue.rib);
    formData.append('CodeBanque', formValue.codeBanque);

    if (formValue.description) {
      formData.append('Description', formValue.description);
    }

    formData.append('Commission', formValue.commission?.toString() || '0.75');
    formData.append('DelaiPreparationJours', formValue.delaiPreparationJours?.toString() || '2');
    formData.append('FraisLivraisonBase', formValue.fraisLivraisonBase?.toString() || '9.99');
    formData.append('EstActif', 'true');

    // Ajouter le fichier logo
    if (formValue.logoFile) {
      formData.append('LogoFile', formValue.logoFile);
    } else {
      // Créer un fichier logo par défaut si aucun n'est sélectionné
      const defaultLogoBlob = new Blob([
        new Uint8Array([
          0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
          0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
          0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
          0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
          0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
          0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
        ])
      ], { type: 'image/png' });
      
      const defaultLogoFile = new File([defaultLogoBlob], 'default-logo.png', { type: 'image/png' });
      formData.append('LogoFile', defaultLogoFile);
    }

    console.log('🧪 FormData avec vraies données:');
    for (let pair of formData.entries()) {
      const key = pair[0];
      const value = pair[1];
      if (value instanceof File) {
        console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }

    this.authService.registerFournisseur(formData).subscribe({
      next: (response) => {
        console.log('✅ Test avec vraies données réussi:', response);
        alert('✅ Test avec vraies données réussi !\nL\'inscription devrait fonctionner maintenant.');
      },
      error: (error) => {
        console.error('❌ Test avec vraies données échoué:', error);
        alert(`❌ Test avec vraies données échoué: ${error.status} - ${error.statusText}\nVoir la console pour plus de détails.`);
      }
    });
  }

  testMinimalData(): void {
    console.log('🧪 Test avec données minimales...');
    
    this.authService.testEndpointWithMinimalData().subscribe({
      next: (response) => {
        console.log('✅ Test minimal réussi:', response);
        alert('✅ Test minimal réussi !\nLe backend fonctionne avec FormData et logo par défaut.');
      },
      error: (error) => {
        console.error('❌ Test minimal échoué:', error);
        alert(`❌ Test minimal échoué: ${error.status} - ${error.statusText}\nVoir la console pour plus de détails.`);
      }
    });
  }

  // Getters pour faciliter l'accès aux contrôles dans le template
  get email() { return this.registerForm.get('email'); }
  get password() { return this.registerForm.get('password'); }
  get confirmPassword() { return this.registerForm.get('confirmPassword'); }
  get nom() { return this.registerForm.get('nom'); }
  get prenom() { return this.registerForm.get('prenom'); }
  get phoneNumber() { return this.registerForm.get('phoneNumber'); }
  get dateNaissance() { return this.registerForm.get('dateNaissance'); }
  get matriculeFiscale() { return this.registerForm.get('matriculeFiscale'); }
  get raisonSociale() { return this.registerForm.get('raisonSociale'); }
  get description() { return this.registerForm.get('description'); }
  get rib() { return this.registerForm.get('rib'); }
  get codeBanque() { return this.registerForm.get('codeBanque'); }
  get commission() { return this.registerForm.get('commission'); }
  get delaiPreparationJours() { return this.registerForm.get('delaiPreparationJours'); }
  get fraisLivraisonBase() { return this.registerForm.get('fraisLivraisonBase'); }

  // Getters pour les champs d'adresse (selon AdresseCreateDto backend)
  get rue() { return this.registerForm.get('rue'); }
  get ville() { return this.registerForm.get('ville'); }
  get codePostal() { return this.registerForm.get('codePostal'); }
  get pays() { return this.registerForm.get('pays'); }

  get logoFile() { return this.registerForm.get('logoFile'); }
}
