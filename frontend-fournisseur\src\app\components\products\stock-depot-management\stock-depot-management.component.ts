import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { 
  Depot, 
  ProduitDepot, 
  StockDepot, 
  StockDepotUpdate,
  StockDepotBulkUpdate,
  StatistiquesDepot,
  FiltreStockDepot 
} from '../../../models/depot.model';
import { DepotService } from '../../../services/depot.service';
import { ImageUrlService } from '../../../services/image-url.service';
import { AuthService } from '../../../services/auth.service';

interface StockEditMode {
  stockDepotId: number;
  nouvelleQuantite: number;
  motif: string;
}

@Component({
  selector: 'app-stock-depot-management',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './stock-depot-management.component.html',
  styleUrls: ['./stock-depot-management.component.css']
})
export class StockDepotManagementComponent implements OnInit {
  // Données principales
  depots: Depot[] = [];
  produits: ProduitDepot[] = [];
  selectedDepot: Depot | null = null;
  statistiques: StatistiquesDepot | null = null;

  // États de l'interface
  isLoading = false;
  error = '';
  viewMode: 'depot' | 'produit' = 'depot';
  
  // Filtres et recherche
  searchQuery = '';
  filtre: FiltreStockDepot = {};
  
  // Édition rapide
  editModes: Map<number, StockEditMode> = new Map();
  bulkEditMode = false;
  bulkUpdates: { produitId: number; quantite: number; motif: string }[] = [];
  
  // Modal de transfert
  showTransferModal = false;
  transferData = {
    produitId: 0,
    depotSourceId: 0,
    depotDestinationId: 0,
    quantite: 0,
    motif: ''
  };

  // Options pour les motifs
  motifsStock = [
    'Réapprovisionnement',
    'Vente',
    'Retour client',
    'Produit défectueux',
    'Inventaire',
    'Correction',
    'Transfert',
    'Échantillon',
    'Perte/Vol',
    'Autre'
  ];

  constructor(
    private depotService: DepotService,
    private imageUrlService: ImageUrlService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  /**
   * Actualiser les données pour le fournisseur connecté
   */
  refreshData(): void {
    console.log('🔄 Actualisation des données stock-dépôt...');

    // Vider le cache localStorage pour forcer la régénération
    localStorage.removeItem('mock-depots');
    localStorage.removeItem('mock-stocks-depots');
    localStorage.removeItem('mock-produits-depot');

    this.depotService.refreshDataForCurrentUser();

    // Réinitialiser les variables locales
    this.depots = [];
    this.selectedDepot = null;
    this.produits = [];
    this.statistiques = null;

    // Recharger les données
    this.loadData();
  }

  /**
   * Charger les données depuis l'API
   */
  loadData(): void {
    this.isLoading = true;
    this.error = '';

    const fournisseurId = this.authService.getCurrentUserId();
    if (!fournisseurId) {
      this.error = 'Utilisateur non connecté';
      this.isLoading = false;
      return;
    }

    // Charger les dépôts
    this.depotService.getDepotsByFournisseur(fournisseurId).subscribe({
      next: (depots) => {
        this.depots = depots;
        if (depots.length > 0) {
          this.selectedDepot = depots.find(d => d.estPrincipal) || depots[0];
          this.loadProduitsAvecStocks();
          this.loadStatistiques();
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Erreur lors du chargement des dépôts';
        console.error('Error loading depots:', error);
        this.isLoading = false;
      }
    });
  }

  /**
   * Charger les produits avec leurs stocks par dépôt
   */
  loadProduitsAvecStocks(): void {
    const fournisseurId = this.authService.getCurrentUserId();
    if (!fournisseurId) return;

    this.depotService.getProduitsAvecStocksDepots(fournisseurId, this.filtre).subscribe({
      next: (produits) => {
        this.produits = produits;
      },
      error: (error) => {
        console.error('Error loading produits:', error);
      }
    });
  }

  /**
   * Charger les statistiques du dépôt sélectionné
   */
  loadStatistiques(): void {
    if (!this.selectedDepot) return;

    this.depotService.getStatistiquesDepot(this.selectedDepot.id).subscribe({
      next: (stats) => {
        this.statistiques = stats;
      },
      error: (error) => {
        console.error('Error loading statistics:', error);
      }
    });
  }

  /**
   * Changer de dépôt sélectionné
   */
  onDepotChange(): void {
    this.loadProduitsAvecStocks();
    this.loadStatistiques();
    this.exitAllEditModes();
  }

  /**
   * Obtenir les produits filtrés
   */
  getFilteredProduits(): ProduitDepot[] {
    let filtered = [...this.produits];

    // Filtre par recherche
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(produit =>
        produit.nom.toLowerCase().includes(query) ||
        produit.referenceOriginal.toLowerCase().includes(query) ||
        (produit.referenceFournisseur && produit.referenceFournisseur.toLowerCase().includes(query))
      );
    }

    // Filtres spécifiques
    if (this.filtre.rupture) {
      filtered = filtered.filter(produit => 
        this.getStockDepot(produit, this.selectedDepot?.id || 0)?.quantite === 0
      );
    }

    if (this.filtre.stockFaible) {
      filtered = filtered.filter(produit => {
        const stock = this.getStockDepot(produit, this.selectedDepot?.id || 0);
        return stock && stock.quantite > 0 && stock.quantite <= 5;
      });
    }

    if (this.filtre.seuilAlerte) {
      filtered = filtered.filter(produit => {
        const stock = this.getStockDepot(produit, this.selectedDepot?.id || 0);
        return stock && stock.quantite <= stock.seuilAlerte;
      });
    }

    return filtered;
  }

  /**
   * Obtenir le stock d'un produit dans un dépôt spécifique
   */
  getStockDepot(produit: ProduitDepot, depotId: number): StockDepot | undefined {
    return produit.stocksDepots.find(s => s.depotId === depotId);
  }

  /**
   * Obtenir l'URL de l'image d'un produit
   */
  getProductImageUrl(produit: ProduitDepot): string {
    if (produit.imagePrincipaleUrl) {
      return this.imageUrlService.getProduitImageUrl(produit.imagePrincipaleUrl);
    }
    return this.imageUrlService.getPlaceholderUrl();
  }

  /**
   * Obtenir la classe CSS pour le niveau de stock
   */
  getStockLevelClass(stock: StockDepot): string {
    if (stock.quantite === 0) return 'stock-empty';
    if (stock.quantite <= stock.seuilAlerte) return 'stock-alert';
    if (stock.quantite <= 5) return 'stock-low';
    return 'stock-ok';
  }

  /**
   * Obtenir le texte du statut de stock
   */
  getStockStatusText(stock: StockDepot): string {
    if (stock.quantite === 0) return 'Rupture';
    if (stock.quantite <= stock.seuilAlerte) return 'Alerte';
    if (stock.quantite <= 5) return 'Faible';
    return 'Suffisant';
  }

  // ==================== ÉDITION RAPIDE ====================

  /**
   * Entrer en mode édition pour un stock
   */
  enterEditMode(stock: StockDepot): void {
    this.editModes.set(stock.id, {
      stockDepotId: stock.id,
      nouvelleQuantite: stock.quantite,
      motif: 'Correction'
    });
  }

  /**
   * Sortir du mode édition
   */
  exitEditMode(stockId: number): void {
    this.editModes.delete(stockId);
  }

  /**
   * Sortir de tous les modes édition
   */
  exitAllEditModes(): void {
    this.editModes.clear();
    this.bulkEditMode = false;
    this.bulkUpdates = [];
  }

  /**
   * Vérifier si un stock est en mode édition
   */
  isInEditMode(stockId: number): boolean {
    return this.editModes.has(stockId);
  }

  /**
   * Obtenir les données d'édition d'un stock
   */
  getEditMode(stockId: number): StockEditMode | undefined {
    return this.editModes.get(stockId);
  }

  /**
   * Sauvegarder les modifications d'un stock
   */
  saveStockEdit(stockId: number): void {
    const editMode = this.editModes.get(stockId);
    if (!editMode) return;

    if (!editMode.motif.trim()) {
      alert('Veuillez saisir un motif pour cette modification');
      return;
    }

    const stockUpdate: StockDepotUpdate = {
      id: editMode.stockDepotId,
      quantite: editMode.nouvelleQuantite,
      motif: editMode.motif
    };

    this.depotService.updateStockDepot(stockUpdate).subscribe({
      next: () => {
        this.exitEditMode(stockId);
        this.loadProduitsAvecStocks();
        this.loadStatistiques();
        // Notification de succès
        this.showSuccessMessage('Stock mis à jour avec succès');
      },
      error: (error) => {
        console.error('Error updating stock:', error);
        alert('Erreur lors de la mise à jour du stock');
      }
    });
  }

  /**
   * Activer le mode édition en lot
   */
  toggleBulkEditMode(): void {
    this.bulkEditMode = !this.bulkEditMode;
    if (!this.bulkEditMode) {
      this.bulkUpdates = [];
    }
    this.exitAllEditModes();
  }

  /**
   * Sauvegarder toutes les modifications en lot
   */
  saveBulkEdits(): void {
    if (!this.selectedDepot || this.bulkUpdates.length === 0) return;

    const bulkUpdate: StockDepotBulkUpdate = {
      depotId: this.selectedDepot.id,
      updates: this.bulkUpdates.filter(u => u.motif.trim() !== '')
    };

    if (bulkUpdate.updates.length === 0) {
      alert('Aucune modification valide à sauvegarder');
      return;
    }

    this.depotService.updateStocksBulk(bulkUpdate).subscribe({
      next: () => {
        this.bulkEditMode = false;
        this.bulkUpdates = [];
        this.loadProduitsAvecStocks();
        this.loadStatistiques();
        this.showSuccessMessage(`${bulkUpdate.updates.length} stocks mis à jour avec succès`);
      },
      error: (error) => {
        console.error('Error bulk updating stocks:', error);
        alert('Erreur lors de la mise à jour en lot');
      }
    });
  }

  // ==================== UTILITAIRES ====================

  /**
   * Formater le prix
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND'
    }).format(price);
  }

  /**
   * Afficher un message de succès
   */
  private showSuccessMessage(message: string): void {
    // Pour l'instant, utiliser alert. Dans une vraie app, utiliser un service de notification
    alert(message);
  }

  /**
   * Calculer la valeur du stock d'un produit dans le dépôt
   */
  calculateStockValue(produit: ProduitDepot, stock: StockDepot): number {
    return stock.quantite * produit.prixAchatHT;
  }

  /**
   * Réinitialiser les données (pour le développement)
   */
  resetData(): void {
    this.depotService.resetMockData();
    this.loadData();
  }

  /**
   * Mettre à jour une modification en lot
   */
  updateBulkEdit(produitId: number, event: any): void {
    const nouvelleQuantite = parseInt(event.target.value) || 0;
    const existingIndex = this.bulkUpdates.findIndex(u => u.produitId === produitId);

    if (existingIndex >= 0) {
      this.bulkUpdates[existingIndex].quantite = nouvelleQuantite;
    } else {
      this.bulkUpdates.push({
        produitId: produitId,
        quantite: nouvelleQuantite,
        motif: ''
      });
    }
  }

  /**
   * Mettre à jour le motif d'une modification en lot
   */
  updateBulkEditMotif(produitId: number, event: any): void {
    const motif = event.target.value;
    const existingIndex = this.bulkUpdates.findIndex(u => u.produitId === produitId);

    if (existingIndex >= 0) {
      this.bulkUpdates[existingIndex].motif = motif;
    } else {
      // Trouver la quantité actuelle du produit
      const produit = this.produits.find(p => p.id === produitId);
      const stock = produit ? this.getStockDepot(produit, this.selectedDepot?.id || 0) : null;

      if (stock) {
        this.bulkUpdates.push({
          produitId: produitId,
          quantite: stock.quantite,
          motif: motif
        });
      }
    }
  }
}
