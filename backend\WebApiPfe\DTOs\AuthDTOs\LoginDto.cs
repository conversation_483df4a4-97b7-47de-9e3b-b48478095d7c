﻿using System.ComponentModel.DataAnnotations;
using WebApiPfe.DTOs.ReadDTOs;

namespace WebApiPfe.DTOs.AuthDTO
{
    public class LoginDto
    {
        [Required]
        public required string Email { get; set; }

        [Required]
        public required string MotDePasse { get; set; }
    }

    public class AuthResponseDto
    {
        public required string Token { get; set; }
        public required DateTime Expiration { get; set; }
        public required UtilisateurBaseDto Utilisateur { get; set; }
    }
}
