import { DetailsCommandeDto } from "./DetailsCommandeDto";
import { PaiementResponseDto } from "./PaiementResponseDto";

export type StatutCommande = 'Brouillon' | 'EnAttente' | 'Validee' | 'EnPreparation' | 'Expediee' | 'Livree' | 'Annulee';

export interface CommandeDto {
  id: number;
  clientId: number;
  dateCreation: Date;
  statut: StatutCommande;
  montantTotal: number;
  montantHT?: number;
  montantTVA?: number;
  fraisLivraison?: number;
  codePromo?: string;
  details: DetailsCommandeDto[];
  paiement?: PaiementResponseDto;
  commandesFournisseurs?: CommandeFournisseurDto[];
}

export interface CommandeFournisseurDto {
  id: number;
  reference: string;
  fournisseurId: number;
  nomFournisseur: string;
  matriculeFiscale: string;
  dateCreation: Date;
  dateLivraison?: Date;
  fraisLivraison: number;
  statut: string;
  numeroBonLivraison: string;
  lignesCommande: LigneCommandeFournisseurDto[];
  montantTotal: number;
}

export interface LigneCommandeFournisseurDto {
  id: number;
  commandeId: number;
  produitId: number;
  nomProduit: string;
  referenceProduit: string;
  quantite: number;
  prixUnitaire: number;
  totalLigne: number;
  imagePrincipale?: string;
}

// DTO pour créer une commande
export interface CreateCommandeDto {
  clientId: number;
  detailsCommandes: CreateDetailsCommandeDto[];
  codePromo?: string;
}

// DTO pour créer un détail de commande
export interface CreateDetailsCommandeDto {
  produitId: number;
  quantite: number;
}
  