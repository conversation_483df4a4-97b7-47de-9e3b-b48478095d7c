using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Dto;
using WebApiPfe.Models.Enum;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = "Fournisseur")]
    public class StatistiquesFournisseurController : ControllerBase
    {
        private readonly AppDbContext _context;

        public StatistiquesFournisseurController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet("dashboard/{fournisseurId}")]
        public async Task<ActionResult<StatistiquesDashboardDto>> GetDashboardStats(int fournisseurId)
        {
            try
            {
                // Vérifier que le fournisseur existe
                var fournisseur = await _context.Fournisseurs
                    .FirstOrDefaultAsync(f => f.Id == fournisseurId);

                if (fournisseur == null)
                {
                    return NotFound("Fournisseur non trouvé");
                }

                // Récupérer les produits du fournisseur
                var produits = await _context.Produits
                    .Where(p => p.FournisseurId == fournisseurId)
                    .ToListAsync();

                // Récupérer les commandes du fournisseur (via les détails de commande)
                var commandesDetails = await _context.DetailsCommandes
                    .Include(dc => dc.Commande)
                    .Include(dc => dc.Produit)
                    .Where(dc => dc.Produit.FournisseurId == fournisseurId)
                    .ToListAsync();

                var commandes = commandesDetails
                    .GroupBy(dc => dc.Commande)
                    .Select(g => g.Key)
                    .ToList();

                // Calculer les statistiques
                var maintenant = DateTime.Now;
                var debutMoisActuel = new DateTime(maintenant.Year, maintenant.Month, 1);
                var debutMoisPrecedent = debutMoisActuel.AddMonths(-1);

                // Commandes du mois actuel
                var commandesMoisActuel = commandes
                    .Where(c => c.DateCreation >= debutMoisActuel)
                    .ToList();

                // Commandes du mois précédent
                var commandesMoisPrecedent = commandes
                    .Where(c => c.DateCreation >= debutMoisPrecedent && c.DateCreation < debutMoisActuel)
                    .ToList();

                // Calculer le chiffre d'affaires
                var chiffreAffaireMensuel = commandesMoisActuel
                    .Where(c => c.Statut == StatutCommande.Validee || c.Statut == StatutCommande.Livree)
                    .Sum(c => c.MontantTotal);

                var chiffreAffairePrecedent = commandesMoisPrecedent
                    .Where(c => c.Statut == StatutCommande.Validee || c.Statut == StatutCommande.Livree)
                    .Sum(c => c.MontantTotal);

                // Calculer l'évolution
                var pourcentageEvolution = 0.0;
                if (chiffreAffairePrecedent > 0)
                {
                    pourcentageEvolution = ((double)(chiffreAffaireMensuel - chiffreAffairePrecedent) / (double)chiffreAffairePrecedent) * 100;
                }

                // Commandes actives
                var commandesActives = commandes
                    .Count(c => c.Statut != StatutCommande.Livree && c.Statut != StatutCommande.Annulee);

                // Livraisons en cours
                var livraisonsEnCours = commandes
                    .Count(c => c.Statut == StatutCommande.EnPreparation || c.Statut == StatutCommande.Expediee);

                // Top produits (les plus vendus - basé sur le stock initial moins le stock actuel)
                var topProduits = produits
                    .OrderByDescending(p => p.Stock) // Temporaire - on peut améliorer avec des vraies stats de vente
                    .Take(5)
                    .Select(p => new ProduitConsulteDto
                    {
                        Id = p.Id,
                        Nom = p.Nom,
                        Prix = p.PrixVenteHT,
                        NombreConsultations = 0, // Pas de champ consultations pour l'instant
                        ImageUrl = "", // Pas d'image URL directe
                        Stock = p.Stock,
                        Categorie = "Non catégorisé" // Pas de relation directe
                    })
                    .ToList();

                // Commandes récentes
                var commandesRecentes = commandes
                    .OrderByDescending(c => c.DateCreation)
                    .Take(5)
                    .Select(c => new CommandeRecenteDto
                    {
                        Id = c.Id.ToString(),
                        Reference = c.Id.ToString(), // Pas de NumeroCommande dans l'entité
                        Client = "Client", // Pas d'accès direct au client
                        Amount = c.MontantTotal,
                        Status = c.Statut.ToString(),
                        Date = c.DateCreation
                    })
                    .ToList();

                var result = new StatistiquesDashboardDto
                {
                    TotalProduits = produits.Count,
                    CommandesActives = commandesActives,
                    LivraisonsEnCours = livraisonsEnCours,
                    ChiffreAffaireMensuel = chiffreAffaireMensuel,
                    EvolutionVentes = new EvolutionVentesDto
                    {
                        MoisActuel = chiffreAffaireMensuel,
                        MoisPrecedent = chiffreAffairePrecedent,
                        PourcentageEvolution = Math.Round(pourcentageEvolution, 1)
                    },
                    TopProduits = topProduits,
                    CommandesRecentes = commandesRecentes
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur lors du calcul des statistiques: {ex.Message}");
            }
        }

        [HttpGet("evolution-ventes/{fournisseurId}")]
        public async Task<ActionResult<List<EvolutionVentesParMoisDto>>> GetEvolutionVentes(int fournisseurId)
        {
            try
            {
                var maintenant = DateTime.Now;
                var debut12Mois = maintenant.AddMonths(-12);

                var commandesDetails = await _context.DetailsCommandes
                    .Include(dc => dc.Commande)
                    .Include(dc => dc.Produit)
                    .Where(dc => dc.Produit.FournisseurId == fournisseurId)
                    .Where(dc => dc.Commande.DateCreation >= debut12Mois)
                    .Where(dc => dc.Commande.Statut == StatutCommande.Validee || dc.Commande.Statut == StatutCommande.Livree)
                    .ToListAsync();

                var evolutionParMois = new List<EvolutionVentesParMoisDto>();

                for (int i = 11; i >= 0; i--)
                {
                    var moisCible = maintenant.AddMonths(-i);
                    var debutMois = new DateTime(moisCible.Year, moisCible.Month, 1);
                    var finMois = debutMois.AddMonths(1);

                    var ventesMois = commandesDetails
                        .Where(dc => dc.Commande.DateCreation >= debutMois && dc.Commande.DateCreation < finMois)
                        .Sum(dc => dc.Commande.MontantTotal);

                    evolutionParMois.Add(new EvolutionVentesParMoisDto
                    {
                        Mois = moisCible.ToString("yyyy-MM"),
                        NomMois = moisCible.ToString("MMMM yyyy", new System.Globalization.CultureInfo("fr-FR")),
                        Ventes = ventesMois
                    });
                }

                return Ok(evolutionParMois);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur lors du calcul de l'évolution des ventes: {ex.Message}");
            }
        }

        [HttpGet("promotions-utilisees/{fournisseurId}")]
        public async Task<ActionResult<List<PromotionUtiliseeStatsDto>>> GetPromotionsUtilisees(int fournisseurId, [FromQuery] int limit = 10)
        {
            try
            {
                var promotionsUtilisees = await _context.PromotionsUtilisees
                    .Include(pu => pu.Promotion)
                    .Include(pu => pu.Commande)
                    .Where(pu => pu.Promotion.FournisseurId == fournisseurId)
                    .GroupBy(pu => pu.Promotion)
                    .Select(g => new PromotionUtiliseeStatsDto
                    {
                        Id = g.Key.Id,
                        NomPromotion = g.Key.NomAffichage ?? "Promotion sans nom",
                        CodePromo = g.Key.CodePromo,
                        Type = g.Key.Type.ToString(),
                        NombreUtilisations = g.Count(),
                        EconomieGeneree = g.Sum(pu => pu.MontantEconomise)
                    })
                    .OrderByDescending(p => p.NombreUtilisations)
                    .Take(limit)
                    .ToListAsync();

                return Ok(promotionsUtilisees);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Erreur lors du calcul des promotions utilisées: {ex.Message}");
            }
        }
    }
}
