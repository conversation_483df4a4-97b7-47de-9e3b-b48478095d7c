.back-to-top-btn {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--primary-color, #3f51b5);
  color: var(--text-color);
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s, visibility 0.3s, transform 0.3s;
  opacity: 0.9;
  &:hover {
    transform: translateY(-3px);
    opacity: 1;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    visibility: visible;
  }

  svg {
    width: 24px;
    height: 24px;
  }

  // Version mobile
  @media (max-width: 768px) {
    width: 40px;
    height: 40px;
    bottom: 20px;
    right: 20px;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 0.9;
    transform: translateY(0);
  }
}

.back-to-top-btn {
  animation: fadeIn 0.3s ease-out;
}
