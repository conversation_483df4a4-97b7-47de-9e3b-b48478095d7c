﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Exceptions;
using WebApiPfe.Services.Interfaces;
using static WebApiPfe.DTOs.ReadDTOs.FournisseurDto;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FournisseursController : ControllerBase
    {
        private readonly IFournisseurService _service;

        public FournisseursController(IFournisseurService service)
        {
            _service = service;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<FournisseurDto>>> GetAll([FromQuery] bool onlyActive = true)
        {
            return Ok(await _service.GetAllAsync(onlyActive));
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<FournisseurDto>> GetById(int id)
        {
            var fournisseur = await _service.GetByIdAsync(id);
            return fournisseur != null ? Ok(fournisseur) : NotFound();
        }

        [HttpPost]
        [Consumes("multipart/form-data")]
        public async Task<ActionResult<FournisseurDto>> Create([FromForm] FournisseurCreateDto dto,
            [FromForm] string? adresseRue = null,
            [FromForm] string? adresseVille = null,
            [FromForm] string? adresseCodePostal = null,
            [FromForm] string? adressePays = null,
            [FromForm] bool adresseEstPrincipale = true)
        {
            // Création de l'adresse si les champs sont fournis
            List<AdresseCreateDto>? adresses = null;
            if (!string.IsNullOrEmpty(adresseRue) && !string.IsNullOrEmpty(adresseVille))
            {
                adresses = new List<AdresseCreateDto>
                {
                    new AdresseCreateDto
                    {
                        Rue = adresseRue,
                        Ville = adresseVille,
                        CodePostal = adresseCodePostal ?? "",
                        Pays = adressePays ?? "Tunisie",
                        EstPrincipale = adresseEstPrincipale,
                        EntityId = 0
                    }
                };
            }

            // Debug: Afficher les données reçues
            Console.WriteLine($"🔍 FournisseurCreateDto reçu:");
            Console.WriteLine($"  - Email: {dto.Email}");
            Console.WriteLine($"  - Nom: {dto.Nom}");
            Console.WriteLine($"  - Adresse Rue: {adresseRue}");
            Console.WriteLine($"  - Adresse Ville: {adresseVille}");
            Console.WriteLine($"  - Adresses count: {adresses?.Count ?? 0}");

            if (adresses != null)
            {
                for (int i = 0; i < adresses.Count; i++)
                {
                    var adresse = adresses[i];
                    Console.WriteLine($"  - Adresse[{i}]: {adresse.Rue}, {adresse.Ville}, {adresse.CodePostal}");
                }
            }

            if (!ModelState.IsValid)
            {
                var errors = ModelState
                    .Where(e => e.Value.Errors.Count > 0)
                    .ToDictionary(
                        kvp => kvp.Key,
                        kvp => kvp.Value.Errors.Select(e => e.ErrorMessage).ToArray()
                    );

                Console.WriteLine($"❌ ModelState invalide:");
                foreach (var error in errors)
                {
                    Console.WriteLine($"  - {error.Key}: {string.Join(", ", error.Value)}");
                }

                return BadRequest(new
                {
                    message = "Validation failed",
                    errors
                });
            }

            try
            {
                var result = await _service.CreateAsync(dto, adresses);
                return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (IdentityException ex)
            {
                return BadRequest(new { message = "Erreur d'identité", errors = ex.Errors });
            }
            catch (Microsoft.EntityFrameworkCore.DbUpdateException ex) when (ex.InnerException?.Message.Contains("IX_AspNetUsers_MatriculeFiscale") == true)
            {
                return BadRequest(new { message = "Ce matricule fiscal est déjà utilisé par un autre fournisseur." });
            }
            catch (Microsoft.EntityFrameworkCore.DbUpdateException ex) when (ex.InnerException?.Message.Contains("IX_AspNetUsers_Email") == true)
            {
                return BadRequest(new { message = "Cette adresse email est déjà utilisée." });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors de la création du fournisseur: {ex.Message}");
                return StatusCode(500, new { message = "Une erreur interne est survenue lors de la création du fournisseur." });
            }
        }


        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, FournisseurUpdateDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            await _service.UpdateAsync(id, dto);
            return NoContent();
        }

        [HttpPatch("{id}/toggle-status")]
        public async Task<IActionResult> ToggleStatus(int id)
        {
            await _service.ToggleStatusAsync(id);
            return NoContent();
        }

        [HttpGet("{id}/produits")]
        public async Task<ActionResult<IEnumerable<ProduitDto>>> GetProduits(int id)
        {
            return Ok(await _service.GetProduitsByFournisseurAsync(id));
        }

        [HttpGet("valider-rib")]
        public async Task<ActionResult<bool>> ValiderRIB([FromQuery] string rib, [FromQuery] string codeBanque)
        {
            return Ok(await _service.ValiderRIBAsync(rib, codeBanque));
        }
        [HttpGet("exists/{id}")]
        public async Task<ActionResult<bool>> Exists(int id)
            => Ok(await _service.ExistsAsync(id));

        [HttpPatch("{id}/commission")]
        public async Task<IActionResult> UpdateCommission(int id, [FromBody] decimal commission)
        {
            try
            {
                await _service.UpdateCommissionAsync(id, commission);
                return NoContent();
            }
            catch (ArgumentOutOfRangeException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (NotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }
        [HttpDelete("{id:int}")]
        public async Task<IActionResult> Delete(int id)
        {

            try
            {
                var fournisseur = await _service.GetByIdAsync(id); if (fournisseur == null)
                    return NotFound("Fournisseur non trouvé.");

                var result = await _service.DeleteAsync(id);
                if (!result)
                    return BadRequest("La suppression a échoué.");

                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Une erreur est survenue lors de la suppression du fournisseur." + ex.Message);
            }
        }
        [HttpPost("{id}/adresses")]
        public async Task<ActionResult<AdresseDto>> AjouterAdresse(int id, AdresseCreateDto dto)
        {
            try
            {
                var adresse = await _service.AjouterAdresseAsync(id, dto);
                return Ok(adresse);
            }
            catch (NotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
        }
    }
}