﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class RemboursementController : ControllerBase
    {
        private readonly IRemboursementService _service;
        private readonly ILogger<RemboursementController> _logger;

        public RemboursementController(IRemboursementService service, ILogger<RemboursementController> logger)
        {
            _service = service;
            _logger = logger;
        }

        [HttpGet("{id:int}")]
        public async Task<ActionResult<RemboursementDto>> Get(int id)
        {
            try
            {
                var remboursement = await _service.GetByIdAsync(id);
                return Ok(remboursement);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération du remboursement {id}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpGet("commande/{commandeId:int}")]
        public async Task<ActionResult<List<RemboursementDto>>> GetByCommande(int commandeId)
        {
            try
            {
                var remboursements = await _service.GetByCommandeAsync(commandeId);
                return Ok(remboursements);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération des remboursements pour la commande {commandeId}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpGet("statut/{statut}")]
        public async Task<ActionResult<List<RemboursementDto>>> GetByStatut(StatutRemboursement statut)
        {
            try
            {
                var remboursements = await _service.GetByStatutAsync(statut);
                return Ok(remboursements);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération des remboursements avec statut {statut}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPost]
        public async Task<ActionResult<RemboursementDto>> Create([FromBody] CreateRemboursementDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var remboursement = await _service.CreateAsync(dto);
                return CreatedAtAction(nameof(Get), new { id = remboursement.Id }, remboursement);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création du remboursement");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPut("{id:int}")]
        public async Task<IActionResult> Update(int id, [FromBody] UpdateRemboursementDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                await _service.UpdateAsync(id, dto);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la mise à jour du remboursement {id}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPost("{id:int}/process")]
        public async Task<IActionResult> ProcessRemboursement(int id, [FromBody] ProcessRemboursementDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                await _service.ProcessRemboursementAsync(id, dto);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors du traitement du remboursement {id}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpGet("utilisateur/{userId}")]
        public async Task<ActionResult<List<RemboursementDto>>> GetByUser(int userId)
        {
            try
            {
                var remboursements = await _service.GetByUserAsync(userId);
                return Ok(remboursements);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération des remboursements pour l'utilisateur {userId}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }
    }
}
