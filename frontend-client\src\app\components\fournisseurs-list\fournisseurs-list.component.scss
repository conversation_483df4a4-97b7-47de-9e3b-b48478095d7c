$mobile-breakpoint: 1400px;
$transition-speed: 0.3s;

@media (min-width: ($mobile-breakpoint + 1)) {
  .fournisseurs-container {
    position: absolute;
    left: 0;
    width: 100%;
    top: calc(100% + 18px);
    z-index: 1000;
    animation: fadeIn $transition-speed ease-out;

    .fournisseurs-hitbox {
      position: absolute;
      top: -18px;
      height: 12px;
      width: 100%;
      background: transparent;
      z-index: 5;
    }

    .fournisseurs-visible {
      background: var(--card-background-color);
      box-shadow: var(--shadow-lg);
      padding: 24px;
      height: 360px;
      margin-top: -1px;
      border-radius: 0 0 12px 12px;
      overflow-y: auto;
      padding-right: 8px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--accent-color);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }
    }

    .fournisseurs-content {
      display: block;
      align-items: start;

      h2 {
        font-size: 1.6rem;
        margin-bottom: 2rem;
        color: var(--accent-color);
        align-items: center;
        align-content: center;
        justify-content: center;
      }

      .fournisseurs-section {
        position: sticky;

        .fournisseurs-list {
          display: grid;
          margin: 0;
          padding: 0;
          grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
          gap: 12px;
          justify-content: space-between;
          text-align: center;
        }

        .fournisseur-item {
          transition: transform $transition-speed;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;

          &:hover {
            transform: translateY(-3px) scale(1.03);
          }

          .button {
            background: transparent !important;

            .fournisseur-link {
              background: transparent !important;
            }
          }

          .fournisseur-name {
            width: 100%;
            text-align: center;
            padding-top: 4px;
            margin: 0 auto;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .fournisseur-image {
          width: 100%;
          height: auto;
          max-height: 50px;
          margin: 0 auto 0px;
          object-fit: contain;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
      }
    }
  }
}

@media (max-width: $mobile-breakpoint) {
  .mobile-fournisseurs {
    .mobile-fournisseurs-content {
      padding: 0 10px;
      background: transparent;

      .mobile-fournisseurs-list {
        display: grid;
        gap: 4px;
        padding: 0;
        margin: 0;
        grid-template-columns: repeat(2, 1fr);

        .mobile-fournisseur-item {
          list-style: none;
          text-align: center;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          .mobile-fournisseur-image {
            width: 80px;
            height: auto;
            max-height: 50px;
            object-fit: contain;
            margin-bottom: 8px;
          }
          .mobile-fournisseur-link {
            display: block;
            padding: 4px 10px;
            background: transparent;
            border: none;
            text-align: left;
            transition: all $transition-speed;
            color: var(--text-color);
            white-space: normal;
            word-wrap: break-word;
            hyphens: auto;
            line-height: 1.4;
            &:hover {
              transform: scale(0.98);
              background: var(--card-background-color-hover);
              border-radius: 10px;
            }
          }
        }
      }
      .see-all-wrapper {
        text-align: center;
        margin-top: 16px;

        .see-all-button {
          background-color: var(--accent-color);
          color: var(--text-color);
          border: none;
          padding: 8px 16px;
          font-size: 1rem;
          font-weight: 600;
          border-radius: 8px;
          cursor: pointer;
          transition: background 0.3s ease;

          &:hover {
            background-color: var(--accent-color-hover);
          }
        }
      }
    }
  }
}

/* Animation commune */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
