﻿using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.CreateDTOs
{
    public class AvisCreateDto
    {
        [Required(ErrorMessage = "L'ID du produit est obligatoire")]
        public int ProduitId { get; set; }

        [Required(ErrorMessage = "La note est obligatoire")]
        [Range(1, 5, ErrorMessage = "La note doit être entre 1 et 5")]
        public int Note { get; set; }

        [StringLength(500, ErrorMessage = "Le commentaire ne peut dépasser 500 caractères")]
        public string? Commentaire { get; set; }
    }
}
