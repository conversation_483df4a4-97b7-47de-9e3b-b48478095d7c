import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { NotificationService, NotificationDto } from '../services/notification.service';
import { AuthService } from '../services/auth.service';

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="notifications-container">
      <div class="notifications-header">
        <h2>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
            <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
          </svg>
          Mes Notifications
        </h2>
        <div class="header-actions">
          <button
            class="primary-btn"
            *ngIf="unreadCount > 0"
            (click)="markAllAsRead()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="9,11 12,14 22,4"></polyline>
              <path d="M21,12v7a2,2 0 0,1 -2,2H5a2,2 0 0,1 -2,-2V5a2,2 0 0,1 2,-2h11"></path>
            </svg>
            Tout marquer comme lu ({{ unreadCount }})
          </button>

        </div>
      </div>

      <div class="notifications-stats" *ngIf="notifications.length > 0">
        <div class="stats-chips">
          <div class="stat-chip">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
            </svg>
            {{ notifications.length }} notification(s)
          </div>
          <div *ngIf="unreadCount > 0" class="stat-chip warn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
            </svg>
            {{ unreadCount }} non lue(s)
          </div>
        </div>
      </div>

      <div class="notifications-content">
        <div *ngIf="loading" class="loading-container">
          <div class="spinner"></div>
          <p>Chargement des notifications...</p>
        </div>

        <div *ngIf="!loading && notifications.length === 0" class="empty-state">
          <svg class="empty-icon" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          <h3>Aucune notification</h3>
          <p>Vous n'avez aucune notification pour le moment.</p>
          <p class="empty-subtitle">Les notifications apparaîtront ici lorsque :</p>
          <ul class="empty-list">
            <li>• Un client laisse un avis sur vos produits</li>
            <li>• Une nouvelle commande est passée</li>
            <li>• Un paiement est confirmé</li>
            <li>• Des mises à jour importantes sont disponibles</li>
          </ul>
        </div>

        <div *ngIf="!loading && notifications.length > 0" class="notifications-list">
          <div
            *ngFor="let notification of notifications; trackBy: trackByNotificationId"
            class="notification-card"
            [class.unread]="!notification.estLue">

            <div class="notification-header">
              <div class="notification-status">
                <svg
                  *ngIf="!notification.estLue"
                  class="unread-indicator"
                  width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                  <circle cx="12" cy="12" r="10"></circle>
                </svg>
                <svg
                  *ngIf="notification.estLue"
                  class="read-indicator"
                  width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22,4 12,14.01 9,11.01"></polyline>
                </svg>
              </div>

              <div class="notification-content">
                {{ notification.contenu }}
              </div>

              <div class="notification-actions">
                <button
                  *ngIf="!notification.estLue"
                  (click)="markAsRead(notification)"
                  class="action-btn primary"
                  title="Marquer comme lu">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="20,6 9,17 4,12"></polyline>
                  </svg>
                </button>

                <button
                  (click)="deleteNotification(notification.id)"
                  class="action-btn delete"
                  title="Supprimer">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="3,6 5,6 21,6"></polyline>
                    <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                  </svg>
                </button>
              </div>
            </div>

            <div class="notification-meta">
              <span class="notification-date">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12,6 12,12 16,14"></polyline>
                </svg>
                {{ formatDate(notification.dateEnvoi) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .notifications-container {
      max-width: 900px;
      margin: 0 auto;
      padding: 24px;
    }

    .notifications-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
      color: white;
    }

    .notifications-header h2 {
      margin: 0;
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 24px;
      font-weight: 500;
    }

    .primary-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      transition: all 0.2s;
    }

    .primary-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }



    .notifications-stats {
      margin-bottom: 24px;
    }

    .stats-chips {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }

    .stat-chip {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: #f5f5f5;
      border-radius: 20px;
      font-size: 14px;
      color: #333;
    }

    .stat-chip.warn {
      background: #ffebee;
      color: #c62828;
    }

    .loading-container {
      text-align: center;
      padding: 48px;
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 16px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .empty-state {
      text-align: center;
      padding: 48px;
      color: #666;
    }

    .empty-icon {
      margin-bottom: 16px;
      color: #ccc;
    }

    .empty-subtitle {
      margin-top: 24px;
      font-weight: 500;
      color: #333;
    }

    .empty-list {
      text-align: left;
      display: inline-block;
      margin-top: 16px;
      list-style: none;
      padding: 0;
    }

    .empty-list li {
      margin-bottom: 8px;
      color: #666;
    }

    .notifications-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .notification-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border-left: 4px solid transparent;
      overflow: hidden;
    }

    .notification-card.unread {
      border-left-color: #2196f3;
      background-color: #f8fbff;
      box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
    }

    .notification-card:hover {
      box-shadow: 0 4px 16px rgba(0,0,0,0.12);
      transform: translateY(-2px);
    }

    .notification-header {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px;
    }

    .notification-status {
      flex-shrink: 0;
      padding-top: 4px;
    }

    .unread-indicator {
      color: #2196f3;
    }

    .read-indicator {
      color: #4caf50;
    }

    .notification-content {
      flex: 1;
      font-size: 16px;
      line-height: 1.5;
      margin: 0;
      color: #333;
    }

    .notification-actions {
      display: flex;
      gap: 4px;
      flex-shrink: 0;
    }

    .action-btn {
      background: none;
      border: none;
      cursor: pointer;
      padding: 8px;
      border-radius: 4px;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .action-btn.primary {
      color: #2196f3;
    }

    .action-btn.primary:hover {
      background: rgba(33, 150, 243, 0.1);
    }

    .action-btn.delete {
      color: #f44336;
    }

    .action-btn.delete:hover {
      background: rgba(244, 67, 54, 0.1);
    }

    .notification-meta {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-top: 8px;
      padding: 0 16px 16px;
    }

    .notification-date {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 14px;
      color: #666;
    }

    @media (max-width: 768px) {
      .notifications-container {
        padding: 16px;
      }

      .notifications-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        text-align: center;
      }

      .notifications-header h2 {
        font-size: 20px;
      }

      .notification-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .notification-actions {
        align-self: flex-end;
      }

      .stats-chips {
        justify-content: center;
      }
    }
  `]
})
export class NotificationsComponent implements OnInit, OnDestroy {
  notifications: NotificationDto[] = [];
  unreadCount = 0;
  loading = true;
  private subscriptions = new Subscription();

  constructor(
    private notificationService: NotificationService,
    private authService: AuthService
  ) {}

  ngOnInit() {
    this.subscriptions.add(
      this.notificationService.notifications$.subscribe(notifications => {
        this.notifications = notifications;
        this.loading = false;
      })
    );

    this.subscriptions.add(
      this.notificationService.unreadCount$.subscribe(count => {
        this.unreadCount = count;
      })
    );

    this.loadNotifications();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  loadNotifications() {
    const currentUser = this.authService.getCurrentUser();
    if (currentUser?.id) {
      this.notificationService.getUserNotifications(currentUser.id).subscribe({
        error: () => this.loading = false
      });
    } else {
      this.loading = false;
    }
  }

  markAsRead(notification: NotificationDto) {
    this.notificationService.markAsRead(notification.id).subscribe();
  }

  markAllAsRead() {
    const unreadNotifications = this.notifications.filter(n => !n.estLue);
    unreadNotifications.forEach(notification => {
      this.notificationService.markAsRead(notification.id).subscribe();
    });
  }

  deleteNotification(notificationId: number) {
    this.notificationService.deleteNotification(notificationId).subscribe();
  }



  trackByNotificationId(index: number, notification: NotificationDto): number {
    return notification.id;
  }

  formatDate(date: Date): string {
    const now = new Date();
    const notifDate = new Date(date);
    const diffInMinutes = Math.floor((now.getTime() - notifDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''}`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `Il y a ${diffInHours} heure${diffInHours > 1 ? 's' : ''}`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;
    
    if (diffInDays < 30) {
      const diffInWeeks = Math.floor(diffInDays / 7);
      return `Il y a ${diffInWeeks} semaine${diffInWeeks > 1 ? 's' : ''}`;
    }
    
    return notifDate.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  }
}
