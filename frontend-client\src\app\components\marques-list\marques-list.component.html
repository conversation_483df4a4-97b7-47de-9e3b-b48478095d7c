<!-- Container principal -->
<div class="marques-wrapper">
  <!-- Version Desktop -->
  <div class="desktop-marques" *ngIf="!isMobileView">
    <div class="marques-container">
      <!-- Hitbox pour le hover -->
      <div
        class="marques-hitbox"
        (mouseenter)="delayClose()"
        (mouseleave)="handleMouseLeave()"
      ></div>

      <!-- Contenu visible -->
      <div class="marques-visible" *ngIf="marques.length > 0">
        <div class="marques-content">
          <h2>Nos Marques</h2>
          <div *ngIf="marques.length > 0" class="marques-section">
            <ul class="marques-list">
              <li *ngFor="let marque of marques" class="marque-item">
                <div
                  (click)="navigateToMarque(marque.id)"
                  class="marque-link"
                  style="cursor: pointer"
                >
                  <div class="marque-image-container">
                    <img
                      [src]="marque.logo"
                      [alt]="marque.name + ' logo'"
                      class="marque-image"
                      loading="lazy"
                    />
                  </div>
                  <p class="marque-name">{{ marque.name }}</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Version Mobile -->
  <div class="mobile-marques" *ngIf="isMobileView">
    <div class="mobile-marques-content" *ngIf="marques.length > 0">
      <div *ngIf="marques.length > 0" class="mobile-marques-section">
        <ul class="mobile-marques-list">
          <li *ngFor="let marque of limitedMarques" class="mobile-marques-item">
              <img
                [src]="marque.logo"
                [alt]="marque.name + ' logo'"
                class="mobile-marque-image"
                loading="lazy"
              />
            <button
              (click)="navigateToMarque(marque.id)"
              class="mobile-marques-link"
            >
              {{ marque.name }}
            </button>
          </li>
        </ul>
        <div class="see-all-wrapper" *ngIf="marques.length > 10">
          <button class="see-all-button" (click)="navigateToAllMarques()">
            Afficher toutes les marques
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
