﻿using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;

namespace WebApiPfe.DTOs.ReadDTOs
{
    public abstract class UtilisateurBaseDto
    {
        public int Id { get; set; }
        public required string Email { get; set; }
        public required string Nom { get; set; }
        public required string Prenom { get; set; }
        public required string PhoneNumber { get; set; }
        public required string Role { get; set; }
        public DateTime DateNaissance { get; set; }
        public DateTime DateInscription { get; set; }
        public DateTime? DerniereConnexion { get; set; }
        public bool EstActif { get; set; }
        public List<AdresseDto> Adresses { get; set; } = new List<AdresseDto>();
    }
}

