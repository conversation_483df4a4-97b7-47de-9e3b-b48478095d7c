import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../../auth/auth.service';
import { ClientService } from '../../../services/client.service';
import { AdresseDto } from '../../../models/AdresseDto';

@Component({
  selector: 'app-address-selection',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './address-selection.component.html',
  styleUrls: ['./address-selection.component.scss']
})
export class AddressSelectionComponent implements OnInit {
  addresses: AdresseDto[] = [];
  selectedAddressId: number | null = null;
  loading = false;
  errorMessage = '';

  constructor(
    private authService: AuthService,
    private clientService: ClientService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadAddresses();
  }

  loadAddresses(): void {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      this.router.navigate(['/auth/login']);
      return;
    }

    this.loading = true;
    this.clientService.getById(currentUser.id).subscribe({
      next: (client) => {
        this.addresses = client.adresses || [];
        
        // Sélectionner automatiquement l'adresse principale si elle existe
        const mainAddress = this.addresses.find(addr => addr.estPrincipale);
        if (mainAddress) {
          this.selectedAddressId = mainAddress.id;
        }
        
        this.loading = false;
      },
      error: (err) => {
        console.error('Erreur lors du chargement des adresses:', err);
        this.errorMessage = 'Erreur lors du chargement des adresses';
        this.loading = false;
      }
    });
  }

  selectAddress(addressId: number): void {
    this.selectedAddressId = addressId;
  }

  getSelectedAddress(): AdresseDto | null {
    if (!this.selectedAddressId) return null;
    return this.addresses.find(addr => addr.id === this.selectedAddressId) || null;
  }

  continuerVersPaiement(): void {
    if (!this.selectedAddressId) {
      this.errorMessage = 'Veuillez sélectionner une adresse de livraison';
      return;
    }

    // Stocker l'adresse sélectionnée dans le sessionStorage
    sessionStorage.setItem('selectedAddressId', this.selectedAddressId.toString());

    this.router.navigate(['/user/checkout/payment']);
  }

  retourConfirmation(): void {
    this.router.navigate(['/user/checkout']);
  }

  ajouterNouvelleAdresse(): void {
    // Rediriger vers la page de profil pour ajouter une adresse
    this.router.navigate(['/user/edit-profile'], {
      queryParams: { returnUrl: '/user/checkout/address' }
    });
  }
}
