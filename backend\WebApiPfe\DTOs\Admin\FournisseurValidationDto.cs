using WebApiPfe.Models.Entity;
using WebApiPfe.DTOs.ReadDTOs;

namespace WebApiPfe.DTOs.Admin
{
    public class FournisseurValidationDto
    {
        public int Id { get; set; }
        public string Email { get; set; } = string.Empty;
        public string Nom { get; set; } = string.Empty;
        public string Prenom { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string MatriculeFiscale { get; set; } = string.Empty;
        public string RaisonSociale { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string RIB { get; set; } = string.Empty;
        public string CodeBanque { get; set; } = string.Empty;
        public decimal Commission { get; set; }
        public int DelaiPreparationJours { get; set; }
        public decimal FraisLivraisonBase { get; set; }
        public string LogoFile { get; set; } = string.Empty;
        public StatutValidationFournisseur StatutValidation { get; set; }
        public DateTime DateInscription { get; set; }
        public DateTime? DateValidation { get; set; }
        public int? ValidePar { get; set; }
        public string? CommentaireValidation { get; set; }
        public List<AdresseDto> Adresses { get; set; } = new List<AdresseDto>();
    }

    public class ValiderFournisseurRequest
    {
        public int FournisseurId { get; set; }
        public bool Accepter { get; set; } // true = accepter, false = rejeter
        public string? Commentaire { get; set; }
    }
}
