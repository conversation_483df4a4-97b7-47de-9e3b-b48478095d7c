﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enums;
using WebApiPfe.Validators;

namespace WebApiPfe.Models.Entity
{
    public class Fournisseur : Utilisateur
    {
        public override string GetRoleSpecifique() => "Fournisseur";

        [Required]
        [StringLength(8, MinimumLength = 8)]
        public required string MatriculeFiscale { get; set; }

        [Required]
        [StringLength(200)]
        public required string RaisonSociale { get; set; }

        public string? Description { get; set; }

        [Required]
        [CustomValidation(typeof(RibValidator), nameof(RibValidator.ValidateRIB))]
        public required string RIB { get; set; }

        [NotMapped]
        public string RIBMasque => !string.IsNullOrEmpty(RIB) && RIB.Length == 20 ? $"****{RIB.Substring(16)}" : RIB;

        [Required]
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Le code banque doit contenir 3 chiffres.")]
        [RegularExpression(@"^[0-9]+$", ErrorMessage = "Seuls les chiffres sont autorisés.")]
        public required string CodeBanque { get; set; }
        [Range(0, 0.5, ErrorMessage = "La commission doit être entre 0% et 50%")]
        public decimal Commission { get; set; } = 0.15m;
        public int DelaiPreparationJours { get; set; } = 2;
        public decimal FraisLivraisonBase { get; set; } = 9.99m;

        [Required]
        public required string LogoFile { get; set; }

        // Statut de validation par l'admin
        public StatutValidationFournisseur StatutValidation { get; set; } = StatutValidationFournisseur.EnAttente;

        public DateTime? DateValidation { get; set; }

        public int? ValidePar { get; set; } // ID de l'admin qui a validé

        public string? CommentaireValidation { get; set; }

        [JsonIgnore]
        public virtual ICollection<StockFournisseur> Stocks { get; set; } = new List<StockFournisseur>();

        [JsonIgnore]
        public virtual ICollection<Produit> Produits { get; set; } = new List<Produit>();

        [JsonIgnore]
        public virtual ICollection<CommandeFournisseur> CommandesFournisseurs { get; set; } = new List<CommandeFournisseur>();
    }
}
