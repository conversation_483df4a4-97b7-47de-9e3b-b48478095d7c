﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PromotionsController : ControllerBase
    {
        private readonly IPromotionService _promotionService;
        private readonly ILogger<PromotionsController> _logger;

        public PromotionsController(
            IPromotionService promotionService,
            ILogger<PromotionsController> logger)
        {
            _promotionService = promotionService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<PromotionDto>>> GetAllActivePromotions()
        {
            try
            {
                var promotions = await _promotionService.GetAllActiveAsync();
                return Ok(promotions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des promotions actives");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpGet("{id:int}")]
        public async Task<ActionResult<PromotionDto>> GetPromotionById(int id)
        {
            try
            {
                var promotion = await _promotionService.GetByIdAsync(id);
                return Ok(promotion);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération de la promotion {id}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpGet("produit/{produitId:int}")]
        public async Task<ActionResult<IEnumerable<PromotionDto>>> GetPromotionsForProduct(int produitId)
        {
            try
            {
                var promotions = await _promotionService.GetByProduitAsync(produitId);
                return Ok(promotions);
            }
            catch (KeyNotFoundException)
            {
                return NotFound("Produit introuvable");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération des promotions pour le produit {produitId}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }



        [HttpPut("{id:int}")]
        public async Task<IActionResult> UpdatePromotion(int id, [FromBody] CreatePromotionDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                await _promotionService.UpdateAsync(id, dto);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la mise à jour de la promotion {id}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }



        [HttpGet("validate-code")]
        public async Task<ActionResult<bool>> ValidatePromoCode([FromQuery] string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return BadRequest("Le code promo est requis");

            try
            {
                var isValid = await _promotionService.ValidateCodePromoAsync(code);
                return Ok(isValid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la validation du code promo {code}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpGet("calculate-price")]
        public async Task<ActionResult<decimal>> CalculateFinalPrice(
            [FromQuery] int produitId,
            [FromQuery] string? codePromo = null)
        {
            try
            {
                var prixFinal = await _promotionService.GetPrixFinalAsync(produitId, codePromo);
                return Ok(prixFinal);
            }
            catch (KeyNotFoundException)
            {
                return NotFound("Produit introuvable");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors du calcul du prix pour le produit {produitId}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        // ==================== ENDPOINTS POUR FOURNISSEURS ====================

        [HttpGet("my-promotions")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult<IEnumerable<PromotionDto>>> GetMyPromotions()
        {
            try
            {
                var fournisseurId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                if (fournisseurId == 0)
                {
                    return Unauthorized("Fournisseur non identifié");
                }

                var promotions = await _promotionService.GetByFournisseurAsync(fournisseurId);
                return Ok(promotions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des promotions du fournisseur");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpPost]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult<PromotionDto>> CreatePromotion([FromBody] PromotionCreateDto promotionDto)
        {
            try
            {
                var fournisseurId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                if (fournisseurId == 0)
                {
                    return Unauthorized("Fournisseur non identifié");
                }

                // Ajouter l'ID du fournisseur au DTO
                promotionDto.FournisseurId = fournisseurId;

                var promotion = await _promotionService.CreateAsync(promotionDto);
                return CreatedAtAction(nameof(GetPromotionById), new { id = promotion.Id }, promotion);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création de la promotion");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpPut("fournisseur/{id:int}")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult<PromotionDto>> UpdatePromotionByFournisseur(int id, [FromBody] PromotionCreateDto promotionDto)
        {
            try
            {
                var fournisseurId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                if (fournisseurId == 0)
                {
                    return Unauthorized("Fournisseur non identifié");
                }

                // Ajouter l'ID du fournisseur au DTO pour la sécurité
                promotionDto.FournisseurId = fournisseurId;

                var updatedPromotion = await _promotionService.UpdateAsync(id, promotionDto, fournisseurId);
                return Ok(updatedPromotion);
            }
            catch (KeyNotFoundException)
            {
                return NotFound("Promotion introuvable");
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid("Vous ne pouvez modifier que vos propres promotions");
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la modification de la promotion {id}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpPatch("{id:int}/toggle")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult> TogglePromotion(int id)
        {
            try
            {
                var fournisseurId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                if (fournisseurId == 0)
                {
                    return Unauthorized("Fournisseur non identifié");
                }

                await _promotionService.ToggleAsync(id, fournisseurId);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid("Vous ne pouvez modifier que vos propres promotions");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la modification du statut de la promotion {id}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }

        [HttpDelete("{id:int}")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult> DeletePromotion(int id)
        {
            try
            {
                var fournisseurId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                if (fournisseurId == 0)
                {
                    return Unauthorized("Fournisseur non identifié");
                }

                await _promotionService.DeleteAsync(id, fournisseurId);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid("Vous ne pouvez supprimer que vos propres promotions");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la suppression de la promotion {id}");
                return StatusCode(500, "Une erreur est survenue");
            }
        }
    }
}
