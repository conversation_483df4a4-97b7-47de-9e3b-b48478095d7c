import { Injectable, signal, computed, effect } from '@angular/core';
import { HttpClient, HttpHeaders, HttpContext, HttpContextToken } from '@angular/common/http';
import { BehaviorSubject, Observable, tap, catchError } from 'rxjs';
import { LoginRequest, LoginResponse, User } from '../models';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  // URLs possibles du backend
  private readonly POSSIBLE_URLS = [
    'http://localhost:7264/api',
    'https://localhost:7264/api',
    'http://localhost:5000/api',
    'https://localhost:5001/api',
    'http://localhost:7265/api',
    'https://localhost:7265/api'
  ];

  private readonly API_URL = 'http://localhost:5014/api'; // Backend HTTP
  private readonly TOKEN_KEY = 'auth_token';
  private readonly USER_KEY = 'current_user';

  // Angular 19: Signals pour l'état d'authentification
  currentUser = signal<User | null>(this.getCurrentUser());
  isAuthenticated = signal<boolean>(this.hasValidToken());

  // Angular 19: Computed signals pour des propriétés dérivées
  userRole = computed(() => this.currentUser()?.role || null);
  isFournisseur = computed(() => this.userRole() === 'Fournisseur');
  isAdmin = computed(() => this.userRole() === 'Admin');
  userName = computed(() => {
    const user = this.currentUser();
    return user ? `${user.prenom} ${user.nom}` : null;
  });

  // Compatibilité avec l'ancien système (pour migration progressive)
  private currentUserSubject = new BehaviorSubject<User | null>(this.getCurrentUser());
  public currentUser$ = this.currentUserSubject.asObservable();

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasValidToken());
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  constructor(private http: HttpClient) {
    // Angular 19: Effect pour synchroniser signals et observables
    effect(() => {
      const user = this.currentUser();
      this.currentUserSubject.next(user);
      console.log('👤 Utilisateur connecté:', user ? this.userName() : 'Aucun');
    });

    effect(() => {
      const authenticated = this.isAuthenticated();
      this.isAuthenticatedSubject.next(authenticated);
      console.log('🔐 État d\'authentification:', authenticated ? 'Connecté' : 'Déconnecté');
    });
  }

  /**
   * Connexion utilisateur
   */
  login(credentials: LoginRequest): Observable<LoginResponse> {
    console.log('🔐 Tentative de connexion pour:', credentials.email);
    console.log('🌐 URL API utilisée:', this.API_URL);
    console.log('🌐 URL complète:', `${this.API_URL}/Auth/login`);

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });

    return this.http.post<LoginResponse>(`${this.API_URL}/Auth/login`, credentials, { headers })
      .pipe(
        tap(response => {
          console.log('✅ Connexion réussie');
          this.setSession(response);
        }),
        catchError(error => {
          console.error('❌ Erreur de connexion:', error);
          // Gérer les erreurs spécifiques de validation des fournisseurs
          if (error.status === 401 && error.error) {
            const message = typeof error.error === 'string' ? error.error : error.error.message || '';
            if (message.includes('en attente de validation')) {
              throw new Error('VALIDATION_PENDING');
            } else if (message.includes('rejetée')) {
              throw new Error('VALIDATION_REJECTED');
            } else if (message.includes('suspendu')) {
              throw new Error('ACCOUNT_SUSPENDED');
            }
          }
          throw error;
        })
      );
  }

  /**
   * Inscription fournisseur
   */
  registerFournisseur(formData: FormData): Observable<any> {
    const url = `${this.API_URL}/Fournisseurs`;
    console.log('📝 Tentative d\'inscription à l\'URL:', url);
    console.log('📋 FormData contents:');

    // Debug détaillé du FormData
    const formDataEntries: any = {};
    for (let pair of formData.entries()) {
      const key = pair[0];
      const value = pair[1];
      formDataEntries[key] = value;

      if (value instanceof File) {
        console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }

    console.log('📊 FormData summary:', formDataEntries);

    // Headers pour multipart/form-data
    const headers = new HttpHeaders();
    // Ne pas définir Content-Type pour multipart/form-data - le navigateur le fait automatiquement

    console.log('🚀 Envoi de la requête...');

    return this.http.post<any>(url, formData, {
      headers: headers,
      reportProgress: true,
      observe: 'response'
    }).pipe(
      tap(response => {
        console.log('✅ Inscription réussie - Response complète:', response);
        console.log('✅ Status:', response.status);
        console.log('✅ Body:', response.body);
        console.log('✅ Headers:', response.headers);
      }),
      catchError(error => {
        console.error('❌ Erreur inscription détaillée:');
        console.error('❌ Status:', error.status);
        console.error('❌ Status Text:', error.statusText);
        console.error('❌ Error body:', error.error);
        console.error('❌ Error message:', error.message);
        console.error('❌ URL:', error.url);

        if (error.headers) {
          console.error('❌ Response headers:');
          error.headers.keys().forEach((key: string) => {
            console.error(`   ${key}: ${error.headers.get(key)}`);
          });
        }

        // Essayer de parser l'erreur
        if (error.error) {
          try {
            const errorDetails = typeof error.error === 'string' ? JSON.parse(error.error) : error.error;
            console.error('❌ Parsed error details:', errorDetails);
          } catch (e) {
            console.error('❌ Raw error:', error.error);
          }
        }

        throw error;
      })
    );
  }

  /**
   * Test d'inscription fournisseur avec FormData (pour debug)
   */
  registerFournisseurJSON(data: any): Observable<any> {
    const url = `${this.API_URL}/Fournisseurs`;
    console.log('🧪 Test inscription avec FormData à l\'URL:', url);
    console.log('🧪 Data:', data);

    // Créer un FormData avec les données et un fichier logo par défaut
    const formData = new FormData();
    
    // Créer un fichier logo par défaut (1x1 pixel PNG transparent)
    const defaultLogoBlob = new Blob([
      new Uint8Array([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
        0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
        0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ])
    ], { type: 'image/png' });
    
    const defaultLogoFile = new File([defaultLogoBlob], 'default-logo.png', { type: 'image/png' });

    // Ajouter toutes les données au FormData
    Object.keys(data).forEach(key => {
      if (data[key] !== null && data[key] !== undefined) {
        formData.append(key, data[key].toString());
      }
    });

    // Ajouter le fichier logo
    formData.append('LogoFile', defaultLogoFile);

    console.log('🧪 FormData avec logo par défaut:');
    for (let pair of formData.entries()) {
      const key = pair[0];
      const value = pair[1];
      if (value instanceof File) {
        console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }

    const headers = new HttpHeaders();
    // Ne pas définir Content-Type pour multipart/form-data

    return this.http.post<any>(url, formData, { headers }).pipe(
      tap(response => {
        console.log('✅ Test FormData réussi:', response);
      }),
      catchError(error => {
        console.error('❌ Test FormData échoué:', error);
        console.error('❌ Status:', error.status);
        console.error('❌ Error body:', error.error);
        throw error;
      })
    );
  }

  /**
   * Version alternative sans fichier pour test
   */
  registerFournisseurWithoutFile(data: any): Observable<any> {
    const url = `${this.API_URL}/Fournisseurs`;
    console.log('🧪 Test inscription sans fichier à l\'URL:', url);

    const formData = new FormData();
    Object.keys(data).forEach(key => {
      if (data[key] !== null && data[key] !== undefined) {
        formData.append(key, data[key].toString());
      }
    });

    console.log('🧪 FormData sans fichier:');
    for (let pair of formData.entries()) {
      console.log(`${pair[0]}: ${pair[1]}`);
    }

    const headers = new HttpHeaders();
    // Ne pas définir Content-Type pour multipart/form-data

    return this.http.post<any>(url, formData, { headers }).pipe(
      tap(response => {
        console.log('✅ Test sans fichier réussi:', response);
      }),
      catchError(error => {
        console.error('❌ Test sans fichier échoué:', error);
        throw error;
      })
    );
  }

  /**
   * Test simple de l'endpoint avec des données minimales
   */
  testEndpointWithMinimalData(): Observable<any> {
    const url = `${this.API_URL}/Fournisseurs`;
    console.log('🧪 Test endpoint avec données minimales à l\'URL:', url);

    // Créer un FormData avec un fichier logo par défaut
    const formData = new FormData();
    
    // Créer un fichier logo par défaut (1x1 pixel PNG transparent)
    const defaultLogoBlob = new Blob([
      new Uint8Array([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
        0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
        0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
      ])
    ], { type: 'image/png' });
    
    const defaultLogoFile = new File([defaultLogoBlob], 'default-logo.png', { type: 'image/png' });

    // Ajouter les données au FormData
    formData.append('Email', '<EMAIL>');
    formData.append('Password', 'Test123!');
    formData.append('Nom', 'Test');
    formData.append('Prenom', 'User');
    formData.append('Telephone', '1234567890');
    formData.append('DateNaissance', '1990-01-01');
    formData.append('MatriculeFiscale', '12345678');
    formData.append('RaisonSociale', 'Test Company');
    formData.append('Rib', '12345678901234567890');
    formData.append('CodeBanque', '123');
    formData.append('Commission', '0.15');
    formData.append('DelaiPreparationJours', '2');
    formData.append('FraisLivraisonBase', '9.99');
    formData.append('EstActif', 'true');
    formData.append('LogoFile', defaultLogoFile);

    console.log('🧪 FormData avec logo par défaut:');
    for (let pair of formData.entries()) {
      const key = pair[0];
      const value = pair[1];
      if (value instanceof File) {
        console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }

    const headers = new HttpHeaders();
    // Ne pas définir Content-Type pour multipart/form-data

    return this.http.post<any>(url, formData, { headers }).pipe(
      tap(response => {
        console.log('✅ Test minimal réussi:', response);
      }),
      catchError(error => {
        console.error('❌ Test minimal échoué:', error);
        console.error('❌ Status:', error.status);
        console.error('❌ Error body:', error.error);
        throw error;
      })
    );
  }

  /**
   * Déconnexion avec Angular 19 signals et nettoyage complet
   */
  logout(): void {
    console.log('🚪 Déconnexion et nettoyage du localStorage...');

    // Suppression de toutes les données de session
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    localStorage.removeItem('fournisseur_profile');
    localStorage.removeItem('supplierId');
    localStorage.removeItem('currentUser'); // Au cas où

    console.log('✅ localStorage nettoyé');

    // Angular 19: Mise à jour des signals
    this.currentUser.set(null);
    this.isAuthenticated.set(false);
  }

  /**
   * Obtenir le token actuel
   */
  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Obtenir l'utilisateur actuel
   */
  getCurrentUser(): User | null {
    const userStr = localStorage.getItem(this.USER_KEY);
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch {
        return null;
      }
    }
    return null;
  }

  /**
   * Obtenir l'ID de l'utilisateur actuel
   */
  getCurrentUserId(): number | null {
    const user = this.getCurrentUser();
    return user?.id || null;
  }

  /**
   * Vérifier si l'utilisateur est connecté (méthode legacy)
   */
  isAuthenticatedLegacy(): boolean {
    return this.hasValidToken();
  }

  /**
   * Vérifier si l'utilisateur est un fournisseur (méthode legacy)
   */
  isFournisseurLegacy(): boolean {
    const user = this.getCurrentUser();
    return user?.role === 'Fournisseur';
  }

  /**
   * Rafraîchir les informations utilisateur
   */
  refreshUser(): Observable<User> {
    const currentUser = this.getCurrentUser();
    if (!currentUser) {
      throw new Error('Aucun utilisateur connecté');
    }

    return this.http.get<User>(`${this.API_URL}/Utilisateurs/${currentUser.id}`)
      .pipe(
        tap(user => {
          localStorage.setItem(this.USER_KEY, JSON.stringify(user));
          this.currentUserSubject.next(user);
        })
      );
  }

  /**
   * Définir la session utilisateur avec Angular 19 signals et stockage complet
   */
  private setSession(authResult: LoginResponse): void {
    console.log('💾 Stockage des données de session complètes...');
    console.log('📦 Données utilisateur reçues:', authResult.utilisateur);

    // Stockage standard
    localStorage.setItem(this.TOKEN_KEY, authResult.token);
    localStorage.setItem(this.USER_KEY, JSON.stringify(authResult.utilisateur));

    // Stockage des données complètes du fournisseur pour le profil
    const fournisseurData = {
      // Données de base
      id: authResult.utilisateur.id,
      email: authResult.utilisateur.email,
      nom: authResult.utilisateur.nom,
      prenom: authResult.utilisateur.prenom,
      role: authResult.utilisateur.role,

      // Données étendues (si disponibles dans la réponse)
      phoneNumber: (authResult.utilisateur as any).phoneNumber ||
                   (authResult.utilisateur as any).telephone || '',
      dateNaissance: (authResult.utilisateur as any).dateNaissance || '',
      dateInscription: (authResult.utilisateur as any).dateInscription || new Date().toISOString(),
      derniereConnexion: new Date().toISOString(),
      estActif: (authResult.utilisateur as any).estActif !== undefined ?
                (authResult.utilisateur as any).estActif : true,

      // Données entreprise
      matriculeFiscale: (authResult.utilisateur as any).matriculeFiscale || '',
      raisonSociale: (authResult.utilisateur as any).raisonSociale || '',
      description: (authResult.utilisateur as any).description || '',

      // Données bancaires
      ribMasque: (authResult.utilisateur as any).ribMasque ||
                 (authResult.utilisateur as any).rib || '',
      codeBanque: (authResult.utilisateur as any).codeBanque || '',

      // Paramètres commerciaux
      commission: (authResult.utilisateur as any).commission || 0,
      delaiPreparationJours: (authResult.utilisateur as any).delaiPreparationJours || 3,
      fraisLivraisonBase: (authResult.utilisateur as any).fraisLivraisonBase || 5.00,

      // Autres données
      logoFile: (authResult.utilisateur as any).logoFile || '',
      adresses: (authResult.utilisateur as any).adresses || [],

      // Métadonnées de session
      sessionTimestamp: new Date().toISOString(),
      tokenExpiry: this.calculateTokenExpiry(authResult.token)
    };

    // Stockage des données complètes du fournisseur
    localStorage.setItem('fournisseur_profile', JSON.stringify(fournisseurData));
    localStorage.setItem('supplierId', authResult.utilisateur.id.toString());

    console.log('✅ Données complètes stockées:', fournisseurData);
    console.log('📊 Clés localStorage créées:');
    console.log('- token:', !!localStorage.getItem(this.TOKEN_KEY));
    console.log('- user:', !!localStorage.getItem(this.USER_KEY));
    console.log('- fournisseur_profile:', !!localStorage.getItem('fournisseur_profile'));
    console.log('- supplierId:', localStorage.getItem('supplierId'));

    // Angular 19: Mise à jour des signals
    this.currentUser.set(authResult.utilisateur);
    this.isAuthenticated.set(true);
  }

  /**
   * Calculer l'expiration du token (estimation)
   */
  private calculateTokenExpiry(token: string): string {
    try {
      // Décoder le JWT pour obtenir l'expiration
      const payload = JSON.parse(atob(token.split('.')[1]));
      if (payload.exp) {
        return new Date(payload.exp * 1000).toISOString();
      }
    } catch (error) {
      console.warn('⚠️ Impossible de décoder le token JWT:', error);
    }

    // Fallback: 24 heures par défaut
    const expiry = new Date();
    expiry.setHours(expiry.getHours() + 24);
    return expiry.toISOString();
  }

  /**
   * Vérifier si le token est valide
   */
  private hasValidToken(): boolean {
    const token = this.getToken();
    if (!token) {
      return false;
    }

    try {
      // Décoder le JWT pour vérifier l'expiration
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      
      return payload.exp > currentTime;
    } catch {
      return false;
    }
  }
}
