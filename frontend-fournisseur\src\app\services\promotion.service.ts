import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import {
  Promotion,
  CreatePromotionDto,
  UpdatePromotionDto,
  FiltrePromotion,
  StatistiquesPromotion,
  UtilisationPromotion,
  TemplatePromotion,
  PreviewPromotion,
  ResultatValidationPromotion,
  TypePromotion,
  StatutPromotion,
  HistoriquePromotion,
  RapportPromotion,
  NotificationPromotion
} from '../models/promotion.model';

@Injectable({
  providedIn: 'root'
})
export class PromotionService {
  private readonly API_URL = `${environment.apiUrl}/promotions`;
  
  // Subjects pour la réactivité
  private promotionsSubject = new BehaviorSubject<Promotion[]>([]);
  private statistiquesSubject = new BehaviorSubject<StatistiquesPromotion>({
    totalPromotions: 0,
    promotionsActives: 0,
    promotionsExpirees: 0,
    totalUtilisations: 0,
    montantTotalEconomise: 0,
    chiffreAffaireTotalGenere: 0,
    tauxUtilisationMoyen: 0
  });
  private notificationsSubject = new BehaviorSubject<NotificationPromotion[]>([]);
  
  public promotions$ = this.promotionsSubject.asObservable();
  public statistiques$ = this.statistiquesSubject.asObservable();
  public notifications$ = this.notificationsSubject.asObservable();

  constructor(private http: HttpClient) {
    this.initializeMockData();
  }

  /**
   * Headers avec token d'authentification
   */
  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('auth_token'); // Utiliser la même clé que AuthService
    return new HttpHeaders({
      'Authorization': token ? `Bearer ${token}` : '',
      'Content-Type': 'application/json'
    });
  }

  // ==================== GESTION DES PROMOTIONS ====================

  /**
   * Obtenir toutes les promotions depuis l'API
   */
  getPromotionsFromAPI(): Observable<Promotion[]> {
    console.log('🔄 PromotionService: Récupération des promotions depuis l\'API...');

    return this.http.get<Promotion[]>(`${environment.apiUrl}/Promotions`, {
      headers: this.getAuthHeaders()
    }).pipe(
      map(promotions => {
        console.log(`✅ PromotionService: ${promotions.length} promotions récupérées:`, promotions);
        // Mettre à jour le BehaviorSubject avec les données de l'API
        this.promotionsSubject.next(promotions);
        return promotions;
      }),
      catchError(error => {
        console.error('❌ PromotionService: Erreur lors de la récupération des promotions:', error);
        // Retourner les données mock en cas d'erreur
        return of(this.promotionsSubject.value);
      })
    );
  }

  /**
   * Obtenir toutes les promotions d'un fournisseur
   */
  getPromotionsByFournisseur(fournisseurId: number, filtre?: FiltrePromotion): Observable<Promotion[]> {
    return new Observable(observer => {
      let promotions = this.promotionsSubject.value.filter(p => p.fournisseurId === fournisseurId);
      
      // Appliquer les filtres
      if (filtre) {
        if (filtre.type) {
          promotions = promotions.filter(p => p.type === filtre.type);
        }
        if (filtre.statut) {
          promotions = promotions.filter(p => p.statut === filtre.statut);
        }
        if (filtre.recherche) {
          const query = filtre.recherche.toLowerCase();
          promotions = promotions.filter(p => 
            p.nom.toLowerCase().includes(query) ||
            p.description.toLowerCase().includes(query) ||
            (p.codePromo && p.codePromo.toLowerCase().includes(query))
          );
        }
        if (filtre.actives) {
          promotions = promotions.filter(p => p.statut === 'active');
        }
        if (filtre.expirees) {
          promotions = promotions.filter(p => p.statut === 'expiree');
        }
      }
      
      observer.next(promotions);
      observer.complete();
    });
  }

  /**
   * Obtenir une promotion par ID
   */
  getPromotionById(id: number): Observable<Promotion> {
    return new Observable(observer => {
      const promotion = this.promotionsSubject.value.find(p => p.id === id);
      if (promotion) {
        observer.next(promotion);
      } else {
        observer.error('Promotion non trouvée');
      }
      observer.complete();
    });
  }

  /**
   * Créer une nouvelle promotion
   */
  createPromotion(promotion: CreatePromotionDto): Observable<Promotion> {
    return new Observable(observer => {
      const currentPromotions = this.promotionsSubject.value;
      const newPromotion: Promotion = {
        id: Date.now(),
        ...promotion,
        statut: 'brouillon',
        utilisationActuelle: 0,
        nombreUtilisations: 0,
        montantEconomise: 0,
        chiffreAffaireGenere: 0,
        dateCreation: new Date(),
        dateModification: new Date(),
        creePar: 'Fournisseur',
        produitsConcernes: promotion.produitsConcernes.map((p, index) => ({
          id: Date.now() + index,
          promotionId: Date.now(),
          ...p
        })),
        conditions: promotion.conditions.map((c, index) => ({
          id: Date.now() + index,
          promotionId: Date.now(),
          type: c.type,
          valeur: c.valeur,
          valeurTexte: c.valeurTexte,
          operateur: c.operateur,
          obligatoire: c.obligatoire
        }))
      };
      
      const updatedPromotions = [...currentPromotions, newPromotion];
      this.promotionsSubject.next(updatedPromotions);
      this.saveMockData();
      this.updateStatistiques();
      
      observer.next(newPromotion);
      observer.complete();
    });
  }

  /**
   * Mettre à jour une promotion
   */
  updatePromotion(id: number, promotion: UpdatePromotionDto): Observable<void> {
    return new Observable(observer => {
      const currentPromotions = this.promotionsSubject.value;
      const index = currentPromotions.findIndex(p => p.id === id);
      
      if (index !== -1) {
        const updatedPromotion = { ...currentPromotions[index] };

        // Mettre à jour les champs simples
        Object.keys(promotion).forEach(key => {
          if (key !== 'conditions' && key !== 'produitsConcernes') {
            (updatedPromotion as any)[key] = (promotion as any)[key];
          }
        });

        // Mettre à jour les conditions si fournies
        if (promotion.conditions) {
          updatedPromotion.conditions = promotion.conditions.map((c, index) => ({
            id: Date.now() + index,
            promotionId: updatedPromotion.id,
            type: c.type,
            valeur: c.valeur,
            valeurTexte: c.valeurTexte,
            operateur: c.operateur,
            obligatoire: c.obligatoire
          }));
        }

        // Mettre à jour les produits concernés si fournis
        if (promotion.produitsConcernes) {
          updatedPromotion.produitsConcernes = promotion.produitsConcernes.map((p, index) => ({
            id: Date.now() + index,
            promotionId: updatedPromotion.id,
            produitId: p.produitId,
            inclus: p.inclus
          }));
        }

        updatedPromotion.dateModification = new Date();
        updatedPromotion.modifiePar = 'Fournisseur';

        currentPromotions[index] = updatedPromotion;
        
        this.promotionsSubject.next([...currentPromotions]);
        this.saveMockData();
        this.updateStatistiques();
        observer.next();
      } else {
        observer.error('Promotion non trouvée');
      }
      observer.complete();
    });
  }

  /**
   * Supprimer une promotion
   */
  deletePromotion(id: number): Observable<void> {
    return new Observable(observer => {
      const currentPromotions = this.promotionsSubject.value;
      const updatedPromotions = currentPromotions.filter(p => p.id !== id);
      
      this.promotionsSubject.next(updatedPromotions);
      this.saveMockData();
      this.updateStatistiques();
      
      observer.next();
      observer.complete();
    });
  }

  /**
   * Activer/Désactiver une promotion
   */
  togglePromotionStatus(id: number): Observable<void> {
    return new Observable(observer => {
      const currentPromotions = this.promotionsSubject.value;
      const index = currentPromotions.findIndex(p => p.id === id);
      
      if (index !== -1) {
        const promotion = currentPromotions[index];
        promotion.statut = promotion.statut === 'active' ? 'inactive' : 'active';
        promotion.dateModification = new Date();
        promotion.modifiePar = 'Fournisseur';
        
        this.promotionsSubject.next([...currentPromotions]);
        this.saveMockData();
        this.updateStatistiques();
        observer.next();
      } else {
        observer.error('Promotion non trouvée');
      }
      observer.complete();
    });
  }

  // ==================== VALIDATION ====================

  /**
   * Valider une promotion avant création/modification
   */
  validatePromotion(promotion: CreatePromotionDto | UpdatePromotionDto): Observable<ResultatValidationPromotion> {
    return new Observable(observer => {
      const resultat: ResultatValidationPromotion = {
        valide: true,
        erreurs: [],
        avertissements: []
      };

      // Validation du nom de promotion
      if (!promotion.nomPromotion || promotion.nomPromotion.trim().length < 3) {
        resultat.erreurs.push({
          champ: 'nomPromotion',
          message: 'Le nom de promotion doit contenir au moins 3 caractères',
          valide: false
        });
      }

      // Validation des dates
      if (promotion.dateDebut && promotion.dateFin) {
        if (promotion.dateDebut >= promotion.dateFin) {
          resultat.erreurs.push({
            champ: 'dates',
            message: 'La date de fin doit être postérieure à la date de début',
            valide: false
          });
        }
      }

      // Validation de la valeur de réduction
      if (promotion.valeurReduction !== undefined) {
        if (promotion.valeurReduction <= 0) {
          resultat.erreurs.push({
            champ: 'valeurReduction',
            message: 'La valeur de réduction doit être positive',
            valide: false
          });
        }
        
        if (promotion.typeReduction === 'pourcentage' && promotion.valeurReduction > 100) {
          resultat.erreurs.push({
            champ: 'valeurReduction',
            message: 'Le pourcentage ne peut pas dépasser 100%',
            valide: false
          });
        }
      }

      // Validation du code promo
      if (promotion.codePromo) {
        if (promotion.codePromo.length < 3) {
          resultat.erreurs.push({
            champ: 'codePromo',
            message: 'Le code promo doit contenir au moins 3 caractères',
            valide: false
          });
        }
        
        // Vérifier l'unicité du code
        const existingPromo = this.promotionsSubject.value.find(p => 
          p.codePromo === promotion.codePromo && p.statut !== 'expiree'
        );
        if (existingPromo) {
          resultat.erreurs.push({
            champ: 'codePromo',
            message: 'Ce code promo est déjà utilisé par une autre promotion active',
            valide: false
          });
        }
      }

      resultat.valide = resultat.erreurs.length === 0;
      observer.next(resultat);
      observer.complete();
    });
  }

  /**
   * Prévisualiser l'application d'une promotion
   */
  previewPromotion(promotionId: number, prixOriginal: number, quantite: number = 1): Observable<PreviewPromotion> {
    return new Observable(observer => {
      const promotion = this.promotionsSubject.value.find(p => p.id === promotionId);
      
      if (!promotion) {
        observer.error('Promotion non trouvée');
        return;
      }

      let prixAvecPromo = prixOriginal;
      let applicable = true;
      let raisonNonApplicable = '';

      // Vérifier si la promotion est active
      if (promotion.statut !== 'active') {
        applicable = false;
        raisonNonApplicable = 'La promotion n\'est pas active';
      }

      // Vérifier les dates
      const maintenant = new Date();
      if (maintenant < promotion.dateDebut || maintenant > promotion.dateFin) {
        applicable = false;
        raisonNonApplicable = 'La promotion n\'est pas dans sa période de validité';
      }

      if (applicable) {
        // Appliquer la réduction
        switch (promotion.typeReduction) {
          case 'pourcentage':
            prixAvecPromo = prixOriginal * (1 - promotion.valeurReduction / 100);
            break;
          case 'montant_fixe':
            prixAvecPromo = Math.max(0, prixOriginal - promotion.valeurReduction);
            break;
          case 'prix_fixe':
            prixAvecPromo = promotion.valeurReduction;
            break;
        }
      }

      const montantEconomise = prixOriginal - prixAvecPromo;
      const pourcentageEconomise = prixOriginal > 0 ? (montantEconomise / prixOriginal) * 100 : 0;

      const preview: PreviewPromotion = {
        prixOriginal,
        prixAvecPromo,
        montantEconomise,
        pourcentageEconomise,
        applicable,
        raisonNonApplicable
      };

      observer.next(preview);
      observer.complete();
    });
  }

  // ==================== TEMPLATES ====================

  /**
   * Obtenir les templates de promotion
   */
  getTemplatesPromotion(): Observable<TemplatePromotion[]> {
    const templates: TemplatePromotion[] = [
      {
        id: 1,
        nomPromotion: 'SOLDES20',
        description: 'Réduction de 20% sur tous les produits',
        type: 'automatique',
        typeReduction: 'pourcentage',
        valeurReduction: 20,
        conditions: [],
        populaire: true,
        categorie: 'soldes'
      },
      {
        id: 2,
        nomPromotion: 'FIDELITE15',
        description: 'Code promo pour clients fidèles',
        type: 'code_promo',
        typeReduction: 'pourcentage',
        valeurReduction: 15,
        conditions: [
          {
            type: 'montant_minimum',
            valeur: 100,
            operateur: 'superieur_egal',
            obligatoire: true
          }
        ],
        populaire: true,
        categorie: 'fidelite'
      },
      {
        id: 3,
        nomPromotion: 'OUTLET50',
        description: 'Déstockage avec réduction importante',
        type: 'outlet',
        typeReduction: 'pourcentage',
        valeurReduction: 50,
        conditions: [],
        populaire: false,
        categorie: 'clearance'
      }
    ];

    return of(templates);
  }

  // ==================== STATISTIQUES ====================

  /**
   * Obtenir les statistiques des promotions
   */
  getStatistiques(fournisseurId: number): Observable<StatistiquesPromotion> {
    return this.statistiques$;
  }

  /**
   * Mettre à jour les statistiques
   */
  private updateStatistiques(): void {
    const promotions = this.promotionsSubject.value;
    
    const stats: StatistiquesPromotion = {
      totalPromotions: promotions.length,
      promotionsActives: promotions.filter(p => p.statut === 'active').length,
      promotionsExpirees: promotions.filter(p => p.statut === 'expiree').length,
      totalUtilisations: promotions.reduce((sum, p) => sum + p.nombreUtilisations, 0),
      montantTotalEconomise: promotions.reduce((sum, p) => sum + p.montantEconomise, 0),
      chiffreAffaireTotalGenere: promotions.reduce((sum, p) => sum + p.chiffreAffaireGenere, 0),
      tauxUtilisationMoyen: 0,
      promotionLaPlusUtilisee: undefined
    };

    // Trouver la promotion la plus utilisée
    const plusUtilisee = promotions.reduce((max, p) => 
      p.nombreUtilisations > (max?.nombreUtilisations || 0) ? p : max, 
      null as Promotion | null
    );

    if (plusUtilisee) {
      stats.promotionLaPlusUtilisee = {
        nomPromotion: plusUtilisee.nomPromotion,
        utilisations: plusUtilisee.nombreUtilisations
      };
    }

    this.statistiquesSubject.next(stats);
  }

  // ==================== DONNÉES MOCK ====================

  private initializeMockData(): void {
    // Forcer la régénération des données pour corriger le problème "Aucun nom"
    localStorage.removeItem('mock-promotions');
    this.generateMockData();
    this.updateStatistiques();
  }

  private saveMockData(): void {
    localStorage.setItem('mock-promotions', JSON.stringify(this.promotionsSubject.value));
  }

  private generateMockData(): void {
    const mockPromotions: Promotion[] = [
      {
        id: 1,
        nomPromotion: 'SUMMER30',
        description: 'Grande promotion d\'été sur toute la collection',
        type: 'automatique',
        typeReduction: 'pourcentage',
        valeurReduction: 30,
        statut: 'active',
        dateDebut: new Date('2024-06-01'),
        dateFin: new Date('2024-08-31'),
        conditions: [],
        produitsConcernes: [],
        categoriesConcernees: [1, 2],
        cumulable: false,
        priorite: 1,
        actifLundiVendredi: true,
        actifWeekend: true,
        fournisseurId: 11,
        dateCreation: new Date('2024-05-15'),
        dateModification: new Date('2024-05-15'),
        creePar: 'Admin',
        utilisationActuelle: 45,
        nombreUtilisations: 45,
        montantEconomise: 2340.50,
        chiffreAffaireGenere: 7801.67
      },
      {
        id: 2,
        nomPromotion: 'VIP2024',
        description: 'Code promo exclusif pour nos clients VIP',
        type: 'code_promo',
        typeReduction: 'pourcentage',
        valeurReduction: 20,
        statut: 'active',
        dateDebut: new Date('2024-01-01'),
        dateFin: new Date('2024-12-31'),
        codePromo: 'VIP2024',
        utilisationMax: 100,
        utilisationParClient: 1,
        conditions: [
          {
            id: 1,
            promotionId: 2,
            type: 'montant_minimum',
            valeur: 150,
            operateur: 'superieur_egal',
            obligatoire: true
          }
        ],
        produitsConcernes: [],
        categoriesConcernees: [],
        cumulable: true,
        priorite: 2,
        fournisseurId: 11,
        dateCreation: new Date('2024-01-01'),
        dateModification: new Date('2024-01-01'),
        creePar: 'Admin',
        utilisationActuelle: 23,
        nombreUtilisations: 23,
        montantEconomise: 1150.00,
        chiffreAffaireGenere: 4600.00
      }
    ];

    this.promotionsSubject.next(mockPromotions);
    this.saveMockData();
  }
}
