import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';

import { CartComponent } from './cart/cart.component';
import { FavoritesComponent } from './favorites/favorites.component';
import { ProfileComponent } from './profile/profile.component';
import { OrdersComponent } from './orders/orders.component';
import { EditProfileComponent } from './edit-profile/edit-profile.component';
import { NotificationsComponent } from './notifications/notifications.component';

// Composants de checkout
import { CheckoutComponent } from './checkout/checkout.component';
import { AddressSelectionComponent } from './checkout/address-selection/address-selection.component';
import { PaymentSelectionComponent } from './checkout/payment-selection/payment-selection.component';
import { FinalConfirmationComponent } from './checkout/final-confirmation/final-confirmation.component';

const routes: Routes = [
  { path: 'cart', component: CartComponent },
  { path: 'favorites', component: FavoritesComponent },
  { path: 'profile', component: ProfileComponent },
  { path: 'edit-profile', component: EditProfileComponent },
  { path: 'orders', component: OrdersComponent },
  { path: 'notifications', component: NotificationsComponent },

  // Routes de checkout
  { path: 'checkout', component: CheckoutComponent },
  { path: 'checkout/address', component: AddressSelectionComponent },
  { path: 'checkout/payment', component: PaymentSelectionComponent },
  { path: 'checkout/final', component: FinalConfirmationComponent }
];

@NgModule({
  declarations: [
    CartComponent,
    FavoritesComponent,
    ProfileComponent,
    OrdersComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    FormsModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    MatIconModule,
    // Import des composants standalone
    EditProfileComponent,
    NotificationsComponent,
    CheckoutComponent,
    AddressSelectionComponent,
    PaymentSelectionComponent,
    FinalConfirmationComponent
  ]
})
export class UserModule {}
