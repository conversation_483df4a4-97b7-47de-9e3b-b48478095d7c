<div class="product-list-container">
  <button (click)="toggleFilter()" class="filter-button">
    <mat-icon>filter_list</mat-icon> Filtrer
  </button>
  <div
    class="filter-overlay"
    [class.show]="showFilter"
    (click)="toggleFilter()"
  ></div>
  <div class="filter-drawer" [class.open]="showFilter">
    <div class="filter-drawer-header">
      <h2>Filtrer</h2>
      <button (click)="closeFilter()" class="close-button">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <app-filter
      [marques]="marques"
      [formes]="formes"
      [prixMin]="prixMin"
      [prixMax]="prixMax"
      [categorieId]="categorieId"
      [sousCategorieId]="sousCategorieId"
      (filterChanged)="onFilterChanged($event)"
      (clearFilters)="onClearFilters()"
      (applyFilters)="onFilterApplied()"
    ></app-filter>
  </div>
  <div *ngIf="loading" class="loading-state">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Chargement des produits...</p>
  </div>
  <div class="main-content">
    <div *ngIf="!loading" class="card-grid">
      <div *ngIf="produitsPagination.length === 0" class="empty-state">
        <mat-icon>search_off</mat-icon>
        <p>Aucun produit ne correspond à vos critères</p>
        <button (click)="onClearFilters()" class="clear-filters-button">
          Réinitialiser les filtres
        </button>
      </div>
      <div
        *ngFor="let produit of produitsPagination"
        class="card border border-surface-200 dark:border-surface-700 rounded m-2"
        (click)="goToProductDetails(produit.id)"
        style="cursor: pointer"
      >
        <div class="card-image relative">
          <img
            [src]="getMainImage(produit)"
            [alt]="produit.nom"
            class="w-full"
            loading="lazy"
          />

          <!-- Badge Nouveauté -->
          <div class="product-badges">
            <span *ngIf="produit.estNouveau" class="badge-new"> Nouveau </span>
            <span *ngIf="produit.tauxRemiseTotale > 0" class="badge-discount">
              -{{ produit.tauxRemiseTotale }}%
            </span>
          </div>
        </div>

        <div class="card-content p-4">
          <h3
            class="mb-2 font-medium cursor-pointer text-primary-600 hover:underline"
          >
            {{ produit.nom }}
          </h3>
          <div class="product-info-right">
            <p class="product-brand">
              {{ produit.marque?.name || "Marque inconnue" }}
            </p>
            <p class="product-fournisseur">
              {{ produit.fournisseur?.raisonSociale || "Fournisseur inconnue" }}
            </p>
            <!-- Note moyenne en étoiles -->
          <div
            *ngIf="produit.noteMoyenne != null || produit.nombreAvis > 0"
            class="rating-section flex items-center"
          >
            <mat-icon
              *ngFor="let star of [1, 2, 3, 4, 5]"
              aria-label="Évaluation"
              [class.filled]="star <= produit.noteMoyenne"
            >
              {{ star <= produit.noteMoyenne ? "star" : "star_border" }}
            </mat-icon>
            <span class="review-count text-xs ml-1"
              >({{ produit.nombreAvis || 0 }})</span
            >
          </div>
          </div>
          <p class="truncate mb-4">{{ produit.description }}</p>

          <div class="price-section">
            <div class="price-stack">
              <!-- Prix final TTC -->
              <div class="price-final">
                {{ produit.prixFinalTTC | currency : "DT" }}
              </div>

              <!-- Prix après promo outlet -->
              <div
                class="price-intermediate"
                *ngIf="
                  produit.prixApresOutlet &&
                  produit.prixApresOutlet !== produit.prixFinalTTC
                "
              >
                Ancien prix :
                <span class="amount">
                  {{ produit.prixApresOutlet | currency : "DT" }}
                </span>
              </div>

              <!-- Prix d'origine -->
              <div
                class="price-original"
                *ngIf="
                  produit.prixOriginalTTC &&
                  produit.prixOriginalTTC !== produit.prixApresOutlet
                "
              >
                Prix d'origine :
                <span class="amount">
                  {{ produit.prixOriginalTTC | currency : "DT" }}
                </span>
              </div>
            </div>
          </div>

          <span class="button-container flex">
            <!-- Bouton Favoris -->
            <p-button
              icon="pi pi-heart"
              styleClass="p-button-secondary p-button-outlined"
              aria-label="Ajouter aux favoris"
              (click)="toggleFavori(produit)"
            ></p-button>

            <!-- Bouton Panier -->
            <p-button
              icon="pi pi-shopping-cart"
              styleClass="p-button-primary ml-2"
              aria-label="Ajouter au panier"
              (click)="ajouterAuPanier(produit)"
            ></p-button>
          </span>
        </div>
      </div>
    </div>
    <!-- Pagination -->
    <div class="pagination-wrapper">
      <div class="pagination-container">
        <div class="pagination-inner">
          <p-paginator
            [rows]="produitsParPage"
            [totalRecords]="totalProduits"
            (onPageChange)="changerPage($event)"
            [first]="pageActuelle * produitsParPage"
            styleClass="border-none"
          ></p-paginator>
        </div>
      </div>
    </div>
  </div>
</div>
