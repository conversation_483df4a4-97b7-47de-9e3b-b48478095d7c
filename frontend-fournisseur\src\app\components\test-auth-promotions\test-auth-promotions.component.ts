import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { PromotionSimpleService } from '../../services/promotion-simple.service';
import { PromotionSimpleDto } from '../../models/promotion-simple.model';

@Component({
  selector: 'app-test-auth-promotions',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="test-container">
      <h2>🧪 Test Authentification & Promotions</h2>
      
      <!-- Section Authentification -->
      <div class="auth-section">
        <h3>🔐 Authentification</h3>
        
        <div class="auth-status">
          <p><strong>Statut:</strong> 
            <span [class]="isAuthenticated ? 'status-success' : 'status-error'">
              {{ isAuthenticated ? '✅ Connecté' : '❌ Non connecté' }}
            </span>
          </p>
          
          <div *ngIf="currentUser">
            <p><strong>Utilisateur:</strong> {{ currentUser.nom }} {{ currentUser.prenom }}</p>
            <p><strong>Email:</strong> {{ currentUser.email }}</p>
            <p><strong>Role:</strong> {{ currentUser.role }}</p>
            <p><strong>ID:</strong> {{ currentUser.id }}</p>
          </div>
          
          <div *ngIf="token">
            <p><strong>Token:</strong> {{ token.substring(0, 50) }}...</p>
          </div>
        </div>
        
        <!-- Formulaire de connexion -->
        <div *ngIf="!isAuthenticated" class="login-form">
          <h4>Connexion</h4>
          <form (ngSubmit)="login()">
            <div class="form-group">
              <label>Email:</label>
              <input type="email" [(ngModel)]="loginData.email" name="email" required>
            </div>
            <div class="form-group">
              <label>Mot de passe:</label>
              <input type="password" [(ngModel)]="loginData.motDePasse" name="password" required>
            </div>
            <button type="submit" [disabled]="loginLoading">
              {{ loginLoading ? '🔄 Connexion...' : '🔐 Se connecter' }}
            </button>
          </form>
          
          <div class="quick-login">
            <h5>Connexion rapide avec fournisseur de test:</h5>
            <button (click)="quickLogin()" [disabled]="loginLoading">
              🚀 Connexion rapide
            </button>
          </div>
        </div>
        
        <div *ngIf="isAuthenticated">
          <button (click)="logout()" class="logout-btn">🚪 Se déconnecter</button>
        </div>
        
        <div *ngIf="authError" class="error">
          ❌ {{ authError }}
        </div>
      </div>
      
      <!-- Section Promotions -->
      <div class="promotions-section">
        <h3>🎯 Promotions</h3>
        
        <button (click)="loadPromotions()" [disabled]="promotionsLoading">
          {{ promotionsLoading ? '🔄 Chargement...' : '🔄 Charger mes promotions' }}
        </button>
        
        <div *ngIf="promotionsError" class="error">
          ❌ {{ promotionsError }}
        </div>
        
        <div *ngIf="promotions.length > 0" class="promotions-list">
          <h4>📋 Mes promotions ({{ promotions.length }})</h4>
          <div *ngFor="let promo of promotions" class="promotion-card">
            <div class="promo-header">
              <span class="promo-type" [ngClass]="'type-' + promo.type.toLowerCase()">
                {{ promo.type }}
              </span>
              <span class="promo-discount">-{{ promo.pourcentageRemise }}%</span>
            </div>
            <h5>{{ promo.nomAffichage }}</h5>
            <p *ngIf="promo.description">{{ promo.description }}</p>
            <p *ngIf="promo.codePromo"><strong>Code:</strong> {{ promo.codePromo }}</p>
            <div class="promo-dates">
              📅 Du {{ formatDate(promo.dateDebut) }} au {{ formatDate(promo.dateFin) }}
            </div>
            <div class="promo-status">
              <span [class]="promo.estValide ? 'status-success' : 'status-error'">
                {{ promo.estValide ? '✅ Active' : '❌ Inactive' }}
              </span>
            </div>
          </div>
        </div>
        
        <div *ngIf="!promotionsLoading && promotions.length === 0 && !promotionsError">
          <p>📭 Aucune promotion trouvée</p>
        </div>
      </div>
      
      <!-- Section Debug -->
      <div class="debug-section">
        <h3>🐛 Debug</h3>
        <button (click)="showDebugInfo = !showDebugInfo">
          {{ showDebugInfo ? '🙈 Masquer' : '👁️ Afficher' }} les infos debug
        </button>
        
        <div *ngIf="showDebugInfo" class="debug-info">
          <h4>LocalStorage:</h4>
          <pre>{{ debugInfo | json }}</pre>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .test-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
      font-family: Arial, sans-serif;
    }
    
    .auth-section, .promotions-section, .debug-section {
      margin: 20px 0;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
      background: #f9f9f9;
    }
    
    .auth-status {
      margin: 15px 0;
      padding: 10px;
      background: white;
      border-radius: 4px;
    }
    
    .status-success { color: #27ae60; font-weight: bold; }
    .status-error { color: #e74c3c; font-weight: bold; }
    
    .login-form {
      margin: 15px 0;
      padding: 15px;
      background: white;
      border-radius: 4px;
    }
    
    .form-group {
      margin: 10px 0;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    .form-group input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    button {
      padding: 10px 15px;
      margin: 5px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }
    
    button:not(:disabled) {
      background: #3498db;
      color: white;
    }
    
    button:disabled {
      background: #bdc3c7;
      color: #7f8c8d;
      cursor: not-allowed;
    }
    
    .logout-btn {
      background: #e74c3c !important;
      color: white !important;
    }
    
    .quick-login {
      margin-top: 15px;
      padding: 10px;
      background: #ecf0f1;
      border-radius: 4px;
    }
    
    .error {
      color: #e74c3c;
      background: #ffeaea;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    
    .promotions-list {
      margin: 15px 0;
    }
    
    .promotion-card {
      background: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 15px;
      margin: 10px 0;
    }
    
    .promo-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    
    .promo-type {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
    }
    
    .type-codepromo { background: #4ecdc4; color: white; }
    .type-automatique { background: #45b7d1; color: white; }
    .type-outlet { background: #ff6b6b; color: white; }
    
    .promo-discount {
      font-size: 20px;
      font-weight: bold;
      color: #e74c3c;
    }
    
    .promo-dates {
      color: #666;
      font-size: 14px;
      margin: 5px 0;
    }
    
    .promo-status {
      margin-top: 10px;
    }
    
    .debug-info {
      background: white;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
    }
    
    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  `]
})
export class TestAuthPromotionsComponent implements OnInit {
  // Authentification
  isAuthenticated = false;
  currentUser: any = null;
  token: string | null = null;
  loginLoading = false;
  authError: string | null = null;
  
  loginData = {
    email: '',
    motDePasse: ''
  };
  
  // Promotions
  promotions: PromotionSimpleDto[] = [];
  promotionsLoading = false;
  promotionsError: string | null = null;
  
  // Debug
  showDebugInfo = false;
  debugInfo: any = {};

  constructor(
    private authService: AuthService,
    private promotionService: PromotionSimpleService
  ) {}

  ngOnInit(): void {
    this.checkAuthStatus();
    this.updateDebugInfo();
  }

  checkAuthStatus(): void {
    this.isAuthenticated = this.authService.isAuthenticated();
    this.currentUser = this.authService.getCurrentUser();
    this.token = this.authService.getToken();
    
    console.log('🔍 Auth Status Check:', {
      isAuthenticated: this.isAuthenticated,
      currentUser: this.currentUser,
      hasToken: !!this.token
    });
  }

  login(): void {
    if (!this.loginData.email || !this.loginData.motDePasse) {
      this.authError = 'Email et mot de passe requis';
      return;
    }

    this.loginLoading = true;
    this.authError = null;

    this.authService.login(this.loginData).subscribe({
      next: (response) => {
        console.log('✅ Connexion réussie:', response);
        this.checkAuthStatus();
        this.updateDebugInfo();
        this.loginLoading = false;
      },
      error: (error) => {
        console.error('❌ Erreur de connexion:', error);
        this.authError = 'Erreur de connexion: ' + (error.message || 'Identifiants incorrects');
        this.loginLoading = false;
      }
    });
  }

  quickLogin(): void {
    this.loginData.email = '<EMAIL>'; // Utiliser un email de test
    this.loginData.motDePasse = 'TestPromo123!';
    this.login();
  }

  logout(): void {
    this.authService.logout();
    this.checkAuthStatus();
    this.updateDebugInfo();
    this.promotions = [];
  }

  loadPromotions(): void {
    if (!this.isAuthenticated) {
      this.promotionsError = 'Vous devez être connecté pour charger les promotions';
      return;
    }

    this.promotionsLoading = true;
    this.promotionsError = null;

    this.promotionService.getMyPromotions().subscribe({
      next: (promotions) => {
        console.log('✅ Promotions chargées:', promotions);
        this.promotions = promotions;
        this.promotionsLoading = false;
      },
      error: (error) => {
        console.error('❌ Erreur promotions:', error);
        this.promotionsError = 'Erreur lors du chargement: ' + (error.message || 'Erreur inconnue');
        this.promotionsLoading = false;
      }
    });
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('fr-FR');
  }

  updateDebugInfo(): void {
    this.debugInfo = {
      localStorage: {
        auth_token: localStorage.getItem('auth_token'),
        current_user: localStorage.getItem('current_user'),
        fournisseur_profile: localStorage.getItem('fournisseur_profile')
      },
      authService: {
        isAuthenticated: this.authService.isAuthenticated(),
        currentUser: this.authService.getCurrentUser(),
        hasToken: !!this.authService.getToken()
      }
    };
  }
}
