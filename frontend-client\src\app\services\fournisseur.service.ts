import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { FournisseurDto } from '../models/FournisseurDto';

@Injectable({
  providedIn: 'root'
})
export class FournisseurService {
  private apiUrl = `${environment.apiUrl}/fournisseurs`;

  constructor(private http: HttpClient) {}

  getAll(onlyActive: boolean = true): Observable<FournisseurDto[]> {
    return this.http.get<FournisseurDto[]>(`${this.apiUrl}?onlyActive=${onlyActive}`);
  }

  getById(id: number): Observable<FournisseurDto> {
    return this.http.get<FournisseurDto>(`${this.apiUrl}/${id}`);
  }

  toggleStatus(id: number): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/toggle-status`, {});
  }

  updateCommission(id: number, commission: number): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/commission`, { commission });
  }
}
