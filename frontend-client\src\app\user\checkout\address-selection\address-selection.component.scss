@import '../checkout.component.scss';

.addresses-section {
  background-color: var(--card-background-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 30px;
  box-shadow: var(--card-shadow);

  h2 {
    color: var(--text-color);
    margin-bottom: 25px;
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.no-addresses {
  text-align: center;
  padding: 60px 20px;

  .no-addresses-icon {
    font-size: 4rem;
    margin-bottom: 20px;
  }

  h3 {
    color: var(--text-color);
    margin-bottom: 10px;
    font-size: 1.2rem;
  }

  p {
    color: var(--secondary-color);
    margin-bottom: 30px;
    font-size: 16px;
  }

  .btn {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: var(--primary-color);
    color: white;

    &:hover {
      background-color: var(--primary-color-hover);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
  }
}

.addresses-list {
  display: grid;
  gap: 15px;
}

.address-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  background-color: var(--card-background-color);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  &.selected {
    border-color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.05);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  }

  .address-radio {
    flex-shrink: 0;

    input[type="radio"] {
      width: 20px;
      height: 20px;
      accent-color: var(--primary-color);
      cursor: pointer;
    }
  }

  .address-content {
    flex: 1;

    .address-header {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 8px;

      h3 {
        color: var(--text-color);
        font-size: 16px;
        font-weight: 600;
        margin: 0;
      }

      .main-badge {
        background-color: var(--primary-color);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
      }
    }

    .address-details {
      p {
        color: var(--secondary-color);
        font-size: 14px;
        margin: 2px 0;
      }
    }
  }

  .address-actions {
    flex-shrink: 0;

    .select-text {
      color: var(--primary-color);
      font-weight: 600;
      font-size: 14px;
    }
  }
}

.add-address-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border: 2px dashed var(--border-color);
  border-radius: 12px;
  background-color: var(--card-background-color);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.02);
  }

  .add-address-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    flex-shrink: 0;
  }

  .add-address-text {
    h3 {
      color: var(--text-color);
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 5px 0;
    }

    p {
      color: var(--secondary-color);
      font-size: 14px;
      margin: 0;
    }
  }
}

.checkout-steps {
  .step {
    &.completed {
      .step-number {
        background-color: var(--success-color);
        color: white;
      }

      .step-label {
        color: var(--success-color);
        font-weight: 600;
      }

      &::after {
        background-color: var(--success-color);
      }
    }
  }
}

@media (max-width: 768px) {
  .addresses-section {
    padding: 20px;
  }

  .address-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;

    .address-radio {
      align-self: flex-end;
    }

    .address-content {
      width: 100%;
    }

    .address-actions {
      align-self: flex-end;
    }
  }

  .add-address-card {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
}
