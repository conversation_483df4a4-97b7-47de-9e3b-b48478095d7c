﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Runtime.CompilerServices;

namespace WebApiPfe.DTOs.ReadDTOs
{
    public class TauxTVADto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Le libellé est obligatoire")]
        [StringLength(50, ErrorMessage = "Max 50 caractères")]
        public string? Libelle { get; set; }

        [Required(ErrorMessage = "Le taux est obligatoire")]
        [Range(0.01, 99.99, ErrorMessage = "Le taux doit être entre 0.01 et 99.99")]
        public decimal Taux { get; set; }
        [Required(ErrorMessage = "La catégorie est obligatoire")]
        [Range(1, int.MaxValue, ErrorMessage = "ID de catégorie invalide")]
        public int CategorieId { get; set; }

        public string? Description { get; set; }

        [Required(ErrorMessage = "Le statut actif est obligatoire")]
        public bool EstActif { get; set; } = true;

        [Required(ErrorMessage = "La date d'effet est obligatoire")]
        public DateTime DateEffet { get; set; } = DateTime.UtcNow;

        public DateTime? DateFin { get; set; }

        // Variantes pour création/mise à jour
        [Display(Name = "TauxTVACreate")]
        public class Create : TauxTVADto { }
        [Display(Name = "TauxTVAUpdate")]
        public class Update : TauxTVADto { public new int Id { get; set; } }
    }
}
