<!-- Container principal -->
<div class="categories-wrapper">
  <!-- Version Desktop (masquée en mobile) -->
  <div class="desktop-categories d-none d-lg-block">
    <div class="sub-categories-container">
      <!-- Hitbox améliorée -->
      <div
        class="sub-categories-hitbox"
        (mouseenter)="delayClose()"
        (mouseleave)="handleMouseLeave()"
      ></div>

      <!-- Contenu visible -->
      <div
        class="sub-categories-visible"
        *ngIf="subCategories.length > 0 || formes.length > 0"
      >
        <div class="sub-categories-content">
          <!-- Section Sous-catégories -->
          <div *ngIf="subCategories.length > 0" class="sub-categories-section">
            <ul class="sub-categories-list">
              <li
                *ngFor="let subCategory of subCategories"
                class="sub-category-item"
              >
                <button
                  (click)="navigateToSubCategory(subCategory.id)"
                  class="sub-category-link"
                >
                  {{ subCategory.nom }}
                </button>
              </li>
            </ul>
          </div>

          <!-- Section Formes -->
          <div *ngIf="formes.length > 0" class="formes-section">
            <h2>Des Styles En Vogue</h2>
            <div class="formes-list">
              <div
                *ngFor="let forme of formes"
                class="forme-item"
                (click)="navigateToForme(forme.id)"
              >
                <img
                  [src]="forme.imageUrl"
                  [alt]="forme.nom + ' logo'"
                  class="forme-image"
                  loading="lazy"
                />
                <p class="forme-nom">{{ forme.nom }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Version Mobile (masquée en desctope) -->
  <div class="mobile-categories d-block d-lg-none">
    <div
      class="mobile-subcategories-content"
      *ngIf="subCategories.length > 0 || formes.length > 0"
    >
      <div
        *ngIf="subCategories.length > 0"
        class="mobile-subcategories-section"
      >
        <ul class="mobile-subcategories-list">
          <li
            *ngFor="let subCategory of subCategories"
            class="mobile-subcategory-item"
          >
            <button
              (click)="navigateToSubCategory(subCategory.id)"
              class="mobile-subcategory-link"
            >
              {{ subCategory.nom }} 
            </button>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
