import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { Router } from '@angular/router';
import { catchError, finalize, of, Subject, takeUntil } from 'rxjs';
import { FournisseurDto } from 'src/app/models/FournisseurDto';
import { FournisseurService } from 'src/app/services/fournisseur.service';
import { ImageUrlService } from 'src/app/services/image-url.service';

@Component({
  selector: 'app-fournisseurs-list',
  standalone: false,
  providers: [FournisseurService],
  templateUrl: './fournisseurs-list.component.html',
  styleUrl: './fournisseurs-list.component.scss',
})
export class FournisseursListComponent implements OnInit, OnDestroy {
  @Input() isMenuOpen: boolean = false;
  @Output() closeMenuEvent = new EventEmitter<void>();
  @Output() mouseLeave = new EventEmitter<void>();

  isActive = false;
  fournisseurs: FournisseurDto[] = [];
  loading = false;
  error: string | null = null;
  isMobileView = false;
  limitedFournisseurs: FournisseurDto[] = [];

  private boundResizeHandler = this.checkViewport.bind(this);
  private closeTimer: number | null = null;
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private fournisseurService: FournisseurService,
    public imageUrlService: ImageUrlService
  ) {}

  ngOnInit(): void {
    this.loadData();
    this.checkViewport();
    window.addEventListener('resize', this.boundResizeHandler);
  }
  checkViewport() {
    const wasMobile = this.isMobileView;
    this.isMobileView = window.innerWidth <= 1400;

    if (this.isMobileView && !wasMobile) {
      this.updateLimitedFournisseurs();
    }
  }

  private updateLimitedFournisseurs(): void {
    this.limitedFournisseurs = this.fournisseurs.slice(0, 10);
  }

  ngOnDestroy(): void {
    this.clearTimer();
    this.destroy$.next();
    this.destroy$.complete();

    window.removeEventListener('resize', this.boundResizeHandler);
  }

  private loadData(): void {
    this.loading = true;
    this.error = null;

    this.fournisseurService
      .getAll()
      .pipe(
        takeUntil(this.destroy$),
        catchError((err) => {
          this.error = 'Erreur lors du chargement des fournisseurs';
          console.error(err);
          return of([]);
        }),
        finalize(() => (this.loading = false))
      )
      .subscribe((fournisseurs) => {
        console.log(
          'Fournisseurs reçus:',
          JSON.stringify(fournisseurs, null, 2)
        );
        this.fournisseurs = fournisseurs;
        this.updateLimitedFournisseurs();
        this.isActive = true;
        if (fournisseurs.length > 0) {
          console.log(
            'URL logo du premier fournisseur:',
            fournisseurs[0].logoFile
          );
        }
      });
  }

  navigateToFournisseur(fournisseurId: number): void {
    if (!fournisseurId) {
      console.error('Fournisseur ID is missing');
      return;
    }

    this.router.navigate(['/products/fournisseur', fournisseurId]);
    this.closeMenu();
    this.closeMenuEvent.emit();
  }

  navigateToAllFournisseurs(): void {
    this.router.navigate(['/fournisseurs']);
  }

  closeMenu(): void {
    this.isActive = false;
    this.closeMenuEvent.emit();
  }

  handleMouseLeave(): void {
    this.mouseLeave.emit();
  }

  delayClose(): void {
    this.clearTimer();
  }

  private clearTimer(): void {
    if (this.closeTimer) {
      clearTimeout(this.closeTimer);
      this.closeTimer = null;
    }
  }
}
