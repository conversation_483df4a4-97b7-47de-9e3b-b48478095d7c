/* ===== FOOTER MODERNE ===== */
footer {
  background: linear-gradient(135deg, var(--card-background-color) 0%, var(--card-background-color-hover) 100%);
  color: var(--text-color);
  padding: 3rem 1rem 2rem;
  margin-top: auto;
  border-top: 1px solid var(--border-color);
  box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.05);

  .footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      text-align: center;
    }
  }

  .footer-section {
    h3 {
      color: var(--primary-color);
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 1rem;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -0.5rem;
        left: 0;
        width: 50px;
        height: 2px;
        background: var(--gradient-primary);

        @media (max-width: 768px) {
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }

    p, li {
      color: var(--secondary-color);
      line-height: 1.6;
      margin-bottom: 0.5rem;
    }

    ul {
      list-style: none;
      padding: 0;

      li {
        margin-bottom: 0.75rem;

        a {
          color: var(--secondary-color);
          text-decoration: none;
          transition: all 0.3s ease;
          position: relative;

          &:hover {
            color: var(--primary-color);
            padding-left: 0.5rem;
          }

          &::before {
            content: '→';
            position: absolute;
            left: -1rem;
            opacity: 0;
            transition: all 0.3s ease;
          }

          &:hover::before {
            opacity: 1;
            left: -0.75rem;
          }
        }
      }
    }
  }

  .social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;

    @media (max-width: 768px) {
      justify-content: center;
    }

    a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: var(--primary-color);
      color: white;
      border-radius: 50%;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        background: var(--primary-color-hover);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
      }

      i {
        font-size: 1.125rem;
      }
    }
  }

  .footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--secondary-color);
    font-size: 0.875rem;

    .footer-links {
      display: flex;
      justify-content: center;
      gap: 2rem;
      margin-bottom: 1rem;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 0.5rem;
      }

      a {
        color: var(--secondary-color);
        text-decoration: none;
        transition: color 0.3s ease;

        &:hover {
          color: var(--primary-color);
        }
      }
    }

    .copyright {
      margin-top: 1rem;

      .heart {
        color: var(--error-color);
        animation: heartbeat 1.5s ease-in-out infinite;
      }
    }
  }
}

/* === ANIMATIONS === */
@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* === NEWSLETTER === */
.newsletter-form {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
  }

  input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 0.5rem;
    background: var(--card-background-color);
    color: var(--text-color);
    font-size: 0.875rem;
    transition: all 0.3s ease;

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    &::placeholder {
      color: var(--secondary-color);
    }
  }

  button {
    padding: 0.75rem 1.5rem;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
    }

    @media (max-width: 768px) {
      width: 100%;
    }
  }
}

/* === CONTACT INFO === */
.contact-info {
  .contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;

    @media (max-width: 768px) {
      justify-content: center;
    }

    i {
      color: var(--primary-color);
      font-size: 1.125rem;
      width: 20px;
      text-align: center;
    }

    span {
      color: var(--secondary-color);
      font-size: 0.875rem;
    }
  }
}
