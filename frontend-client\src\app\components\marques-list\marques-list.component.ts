import {
  Component,
  Input,
  OnDestroy,
  OnInit,
  Output,
  EventEmitter,
} from '@angular/core';
import { Router } from '@angular/router';
import { catchError, finalize, of, Subject, takeUntil } from 'rxjs';
import { MarqueDto } from 'src/app/models/MarqueDto';
import { MarqueService } from 'src/app/services/marque.service';

@Component({
  selector: 'app-marques-list',
  standalone: false,
  providers: [MarqueService],
  templateUrl: './marques-list.component.html',
  styleUrls: ['./marques-list.component.scss'],
})
export class MarquesListComponent implements OnInit, OnDestroy {
  @Input() isMenuOpen: boolean = false;
  @Output() closeMenuEvent = new EventEmitter<void>();
  @Output() mouseLeave = new EventEmitter<void>();

  isActive = false;
  marques: MarqueDto[] = [];
  loading = false;
  error: string | null = null;
  isMobileView = false;
  limitedMarques: MarqueDto[] = [];
  
  private boundResizeHandler = this.checkViewport.bind(this);
  private closeTimer: number | null = null;
  private destroy$ = new Subject<void>();

  constructor(private router: Router, private marqueService: MarqueService) {}

  ngOnInit(): void {
    this.loadData();
    this.checkViewport();
    window.addEventListener('resize', this.boundResizeHandler);
  }
  checkViewport() {
    const wasMobile = this.isMobileView;
    this.isMobileView = window.innerWidth <= 1400;

    if (this.isMobileView && !wasMobile) {
      this.updateLimitedMarques();
    }
  }

  private updateLimitedMarques(): void {
    this.limitedMarques = this.marques.slice(0, 10);
  }

  ngOnDestroy(): void {
    this.clearTimer();
    this.destroy$.next();
    this.destroy$.complete();

    window.removeEventListener('resize', this.boundResizeHandler);
  }

  private loadData(): void {
    this.loading = true;
    this.error = null;

    this.marqueService
      .getAll()
      .pipe(
        takeUntil(this.destroy$),
        catchError((err) => {
          this.error = 'Erreur lors du chargement des marques';
          console.error(err);
          return of([]);
        }),
        finalize(() => (this.loading = false))
      )
      .subscribe((marques) => {
        this.marques = marques;
        this.updateLimitedMarques();
        this.isActive = true;
      });
  }

  navigateToMarque(marqueId: number): void {
    if (!marqueId) {
      console.error('Marque ID is missing');
      return;
    }

    this.router.navigate(['/products/marque', marqueId]);
    this.closeMenu();
    this.closeMenuEvent.emit();
  }
  navigateToAllMarques(): void {
    this.router.navigate(['/marques']);
  }

  closeMenu(): void {
    this.isActive = false;
    this.closeMenuEvent.emit();
  }

  handleMouseLeave(): void {
    this.mouseLeave.emit();
  }

  delayClose(): void {
    this.clearTimer();
  }

  private clearTimer(): void {
    if (this.closeTimer) {
      clearTimeout(this.closeTimer);
      this.closeTimer = null;
    }
  }
}
