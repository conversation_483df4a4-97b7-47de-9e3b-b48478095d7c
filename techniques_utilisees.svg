<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="800" height="400" fill="#ffffff"/>
  
  <!-- Title -->
  <text x="40" y="50" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#1e40af">
    Les techniques utilisées
  </text>
  
  <!-- Angular Frontend Container -->
  <rect x="50" y="80" width="320" height="280" rx="15" fill="none" stroke="#dc2626" stroke-width="2" stroke-dasharray="8,4"/>
  <text x="180" y="110" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#dc2626" text-anchor="middle">Angular 19</text>
  
  <!-- Angular Components -->
  <rect x="70" y="130" width="80" height="60" fill="#4b5563" rx="5"/>
  <text x="110" y="150" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">Components</text>
  <text x="110" y="165" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">TypeScript</text>
  
  <rect x="160" y="130" width="80" height="60" fill="#4b5563" rx="5"/>
  <text x="200" y="150" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">Services</text>
  <text x="200" y="165" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">RxJS</text>
  
  <rect x="250" y="130" width="80" height="60" fill="#4b5563" rx="5"/>
  <text x="290" y="150" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">Router</text>
  <text x="290" y="165" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">Guards</text>
  
  <!-- ASP.NET Core Backend Container -->
  <rect x="430" y="80" width="320" height="280" rx="15" fill="none" stroke="#7c3aed" stroke-width="2" stroke-dasharray="8,4"/>
  <text x="590" y="110" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#7c3aed" text-anchor="middle">ASP.NET Core</text>
  
  <!-- Backend Components -->
  <rect x="450" y="130" width="80" height="60" fill="#4b5563" rx="5"/>
  <text x="490" y="150" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">Controllers</text>
  <text x="490" y="165" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">Web API</text>
  
  <rect x="540" y="130" width="80" height="60" fill="#4b5563" rx="5"/>
  <text x="580" y="150" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">Services</text>
  <text x="580" y="165" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">C#</text>
  
  <rect x="630" y="130" width="80" height="60" fill="#4b5563" rx="5"/>
  <text x="670" y="150" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">Entity</text>
  <text x="670" y="165" font-family="Arial, sans-serif" font-size="11" fill="white" text-anchor="middle">Framework</text>
  
  <!-- SQL Server Database -->
  <ellipse cx="590" cy="300" rx="65" ry="45" fill="none" stroke="#dc2626" stroke-width="2"/>
  <ellipse cx="590" cy="295" rx="65" ry="10" fill="#dc2626"/>
  <ellipse cx="590" cy="300" rx="65" ry="10" fill="none" stroke="#dc2626" stroke-width="1"/>
  <ellipse cx="590" cy="305" rx="65" ry="10" fill="none" stroke="#dc2626" stroke-width="1"/>
  <text x="590" y="320" font-family="Arial, sans-serif" font-size="13" fill="#dc2626" text-anchor="middle" font-weight="bold">SQL Server</text>
  <text x="590" y="335" font-family="Arial, sans-serif" font-size="11" fill="#dc2626" text-anchor="middle">Database</text>
  
  <!-- HTTP REST API Connection -->
  <rect x="350" y="210" width="100" height="35" fill="#f59e0b" rx="17"/>
  <text x="400" y="225" font-family="Arial, sans-serif" font-size="13" fill="white" text-anchor="middle" font-weight="bold">HTTP</text>
  <text x="400" y="240" font-family="Arial, sans-serif" font-size="10" fill="white" text-anchor="middle">REST API</text>
  
  <!-- Entity Framework Connection -->
  <rect x="540" y="250" width="100" height="35" fill="#f59e0b" rx="17"/>
  <text x="590" y="265" font-family="Arial, sans-serif" font-size="13" fill="white" text-anchor="middle" font-weight="bold">EF Core</text>
  <text x="590" y="280" font-family="Arial, sans-serif" font-size="10" fill="white" text-anchor="middle">ORM</text>
  
  <!-- Additional Technologies -->
  <text x="70" y="220" font-family="Arial, sans-serif" font-size="11" fill="#6b7280">• HTML5/CSS3</text>
  <text x="70" y="235" font-family="Arial, sans-serif" font-size="11" fill="#6b7280">• JWT Authentication</text>
  <text x="70" y="250" font-family="Arial, sans-serif" font-size="11" fill="#6b7280">• Responsive Design</text>
  
  <text x="450" y="220" font-family="Arial, sans-serif" font-size="11" fill="#6b7280">• Stripe Payment</text>
  <text x="450" y="235" font-family="Arial, sans-serif" font-size="11" fill="#6b7280">• AutoMapper</text>
  <text x="450" y="250" font-family="Arial, sans-serif" font-size="11" fill="#6b7280">• Swagger/OpenAPI</text>
  
  <!-- Connection Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
  </defs>
  
  <line x1="330" y1="227" x2="350" y2="227" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="450" y1="227" x2="430" y2="227" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="590" y1="250" x2="590" y2="285" stroke="#374151" stroke-width="2" marker-end="url(#arrowhead)"/>
</svg>
