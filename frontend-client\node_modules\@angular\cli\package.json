{"name": "@angular/cli", "version": "19.2.15", "description": "CLI tool for Angular", "main": "lib/cli/index.js", "bin": {"ng": "./bin/ng.js"}, "keywords": ["Angular CLI", "Angular DevKit", "angular", "angular-cli", "devkit", "sdk"], "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli", "dependencies": {"@angular-devkit/architect": "0.1902.15", "@angular-devkit/core": "19.2.15", "@angular-devkit/schematics": "19.2.15", "@inquirer/prompts": "7.3.2", "@listr2/prompt-adapter-inquirer": "2.0.18", "@schematics/angular": "19.2.15", "@yarnpkg/lockfile": "1.1.0", "ini": "5.0.0", "jsonc-parser": "3.3.1", "listr2": "8.2.5", "npm-package-arg": "12.0.2", "npm-pick-manifest": "10.0.0", "pacote": "20.0.0", "resolve": "1.22.10", "semver": "7.7.1", "symbol-observable": "4.0.0", "yargs": "17.7.2"}, "ng-update": {"migrations": "@schematics/angular/migrations/migration-collection.json", "packageGroup": {"@angular/cli": "19.2.15", "@angular/build": "19.2.15", "@angular/ssr": "19.2.15", "@angular-devkit/architect": "0.1902.15", "@angular-devkit/build-angular": "19.2.15", "@angular-devkit/build-webpack": "0.1902.15", "@angular-devkit/core": "19.2.15", "@angular-devkit/schematics": "19.2.15"}}, "packageManager": "pnpm@9.15.6", "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}}