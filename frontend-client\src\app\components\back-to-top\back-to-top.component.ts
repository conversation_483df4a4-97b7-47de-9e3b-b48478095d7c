import { Component, HostListener, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { fromEvent, Subscription, throttleTime } from 'rxjs';

@Component({
  selector: 'app-back-to-top',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './back-to-top.component.html',
  styleUrls: ['./back-to-top.component.scss']
})
export class BackToTopComponent implements OnDestroy {
  isVisible = false;
  private readonly SHOW_POSITION = 300; // Seuil d'apparition (px)
  private scrollSubscription?: Subscription;

  constructor() {
    this.scrollSubscription = fromEvent(window, 'scroll')
      .pipe(throttleTime(100)) // Limite à 1 vérification/100ms
      .subscribe(() => this.checkScroll());
  }

  private checkScroll(): void {
    this.isVisible = window.scrollY > this.SHOW_POSITION;
  }

  scrollToTop(): void {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }

  ngOnDestroy(): void {
    this.scrollSubscription?.unsubscribe();
  }
}
