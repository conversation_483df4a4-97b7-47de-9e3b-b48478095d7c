import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { DashboardService, DashboardStats, RecentOrder, RecentActivity, Fournisseur } from '../../services/dashboard.service';
import { User } from '../../models/user.model';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {
  currentUser: User | null = null;
  fournisseurInfo: Fournisseur | null = null;

  // États de chargement
  isLoading = true;
  isStatsLoading = true;
  isOrdersLoading = true;
  isActivityLoading = true;

  // Statistiques du dashboard (données réelles)
  stats: DashboardStats = {
    totalProducts: 0,
    activeOrders: 0,
    pendingDeliveries: 0,
    monthlyRevenue: 0
  };

  // Commandes récentes (données réelles)
  recentOrders: RecentOrder[] = [];

  // Activité récente (données réelles)
  recentActivity: RecentActivity[] = [];

  // Données pour affichage simple (fallback si pas de données)
  public salesData = [
    { month: 'Janvier', sales: 65 },
    { month: 'Février', sales: 59 },
    { month: 'Mars', sales: 80 },
    { month: 'Avril', sales: 81 },
    { month: 'Mai', sales: 56 },
    { month: 'Juin', sales: 55 }
  ];

  constructor(
    private authService: AuthService,
    private dashboardService: DashboardService
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });

    // Test du formatage des devises
    console.log('🧪 Test formatage devise:');
    console.log('- 1234.567 =>', this.formatCurrency(1234.567));
    console.log('- 0 =>', this.formatCurrency(0));
    console.log('- null =>', this.formatCurrency(null as any));
    console.log('- undefined =>', this.formatCurrency(undefined as any));

    // Charger les données du dashboard
    this.loadDashboardData();
  }

  /**
   * Charger toutes les données du dashboard
   */
  private loadDashboardData(): void {
    this.isLoading = true;
    console.log('🔄 Chargement des données du dashboard...');

    // Vérifier l'utilisateur connecté
    const currentUser = this.authService.getCurrentUser();
    const supplierId = localStorage.getItem('supplierId');
    console.log('👤 Utilisateur connecté:', currentUser);
    console.log('🏪 ID Fournisseur (localStorage):', supplierId);

    // Charger les informations du fournisseur
    this.dashboardService.getFournisseurInfo().subscribe({
      next: (fournisseur) => {
        this.fournisseurInfo = fournisseur;
        console.log('✅ Informations fournisseur chargées:', fournisseur);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement du fournisseur:', error);
      }
    });

    // Charger les statistiques
    this.loadStats();

    // Charger les commandes récentes
    this.loadRecentOrders();

    // Charger l'activité récente
    this.loadRecentActivity();

    this.isLoading = false;
  }

  /**
   * Charger les statistiques
   */
  private loadStats(): void {
    this.isStatsLoading = true;
    console.log('📊 Chargement des statistiques...');

    // Utiliser la nouvelle méthode qui essaie le backend puis fait un fallback
    this.dashboardService.getDashboardStatsComplete().subscribe({
      next: (stats) => {
        this.stats = stats;
        this.isStatsLoading = false;
        console.log('✅ Statistiques chargées:', stats);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des statistiques:', error);
        this.isStatsLoading = false;
        // Garder les valeurs par défaut (0)
      }
    });
  }

  /**
   * Charger les commandes récentes
   */
  private loadRecentOrders(): void {
    this.isOrdersLoading = true;

    this.dashboardService.getRecentOrders().subscribe({
      next: (orders) => {
        this.recentOrders = orders;
        this.isOrdersLoading = false;
        console.log('Commandes récentes chargées:', orders);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des commandes:', error);
        this.isOrdersLoading = false;
        // Garder un tableau vide
      }
    });
  }

  /**
   * Charger l'activité récente
   */
  private loadRecentActivity(): void {
    this.isActivityLoading = true;

    this.dashboardService.getRecentActivity().subscribe({
      next: (activity) => {
        this.recentActivity = activity;
        this.isActivityLoading = false;
        console.log('Activité récente chargée:', activity);
      },
      error: (error) => {
        console.error('Erreur lors du chargement de l\'activité:', error);
        this.isActivityLoading = false;
        // Garder un tableau vide
      }
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Livré':
        return 'status-delivered';
      case 'En cours':
        return 'status-processing';
      case 'En préparation':
        return 'status-preparing';
      default:
        return 'status-default';
    }
  }

  formatCurrency(amount: number): string {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return '0,000 TND';
    }

    try {
      // Essayer d'abord avec le formatage TND
      return new Intl.NumberFormat('fr-TN', {
        style: 'currency',
        currency: 'TND',
        minimumFractionDigits: 3
      }).format(amount);
    } catch (error) {
      // Fallback si TND n'est pas supporté
      console.warn('TND currency not supported, using fallback formatting');
      return new Intl.NumberFormat('fr-FR', {
        minimumFractionDigits: 3
      }).format(amount) + ' TND';
    }
  }
}
