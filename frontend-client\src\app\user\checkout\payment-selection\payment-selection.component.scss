@import '../checkout.component.scss';

.payment-section {
  background-color: var(--card-background-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 30px;
  box-shadow: var(--card-shadow);

  h2 {
    color: var(--text-color);
    margin-bottom: 25px;
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.payment-methods {
  display: grid;
  gap: 15px;
  margin-bottom: 30px;
}

.payment-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border: 2px solid var(--border-color);
  border-radius: 12px;
  background-color: var(--card-background-color);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(.disabled) {
    border-color: var(--primary-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  &.selected {
    border-color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.05);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: var(--disabled-background, #f8f9fa);

    &:hover {
      transform: none;
      box-shadow: none;
      border-color: var(--border-color);
    }
  }

  .payment-radio {
    flex-shrink: 0;

    input[type="radio"] {
      width: 20px;
      height: 20px;
      accent-color: var(--primary-color);
      cursor: pointer;

      &:disabled {
        cursor: not-allowed;
      }
    }
  }

  .payment-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(52, 152, 219, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
  }

  .payment-content {
    flex: 1;

    h3 {
      color: var(--text-color);
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 5px 0;
    }

    p {
      color: var(--secondary-color);
      font-size: 14px;
      margin: 0 0 5px 0;
    }

    .unavailable-badge {
      background-color: var(--warning-color, #f39c12);
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
  }

  .payment-actions {
    flex-shrink: 0;

    .select-text {
      color: var(--primary-color);
      font-weight: 600;
      font-size: 14px;
    }
  }
}

.security-info {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background-color: rgba(40, 167, 69, 0.05);
  border: 1px solid rgba(40, 167, 69, 0.2);
  border-radius: 8px;

  .security-icon {
    font-size: 24px;
    flex-shrink: 0;
  }

  .security-text {
    h4 {
      color: var(--success-color);
      font-size: 14px;
      font-weight: 600;
      margin: 0 0 5px 0;
    }

    p {
      color: var(--text-color);
      font-size: 13px;
      margin: 0;
      opacity: 0.8;
    }
  }
}

.checkout-steps {
  .step {
    &.completed {
      .step-number {
        background-color: var(--success-color);
        color: white;
      }

      .step-label {
        color: var(--success-color);
        font-weight: 600;
      }

      &::after {
        background-color: var(--success-color);
      }
    }
  }
}

@media (max-width: 768px) {
  .payment-section {
    padding: 20px;
  }

  .payment-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;

    .payment-radio {
      align-self: flex-end;
    }

    .payment-icon {
      align-self: center;
    }

    .payment-content {
      text-align: center;
      width: 100%;
    }

    .payment-actions {
      align-self: flex-end;
    }
  }

  .security-info {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
}
