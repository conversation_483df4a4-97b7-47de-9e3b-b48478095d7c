import { Injectable } from '@angular/core';
import { catchError, forkJoin, map, Observable, of, switchMap, tap } from 'rxjs';
import { ProduitCard } from '../models/ProduitCard';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { ProduitDto } from '../models/ProduitDto';
import { ImageProduitDto } from '../models/ImageProduitDto';
import { mapProduitDtoToCard } from '../utils/produit-mapper';
import { MarqueDto } from '../models/MarqueDto';
import { FormeDto } from '../models/FormeDto';

@Injectable({
  providedIn: 'root',
})
export class ProduitService {
  private baseUrl = `${environment.apiUrl}/produits`;
  private produitCache = new Map<number, ProduitCard>();

  constructor(private http: HttpClient) {}

  private enrichirProduits(dtos: ProduitDto[]): Observable<ProduitCard[]> {
    const requests = dtos.map(dto => 
      this.estComplet(dto) 
        ? of(mapProduitDtoToCard(dto)) 
        : this.getByIdFromCache(dto.id)
    );
    return forkJoin(requests);
  }

  private estComplet(dto: ProduitDto): boolean {
    return !!dto.promotions?.length && !!dto.prixApresRemises;
  }

  private getByIdFromCache(id: number): Observable<ProduitCard> {
    if (this.produitCache.has(id)) {
      return of(this.produitCache.get(id)!);
    }
    return this.getById(id);
  }

  getAll(): Observable<ProduitCard[]> {
    return this.http.get<ProduitDto[]>(this.baseUrl).pipe(
      switchMap(dtos => this.enrichirProduits(dtos)),
      catchError(() => of([]))
    );
  }

  getById(id: number): Observable<ProduitCard> {
    return this.http.get<ProduitDto>(`${this.baseUrl}/${id}`).pipe(
      map(mapProduitDtoToCard),
      tap(card => this.produitCache.set(id, card))
    );
  }

  search(term: string): Observable<ProduitCard[]> {
    return this.http.get<ProduitDto[]>(`${this.baseUrl}/search?term=${encodeURIComponent(term)}`).pipe(
      switchMap(dtos => this.enrichirProduits(dtos)),
      catchError(() => of([]))
    );
  }

  getBySousCategorie(id: number): Observable<ProduitCard[]> {
    return this.http.get<ProduitDto[]>(`${this.baseUrl}/by-sous-categorie/${id}`).pipe(
      switchMap(dtos => this.enrichirProduits(dtos)),
      catchError(() => of([]))
    );
  }

  getByCategorie(id: number): Observable<ProduitCard[]> {
    return this.http.get<ProduitDto[]>(`${this.baseUrl}/by-categorie/${id}`).pipe(
      switchMap(dtos => this.enrichirProduits(dtos)),
      catchError(() => of([]))
    );
  }

  getByFournisseur(id: number): Observable<ProduitCard[]> {
    return this.http.get<ProduitDto[]>(`${this.baseUrl}/by-fournisseur/${id}`).pipe(
      switchMap(dtos => this.enrichirProduits(dtos)),
      catchError(() => of([]))
    );
  }

  getByMarque(id: number): Observable<ProduitCard[]> {
    return this.http.get<ProduitDto[]>(`${this.baseUrl}/by-marque/${id}`).pipe(
      switchMap(dtos => this.enrichirProduits(dtos)),
      catchError(() => of([]))
    );
  }

  getByForme(id: number): Observable<ProduitCard[]> {
    return this.http.get<ProduitDto[]>(`${this.baseUrl}/by-forme/${id}`).pipe(
      switchMap(dtos => this.enrichirProduits(dtos)),
      catchError(() => of([]))
    );
  }

  getMeilleuresVentes(limit = 5): Observable<ProduitCard[]> {
    return this.http.get<ProduitDto[]>(`${this.baseUrl}/meilleures-ventes?limit=${limit}`).pipe(
      switchMap(dtos => this.enrichirProduits(dtos)),
      catchError(() => of([]))
    );
  }

  getNouveauxArrivages(limit = 10): Observable<ProduitCard[]> {
    return this.http.get<ProduitDto[]>(`${this.baseUrl}/nouveaux-arrivages?limit=${limit}`).pipe(
      switchMap(dtos => this.enrichirProduits(dtos)),
      catchError(() => of([]))
    );
  }

  getProduitsEnPromotion(): Observable<ProduitCard[]> {
    return this.http.get<ProduitDto[]>(`${this.baseUrl}/promotions`).pipe(
      switchMap(dtos => this.enrichirProduits(dtos)),
      catchError(() => of([]))
    );
  }

  getProduitsEnStock(): Observable<ProduitCard[]> {
    return this.http.get<ProduitDto[]>(`${this.baseUrl}/en-stock`).pipe(
      switchMap(dtos => this.enrichirProduits(dtos)),
      catchError(() => of([]))
    );
  }

  getImages(produitId: number): Observable<ImageProduitDto[]> {
    return this.http.get<ImageProduitDto[]>(`${this.baseUrl}/${produitId}/images`);
  }
}