Write-Host "Vérification des notifications pour l'admin (ID=1)..."
try {
    $notifications = Invoke-RestMethod -Uri 'http://localhost:5014/api/notification/user/1' -Method GET
    Write-Host "Notifications trouvées: $($notifications.Count)"
    if ($notifications.Count -gt 0) {
        $notifications | ForEach-Object { 
            Write-Host "- ID: $($_.id)"
            Write-Host "  Contenu: $($_.contenu)"
            Write-Host "  Date: $($_.dateEnvoi)"
            Write-Host "  Lue: $($_.estLue)"
            Write-Host ""
        }
    } else {
        Write-Host "Aucune notification trouvée"
    }
} catch {
    Write-Host "Erreur lors de la récupération des notifications: $($_.Exception.Message)"
    Write-Host "Détails: $($_.ErrorDetails.Message)"
}
