﻿namespace WebApiPfe.DTOs.Admin
{
    public class PromotionGestionDto
    {
        public int Id { get; set; }                          // Identifiant unique de la promotion
        public int ProduitId { get; set; }                   // Produit concerné
        public string Code { get; set; }                    // Titre de la promotion
        public string? Description { get; set; }             // Description optionnelle
        public decimal PourcentageReduction { get; set; }    // Pourcentage de réduction (ex : 10%)
        public DateTime DateDebut { get; set; }              // Début de validité
        public DateTime DateFin { get; set; }                // Fin de validité
        public bool EstActive { get; set; }
    }
}
