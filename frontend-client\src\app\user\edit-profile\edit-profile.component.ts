import { CommonModule } from '@angular/common';
import { Component, inject, OnInit, OnDestroy } from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { Router, RouterModule } from '@angular/router';
import { AuthService } from 'src/app/auth/auth.service';
import { AdresseDto } from 'src/app/models/AdresseDto';
import { ClientDto } from 'src/app/models/ClientDto';
import { ClientService } from 'src/app/services/client.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-edit-profile',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, MatIconModule],
  templateUrl: './edit-profile.component.html',
  styleUrls: ['./edit-profile.component.scss'],
})
export class EditProfileComponent implements OnInit, OnDestroy {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private clientService = inject(ClientService);
  public router = inject(Router);
  editNom = false;
  editPrenom = false;
  editEmail = false;
  editPhone = false;
  editNaissance = false;
  editAdresseLivraison = false;
  adresseLivraison: AdresseDto = {
    id: 0,
    rue: '',
    ville: '',
    codePostal: '',
    pays: 'Tunisie',
    estPrincipale: true,
  };
  editingAdresseLivraison = false;
  showOtherAddresses = false;
  editingAddresses = new Set<number>(); // Pour tracker quelles adresses sont en mode édition
  private subscription: Subscription = new Subscription();
  private isSubmitting = false;
  profileForm!: FormGroup; // Initialisé dans ngOnInit

  private initForm(): FormGroup {
    const currentUser = this.authService.getCurrentUser();

    const formGroup = this.fb.group({
      nom: ['', Validators.required],
      prenom: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [''],
      dateNaissance: [''],
      adresseLivraison: this.fb.group({
        id: [0],
        rue: [''],
        ville: [''],
        codePostal: [''],
        pays: ['Tunisie'],
        estPrincipale: [true],
      }),
      adresses: this.fb.array([]),
    });

    if (currentUser) {
      formGroup.patchValue({
        nom: currentUser.nom,
        prenom: currentUser.prenom,
        email: currentUser.email,
        phoneNumber: currentUser.phoneNumber,
        dateNaissance: this.formatDate(currentUser.dateNaissance),
      });

      // Mise à jour de l'adresse de livraison
      let adresseLivraisonData: AdresseDto | null = null;

      if (currentUser.adresseLivraison) {
        adresseLivraisonData = currentUser.adresseLivraison;
        console.log('🏠 Adresse de livraison du serveur:', currentUser.adresseLivraison);
        formGroup
          .get('adresseLivraison')
          ?.patchValue(currentUser.adresseLivraison);
      } else if (currentUser.adresses && currentUser.adresses.length > 0) {
        const mainAddress =
          currentUser.adresses.find((a) => a.estPrincipale) ||
          currentUser.adresses[0];
        adresseLivraisonData = mainAddress;
        formGroup.get('adresseLivraison')?.patchValue(mainAddress);
      }

      // Synchroniser la propriété du composant avec les données du formulaire
      if (adresseLivraisonData) {
        this.adresseLivraison = { ...adresseLivraisonData };
      } else {
        // Aucune adresse trouvée, initialiser avec des valeurs vides
        this.adresseLivraison = {
          id: 0,
          rue: '',
          ville: '',
          codePostal: '',
          pays: 'Tunisie',
          estPrincipale: true,
        };
        // S'assurer que le FormGroup a aussi des valeurs vides
        formGroup.get('adresseLivraison')?.patchValue(this.adresseLivraison);
      }

      const otherAddresses = (currentUser.adresses || []).filter(
        (a) =>
          !formGroup.get('adresseLivraison')?.value ||
          a.id !== formGroup.get('adresseLivraison')?.value.id
      );

      this.initAdresses(otherAddresses, formGroup.get('adresses') as FormArray);
    }
    return formGroup;
  }

  ngOnInit(): void {
    if (!this.authService.getCurrentUser()?.id) {
      this.router.navigate(['/login']);
      return;
    }

    // Initialiser le formulaire maintenant que toutes les propriétés sont définies
    this.profileForm = this.initForm();

    // S'assurer que les données complètes sont chargées
    this.authService.loadFullClientData();

    // S'abonner aux changements de l'utilisateur pour mettre à jour le formulaire
    // Mais seulement si on n'est pas en train de soumettre
    const userSubscription = this.authService.currentUser$.subscribe(user => {
      if (user && !this.isSubmitting) {
        console.log('🔄 Mise à jour du formulaire avec les nouvelles données utilisateur');
        this.updateFormWithUserData(user);
      }
    });

    this.subscription.add(userSubscription);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
  toggleEdit(field: string) {
    switch (field) {
      case 'nom':
        this.editNom = !this.editNom;
        break;
      case 'prenom':
        this.editPrenom = !this.editPrenom;
        break;
      case 'email':
        this.editEmail = !this.editEmail;
        break;
      case 'phone':
        this.editPhone = !this.editPhone;
        break;
      case 'naissance':
        this.editNaissance = !this.editNaissance;
        break;
      case 'adresseLivraison':
        this.editAdresseLivraison = !this.editAdresseLivraison;
        break;
    }
  }

  get adresses(): FormArray {
    return this.profileForm.get('adresses') as FormArray;
  }

  addAdresse(adresse?: AdresseDto, targetArray?: FormArray): void {
    const array = targetArray || this.adresses;
    const newIndex = array.length;

    array.push(
      this.fb.group({
        id: [adresse?.id || 0],
        rue: [adresse?.rue || '', Validators.required],
        ville: [adresse?.ville || '', Validators.required],
        codePostal: [adresse?.codePostal || '', Validators.required],
        pays: [adresse?.pays || 'Tunisie', Validators.required],
        estPrincipale: [adresse?.estPrincipale || false],
      })
    );

    // Si c'est une nouvelle adresse vide (pas de données existantes), la mettre en mode édition
    if (!adresse || !adresse.rue) {
      this.editingAddresses.add(newIndex);
    }
  }

  private initAdresses(
    adresses: AdresseDto[],
    adressesArray?: FormArray
  ): void {
    const targetArray = adressesArray || this.adresses;

    while (targetArray.length) {
      targetArray.removeAt(0);
    }

    adresses.forEach((adresse) => this.addAdresse(adresse, targetArray));

    if (adresses.length === 0) {
      this.addAdresse(undefined, targetArray);
    }
  }

  removeAdresse(index: number): void {
    this.adresses.removeAt(index);
  }

  setAsMain(index: number): void {
    // Mettre à jour les statuts dans le tableau des autres adresses
    // Une seule adresse peut être principale à la fois
    this.adresses.controls.forEach((control, i) => {
      control.get('estPrincipale')?.setValue(i === index);
    });

    console.log('✅ Adresse marquée comme principale (sera appliquée à la sauvegarde):',
                this.adresses.controls[index].value);
  }

  private formatDate(date: Date): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toISOString().split('T')[0];
  }
  toggleEditAdresseLivraison(): void {
    this.editingAdresseLivraison = !this.editingAdresseLivraison;
  }

  toggleShowOtherAddresses(): void {
    this.showOtherAddresses = !this.showOtherAddresses;
  }

  isEditingAddress(index: number): boolean {
    return this.editingAddresses.has(index);
  }

  toggleEditAddress(index: number): void {
    if (this.editingAddresses.has(index)) {
      this.editingAddresses.delete(index);
    } else {
      this.editingAddresses.add(index);
    }
  }

  isDeliveryAddress(addressId: number): boolean {
    const deliveryAddressId = this.profileForm.get('adresseLivraison')?.value?.id;
    return addressId && deliveryAddressId && addressId === deliveryAddressId;
  }
  onSubmit(): void {
    console.log('🚀 onSubmit appelé');
    console.log('Formulaire valide:', this.profileForm.valid);

    if (this.isSubmitting) {
      console.log('⏳ Soumission déjà en cours...');
      return;
    }

    if (!this.profileForm.valid) {
      console.error('❌ Formulaire invalide');
      console.log('Erreurs du formulaire:', this.profileForm.errors);
      return;
    }

    const currentUser = this.authService.getCurrentUser();
    console.log('👤 Utilisateur actuel:', currentUser);

    if (!currentUser) {
      console.error('❌ Utilisateur non connecté ou id manquant');
      alert('Erreur: Utilisateur non connecté. Veuillez vous reconnecter.');
      this.router.navigate(['/login']);
      return;
    }

    if (!currentUser.id) {
      console.error('❌ ID utilisateur manquant');
      alert('Erreur: ID utilisateur manquant. Veuillez vous reconnecter.');
      this.router.navigate(['/login']);
      return;
    }

    const formValue = this.profileForm.value;
    console.log('📝 Valeurs du formulaire:', formValue);

    this.isSubmitting = true;

    // 1. Mettre à jour les informations de base (sans les adresses et sans mot de passe)
    const profileData = {
      nom: formValue.nom,
      prenom: formValue.prenom,
      email: formValue.email,
      phoneNumber: formValue.phoneNumber,
      dateNaissance: new Date(formValue.dateNaissance)
    };

    console.log('🔄 Mise à jour du profil:', profileData);

    this.clientService.updateProfile(currentUser.id, profileData).subscribe({
      next: () => {
        console.log('✅ Informations de base mises à jour');

        // 2. Mettre à jour les adresses séparément
        this.updateAddresses(currentUser.id, formValue);
      },
      error: (err) => {
        console.error('❌ Erreur lors de la mise à jour des informations de base:', err);
        alert('Erreur lors de la mise à jour du profil. Veuillez réessayer.');
        this.isSubmitting = false;
      },
    });
  }

  private updateAddresses(userId: number, formValue: any): void {
    // Préparer la liste des adresses
    const adresses: AdresseDto[] = [];
    const adressesAjoutees = new Set<number>(); // Pour éviter les doublons

    // Chercher s'il y a une nouvelle adresse principale dans les autres adresses
    let nouvelleAdressePrincipale: AdresseDto | null = null;
    if (formValue.adresses && Array.isArray(formValue.adresses)) {
      nouvelleAdressePrincipale = formValue.adresses.find((adresse: AdresseDto) => adresse.estPrincipale);
    }

    console.log('🔍 Nouvelle adresse principale trouvée:', nouvelleAdressePrincipale);

    // Déterminer quelle adresse sera l'adresse de livraison
    let adresseLivraisonFinale: AdresseDto;

    if (nouvelleAdressePrincipale) {
      // Une nouvelle adresse a été sélectionnée comme principale
      adresseLivraisonFinale = {
        ...nouvelleAdressePrincipale,
        estPrincipale: true
      };
      console.log('✅ Changement d\'adresse de livraison vers:', adresseLivraisonFinale);
    } else {
      // Garder l'adresse de livraison actuelle
      adresseLivraisonFinale = {
        ...formValue.adresseLivraison,
        estPrincipale: true
      };
      console.log('✅ Conservation de l\'adresse de livraison actuelle:', adresseLivraisonFinale);
    }

    // Ajouter l'adresse de livraison finale
    if (adresseLivraisonFinale && adresseLivraisonFinale.rue) {
      // Récupérer le pays depuis l'adresse ou utiliser la valeur par défaut
      let pays = 'Tunisie'; // Valeur par défaut
      if (adresseLivraisonFinale.pays) {
        pays = adresseLivraisonFinale.pays;
      } else if (this.adresseLivraison && this.adresseLivraison.pays) {
        pays = this.adresseLivraison.pays;
      }

      const adresseLivraisonToAdd = {
        id: adresseLivraisonFinale.id || 0,
        rue: adresseLivraisonFinale.rue,
        ville: adresseLivraisonFinale.ville,
        codePostal: adresseLivraisonFinale.codePostal,
        pays: pays,
        estPrincipale: true
      };

      console.log('✅ Adresse de livraison à ajouter:', adresseLivraisonToAdd);
      adresses.push(adresseLivraisonToAdd);

      // Marquer cette adresse comme ajoutée pour éviter les doublons
      if (adresseLivraisonToAdd.id) {
        adressesAjoutees.add(adresseLivraisonToAdd.id);
      }
    }

    // Si l'adresse de livraison a changé, ajouter l'ancienne aux autres adresses
    if (nouvelleAdressePrincipale && formValue.adresseLivraison && formValue.adresseLivraison.id) {
      const ancienneAdresseLivraison = formValue.adresseLivraison;

      // Ajouter l'ancienne adresse de livraison si elle n'est pas déjà dans la liste
      if (!adressesAjoutees.has(ancienneAdresseLivraison.id)) {
        let pays = 'Tunisie';
        if (ancienneAdresseLivraison.pays) {
          pays = ancienneAdresseLivraison.pays;
        } else if (this.adresseLivraison && this.adresseLivraison.pays) {
          pays = this.adresseLivraison.pays;
        }

        const ancienneAdresseToAdd = {
          id: ancienneAdresseLivraison.id,
          rue: ancienneAdresseLivraison.rue,
          ville: ancienneAdresseLivraison.ville,
          codePostal: ancienneAdresseLivraison.codePostal,
          pays: pays,
          estPrincipale: false // L'ancienne adresse n'est plus principale
        };

        console.log('✅ Ancienne adresse de livraison ajoutée aux autres adresses:', ancienneAdresseToAdd);
        adresses.push(ancienneAdresseToAdd);
        adressesAjoutees.add(ancienneAdresseLivraison.id);
      }
    }

    // Ajouter les autres adresses (en évitant les doublons)
    if (formValue.adresses && Array.isArray(formValue.adresses)) {
      formValue.adresses.forEach((adresse: AdresseDto, index: number) => {
        console.log(`🔍 Adresse ${index} du formulaire:`, adresse);

        // Éviter les doublons : ne pas ajouter si l'adresse a déjà été ajoutée (sauf pour les nouvelles adresses avec id=0)
        if (adresse.rue && (adresse.id === 0 || (adresse.id && !adressesAjoutees.has(adresse.id)))) {
          // Récupérer le pays depuis l'adresse existante ou utiliser la valeur par défaut
          let pays = 'Tunisie'; // Valeur par défaut
          if (adresse.pays) {
            pays = adresse.pays;
          }

          // Toutes les autres adresses sont non-principales (la principale est déjà ajoutée)
          const adresseToAdd = {
            id: adresse.id || 0, // Préserver l'ID existant ou créer une nouvelle adresse
            rue: adresse.rue,
            ville: adresse.ville,
            codePostal: adresse.codePostal,
            pays: pays,
            estPrincipale: false // Seule l'adresse de livraison est principale
          };
          console.log(`✅ Adresse ${index} ajoutée (non-principale):`, adresseToAdd);
          adresses.push(adresseToAdd);
          // Ajouter au Set seulement si ce n'est pas une nouvelle adresse
          if (adresse.id && adresse.id !== 0) {
            adressesAjoutees.add(adresse.id);
          }
        } else if (adresse.id && adressesAjoutees.has(adresse.id)) {
          console.log(`⚠️ Adresse ${index} ignorée (doublon avec ID ${adresse.id})`);
        }
      });
    }

    console.log('🏠 Mise à jour des adresses:', adresses);
    console.log('🏠 Détail des adresses à envoyer:');
    adresses.forEach((addr, index) => {
      console.log(`  Adresse ${index}:`, {
        id: addr.id,
        rue: addr.rue,
        ville: addr.ville,
        codePostal: addr.codePostal,
        pays: addr.pays,
        estPrincipale: addr.estPrincipale
      });
    });

    if (adresses.length > 0) {
      console.log('📤 Données JSON envoyées au backend:', JSON.stringify(adresses, null, 2));
      this.clientService.updateAdresses(userId, adresses).subscribe({
        next: () => {
          console.log('✅ Adresses mises à jour');
          this.finishUpdate(userId);
        },
        error: (err) => {
          console.error('❌ Erreur lors de la mise à jour des adresses:', err);
          alert('Erreur lors de la mise à jour des adresses. Veuillez réessayer.');
          this.isSubmitting = false;
        },
      });
    } else {
      // Pas d'adresses à mettre à jour
      this.finishUpdate(userId);
    }
  }

  private finishUpdate(userId: number): void {
    // Recharger les données complètes de l'utilisateur
    this.clientService.getById(userId).subscribe({
      next: (updatedUser) => {
        console.log('✅ Données utilisateur rechargées:', updatedUser);
        this.authService.updateCurrentUser(updatedUser);
        this.isSubmitting = false;
        this.router.navigate(['/user/profile']);
      },
      error: (err) => {
        console.error('❌ Erreur lors du rechargement des données:', err);
        // Même en cas d'erreur de rechargement, on considère la mise à jour comme réussie
        this.isSubmitting = false;
        this.router.navigate(['/user/profile']);
      },
    });
  }


  private updateFormWithUserData(user: ClientDto): void {
    console.log('🔄 updateFormWithUserData appelé avec:', user);

    // Mettre à jour les champs du formulaire
    this.profileForm.patchValue({
      nom: user.nom,
      prenom: user.prenom,
      email: user.email,
      phoneNumber: user.phoneNumber,
      dateNaissance: this.formatDate(user.dateNaissance),
    });

    console.log('📝 Formulaire mis à jour avec les données de base');
    console.log('PhoneNumber dans user:', user.phoneNumber);
    console.log('PhoneNumber dans formulaire:', this.profileForm.get('phoneNumber')?.value);

    // Mettre à jour l'adresse de livraison
    let adresseLivraisonData: AdresseDto | null = null;

    console.log('🏠 Adresse de livraison dans user:', user.adresseLivraison);
    console.log('🏠 Adresses dans user:', user.adresses);

    if (user.adresseLivraison) {
      adresseLivraisonData = user.adresseLivraison;
      this.profileForm.get('adresseLivraison')?.patchValue(user.adresseLivraison);
      console.log('✅ Adresse de livraison mise à jour depuis adresseLivraison');
    } else if (user.adresses && user.adresses.length > 0) {
      const mainAddress = user.adresses.find((a) => a.estPrincipale) || user.adresses[0];
      adresseLivraisonData = mainAddress;
      this.profileForm.get('adresseLivraison')?.patchValue(mainAddress);
      console.log('✅ Adresse de livraison mise à jour depuis adresses:', mainAddress);
    }

    // Synchroniser la propriété du composant avec les données du formulaire
    if (adresseLivraisonData) {
      this.adresseLivraison = { ...adresseLivraisonData };
      console.log('✅ Propriété adresseLivraison du composant mise à jour:', this.adresseLivraison);
    } else {
      // Réinitialiser l'adresse si aucune adresse n'est trouvée
      this.adresseLivraison = {
        id: 0,
        rue: '',
        ville: '',
        codePostal: '',
        pays: 'Tunisie',
        estPrincipale: true,
      };
      // S'assurer que le FormGroup a aussi des valeurs vides
      this.profileForm.get('adresseLivraison')?.patchValue(this.adresseLivraison);
      console.log('⚠️ Aucune adresse trouvée, réinitialisation');
    }

    // Mettre à jour les autres adresses
    const otherAddresses = (user.adresses || []).filter(
      (a) =>
        !this.profileForm.get('adresseLivraison')?.value ||
        a.id !== this.profileForm.get('adresseLivraison')?.value.id
    );

    // Réinitialiser le FormArray des adresses
    const adressesFormArray = this.profileForm.get('adresses') as FormArray;
    adressesFormArray.clear();
    this.initAdresses(otherAddresses, adressesFormArray);
  }
}
