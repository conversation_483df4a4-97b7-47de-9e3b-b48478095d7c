import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { FormeDto } from 'src/app/models/FormeDto';
import { MarqueDto } from 'src/app/models/MarqueDto';
import { CategorieService } from 'src/app/services/categorie.service';
interface FilterOptions {
  sort?: string | null;
  sexe?: string[];
  choixPopulaire?: string | null;
  marques?: number[];
  formes?: number[];
  couleurs?: string[];
  prixMin?: number | null;
  prixMax?: number | null;
}
@Component({
  selector: 'app-filter',
  standalone: true,
  imports: [CommonModule, FormsModule],
  providers: [CategorieService],
  templateUrl: './filter.component.html',
  styleUrl: './filter.component.scss',
})
export class FilterComponent {
  @Input() marques: MarqueDto[] = [];
  @Input() formes: FormeDto[] = [];
  @Input() couleurs: string[] = [];
  @Input() prixMin: number = 0;
  @Input() prixMax: number = 0;
  @Input() categorieId!: number | null;
  @Input() sousCategorieId!: number | null;

  sortOptions = [
    { label: 'Prix croissant', value: 'priceAsc' },
    { label: 'Prix décroissant', value: 'priceDesc' },
    { label: 'Nom A à Z', value: 'nameAsc' },
    { label: 'Remise décroissant', value: 'discountDesc' },
    { label: 'Le plus récent', value: 'recent' },
  ];
  selectedSort: string | null = null;
  selectedSexe: Set<string> = new Set();
  choixPopulaires: string | null = null;
  selectedMarques: Set<number> = new Set();
  selectedFormes: Set<number> = new Set();
  selectedCouleurs: Set<string> = new Set();
  filterTextMarque: string = '';
  @Output() filterChanged = new EventEmitter<FilterOptions>();
  @Output() clearFilters = new EventEmitter<void>();
  @Output() applyFilters = new EventEmitter<void>();

  constructor(private CategoriesService: CategorieService) {}
  toggleSelection(set: Set<any>, value: any): void {
    if (set.has(value)) {
      set.delete(value);
    } else {
      set.add(value);
    }
    this.emitChanges();
  }
  loadCategories() {
    this.CategoriesService.getAll().subscribe((dat: any) => {});
  }
  onChoixPopulaireChange(choix: string | null): void {
    this.choixPopulaires = choix;
    this.emitChanges();
  }

  get marquesFiltrees(): MarqueDto[] {
    return this.marques
      .filter((m) =>
        m.name.toLowerCase().includes(this.filterTextMarque.toLowerCase())
      )
      .slice(0, 10);
  }
  toggleMarque(marqueId: number) {
    this.toggleSelection(this.selectedMarques, marqueId);
  }
  toggleForme(formeId: number): void {
    this.toggleSelection(this.selectedFormes, formeId);
  }
  toggleCouleur(couleur: string): void {
    this.toggleSelection(this.selectedCouleurs, couleur);
  }
  toggleSexe(sexe: string): void {
    this.toggleSelection(this.selectedSexe, sexe);
  }
  onSortChange(value: string | null): void {
    this.selectedSort = value;
    this.emitChanges();
  }

  onPrixMinChange(value: number | 0): void {
    this.prixMin = value;
    this.emitChanges();
  }

  onPrixMaxChange(value: number | 0): void {
    this.prixMax = value;
    this.emitChanges();
  }
  emitChanges() {
    this.filterChanged.emit({
      sort: this.selectedSort,
      sexe: Array.from(this.selectedSexe),
      choixPopulaire: this.choixPopulaires,
      marques: Array.from(this.selectedMarques),
      formes: Array.from(this.selectedFormes),
      couleurs: Array.from(this.selectedCouleurs),
      prixMin: this.prixMin,
      prixMax: this.prixMax,
    });
  }
  onAppliquerClicked() {
    this.applyFilters.emit();
  }
  resetFilters() {
    this.selectedSort = null;
    this.selectedSexe.clear();
    this.choixPopulaires = null;
    this.selectedMarques.clear();
    this.selectedFormes.clear();
    this.selectedCouleurs.clear();
    this.prixMin = 0;
    this.prixMax = 0;
    this.clearFilters.emit();
  }
}
