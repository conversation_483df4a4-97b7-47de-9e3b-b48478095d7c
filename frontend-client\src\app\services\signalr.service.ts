import { Injectable } from '@angular/core';
import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr';
import { BehaviorSubject, Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { AuthService } from '../auth/auth.service';
import { NotificationService, NotificationDto } from './notification.service';

@Injectable({
  providedIn: 'root'
})
export class SignalRService {
  private hubConnection: HubConnection | null = null;
  private connectionStateSubject = new BehaviorSubject<boolean>(false);
  public connectionState$ = this.connectionStateSubject.asObservable();

  constructor(
    private authService: AuthService,
    private notificationService: NotificationService
  ) {}

  public async startConnection(): Promise<void> {
    if (this.hubConnection?.state === 'Connected') {
      return;
    }

    try {
      this.hubConnection = new HubConnectionBuilder()
        .withUrl(`${environment.apiUrl}/notificationHub`, {
          accessTokenFactory: () => {
            const token = this.authService.getToken();
            return token || '';
          }
        })
        .withAutomaticReconnect()
        .configureLogging(LogLevel.Information)
        .build();

      // Gestion des événements de connexion
      this.hubConnection.onreconnecting(() => {
        console.log('SignalR: Tentative de reconnexion...');
        this.connectionStateSubject.next(false);
      });

      this.hubConnection.onreconnected(() => {
        console.log('SignalR: Reconnecté avec succès');
        this.connectionStateSubject.next(true);
        this.joinUserGroup();
      });

      this.hubConnection.onclose(() => {
        console.log('SignalR: Connexion fermée');
        this.connectionStateSubject.next(false);
      });

      // Écouter les notifications
      this.setupNotificationListeners();

      await this.hubConnection.start();
      console.log('SignalR: Connexion établie');
      this.connectionStateSubject.next(true);
      
      // Rejoindre le groupe de l'utilisateur
      await this.joinUserGroup();

    } catch (error) {
      console.error('SignalR: Erreur de connexion', error);
      this.connectionStateSubject.next(false);
    }
  }

  public async stopConnection(): Promise<void> {
    if (this.hubConnection) {
      await this.hubConnection.stop();
      this.hubConnection = null;
      this.connectionStateSubject.next(false);
      console.log('SignalR: Connexion fermée');
    }
  }

  private async joinUserGroup(): Promise<void> {
    const currentUser = this.authService.getCurrentUser();
    if (this.hubConnection && currentUser?.id) {
      try {
        await this.hubConnection.invoke('JoinUserGroup', currentUser.id.toString());
        console.log(`SignalR: Rejoint le groupe utilisateur ${currentUser.id}`);
      } catch (error) {
        console.error('SignalR: Erreur lors de la jointure du groupe', error);
      }
    }
  }

  private setupNotificationListeners(): void {
    if (!this.hubConnection) return;

    // Écouter les nouvelles notifications
    this.hubConnection.on('ReceiveNotification', (notification: NotificationDto) => {
      console.log('SignalR: Nouvelle notification reçue', notification);
      
      // Mettre à jour le service de notifications
      const currentUser = this.authService.getCurrentUser();
      if (currentUser?.id) {
        this.notificationService.getUserNotifications(currentUser.id).subscribe();
      }

      // Afficher une notification toast (optionnel)
      this.showNotificationToast(notification);
    });

    // Écouter les mises à jour de notifications
    this.hubConnection.on('NotificationUpdated', (notificationId: number) => {
      console.log('SignalR: Notification mise à jour', notificationId);
      
      // Recharger les notifications
      const currentUser = this.authService.getCurrentUser();
      if (currentUser?.id) {
        this.notificationService.getUserNotifications(currentUser.id).subscribe();
      }
    });

    // Écouter les suppressions de notifications
    this.hubConnection.on('NotificationDeleted', (notificationId: number) => {
      console.log('SignalR: Notification supprimée', notificationId);
      
      // Recharger les notifications
      const currentUser = this.authService.getCurrentUser();
      if (currentUser?.id) {
        this.notificationService.getUserNotifications(currentUser.id).subscribe();
      }
    });
  }

  private showNotificationToast(notification: NotificationDto): void {
    // Vérifier si les notifications du navigateur sont supportées
    if ('Notification' in window) {
      // Demander la permission si nécessaire
      if (Notification.permission === 'default') {
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            this.displayBrowserNotification(notification);
          }
        });
      } else if (Notification.permission === 'granted') {
        this.displayBrowserNotification(notification);
      }
    }
  }

  private displayBrowserNotification(notification: NotificationDto): void {
    const browserNotification = new Notification('Nouvelle notification', {
      body: notification.contenu,
      icon: '/assets/icons/notification-icon.png',
      badge: '/assets/icons/notification-badge.png',
      tag: `notification-${notification.id}`,
      requireInteraction: false,
      silent: false
    });

    // Fermer automatiquement après 5 secondes
    setTimeout(() => {
      browserNotification.close();
    }, 5000);

    // Gérer le clic sur la notification
    browserNotification.onclick = () => {
      window.focus();
      // Optionnel: naviguer vers la page des notifications
      // this.router.navigate(['/user/notifications']);
      browserNotification.close();
    };
  }

  public async sendNotificationToUser(userId: number, content: string): Promise<void> {
    if (this.hubConnection?.state === 'Connected') {
      try {
        await this.hubConnection.invoke('SendNotificationToUser', userId, content);
        console.log('SignalR: Notification envoyée à l\'utilisateur', userId);
      } catch (error) {
        console.error('SignalR: Erreur lors de l\'envoi de notification', error);
      }
    }
  }

  public async sendNotificationToGroup(groupName: string, content: string): Promise<void> {
    if (this.hubConnection?.state === 'Connected') {
      try {
        await this.hubConnection.invoke('SendNotificationToGroup', groupName, content);
        console.log('SignalR: Notification envoyée au groupe', groupName);
      } catch (error) {
        console.error('SignalR: Erreur lors de l\'envoi de notification au groupe', error);
      }
    }
  }

  public isConnected(): boolean {
    return this.hubConnection?.state === 'Connected';
  }

  public getConnectionId(): string | null {
    return this.hubConnection?.connectionId || null;
  }
}
