<div class="admin-notifications-container">
  <div class="header">
    <h1>🔔 Notifications & Validation des Fournisseurs</h1>
    <p><PERSON><PERSON><PERSON> les demandes d'inscription des fournisseurs et les notifications système</p>
  </div>

  <!-- Section des notifications -->
  <div class="notifications-section">
    <h2>📧 Notifications récentes</h2>
    
    <div *ngIf="isLoading" class="loading">
      <div class="spinner"></div>
      <p>Chargement des notifications...</p>
    </div>

    <div *ngIf="error" class="error-message">
      <i class="icon-alert"></i>
      <p>{{ error }}</p>
      <button (click)="loadNotifications()" class="retry-btn">Réessayer</button>
    </div>

    <div *ngIf="!isLoading && !error" class="notifications-list">
      <div *ngIf="notifications.length === 0" class="no-notifications">
        <i class="icon-bell-off"></i>
        <h3>Aucune notification</h3>
        <p>Toutes les notifications ont été traitées</p>
      </div>

      <div *ngFor="let notification of notifications" 
           class="notification-card"
           [class.unread]="!notification.estLue">
        
        <div class="notification-header">
          <span class="notification-date">{{ formatDate(notification.dateEnvoi) }}</span>
          <div class="notification-actions">
            <button *ngIf="!notification.estLue" 
                    (click)="markAsRead(notification)"
                    class="mark-read-btn"
                    title="Marquer comme lu">
              <i class="icon-check"></i>
            </button>
            <button (click)="deleteNotification(notification.id)"
                    class="delete-btn"
                    title="Supprimer">
              <i class="icon-trash"></i>
            </button>
          </div>
        </div>

        <div class="notification-content">
          <p>{{ notification.contenu }}</p>
        </div>

        <div class="notification-footer">
          <button (click)="viewFournisseurFromNotification(notification)"
                  class="view-details-btn">
            <i class="icon-eye"></i>
            Voir les détails
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Section des fournisseurs en attente -->
  <div class="pending-suppliers-section">
    <h2>⏳ Fournisseurs en attente de validation</h2>
    
    <div *ngIf="fournisseursEnAttente.length === 0" class="no-pending">
      <i class="icon-check-circle"></i>
      <h3>Aucun fournisseur en attente</h3>
      <p>Toutes les demandes ont été traitées</p>
    </div>

    <div class="suppliers-grid">
      <div *ngFor="let fournisseur of fournisseursEnAttente" 
           class="supplier-card">
        
        <div class="supplier-header">
          <div class="supplier-logo">
            <img *ngIf="fournisseur.logoFile" 
                 [src]="fournisseur.logoFile" 
                 [alt]="fournisseur.raisonSociale">
            <div *ngIf="!fournisseur.logoFile" class="default-logo">
              <i class="icon-building"></i>
            </div>
          </div>
          <div class="supplier-info">
            <h3>{{ fournisseur.raisonSociale }}</h3>
            <p>{{ fournisseur.nom }} {{ fournisseur.prenom }}</p>
            <span class="supplier-email">{{ fournisseur.email }}</span>
          </div>
        </div>

        <div class="supplier-details">
          <div class="detail-item">
            <span class="label">Matricule fiscal:</span>
            <span class="value">{{ fournisseur.matriculeFiscale }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Date d'inscription:</span>
            <span class="value">{{ formatDate(fournisseur.dateInscription) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Commission:</span>
            <span class="value">{{ fournisseur.commission }}%</span>
          </div>
        </div>

        <div class="supplier-actions">
          <button (click)="viewFournisseurDetails(fournisseur.id)"
                  class="view-btn">
            <i class="icon-eye"></i>
            Voir détails
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal de détails du fournisseur -->
<div *ngIf="selectedFournisseur" class="modal-overlay" (click)="closeDetails()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>📋 Détails du fournisseur</h2>
      <button (click)="closeDetails()" class="close-btn">
        <i class="icon-x"></i>
      </button>
    </div>

    <div class="modal-body">
      <div class="supplier-full-details">
        <!-- Informations générales -->
        <div class="details-section">
          <h3>👤 Informations générales</h3>
          <div class="details-grid">
            <div class="detail-row">
              <span class="label">Raison sociale:</span>
              <span class="value">{{ selectedFournisseur.raisonSociale }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Contact:</span>
              <span class="value">{{ selectedFournisseur.nom }} {{ selectedFournisseur.prenom }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Email:</span>
              <span class="value">{{ selectedFournisseur.email }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Téléphone:</span>
              <span class="value">{{ selectedFournisseur.phoneNumber }}</span>
            </div>
          </div>
        </div>

        <!-- Informations commerciales -->
        <div class="details-section">
          <h3>💼 Informations commerciales</h3>
          <div class="details-grid">
            <div class="detail-row">
              <span class="label">Matricule fiscal:</span>
              <span class="value">{{ selectedFournisseur.matriculeFiscale }}</span>
            </div>
            <div class="detail-row">
              <span class="label">RIB:</span>
              <span class="value">{{ selectedFournisseur.rib }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Code banque:</span>
              <span class="value">{{ selectedFournisseur.codeBanque }}</span>
            </div>
            <div class="detail-row">
              <span class="label">Commission:</span>
              <span class="value">{{ selectedFournisseur.commission }}%</span>
            </div>
            <div class="detail-row">
              <span class="label">Délai préparation:</span>
              <span class="value">{{ selectedFournisseur.delaiPreparationJours }} jours</span>
            </div>
            <div class="detail-row">
              <span class="label">Frais livraison:</span>
              <span class="value">{{ selectedFournisseur.fraisLivraisonBase }} TND</span>
            </div>
          </div>
        </div>

        <!-- Adresses -->
        <div class="details-section">
          <h3>📍 Adresses</h3>
          <div *ngFor="let adresse of selectedFournisseur.adresses" class="address-card">
            <div class="address-header">
              <span *ngIf="adresse.estPrincipale" class="principal-badge">Principale</span>
            </div>
            <p>{{ adresse.rue }}</p>
            <p>{{ adresse.codePostal }} {{ adresse.ville }}</p>
            <p>{{ adresse.pays }}</p>
          </div>
        </div>

        <!-- Description -->
        <div *ngIf="selectedFournisseur.description" class="details-section">
          <h3>📝 Description</h3>
          <p>{{ selectedFournisseur.description }}</p>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button (click)="openValidationModal('reject')" class="reject-btn">
        <i class="icon-x"></i>
        Rejeter
      </button>
      <button (click)="openValidationModal('accept')" class="approve-btn">
        <i class="icon-check"></i>
        Valider
      </button>
    </div>
  </div>
</div>

<!-- Modal de validation -->
<div *ngIf="showValidationModal" class="modal-overlay" (click)="closeValidationModal()">
  <div class="modal-content validation-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>
        <i [class]="validationAction === 'accept' ? 'icon-check' : 'icon-x'"></i>
        {{ validationAction === 'accept' ? 'Valider' : 'Rejeter' }} le fournisseur
      </h2>
      <button (click)="closeValidationModal()" class="close-btn">
        <i class="icon-x"></i>
      </button>
    </div>

    <div class="modal-body">
      <p>
        Êtes-vous sûr de vouloir 
        <strong>{{ validationAction === 'accept' ? 'valider' : 'rejeter' }}</strong>
        le fournisseur <strong>{{ selectedFournisseur?.raisonSociale }}</strong> ?
      </p>

      <div class="form-group">
        <label for="validationComment">
          Commentaire {{ validationAction === 'reject' ? '(obligatoire)' : '(optionnel)' }}:
        </label>
        <textarea 
          id="validationComment"
          [(ngModel)]="validationComment"
          placeholder="Ajoutez un commentaire..."
          rows="4">
        </textarea>
      </div>
    </div>

    <div class="modal-footer">
      <button (click)="closeValidationModal()" class="cancel-btn">
        Annuler
      </button>
      <button (click)="confirmValidation()" 
              [class]="validationAction === 'accept' ? 'approve-btn' : 'reject-btn'"
              [disabled]="validationAction === 'reject' && !validationComment.trim()">
        <i [class]="validationAction === 'accept' ? 'icon-check' : 'icon-x'"></i>
        {{ validationAction === 'accept' ? 'Valider' : 'Rejeter' }}
      </button>
    </div>
  </div>
</div>
