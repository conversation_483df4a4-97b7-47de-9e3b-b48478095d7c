import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ProductListComponent } from 'src/app/pages/product-list/product-list.component';
import { ProduitService } from 'src/app/services/produit.service';
import { CategorieService } from 'src/app/services/categorie.service';
import { CategorieDto } from 'src/app/models/CategorieDto';
import { ProduitCard } from 'src/app/models/ProduitCard';
import { catchError, finalize, forkJoin, of } from 'rxjs';
import { MarqueDto } from 'src/app/models/MarqueDto';
import { FormeDto } from 'src/app/models/FormeDto';

@Component({
  selector: 'app-products-by-category',
  standalone: true,
  imports: [ProductListComponent, CommonModule],
  providers: [ProduitService, CategorieService],
  template: `
    <div class="nom-header">
      <h1>{{ categorie?.nom }}</h1>
      <p *ngIf="error" class="error-message">{{ error }}</p>
    </div>  
    <app-product-list 
      [produits]="produits"
      [marques]="marques"
      [formes]="formes"
      [loading]="loading">
    </app-product-list>
  `,
  styleUrl: './products-by-category.component.scss',
})
export class ProductsByCategoryComponent implements OnInit {
  categoryId!: number;
  categorie?: CategorieDto;
  marques: MarqueDto[] = [];
  formes: FormeDto[] = [];
  produits: ProduitCard[] = [];
  loading = true;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private produitService: ProduitService,
    private categorieService: CategorieService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.categoryId = +params['id'];
      this.chargerDonnees();
    });
  }

  chargerDonnees(): void {
    this.loading = true;
    this.error = null;
    
    forkJoin([
      this.produitService.getByCategorie(this.categoryId).pipe(catchError(() => of([]))),
      this.categorieService.getById(this.categoryId).pipe(catchError(() => of(undefined))),
    ]).pipe(
      finalize(() => this.loading = false)
    ).subscribe({
      next: ([produits, categorie]) => {
        this.produits = produits;
        this.categorie = categorie;
      },
      error: () => this.error = 'Erreur de chargement'
    });
  }
}
