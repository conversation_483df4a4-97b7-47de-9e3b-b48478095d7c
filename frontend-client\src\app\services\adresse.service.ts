import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AdresseDto } from '../models/AdresseDto';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AdresseService {
  private apiUrl = `${environment.apiUrl}/Adresses`; 

  constructor(private http: HttpClient) {}

  // GET: /api/Adresses/{id}
  getAdresse(id: number): Observable<AdresseDto> {
    return this.http.get<AdresseDto>(`${this.apiUrl}/${id}`);
  }

  // GET: /api/Adresses/entity/{entityId}
  getAdressesByEntity(entityId: number): Observable<AdresseDto[]> {
    return this.http.get<AdresseDto[]>(`${this.apiUrl}/entity/${entityId}`);
  }

  // POST: /api/Adresses
  createAdresse(adresse: Omit<AdresseDto, 'id'>): Observable<AdresseDto> {
    return this.http.post<AdresseDto>(this.apiUrl, adresse);
  }

  // PUT: /api/Adresses/{id}
  updateAdresse(id: number, adresse: Omit<AdresseDto, 'id'>): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, adresse);
  }

  // PATCH: /api/Adresses/{id}/principale/{entityId}
  setPrincipale(id: number, entityId: number): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${id}/principale/${entityId}`, {});
  }

  // DELETE: /api/Adresses/{id}
  deleteAdresse(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
