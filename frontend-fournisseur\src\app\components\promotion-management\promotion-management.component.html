<div class="promotion-management">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1>🎯 Gestion des Promotions</h1>
      <p>C<PERSON>ez et gérez vos promotions pour booster vos ventes</p>
    </div>

    <div class="header-actions">
      <button
        class="btn btn-primary"
        (click)="showCreateForm = !showCreateForm"
        [disabled]="loading">
        <span *ngIf="!showCreateForm">✨ Nouvelle Promotion</span>
        <span *ngIf="showCreateForm">❌ Annuler</span>
      </button>

      <button
        class="btn btn-danger"
        (click)="deleteAllPromotions()"
        *ngIf="promotions.length > 0 && !showCreateForm"
        [disabled]="loading"
        style="margin-left: 10px;">
        🗑️ Supprimer tout
      </button>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="stats-section">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">🎯</div>
        <div class="stat-content">
          <div class="stat-value">{{ promotions.length }}</div>
          <div class="stat-label">Total promotions</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <div class="stat-value">{{ getActivePromotionsCount() }}</div>
          <div class="stat-label">Promotions actives</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">⏰</div>
        <div class="stat-content">
          <div class="stat-value">{{ getPendingPromotionsCount() }}</div>
          <div class="stat-label">À venir</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Formulaire de création -->
  <div class="create-form" *ngIf="showCreateForm">
    <div class="form-card">
      <h2>{{ editingPromotion ? '✏️ Modifier la promotion' : '✨ Créer une nouvelle promotion' }}</h2>
      
      <div class="form-row">
        <div class="form-group">
          <label>Type de promotion *</label>
          <select 
            [(ngModel)]="formData.type" 
            (ngModelChange)="onTypeChange()"
            class="form-control">
            <option value="CodePromo">🎫 Code Promo</option>
            <option value="PromotionAutomatique">⚡ Promotion Automatique</option>
          </select>
          <small class="form-text">
            <span *ngIf="formData.type === 'CodePromo'">
              💡 Les clients devront saisir un code pour bénéficier de la remise
            </span>
            <span *ngIf="formData.type === 'PromotionAutomatique'">
              💡 La remise sera appliquée automatiquement aux produits éligibles
            </span>
          </small>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Nom d'affichage *</label>
          <input
            type="text"
            [(ngModel)]="formData.nomAffichage"
            class="form-control"
            placeholder="Ex: Soldes d'été, Promotion lunettes..."
            maxlength="100">
        </div>

        <div class="form-group">
          <label>Nom de la promotion</label>
          <input
            type="text"
            [(ngModel)]="formData.nomPromotion"
            class="form-control"
            placeholder="Ex: SUMMER, WINTER, BLACK_FRIDAY..."
            maxlength="50"
            style="text-transform: uppercase;">
          <small class="form-text">
            💡 Nom court pour identifier facilement la promotion (optionnel)
          </small>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Pourcentage de remise (%) *</label>
          <input
            type="number"
            [(ngModel)]="formData.pourcentageRemise"
            class="form-control"
            min="1"
            max="100"
            step="1"
            placeholder="10">
        </div>
      </div>

      <div class="form-row" *ngIf="formData.type === 'CodePromo'">
        <div class="form-group">
          <label>Code Promo *</label>
          <div class="code-input-group">
            <input 
              type="text" 
              [(ngModel)]="formData.codePromo"
              class="form-control code-input"
              placeholder="PROMO123"
              maxlength="20">
            <button 
              type="button" 
              class="btn btn-secondary"
              (click)="formData.codePromo = generatePromoCode()">
              🎲 Générer
            </button>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Date de début *</label>
          <input 
            type="date" 
            [(ngModel)]="formData.dateDebut"
            class="form-control">
        </div>
        
        <div class="form-group">
          <label>Date de fin *</label>
          <input 
            type="date" 
            [(ngModel)]="formData.dateFin"
            class="form-control">
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label>Description (optionnelle)</label>
          <textarea 
            [(ngModel)]="formData.description"
            class="form-control"
            rows="3"
            placeholder="Description de la promotion..."
            maxlength="500"></textarea>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              [(ngModel)]="formData.appliquerSurHT">
            <span class="checkmark"></span>
            Appliquer sur le prix HT (sinon sur TTC)
          </label>
        </div>
      </div>

      <div class="error-message" *ngIf="error">
        ⚠️ {{ error }}
      </div>

      <!-- Messages de succès -->
      <div class="success-message" *ngIf="successMessage">
        ✅ {{ successMessage }}
      </div>

      <div class="form-actions">
        <button
          type="button"
          class="btn btn-secondary"
          (click)="cancelEdit()">
          Annuler
        </button>
        <button
          type="button"
          class="btn btn-primary"
          (click)="savePromotion()"
          [disabled]="saving">
          {{ saving ? '⏳ Sauvegarde...' : (editingPromotion ? '💾 Modifier' : '✅ Créer') }}
        </button>

        <!-- Bouton de test temporaire -->
        <button
          type="button"
          class="btn btn-secondary"
          (click)="testUpdate()"
          *ngIf="editingPromotion"
          style="margin-left: 10px;">
          🧪 Test API
        </button>
      </div>
    </div>
  </div>

  <!-- Liste des promotions -->
  <div class="promotions-list">
    <div class="loading" *ngIf="loading && !showCreateForm">
      ⏳ Chargement des promotions...
    </div>

    <div class="error-message" *ngIf="error && !showCreateForm">
      ⚠️ {{ error }}
    </div>

    <div class="empty-state" *ngIf="!loading && promotions.length === 0 && !error">
      <div class="empty-icon">🎯</div>
      <h3>Aucune promotion créée</h3>
      <p>Créez votre première promotion pour attirer plus de clients !</p>
      <button 
        class="btn btn-primary"
        (click)="showCreateForm = true">
        ➕ Créer ma première promotion
      </button>
    </div>

    <!-- Table des promotions -->
    <div class="promotion-table" *ngIf="promotions.length > 0">
      <!-- En-tête du tableau -->
      <div class="table-header">
        <div>Promotion</div>
        <div>Type</div>
        <div>Remise</div>
        <div>Code</div>
        <div>Statut</div>
        <div>Période</div>
        <div>Actions</div>
      </div>

      <!-- Lignes du tableau -->
      <div class="table-row" *ngFor="let promotion of promotions">
        <!-- Informations de la promotion -->
        <div class="promotion-info">
          <div class="promotion-name">{{ promotion.nomAffichage }}</div>
          <div class="promotion-code-name">
            <span class="promo-tag" *ngIf="promotion.nomPromotion">{{ promotion.nomPromotion }}</span>
          </div>
          <div class="promotion-description" *ngIf="promotion.description">
            {{ promotion.description }}
          </div>
        </div>

        <!-- Type -->
        <div>
          <span class="type-badge" [class]="'type-' + getTypeClass(promotion.type)">
            {{ getTypeText(promotion.type) }}
          </span>
        </div>

        <!-- Remise -->
        <div>
          <span class="discount-value">-{{ promotion.pourcentageRemise }}%</span>
        </div>

        <!-- Code promo -->
        <div>
          <span class="code-promo" *ngIf="promotion.codePromo">
            {{ promotion.codePromo }}
          </span>
          <span *ngIf="!promotion.codePromo" class="text-muted">-</span>
        </div>

        <!-- Statut -->
        <div>
          <span class="status-badge" [class]="getStatusClass(promotion)">
            {{ getStatusText(promotion) }}
          </span>
        </div>

        <!-- Période -->
        <div>
          <div class="date-range">
            {{ formatDate(promotion.dateDebut) }} - {{ formatDate(promotion.dateFin) }}
          </div>
        </div>

        <!-- Actions -->
        <div class="actions-cell">
          <button
            class="btn btn-sm btn-primary"
            (click)="editPromotion(promotion)"
            title="Modifier cette promotion"
            style="min-width: 80px;">
            ✏️ Modifier
          </button>
          <button
            class="btn btn-sm"
            [class]="promotion.estValide ? 'btn-warning' : 'btn-success'"
            (click)="togglePromotion(promotion)"
            title="{{ promotion.estValide ? 'Désactiver' : 'Activer' }}"
            style="min-width: 80px;">
            {{ promotion.estValide ? '⏸️ Désactiver' : '▶️ Activer' }}
          </button>
          <button
            class="btn btn-sm btn-danger"
            (click)="deletePromotion(promotion)"
            title="Supprimer cette promotion"
            style="min-width: 80px;">
            🗑️ Supprimer
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
