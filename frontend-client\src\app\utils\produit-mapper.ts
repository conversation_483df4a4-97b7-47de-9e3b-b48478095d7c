import { ProduitDto } from '../models/ProduitDto';
import { ProduitCard } from '../models/ProduitCard';

export function mapProduitDtoToCard(dto: ProduitDto): ProduitCard {
  const hasPromotions = (dto.promotions?.length ?? 0) > 0;
  const prixFinalTTC = hasPromotions 
    ? Number(dto.prixApresRemises) || Number(dto.prixVenteTTC) 
    : Number(dto.prixVenteTTC);

  const tauxRemiseOutlet =
    dto.prixVenteHT > 0
      ? Math.round(
          (100 *
            (dto.prixVenteHT -
              dto.prixApresRemisesOutlet /
                (1 + (dto.tauxTVA?.taux ?? 0) / 100))) /
            dto.prixVenteHT
        )
      : 0;
  const result: ProduitCard = {
    id: dto.id,
    nom: dto.nom,
    description: dto.description,

    prixHT: dto.prixVenteHT,
    prixOriginalTTC: dto.prixVenteTTC,
    prixApresOutlet: dto.prixApresRemisesOutlet,
    prixApresPromo: dto.prixApresAutresPromotions,
    prixFinalTTC: dto.prixApresRemises,

    tauxRemiseOutlet: tauxRemiseOutlet,
    tauxRemiseTotale: dto.pourcentageRemiseTotale ?? 0,

    estEnPromo: hasPromotions,
    isInPromo: hasPromotions,
    promotions: dto.promotions ?? [],

    noteMoyenne: dto.noteMoyenne,
    nombreAvis: dto.nombreAvis,
    dateAjout: new Date(dto.dateAjout),
    stock: dto.stock,
    isInStock: dto.stock > 0,
    estNouveau:
      new Date().getTime() - new Date(dto.dateAjout).getTime() <
      30 * 24 * 60 * 60 * 1000,

    forme: dto.forme,

    marque: dto.marque,

    sousCategorie: dto.sousCategorie,

    fournisseur: dto.fournisseur,

    tauxTVA: dto.tauxTVA,
    imageUrl: dto.imagePrincipaleUrl ?? dto.images?.[0]?.imageUrl ?? '',
    images: dto.images ?? [],
  };
  return result;
}
