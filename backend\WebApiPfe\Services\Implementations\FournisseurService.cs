﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using System.Text.Json;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Exceptions;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Services.Implementations
{
    public class FournisseurService : IFournisseurService
    {
        private readonly AppDbContext _context;
        private readonly IMapper _mapper;
        private readonly UserManager<Utilisateur> _userManager;
        private readonly INotificationService _notificationService;

        public FournisseurService(AppDbContext context, IMapper mapper, UserManager<Utilisateur> userManager, INotificationService notificationService)
        {
            _context = context;
            _mapper = mapper;
            _userManager = userManager;
            _notificationService = notificationService;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            if (id <= 0) return false;
            return await _context.Fournisseurs
                .AsNoTracking()
                .AnyAsync(f => f.Id == id);
        }

        public async Task<FournisseurDto> GetByIdAsync(int id)
        {
            var fournisseur = await _context.Fournisseurs
                .Include(f => f.Adresses)
                .Include(f => f.Produits)
                .Include(f => f.CommandesFournisseurs)
                .FirstOrDefaultAsync(f => f.Id == id);

            if (fournisseur == null)
                throw new NotFoundException($"Fournisseur avec ID {id} non trouvé.");

            return _mapper.Map<FournisseurDto>(fournisseur);
        }

        public async Task<IEnumerable<FournisseurDto>> GetAllAsync(bool onlyActive = false)
        {
            var query = _context.Fournisseurs
                .Include(f => f.Adresses)
                .ProjectTo<FournisseurDto>(_mapper.ConfigurationProvider)
                .AsQueryable();

            if (onlyActive)
            {
                query = query.Where(f => f.EstActif);
            }

            return await query.ToListAsync();
        }

        public async Task<FournisseurDto> CreateAsync(FournisseurCreateDto dto, List<AdresseCreateDto>? adresses = null)
        {
            if (dto.LogoFile == null || dto.LogoFile.Length == 0)
                throw new ArgumentException("Le logo est obligatoire");

            if (dto.LogoFile.Length > 5 * 1024 * 1024)
                throw new ArgumentException("La taille du logo dépasse 5MB");

            if (!Regex.IsMatch(dto.MatriculeFiscale, @"^\d{8}$"))
                throw new ArgumentException("Matricule fiscale invalide");

            if (string.IsNullOrEmpty(dto.Password))
                throw new ArgumentException("Le mot de passe est obligatoire");

            // Vérifier l'unicité du matricule fiscal
            var existingFournisseurByMatricule = await _context.Fournisseurs
                .FirstOrDefaultAsync(f => f.MatriculeFiscale == dto.MatriculeFiscale);
            if (existingFournisseurByMatricule != null)
                throw new ArgumentException($"Un fournisseur avec le matricule fiscal {dto.MatriculeFiscale} existe déjà");

            // Vérifier l'unicité de l'email
            var existingUserByEmail = await _userManager.FindByEmailAsync(dto.Email);
            if (existingUserByEmail != null)
                throw new ArgumentException($"Un utilisateur avec l'email {dto.Email} existe déjà");

            var logoPath = await SaveLogoFile(dto.LogoFile);

            var fournisseur = _mapper.Map<Fournisseur>(dto);
            fournisseur.UserName = dto.Email;
            fournisseur.LogoFile = logoPath;

            var userCreationResult = await _userManager.CreateAsync(fournisseur, dto.Password);
            if (!userCreationResult.Succeeded)
            {
                var errors = string.Join("; ", userCreationResult.Errors.Select(e => e.Description));
                throw new IdentityException($"Échec de création du fournisseur : {errors}", userCreationResult.Errors);
            }

            var roleResult = await _userManager.AddToRoleAsync(fournisseur, RoleUtilisateur.Fournisseur.ToString());
            if (!roleResult.Succeeded)
            {
                var errors = string.Join("; ", roleResult.Errors.Select(e => e.Description));
                throw new IdentityException($"Échec d'attribution du rôle : {errors}", roleResult.Errors);
            }
            if (adresses != null && adresses.Any())
            {
                fournisseur.Adresses = adresses.Select(a => new Adresse
                {
                    Rue = a.Rue,
                    Ville = a.Ville,
                    CodePostal = a.CodePostal,
                    Pays = a.Pays,
                    EstPrincipale = a.EstPrincipale,
                    Utilisateur = fournisseur
                }).ToList();
            }
            await _context.SaveChangesAsync();

            // Envoyer une notification à tous les admins
            var contenuNotification = $"🏪 Nouveau fournisseur inscrit : {fournisseur.RaisonSociale} ({fournisseur.Email}). Matricule fiscal : {fournisseur.MatriculeFiscale}. Veuillez vérifier et valider le compte.";
            await _notificationService.NotifierTousLesAdminsAsync(contenuNotification);

            return _mapper.Map<FournisseurDto>(fournisseur);
        }
        private async Task<string> SaveLogoFile(IFormFile file)
        {
            if (file.Length == 0)
                throw new ArgumentException("Le fichier logo est vide", nameof(file));
            var uploadsFolder = Path.Combine("wwwroot", "uploads", "logos");
            Directory.CreateDirectory(uploadsFolder);

            var uniqueFileName = Guid.NewGuid() + Path.GetExtension(file.FileName);
            var filePath = Path.Combine(uploadsFolder, uniqueFileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            return $"/uploads/logos/{uniqueFileName}";
        }

        private void ValiderCommission(decimal commission)
        {
            if (commission < 0 || commission > 0.5m)
                throw new ArgumentOutOfRangeException(nameof(commission), "La commission doit être entre 0% et 50%");
        }

        public async Task UpdateAsync(int id, FournisseurUpdateDto dto)
        {
            ValiderCommission(dto.Commission ?? 0); 

            var fournisseur = await _context.Fournisseurs.FindAsync(id);
            if (fournisseur == null)
                throw new NotFoundException("Fournisseur introuvable");

            _mapper.Map(dto, fournisseur);
            await _context.SaveChangesAsync();
        }

        public async Task ToggleStatusAsync(int id)
        {
            var fournisseur = await _context.Fournisseurs.FindAsync(id);
            if (fournisseur == null)
                throw new NotFoundException("Fournisseur introuvable");

            fournisseur.EstActif = !fournisseur.EstActif;
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<ProduitDto>> GetProduitsByFournisseurAsync(int fournisseurId)
        {
            var produits = await _context.Produits
                .Where(p => p.FournisseurId == fournisseurId)
                .ToListAsync();

            return _mapper.Map<IEnumerable<ProduitDto>>(produits);
        }

        public async Task<bool> ValiderRIBAsync(string rib, string codeBanque)
        {
            return await Task.FromResult(
                !string.IsNullOrEmpty(rib) &&
                rib.Length == 20 &&
                rib.StartsWith(codeBanque));
        }

        public async Task UpdateCommissionAsync(int id, decimal commission)
        {
            ValiderCommission(commission);

            var fournisseur = await _context.Fournisseurs.FindAsync(id);
            if (fournisseur == null)
                throw new NotFoundException($"Fournisseur avec l'ID {id} introuvable");

            fournisseur.Commission = commission;
            await _context.SaveChangesAsync();
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var fournisseur = await _context.Fournisseurs.FindAsync(id);
            if (fournisseur == null) return false;

            _context.Fournisseurs.Remove(fournisseur);
            await _context.SaveChangesAsync();
            return true;
        }
        public async Task<AdresseDto> AjouterAdresseAsync(int fournisseurId, AdresseCreateDto dto)
        {
            var fournisseur = await _context.Fournisseurs
                .Include(f => f.Adresses)
                .FirstOrDefaultAsync(f => f.Id == fournisseurId);

            if (fournisseur == null)
                throw new NotFoundException($"Fournisseur avec ID {fournisseurId} non trouvé.");

            var nouvelleAdresse = new Adresse
            {
                Rue = dto.Rue,
                Ville = dto.Ville,
                CodePostal = dto.CodePostal,
                Pays = dto.Pays,
                EstPrincipale = dto.EstPrincipale,
                UtilisateurId = fournisseur.Id
            };

            if (dto.EstPrincipale)
            {
                foreach (var a in fournisseur.Adresses)
                    a.EstPrincipale = false;
            }

            fournisseur.Adresses.Add(nouvelleAdresse);

            await _context.SaveChangesAsync();

            return _mapper.Map<AdresseDto>(nouvelleAdresse);
        }

    }
}
