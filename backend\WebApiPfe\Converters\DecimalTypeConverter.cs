using System.ComponentModel;
using System.Globalization;

namespace WebApiPfe.Converters
{
    public class DecimalTypeConverter : TypeConverter
    {
        public override bool CanConvertFrom(ITypeDescriptorContext? context, Type sourceType)
        {
            return sourceType == typeof(string) || base.CanConvertFrom(context, sourceType);
        }

        public override object? ConvertFrom(ITypeDescriptorContext? context, CultureInfo? culture, object value)
        {
            if (value is string stringValue)
            {
                if (string.IsNullOrWhiteSpace(stringValue))
                    return null;

                // Normaliser la chaîne : remplacer la virgule par un point
                var normalizedValue = stringValue.Replace(',', '.');

                // Essayer de parser avec la culture invariante
                if (decimal.TryParse(normalizedValue, NumberStyles.Number, CultureInfo.InvariantCulture, out var result))
                {
                    return result;
                }

                // Si ça échoue, essayer avec la culture actuelle
                if (decimal.TryParse(stringValue, NumberStyles.Number, culture ?? CultureInfo.CurrentCulture, out result))
                {
                    return result;
                }

                throw new FormatException($"Impossible de convertir '{stringValue}' en decimal.");
            }

            return base.ConvertFrom(context, culture, value);
        }
    }
}
