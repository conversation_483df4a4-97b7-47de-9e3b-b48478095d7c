<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Techniques Utilisées - PFE E-commerce</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e40af;
            font-size: 28px;
            margin-bottom: 30px;
        }
        .architecture {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 20px;
            margin: 30px 0;
        }
        .frontend, .backend {
            border: 2px dashed;
            border-radius: 15px;
            padding: 20px;
            width: 45%;
        }
        .frontend {
            border-color: #dc2626;
        }
        .backend {
            border-color: #7c3aed;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
        }
        .frontend .section-title {
            color: #dc2626;
        }
        .backend .section-title {
            color: #7c3aed;
        }
        .components {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            margin-bottom: 20px;
        }
        .component {
            background: #4b5563;
            color: white;
            padding: 15px 10px;
            border-radius: 5px;
            text-align: center;
            flex: 1;
            font-size: 11px;
        }
        .connection {
            background: #f59e0b;
            color: white;
            padding: 10px 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px auto;
            width: fit-content;
            font-weight: bold;
        }
        .database {
            text-align: center;
            margin: 20px 0;
        }
        .db-icon {
            width: 120px;
            height: 80px;
            border: 2px solid #dc2626;
            border-radius: 50%;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(to bottom, #dc2626 0%, #dc2626 20%, transparent 20%);
        }
        .db-text {
            color: #dc2626;
            font-weight: bold;
        }
        .tech-list {
            font-size: 11px;
            color: #6b7280;
            line-height: 1.5;
        }
        .arrow {
            font-size: 24px;
            color: #374151;
            align-self: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Les techniques utilisées</h1>
        
        <div class="architecture">
            <div class="frontend">
                <div class="section-title">Angular 19</div>
                <div class="components">
                    <div class="component">
                        Components<br>TypeScript
                    </div>
                    <div class="component">
                        Services<br>RxJS
                    </div>
                    <div class="component">
                        Router<br>Guards
                    </div>
                </div>
                <div class="tech-list">
                    • HTML5/CSS3<br>
                    • JWT Authentication<br>
                    • Responsive Design
                </div>
            </div>
            
            <div class="arrow">→</div>
            
            <div class="backend">
                <div class="section-title">ASP.NET Core</div>
                <div class="components">
                    <div class="component">
                        Controllers<br>Web API
                    </div>
                    <div class="component">
                        Services<br>C#
                    </div>
                    <div class="component">
                        Entity<br>Framework
                    </div>
                </div>
                <div class="tech-list">
                    • Stripe Payment<br>
                    • AutoMapper<br>
                    • Swagger/OpenAPI
                </div>
            </div>
        </div>
        
        <div class="connection">HTTP REST API</div>
        
        <div class="connection">EF Core ORM</div>
        
        <div class="database">
            <div class="db-icon">
                <div class="db-text">DB</div>
            </div>
            <div class="db-text">SQL Server Database</div>
        </div>
    </div>
</body>
</html>
