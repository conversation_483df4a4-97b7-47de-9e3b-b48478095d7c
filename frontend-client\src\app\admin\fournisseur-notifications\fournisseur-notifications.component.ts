import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { NotificationService, NotificationDto } from '../../services/notification.service';
import { FournisseurValidationService, FournisseurValidationDto, StatutValidationFournisseur, ValiderFournisseurRequest } from '../../services/fournisseur-validation.service';
import { SignalRService } from '../../services/signalr.service';
import { AuthService } from '../../auth/auth.service';

@Component({
  selector: 'app-fournisseur-notifications',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './fournisseur-notifications.component.html',
  styleUrls: ['./fournisseur-notifications.component.css']
})
export class FournisseurNotificationsComponent implements OnInit, OnDestroy {
  notifications: NotificationDto[] = [];
  fournisseursEnAttente: FournisseurValidationDto[] = [];
  selectedFournisseur: FournisseurValidationDto | null = null;
  isLoading = false;
  error = '';
  
  // Modal de validation
  showValidationModal = false;
  validationComment = '';
  validationAction: 'accept' | 'reject' | null = null;
  
  private subscriptions: Subscription[] = [];

  constructor(
    private notificationService: NotificationService,
    private fournisseurValidationService: FournisseurValidationService,
    private signalRService: SignalRService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadNotifications();
    this.loadFournisseursEnAttente();
    this.setupSignalRConnection();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private async setupSignalRConnection(): Promise<void> {
    try {
      await this.signalRService.startConnection();
      
      // Écouter les nouvelles notifications
      const notificationSub = this.notificationService.notifications$.subscribe(
        notifications => {
          this.notifications = notifications;
        }
      );
      this.subscriptions.push(notificationSub);
    } catch (error) {
      console.error('Erreur lors de la connexion SignalR:', error);
    }
  }

  loadNotifications(): void {
    this.isLoading = true;
    this.error = '';
    
    const sub = this.notificationService.getAllAdminNotifications().subscribe({
      next: (notifications) => {
        this.notifications = notifications;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des notifications:', error);
        this.error = 'Erreur lors du chargement des notifications';
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  loadFournisseursEnAttente(): void {
    const sub = this.fournisseurValidationService.getFournisseursEnAttente().subscribe({
      next: (fournisseurs) => {
        this.fournisseursEnAttente = fournisseurs;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des fournisseurs en attente:', error);
      }
    });
    this.subscriptions.push(sub);
  }

  markAsRead(notification: NotificationDto): void {
    if (!notification.estLue) {
      const sub = this.notificationService.markAsRead(notification.id).subscribe({
        next: () => {
          notification.estLue = true;
        },
        error: (error) => {
          console.error('Erreur lors du marquage comme lu:', error);
        }
      });
      this.subscriptions.push(sub);
    }
  }

  deleteNotification(notificationId: number): void {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette notification ?')) {
      const sub = this.notificationService.deleteNotification(notificationId).subscribe({
        next: () => {
          this.notifications = this.notifications.filter(n => n.id !== notificationId);
        },
        error: (error) => {
          console.error('Erreur lors de la suppression:', error);
        }
      });
      this.subscriptions.push(sub);
    }
  }

  viewFournisseurDetails(fournisseurId: number): void {
    const sub = this.fournisseurValidationService.getFournisseurById(fournisseurId).subscribe({
      next: (fournisseur) => {
        this.selectedFournisseur = fournisseur;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des détails du fournisseur:', error);
      }
    });
    this.subscriptions.push(sub);
  }

  openValidationModal(action: 'accept' | 'reject'): void {
    this.validationAction = action;
    this.validationComment = '';
    this.showValidationModal = true;
  }

  closeValidationModal(): void {
    this.showValidationModal = false;
    this.validationAction = null;
    this.validationComment = '';
  }

  confirmValidation(): void {
    if (!this.selectedFournisseur || !this.validationAction) return;

    const request: ValiderFournisseurRequest = {
      fournisseurId: this.selectedFournisseur.id,
      accepter: this.validationAction === 'accept',
      commentaire: this.validationComment || undefined
    };

    const sub = this.fournisseurValidationService.validerFournisseur(request).subscribe({
      next: () => {
        // Recharger les données
        this.loadFournisseursEnAttente();
        this.loadNotifications();
        
        // Fermer le modal et réinitialiser la sélection
        this.closeValidationModal();
        this.selectedFournisseur = null;
        
        // Afficher un message de succès
        const action = this.validationAction === 'accept' ? 'validé' : 'rejeté';
        alert(`Fournisseur ${action} avec succès !`);
      },
      error: (error) => {
        console.error('Erreur lors de la validation:', error);
        alert('Erreur lors de la validation du fournisseur');
      }
    });
    this.subscriptions.push(sub);
  }

  closeDetails(): void {
    this.selectedFournisseur = null;
  }

  formatDate(date: Date | string): string {
    const d = new Date(date);
    return d.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getStatutValidationText(statut: StatutValidationFournisseur): string {
    return this.fournisseurValidationService.getStatutValidationText(statut);
  }

  getStatutValidationClass(statut: StatutValidationFournisseur): string {
    return this.fournisseurValidationService.getStatutValidationClass(statut);
  }

  extractFournisseurIdFromNotification(content: string): number | null {
    // Essayer d'extraire l'ID du fournisseur depuis le contenu de la notification
    // Cette méthode peut être améliorée selon le format exact des notifications
    const match = content.match(/Matricule fiscal\s*:\s*(\d+)/);
    if (match) {
      // Chercher le fournisseur par matricule fiscal
      const matricule = match[1];
      const fournisseur = this.fournisseursEnAttente.find(f => f.matriculeFiscale === matricule);
      return fournisseur?.id || null;
    }
    return null;
  }

  viewFournisseurFromNotification(notification: NotificationDto): void {
    const fournisseurId = this.extractFournisseurIdFromNotification(notification.contenu);
    if (fournisseurId) {
      this.viewFournisseurDetails(fournisseurId);
      this.markAsRead(notification);
    }
  }
}
