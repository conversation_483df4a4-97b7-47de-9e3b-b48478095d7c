import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, catchError, switchMap, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { PanierDto } from '../models/PanierDto';
import { ItemPanierDto } from '../models/ItemPanierDto';

@Injectable({
  providedIn: 'root'
})
export class CartService {
  private apiUrl = `${environment.apiUrl}/panier`;
  private itemApiUrl = `${environment.apiUrl}/itempanier`;

  constructor(private http: HttpClient) {}

  getCart(clientId: number): Observable<PanierDto> {
    return this.http.get<PanierDto>(`${this.apiUrl}/client/${clientId}`);
  }

  createCart(clientId: number): Observable<PanierDto> {
    const createDto: CreatePanierDto = { clientId };
    return this.http.post<PanierDto>(this.apiUrl, createDto);
  }

  getOrCreateCart(clientId: number): Observable<PanierDto> {
    return this.getCart(clientId).pipe(
      catchError(error => {
        if (error.status === 404) {
          // Si le panier n'existe pas, on le crée
          return this.createCart(clientId);
        }
        throw error;
      })
    );
  }

  addItem(panierId: number, item: AddItemPanierDto): Observable<ItemPanierDto> {
    return this.http.post<ItemPanierDto>(`${this.apiUrl}/${panierId}/items`, item);
  }

  // Nouvelle méthode qui utilise clientId directement et gère les doublons
  addItemByClientId(clientId: number, item: AddItemPanierDto): Observable<ItemPanierDto> {
    return this.getOrCreateCart(clientId).pipe(
      switchMap(panier => {
        // Vérifier si le produit existe déjà dans le panier
        const existingItem = panier.items.find(i => i.produitId === item.produitId);

        if (existingItem) {
          // Si le produit existe déjà, augmenter la quantité
          console.log('Produit déjà dans le panier, augmentation de la quantité');
          const newQuantity = existingItem.quantite + item.quantite;

          return this.updateItem(existingItem.id, { quantite: newQuantity }).pipe(
            switchMap(() => {
              // Retourner l'item mis à jour
              return of({
                ...existingItem,
                quantite: newQuantity,
                sousTotal: existingItem.prixUnitaire * newQuantity
              } as ItemPanierDto);
            })
          );
        } else {
          // Si le produit n'existe pas, l'ajouter normalement
          return this.http.post<ItemPanierDto>(`${this.apiUrl}/${panier.id}/items`, item).pipe(
            catchError(error => {
              console.log('Erreur lors de l\'ajout, mais l\'item pourrait être ajouté:', error);

              // Si c'est l'erreur spécifique que nous connaissons et que l'item est ajouté quand même
              if (error.status === 404 &&
                  (error.error?.includes?.('Aucun panier actif') ||
                   typeof error.error === 'string' && error.error.includes('Aucun panier actif'))) {

                console.log('Erreur connue - l\'item a probablement été ajouté quand même');

                // L'item a probablement été ajouté, on retourne un objet factice pour indiquer le succès
                return of({
                  id: Date.now(), // ID temporaire
                  produitId: item.produitId,
                  nomProduit: 'Produit ajouté',
                  referenceProduit: '',
                  quantite: item.quantite,
                  prixUnitaire: 0,
                  sousTotal: 0,
                  dateAjout: new Date()
                } as ItemPanierDto);
              }

              // Pour toute autre erreur, on la propage
              throw error;
            })
          );
        }
      })
    );
  }

  updateItem(itemId: number, update: UpdateItemPanierDto): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/items/${itemId}`, update);
  }

  removeItem(itemId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/items/${itemId}`);
  }

  applyPromoCode(panierId: number, code: string): Observable<void> {
    // Utiliser l'endpoint update pour appliquer le code promo
    const updateDto: UpdatePanierDto = { codePromoApplique: code };
    return this.http.put<void>(`${this.apiUrl}/${panierId}`, updateDto);
  }

  clearCart(panierId: number): Observable<void> {
    // Pour vider le panier, on supprime tous les items un par un
    // ou on utilise l'endpoint de suppression du panier puis on le recrée
    return this.http.delete<void>(`${this.apiUrl}/${panierId}`);
  }
}

export interface AddItemPanierDto {
  produitId: number;
  quantite: number;
}

export interface UpdateItemPanierDto {
  quantite?: number;
  prixApresPromotion?: number;
}

export interface CreatePanierDto {
  clientId: number;
}

export interface UpdatePanierDto {
  estActif?: boolean;
  codePromoApplique?: string;
}
