<!-- Container principal -->
<div class="fournisseurs-wrapper">
  <!-- Version Desktop -->
  <div class="desktop-fournisseurs" >
    <div class="fournisseurs-container">
      <!-- Contenu visible -->
      <div class="fournisseurs-visible" *ngIf="fournisseurs.length > 0">
        <div class="fournisseurs-content">
          <h2>Nos Fournisseurs</h2>
          <div *ngIf="fournisseurs.length > 0" class="fournisseurs-section">
            <ul class="fournisseurs-list">
              <li
                *ngFor="let fournisseur of fournisseurs"
                class="fournisseur-item"
              >
                <div
                  (click)="navigateToFournisseur(fournisseur.id)"
                  class="fournisseur-link"
                  style="cursor: pointer"
                >
                  <div class="fournisseur-image-container">
                    <img
                      [src]="imageUrlService.getFournisseurLogoUrl(fournisseur.logoFile)"
                      [alt]="
                        fournisseur.nom + ' ' + fournisseur.prenom + ' logo'
                      "
                      class="fournisseur-image"
                      loading="lazy"
                    />
                  </div>
                  <p class="fournisseur-name">
                    {{ fournisseur.nom }} {{ fournisseur.prenom }}
                  </p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
