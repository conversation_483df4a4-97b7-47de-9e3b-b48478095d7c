<div class="checkout-container">
  <div class="checkout-header">
    <h1>Confirmation de commande</h1>
    <div class="checkout-steps">
      <div class="step active">
        <span class="step-number">1</span>
        <span class="step-label">Confirmation</span>
      </div>
      <div class="step">
        <span class="step-number">2</span>
        <span class="step-label">Adresse</span>
      </div>
      <div class="step">
        <span class="step-number">3</span>
        <span class="step-label">Paiement</span>
      </div>
      <div class="step">
        <span class="step-number">4</span>
        <span class="step-label">Confirmation</span>
      </div>
    </div>
  </div>

  <div *ngIf="loading" class="loading">
    <div class="spinner"></div>
    <p>Chargement...</p>
  </div>

  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <div *ngIf="cart && !loading" class="checkout-content">
    <!-- Récapitulatif de la commande -->
    <div class="order-summary">
      <h2>Récapitulatif de votre commande</h2>
      
      <div class="cart-items">
        <div *ngFor="let item of cartItemsWithDetails" class="cart-item">
          <div class="item-image">
            <img [src]="getProductImage(item)"
                 [alt]="item.nomProduit"
                 (error)="onImageError($event)">
          </div>
          <div class="item-details">
            <h3>{{ item.nomProduit }}</h3>
            <p class="item-reference">Réf: {{ item.referenceProduit }}</p>
            <div class="item-quantity">
              <span>Quantité: {{ item.quantite }}</span>
            </div>
          </div>
          <div class="item-price">
            <span class="unit-price">{{ item.prixUnitaire.toFixed(2) }} DT</span>
            <span class="total-price">{{ (item.prixUnitaire * item.quantite).toFixed(2) }} DT</span>
          </div>
        </div>
      </div>

      <!-- Total -->
      <div class="order-total">
        <div class="total-line">
          <span>Nombre d'articles:</span>
          <span>{{ getTotalItems() }}</span>
        </div>
        <div class="total-line">
          <span>Sous-total:</span>
          <span>{{ getTotal().toFixed(2) }} DT</span>
        </div>

        <!-- Frais de livraison détaillés -->
        <div class="delivery-section">
          <div class="total-line">
            <span>Frais de livraison:</span>
            <span *ngIf="!fraisLivraisonDetails" class="calculating">🚚 Calcul en cours...</span>
            <span *ngIf="fraisLivraisonDetails && fraisLivraisonDetails.fraisLivraisonTotal === 0" class="free-delivery">Gratuit</span>
            <span *ngIf="fraisLivraisonDetails && fraisLivraisonDetails.fraisLivraisonTotal > 0" class="delivery-total">{{ fraisLivraisonDetails.fraisLivraisonTotal.toFixed(2) }} DT</span>
          </div>

          <!-- Détail par fournisseur -->
          <div *ngIf="fraisLivraisonDetails && fraisLivraisonDetails.fraisParFournisseur.length > 0" class="delivery-breakdown">
            <h4>Détail des frais de livraison :</h4>
            <div class="delivery-items">
              <div *ngFor="let detail of fraisLivraisonDetails.fraisParFournisseur" class="delivery-item">
                <div class="supplier-info">
                  <span class="supplier-icon">🏪</span>
                  <span class="supplier-name">{{ detail.nomFournisseur }}</span>
                </div>
                <div class="supplier-delivery-fee">
                  <span *ngIf="detail.fraisLivraison === 0" class="free">Gratuit</span>
                  <span *ngIf="detail.fraisLivraison > 0" class="fee">{{ detail.fraisLivraison.toFixed(2) }} DT</span>
                </div>
              </div>
            </div>
            <!-- Total des frais de livraison -->
            <div class="delivery-total-summary">
              <span class="total-label">Total frais de livraison :</span>
              <span class="total-value">
                <span *ngIf="fraisLivraisonDetails.fraisLivraisonTotal === 0" class="free">Gratuit</span>
                <span *ngIf="fraisLivraisonDetails.fraisLivraisonTotal > 0" class="total">{{ fraisLivraisonDetails.fraisLivraisonTotal.toFixed(2) }} DT</span>
              </span>
            </div>
          </div>
        </div>

        <div class="total-line final-total">
          <span>Total:</span>
          <span>{{ getTotalAvecLivraison().toFixed(2) }} DT</span>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="checkout-actions">
      <button type="button" class="btn btn-secondary" (click)="retourPanier()">
        Retour au panier
      </button>
      <button type="button"
              class="btn btn-primary"
              (click)="continuerVersAdresse()">
        Continuer
      </button>
    </div>
  </div>
</div>
