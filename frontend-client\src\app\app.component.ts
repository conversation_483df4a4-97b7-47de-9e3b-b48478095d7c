import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { TokenStorageService } from './shared/services/token-storage.service';
import { AuthService } from './auth/auth.service';
import { SignalRService } from './services/signalr.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  standalone: false,
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'optic-marketplace';
  private subscriptions = new Subscription();

  constructor(
    private tokenService: TokenStorageService,
    private authService: AuthService,
    private signalRService: SignalRService
  ) {}

  ngOnInit() {
    const token = this.tokenService.getToken();
    if (token) {
      this.authService.initializeUserFromStorage();

      // Initialiser SignalR si l'utilisateur est connecté
      this.subscriptions.add(
        this.authService.currentUser$.subscribe(user => {
          if (user) {
            this.signalRService.startConnection().catch(error => {
              console.error('Erreur lors de l\'initialisation de SignalR:', error);
            });
          } else {
            this.signalRService.stopConnection().catch(error => {
              console.error('Erreur lors de l\'arrêt de SignalR:', error);
            });
          }
        })
      );
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    this.signalRService.stopConnection().catch(error => {
      console.error('Erreur lors de l\'arrêt de SignalR:', error);
    });
  }
}
