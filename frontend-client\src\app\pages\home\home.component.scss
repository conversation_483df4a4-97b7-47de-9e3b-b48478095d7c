/* ===== PAGE D'ACCUEIL MODERNE ===== */
.home {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: var(--background-color);
  min-height: 100vh;
  padding: 2rem 0;

  .contenu {
    display: flex;
    margin-top: 0;
    margin-bottom: 2rem;
    width: min(90vw, 1200px);
    max-width: 1200px;
    gap: 1rem;
    position: relative;
    box-shadow: var(--card-shadow);
    border-radius: 1.5rem;
    overflow: hidden;
    background: var(--card-background-color);

    .big-rectangle {
      flex: 1;
      aspect-ratio: 16/9;
      position: relative;
      border-radius: 1.5rem 0 0 1.5rem;
      overflow: hidden;
      height: calc(200px * 2 + 1rem);
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-color-hover) 100%);

      .slider {
        position: absolute;
        inset: 0;

        .slide {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
          transition: opacity 0.5s ease;

          &.active {
            opacity: 1;
            z-index: 2;
          }

          &-container {
            position: relative;
            width: 100%;
            height: 100%;
          }

          img {
            position: absolute;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 1;
          }

          &-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            z-index: 3;
            max-width: 70%;
            text-align: center;

            h3 {
              font-size: 1.2em;
              margin-bottom: 0.5rem;
              font-weight: 500;
              opacity: 0.9;
              text-transform: uppercase;
              letter-spacing: 1px;
            }

            h2 {
              font-size: 2.5em;
              margin-bottom: 1.5rem;
              text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
              font-weight: 700;
              line-height: 1.2;
            }

            button {
              background: rgba(255, 255, 255, 0.15);
              backdrop-filter: blur(10px);
              border: 2px solid rgba(255, 255, 255, 0.8);
              padding: 0.75rem 2rem;
              color: white;
              border-radius: 50px;
              cursor: pointer;
              font-weight: 600;
              font-size: 1rem;
              transition: all 0.3s ease;
              text-transform: uppercase;
              letter-spacing: 0.5px;

              &:hover {
                transform: translateY(-2px);
                background: white;
                color: var(--primary-color);
                box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
              }

              &:active {
                transform: translateY(0);
              }
            }
          }
        }
      }
    }
    .small-rectangles {
      display: flex;
      flex-direction: column;
      gap: 10px;
      width: 200px;

      > div {
        aspect-ratio: 1;
        overflow: hidden;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;

        &:first-child {
          border-radius: 0 20px 0 0;
        }

        &:last-child {
          border-radius: 0 0 20px 0;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          filter: brightness(0.7);
          position: relative;
          z-index: 1;
        }

        .small-content {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: white;
          z-index: 2;

          h3 {
            font-size: 1em;
            margin-bottom: 5px;
          }

          h2 {
            font-size: 1.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
          }

          button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            padding: 8px 20px;
            color: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;

            &:hover {
              background: white;
              transform: scale(1.05);
              color: #70937e;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 800px) {
  .home {
    padding: 0;
    .contenu {
      width: 100%;
      height: 30vh;
      flex-direction: column;

      .big-rectangle {
        width: 100% !important;
        height: 100% !important;
        border-radius: 0;
        .slide {
          img {
            width: 100%;
          }
          &-content {
            bottom: 10px;
            left: 10px;
            h3 {
              font-size: 0.9em;
            }
            h2 {
              font-size: 1.3em;
            }
            button {
              padding: 6px 12px;
              font-size: 0.9em;
            }
          }
        }
      }
      .small-rectangles {
        display: none !important;
      }
    }
  }
}
