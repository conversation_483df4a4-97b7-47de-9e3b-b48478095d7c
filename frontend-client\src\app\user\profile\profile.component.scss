.profile-card {
  background-color: var(--card-background-color-hover);
  border-radius: 8px;
  padding: 20px;
  box-shadow: var(--secondary-color-hover);
  margin: 20px auto;
  max-width: 500px;
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
}
h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  margin-top: 2rem;
  color: var(--accent-color);
  text-align: center;
}
.profile-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 16px;
  color: var(--text-color);

  i {
    font-size: 20px;
    margin-right: 10px;
    color: var(--primary-color);
  }

  span {
    strong {
      color: var(--primary-color);
    }
  }
}

// Style pour le bouton "Mes Commandes"
.btn-orders {
  margin-top: 20px;
  padding: 10px 20px;
  background-color: var(--secondary-color);
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: var(--accent-color-hover);
  }
}
.btn-edit  {
  margin-left: 20px;
  margin-top: 20px;
  padding: 10px 20px;
  background-color: var(--secondary-color);
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: var(--accent-color-hover);
  }
}
.btn-logout  {
  margin-left: 20px;
  margin-top: 20px;
  padding: 10px 20px;
  background-color: var(--secondary-color);
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: var(--accent-color-hover);
  }
}
