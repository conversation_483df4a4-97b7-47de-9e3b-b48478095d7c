import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface FournisseurValidationDto {
  id: number;
  email: string;
  nom: string;
  prenom: string;
  phoneNumber: string;
  matriculeFiscale: string;
  raisonSociale: string;
  description?: string;
  rib: string;
  codeBanque: string;
  commission: number;
  delaiPreparationJours: number;
  fraisLivraisonBase: number;
  logoFile: string;
  statutValidation: StatutValidationFournisseur;
  dateInscription: Date;
  dateValidation?: Date;
  validePar?: number;
  commentaireValidation?: string;
  adresses: AdresseDto[];
}

export interface AdresseDto {
  id: number;
  rue: string;
  ville: string;
  codePostal: string;
  pays: string;
  estPrincipale: boolean;
}

export enum StatutValidationFournisseur {
  EnAttente = 0,
  Valide = 1,
  Rejete = 2
}

export interface ValiderFournisseurRequest {
  fournisseurId: number;
  accepter: boolean;
  commentaire?: string;
}

@Injectable({
  providedIn: 'root'
})
export class FournisseurValidationService {
  private apiUrl = `${environment.apiUrl}/api/Admin/fournisseurs`;

  constructor(private http: HttpClient) {}

  public getFournisseursEnAttente(): Observable<FournisseurValidationDto[]> {
    return this.http.get<FournisseurValidationDto[]>(`${this.apiUrl}/en-attente`);
  }

  public getAllFournisseurs(): Observable<FournisseurValidationDto[]> {
    return this.http.get<FournisseurValidationDto[]>(this.apiUrl);
  }

  public getFournisseurById(id: number): Observable<FournisseurValidationDto> {
    return this.http.get<FournisseurValidationDto>(`${this.apiUrl}/${id}`);
  }

  public validerFournisseur(request: ValiderFournisseurRequest): Observable<any> {
    return this.http.post(`${this.apiUrl}/valider`, request);
  }

  public getStatutValidationText(statut: StatutValidationFournisseur): string {
    switch (statut) {
      case StatutValidationFournisseur.EnAttente:
        return 'En attente';
      case StatutValidationFournisseur.Valide:
        return 'Validé';
      case StatutValidationFournisseur.Rejete:
        return 'Rejeté';
      default:
        return 'Inconnu';
    }
  }

  public getStatutValidationClass(statut: StatutValidationFournisseur): string {
    switch (statut) {
      case StatutValidationFournisseur.EnAttente:
        return 'status-pending';
      case StatutValidationFournisseur.Valide:
        return 'status-approved';
      case StatutValidationFournisseur.Rejete:
        return 'status-rejected';
      default:
        return 'status-unknown';
    }
  }
}
