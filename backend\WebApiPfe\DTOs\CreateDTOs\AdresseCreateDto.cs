﻿using System.ComponentModel.DataAnnotations;
namespace WebApiPfe.DTOs.CreateDTOs
{
    public class AdresseCreateDto
    {
        [Required]
        public string Rue { get; set; }
        [Required]
        public string Ville { get; set; }
        [Required]
        public string CodePostal { get; set; }
        [Required]
        public string Pays { get; set; }
        public bool EstPrincipale { get; set; }
        [Required]
        public int EntityId { get; set; }
    }
}
