﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DetailsCommandeController : ControllerBase
    {
        private readonly IDetailsCommandeService _service;

        public DetailsCommandeController(IDetailsCommandeService service)
        {
            _service = service;
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<DetailsCommandeDto>> Get(int id)
        {
            try
            {
                return Ok(await _service.GetByIdAsync(id));
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpGet("commande/{commandeId}")]
        public async Task<ActionResult<IEnumerable<DetailsCommandeDto>>> GetByCommande(int commandeId)
        {
            return Ok(await _service.GetByCommandeAsync(commandeId));
        }

        [HttpPost]
        public async Task<ActionResult<DetailsCommandeDto>> Post([FromBody] CreateDetailsCommandeDto dto)
        {
            try
            {
                var created = await _service.CreateAsync(dto);
                return CreatedAtAction(nameof(Get), new { id = created.Id }, created);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Put(int id, [FromBody] UpdateDetailsCommandeDto dto)
        {
            try
            {
                await _service.UpdateAsync(id, dto);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                await _service.DeleteAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }

        [HttpPost("{id}/recalculer")]
        public async Task<IActionResult> RecalculerPrix(int id)
        {
            try
            {
                await _service.RecalculerPrixAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
        }
    }
}
