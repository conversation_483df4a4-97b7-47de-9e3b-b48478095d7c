using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.CreateDTOs
{
    public class CreateCommandeDto
    {
        [Required]
        public int ClientId { get; set; }

        [Required]
        public List<CreateDetailsCommandeSimpleDto> DetailsCommandes { get; set; } = new List<CreateDetailsCommandeSimpleDto>();

        public string? CodePromo { get; set; }
    }

    public class CreateDetailsCommandeSimpleDto
    {
        [Required]
        public int ProduitId { get; set; }

        [Required]
        [Range(1, int.MaxValue)]
        public int Quantite { get; set; }
    }
}
