<div class="register-container">
  <mat-card>
    <div class="card-content-wrapper">
      <div class="form-section">
        <mat-card-header class="auth-header">
          <mat-card-title>Création de compte</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
            <div class="form-grid">
              <mat-form-field appearance="outline">
                <mat-label>Nom</mat-label>
                <input matInput formControlName="nom" />
                <mat-error>Champ obligatoire</mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Prénom</mat-label>
                <input matInput formControlName="prenom" />
                <mat-error>Champ obligatoire</mat-error>
              </mat-form-field>
            </div>

            <mat-form-field appearance="outline">
              <mat-label>Nom d'utilisateur</mat-label>
              <input matInput formControlName="username" />
              <mat-error>3 caractères minimum</mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Email</mat-label>
              <input matInput formControlName="email" type="email" />
              <mat-error>Email invalide</mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Téléphone</mat-label>
              <input matInput formControlName="phoneNumber" type="tel" />
              <mat-error>Numéro de téléphone requis</mat-error>
            </mat-form-field>
            <mat-form-field appearance="outline">
              <mat-label>Date de naissance</mat-label>
              <input
                matInput
                [matDatepicker]="picker"
                [max]="maxBirthDate"
                formControlName="dateNaissance"
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="picker"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
            <mat-form-field appearance="outline">
              <mat-label>Mot de passe</mat-label>
              <input matInput formControlName="motDePasse" type="password" />
              <mat-error>6 caractères minimum</mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>Confirmation</mat-label>
              <input
                matInput
                formControlName="confirmPassword"
                type="password"
              />
              <mat-error>Doit correspondre</mat-error>
            </mat-form-field>

            <button
              mat-raised-button
              color="primary"
              type="submit"
              class="submit-btn"
            >
              <span *ngIf="!isLoading">S'inscrire</span>
              <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
            </button>
          </form>
        </mat-card-content>
      </div>
      <div
        class="welcome-section"
        [style.background-image]="'url(\'assets/images/auth/welcome-register.jpg\')'"
      >
        <div class="welcome-message">
          <mat-card-title>Content de te revoir</mat-card-title>
          <div class="action-link">
            <span>Déjà un compte ?</span>
            <button mat-button routerLink="/auth/login" color="accent">
              Se connecter
            </button>
          </div>
        </div>
      </div>
    </div>
  </mat-card>
</div>
