<div class="cart-container">
  <h2>Vot<PERSON>ier</h2>
  
  <div *ngIf="loading" class="loading">Chargement...</div>
  
  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
    <button (click)="errorMessage = ''">×</button>
  </div>

  <div *ngIf="cart && cart.items.length > 0; else emptyCart">
    <div class="cart-items">
      <div class="cart-item" *ngFor="let item of cartItemsWithDetails">
        <div class="product-image">
          <img
            [src]="getSimpleProductImage(item)"
            [alt]="item.nomProduit"
            class="product-img"
            (error)="onImageError($event)"
          >
          <div *ngIf="hasPromotion(item)" class="promo-badge">
            -{{ getPromotionPercentage(item) }}%
          </div>
        </div>

        <!-- Informations produit au centre -->
        <div class="product-info">
          <h3 class="product-name">{{ item.nomProduit }}</h3>

          <div class="product-meta">
            <span class="product-reference">{{ item.referenceProduit }}</span>
            <span class="product-brand" *ngIf="item.produitDetails?.marque?.name">{{ item.produitDetails?.marque?.name }}</span>
            <span class="product-stock"
                  [class.in-stock]="(item.produitDetails?.stock || 0) >= item.quantite"
                  [class.insufficient-stock]="item.produitDetails && item.produitDetails.stock < item.quantite"
                  [class.out-of-stock]="(item.produitDetails?.stock || 0) === 0">
              <span *ngIf="(item.produitDetails?.stock || 0) === 0"> Rupture de stock</span>
              <span *ngIf="(item.produitDetails?.stock || 0) > 0 && item.produitDetails && item.produitDetails.stock >= item.quantite">
                En stock ({{ item.produitDetails.stock || 0 }})
              </span>
              <span *ngIf="item.produitDetails && item.produitDetails.stock > 0 && item.produitDetails.stock < item.quantite">
                ⚠️ Stock insuffisant ({{ item.produitDetails.stock }} disponible{{ item.produitDetails.stock > 1 ? 's' : '' }})
              </span>
            </span>
          </div>
        </div>

        <!-- Contrôles et prix à droite -->
        <div class="product-controls">
          <!-- Prix -->
          <div class="price-section">
            <div *ngIf="hasPromotion(item)" class="price-with-promo">
              <span class="original-price">{{ formatPrice(item.prixUnitaire) }}</span>
              <span class="promo-price">{{ formatPrice(item.prixApresPromotion) }}</span>
            </div>
            <div *ngIf="!hasPromotion(item)" class="regular-price">
              {{ formatPrice(item.prixUnitaire) }}
            </div>
          </div>

          <!-- Contrôle de quantité -->
          <div class="quantity-control">
            <button (click)="updateQuantity(item, item.quantite - 1)" [disabled]="item.quantite <= 1">-</button>
            <span>{{ item.quantite }}</span>
            <button (click)="updateQuantity(item, item.quantite + 1)">+</button>
          </div>

          <!-- Sous-total -->
          <div class="subtotal">
            {{ formatPrice(getItemSubtotal(item)) }}
          </div>
        </div>

        <!-- Boutons d'action -->
        <div class="item-actions">
          <button class="action-btn favorite-btn" (click)="moveToFavorites(item)" title="Déplacer vers favoris">♡</button>
          <button class="action-btn remove-btn" (click)="removeItem(item.id)" title="Supprimer cet article">×</button>
        </div>
      </div>
    </div>

    <div class="promo-section">
      <input [(ngModel)]="promoCode" placeholder="Code promo">
      <button (click)="applyPromoCode()" [disabled]="!promoCode">Appliquer</button>
    </div>

    <div class="cart-summary">
      <div class="summary-row">
        <span>Sous-total ({{ getTotalItems() }} articles)</span>
        <span>{{ formatPrice(getSubtotalBeforePromo()) }}</span>
      </div>

      <div class="summary-row" *ngIf="getTotalSavings() > 0">
        <span class="savings">Économies</span>
        <span class="savings">-{{ formatPrice(getTotalSavings()) }}</span>
      </div>

      <div class="summary-row" *ngIf="cart?.codePromoApplique">
        <span>Code promo: {{ cart.codePromoApplique }}</span>
        <span class="promo-applied">✓</span>
      </div>

      <div class="summary-row total">
        <span>Total</span>
        <span>{{ formatPrice(getCartTotal()) }}</span>
      </div>
    </div>

    <!-- Alerte stock insuffisant -->
    <div *ngIf="hasOutOfStockItems()" class="stock-alert">
      <div class="alert-header">
        <span class="alert-icon">⚠️</span>
        <h4>Attention - Stock insuffisant</h4>
      </div>
      <p>Les produits suivants ne sont pas disponibles en quantité suffisante :</p>
      <ul>
        <li *ngFor="let item of getOutOfStockItems()">
          <strong>{{ item.nomProduit }}</strong> -
          Demandé: {{ item.quantite }},
          Disponible: {{ item.produitDetails?.stock || 0 }}
        </li>
      </ul>
      <p><strong>Veuillez ajuster les quantités pour pouvoir passer votre commande.</strong></p>
    </div>

    <div class="cart-actions">
      <button class="clear-btn" (click)="clearCart()">Vider le panier</button>
      <button
        class="checkout-btn"
        (click)="passerCommande()"
        [disabled]="loading || !cart || cart.items.length === 0 || hasOutOfStockItems()"
        [class.disabled]="hasOutOfStockItems()"
      >
        <span *ngIf="loading">Traitement...</span>
        <span *ngIf="!loading && !hasOutOfStockItems()">Passer la commande</span>
        <span *ngIf="!loading && hasOutOfStockItems()">Stock insuffisant</span>
      </button>
    </div>
  </div>

  <ng-template #emptyCart>
    <div class="empty-cart">
      <img src="assets/images/empty-cart.png" alt="Panier vide">
      <h3>Votre panier est vide</h3>
      <p>Parcourez nos produits et ajoutez des articles à votre panier</p>
      <button routerLink="/products">Voir les produits</button>
    </div>
  </ng-template>
</div>
