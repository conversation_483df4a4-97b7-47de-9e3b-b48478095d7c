import {
  Component,
  EventEmitter,
  Input,
  OnD<PERSON>roy,
  OnInit,
  Output,
} from '@angular/core';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Router } from '@angular/router';
import { Subscription, Subject, takeUntil, forkJoin } from 'rxjs';
import { ThemeService } from 'src/app/theme.service';
import { FormeService } from 'src/app/services/forme.service';
import { MarqueService } from 'src/app/services/marque.service';
import { FournisseurService } from 'src/app/services/fournisseur.service';
import { CategorieService } from 'src/app/services/categorie.service';
import { FormeDto } from 'src/app/models/FormeDto';
import { CategorieDto } from 'src/app/models/CategorieDto';
import { MarqueDto } from 'src/app/models/MarqueDto';
import { FournisseurDto } from 'src/app/models/FournisseurDto';
import { NotificationService } from 'src/app/services/notification.service';
import { NotificationClient } from 'src/app/models/NotificationClient';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from 'src/app/auth/auth.service';
import { NotificationIconComponent } from '../notification-icon/notification-icon.component';
@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  standalone: false,
  providers: [FormeService, ThemeService],
})
export class HeaderComponent implements OnInit, OnDestroy {
  @Output() closeMenuEvent = new EventEmitter<void>();

  isDesktop: boolean = true;
  isMobile: boolean = false;
  isMenuOpen: boolean = false;
  isDarkMode: boolean = false;

  isInSubCategories: boolean = false;
  isInMarques: boolean = false;
  isInFournisseurs: boolean = false;

  hoveredCategory: CategorieDto | null = null;
  hoveredMarqueMenu: boolean = false;
  hoveredFournisseurMenu: boolean = false;
  searchQuery: string = '';
  formes: FormeDto[] = [];
  categories: CategorieDto[] = [];
  marques: MarqueDto[] = [];
  fournisseurs: FournisseurDto[] = [];
  notifications: NotificationClient[] = [];

  showNotificationsPanel = false;

  unreadCount = 0;

  private timerType = null as unknown as ReturnType<typeof setTimeout>;
  private timers: {
    category: ReturnType<typeof setTimeout> | null;
    marque: ReturnType<typeof setTimeout> | null;
    fournisseur: ReturnType<typeof setTimeout> | null;
  } = {
    category: null,
    marque: null,
    fournisseur: null,
  };

  private destroy$ = new Subject<void>();
  private resizeSubscription!: Subscription;

  constructor(
    private themeService: ThemeService,
    private breakpointObserver: BreakpointObserver,
    private router: Router,
    private formeService: FormeService,
    private categorieService: CategorieService,
    private marqueService: MarqueService,
    private fournisseurService: FournisseurService,
    private notificationService: NotificationService,
    private snackBar: MatSnackBar,
    private authService: AuthService
  ) {
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe((isDarkMode) => {
        this.isDarkMode = isDarkMode;
      });
  }
  goToProfile(): void {
    const user = this.authService.getCurrentUser();
    if (user) {
      this.router.navigate(['/user/profile']);
    } else {
      localStorage.setItem('redirectAfterLogin', '/user/profile');
      this.router.navigate(['/auth/login']);
    }
    //this.navigateIfLoggedIn('/user/profile');
  }

  goToFavorites(): void {
    this.navigateIfLoggedIn('/user/favorites');
  }

  goToCart(): void {
    this.navigateIfLoggedIn('/user/cart');
  }

  private navigateIfLoggedIn(targetUrl: string): void {
    const user = this.authService.getCurrentUser();
    console.log('User:', user);
    if (user) {
      this.router.navigate([targetUrl]);
    } else {
      localStorage.setItem('redirectAfterLogin', targetUrl);
      this.router.navigate(['/auth/login']);
    }
  }

  private updateLayout(): void {
    this.isMobile = window.innerWidth <= 1400;
    this.isDesktop = !this.isMobile;
  }

  private clearTimer(timerKey: keyof typeof this.timers): void {
    if (this.timers[timerKey]) {
      clearTimeout(this.timers[timerKey] as ReturnType<typeof setTimeout>);
      this.timers[timerKey] = null;
    }
  }

  ngOnInit(): void {
    this.loadAllData();
    this.setupLayoutObserver();
  }
  private showError(message: string): void {
    console.error(message);
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'right',
      verticalPosition: 'top',
    });
  }

  private loadAllData(): void {
    forkJoin([
      this.formeService.getAll(),
      this.categorieService.getAll(),
      this.marqueService.getAll(),
      this.fournisseurService.getAll(),
    ])
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: ([formes, categories, marques, fournisseurs]) => {
          this.formes = formes;
          this.categories = categories;
          this.marques = marques;
          this.fournisseurs = fournisseurs;
        },
        error: (err) => this.showError(`Erreur chargement: ${err.message}`),
      });
  }

  private setupLayoutObserver(): void {
    this.updateLayout();
    this.resizeSubscription = this.breakpointObserver
      .observe([Breakpoints.Handset, Breakpoints.Web])
      .subscribe(() => this.updateLayout());
    window.addEventListener('resize', this.updateLayout.bind(this));
  }

  private clearAllTimers(): void {
    (Object.keys(this.timers) as Array<keyof typeof this.timers>).forEach(
      (key) => {
        if (this.timers[key]) {
          clearTimeout(this.timers[key] as ReturnType<typeof setTimeout>);
          this.timers[key] = null;
        }
      }
    );
  }

  private handleHoverEnter<T>(
    entity: T | boolean,
    hoveredProp:
      | 'hoveredCategory'
      | 'hoveredMarqueMenu'
      | 'hoveredFournisseurMenu',
    isInProp: 'isInSubCategories' | 'isInMarques' | 'isInFournisseurs',
    timerKey: keyof typeof this.timers
  ): void {
    if (this.timers[timerKey]) {
      clearTimeout(this.timers[timerKey] as ReturnType<typeof setTimeout>);
    }
    this[hoveredProp] = entity as any;
    this[isInProp] = false;
  }

  private handleHoverLeave(
    isInProp: 'isInSubCategories' | 'isInMarques' | 'isInFournisseurs',
    hoveredProp:
      | 'hoveredCategory'
      | 'hoveredMarqueMenu'
      | 'hoveredFournisseurMenu',
    timerKey: keyof typeof this.timers
  ): void {
    this.timers[timerKey] = setTimeout(() => {
      if (!this[isInProp]) {
        if (hoveredProp === 'hoveredCategory') {
          this[hoveredProp] = null;
        } else {
          this[hoveredProp] = false;
        }
      }
    }, 200) as unknown as ReturnType<typeof setTimeout>;
  }

  private handleItemsEnter(
    isInProp: 'isInSubCategories' | 'isInMarques' | 'isInFournisseurs',
    timerKey: keyof typeof this.timers
  ): void {
    if (this.timers[timerKey]) {
      clearTimeout(this.timers[timerKey] as ReturnType<typeof setTimeout>);
    }
    this[isInProp] = true;
  }

  private handleItemsLeave(
    isInProp: 'isInSubCategories' | 'isInMarques' | 'isInFournisseurs',
    hoveredProp:
      | 'hoveredCategory'
      | 'hoveredMarqueMenu'
      | 'hoveredFournisseurMenu',
    timerKey: keyof typeof this.timers
  ): void {
    this[isInProp] = false;
    this.timers[timerKey] = setTimeout(() => {
      if (hoveredProp === 'hoveredCategory') {
        this[hoveredProp] = null;
      } else {
        this[hoveredProp] = false;
      }
    }, 200) as unknown as ReturnType<typeof setTimeout>;
  }

  navigateTo(path: string, id: number): void {
    this.router.navigate([path, id]);
    this.isMenuOpen = false;
    this.closeMenuEvent.emit();
  }

  onCategoryMouseEnter = (category: CategorieDto) =>
    this.handleHoverEnter(
      category,
      'hoveredCategory',
      'isInSubCategories',
      'category'
    );

  onCategoryMouseLeave = () =>
    this.handleHoverLeave('isInSubCategories', 'hoveredCategory', 'category');

  onSubCategoriesEnter = () =>
    this.handleItemsEnter('isInSubCategories', 'category');

  onSubCategoriesLeave = () =>
    this.handleItemsLeave('isInSubCategories', 'hoveredCategory', 'category');

  onMarquesListEnter() {
    this.handleItemsEnter('isInMarques', 'marque');
  }

  onMarquesListLeave() {
    this.handleItemsLeave('isInMarques', 'hoveredMarqueMenu', 'marque');
  }
  onMarqueButtonMouseEnter() {
    this.handleHoverEnter(true, 'hoveredMarqueMenu', 'isInMarques', 'marque');
  }

  onMarqueButtonMouseLeave() {
    this.handleHoverLeave('isInMarques', 'hoveredMarqueMenu', 'marque');
  }

  onFournisseurButtonMouseEnter() {
    this.handleHoverEnter(
      true,
      'hoveredFournisseurMenu',
      'isInFournisseurs',
      'fournisseur'
    );
  }

  onFournisseurButtonMouseLeave() {
    this.handleHoverLeave(
      'isInFournisseurs',
      'hoveredFournisseurMenu',
      'fournisseur'
    );
  }

  onFournisseursListEnter() {
    this.handleItemsEnter('isInFournisseurs', 'fournisseur');
  }

  onFournisseursListLeave() {
    this.handleItemsLeave(
      'isInFournisseurs',
      'hoveredFournisseurMenu',
      'fournisseur'
    );
  }

  navigateToCategory(category: CategorieDto): void {
    if (!category || !category.id) {
      console.error('Impossible de naviguer : ID de catégorie manquant');
      return;
    }
    this.navigateTo('/products/category', category.id);
  }

  navigateToMarque(marque: MarqueDto): void {
    this.navigateTo('/products/marque', marque.id);
  }

  navigateToFournisseur(fournisseur: FournisseurDto): void {
    console.log('Fournisseur ID:', fournisseur.id);
    this.navigateTo('/products/fournisseur', fournisseur.id);
  }
  toggleTheme(): void {
    this.themeService.toggleTheme();
  }

  toggleMenu(): void {
    this.isMenuOpen = !this.isMenuOpen;
    if (!this.isMenuOpen) {
      this.hoveredCategory = null;
      this.hoveredMarqueMenu = false;
      this.hoveredFournisseurMenu = false;
      this.clearAllTimers();
    }
  }

  onSearch(event: Event): void {
    event.preventDefault();
    if (this.searchQuery.trim()) {
      this.router.navigate(['products/search'], {
        queryParams: { q: this.searchQuery.trim() },
      });
      if (this.isMenuOpen) {
        this.toggleMenu();
      }
      this.searchQuery = '';
    }
  }

  ngOnDestroy(): void {
    this.clearAllTimers();
    if (this.resizeSubscription) {
      this.resizeSubscription.unsubscribe();
    }
    this.destroy$.next();
    this.destroy$.complete();
    window.removeEventListener('resize', this.updateLayout.bind(this));
  }
}