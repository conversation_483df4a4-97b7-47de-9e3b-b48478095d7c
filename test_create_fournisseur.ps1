# Test de création d'un fournisseur et test des promotions
$baseUrl = "http://localhost:5014/api"

Write-Host "=== CREATION FOURNISSEUR ET TEST PROMOTIONS ===" -ForegroundColor Cyan

# 1. Créer un fournisseur de test
Write-Host "`n1. Création d'un fournisseur de test..." -ForegroundColor Yellow

$fournisseurData = @{
    nom = "Test"
    prenom = "Fournisseur"
    email = "<EMAIL>"
    password = "Test123!"
    phoneNumber = "12345678"
    raisonSociale = "Test Fournisseur SARL"
    matriculeFiscale = "123456789"
    rib = "12345678901234567890"
    codeBanque = "12345"
    commission = 5.0
    delaiPreparationJours = 2
    fraisLivraisonBase = 10.0
    description = "Fournisseur de test pour les promotions"
} | ConvertTo-Json

$headers = @{"Content-Type" = "application/json"}

try {
    $newFournisseur = Invoke-RestMethod -Uri "$baseUrl/Auth/register/fournisseur" -Method Post -Headers $headers -Body $fournisseurData
    Write-Host "✅ Fournisseur créé !" -ForegroundColor Green
    Write-Host "ID: $($newFournisseur.id) - Nom: $($newFournisseur.nom) $($newFournisseur.prenom)" -ForegroundColor White
    
} catch {
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "⚠️ Fournisseur existe déjà, on continue..." -ForegroundColor Yellow
    } else {
        Write-Host "❌ Erreur création fournisseur: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}

# 2. Connexion du fournisseur
Write-Host "`n2. Connexion du fournisseur..." -ForegroundColor Yellow

$loginData = @{
    email = "<EMAIL>"
    motDePasse = "Test123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
    Write-Host "✅ Connexion réussie !" -ForegroundColor Green
    Write-Host "Fournisseur: $($loginResponse.utilisateur.nom) $($loginResponse.utilisateur.prenom)" -ForegroundColor White
    Write-Host "Role: $($loginResponse.utilisateur.role)" -ForegroundColor White
    
    # Ajouter le token aux headers
    $headers["Authorization"] = "Bearer $($loginResponse.token)"
    
} catch {
    Write-Host "❌ Erreur de connexion: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
    exit 1
}

# 3. Test de l'endpoint my-promotions
Write-Host "`n3. Test de l'endpoint my-promotions..." -ForegroundColor Yellow

try {
    $myPromotions = Invoke-RestMethod -Uri "$baseUrl/promotions/my-promotions" -Method Get -Headers $headers
    Write-Host "✅ My-promotions récupérées: $($myPromotions.Count)" -ForegroundColor Green
    
    if ($myPromotions.Count -gt 0) {
        Write-Host "Mes promotions:" -ForegroundColor White
        foreach ($promo in $myPromotions) {
            $status = if ($promo.estValide) { "✅ Active" } else { "❌ Inactive" }
            Write-Host "  - $($promo.nomAffichage) ($($promo.type)) - $($promo.pourcentageRemise)% - $status" -ForegroundColor Gray
            if ($promo.codePromo) {
                Write-Host "    Code: $($promo.codePromo)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "Aucune promotion trouvée pour ce fournisseur" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "❌ Erreur my-promotions: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 4. Créer une promotion de test
Write-Host "`n4. Création d'une promotion de test..." -ForegroundColor Yellow

$promoData = @{
    type = "CodePromo"
    pourcentageRemise = 25
    dateDebut = (Get-Date).ToString("yyyy-MM-dd")
    dateFin = (Get-Date).AddDays(30).ToString("yyyy-MM-dd")
    codePromo = "TEST25"
    nomAffichage = "Promotion Test Fournisseur"
    description = "Promotion de test créée via API"
    appliquerSurHT = $false
} | ConvertTo-Json

try {
    $newPromo = Invoke-RestMethod -Uri "$baseUrl/promotions" -Method Post -Headers $headers -Body $promoData
    Write-Host "✅ Promotion créée !" -ForegroundColor Green
    Write-Host "ID: $($newPromo.id) - Nom: $($newPromo.nomAffichage)" -ForegroundColor White
    
} catch {
    Write-Host "❌ Erreur création promotion: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    Write-Host "Détails: $($_.ErrorDetails.Message)" -ForegroundColor Red
}

# 5. Vérifier à nouveau my-promotions
Write-Host "`n5. Vérification finale my-promotions..." -ForegroundColor Yellow

try {
    $finalPromotions = Invoke-RestMethod -Uri "$baseUrl/promotions/my-promotions" -Method Get -Headers $headers
    Write-Host "✅ Promotions finales: $($finalPromotions.Count)" -ForegroundColor Green
    
    foreach ($promo in $finalPromotions) {
        $status = if ($promo.estValide) { "✅ Active" } else { "❌ Inactive" }
        Write-Host "  - $($promo.nomAffichage) ($($promo.type)) - $($promo.pourcentageRemise)% - $status" -ForegroundColor White
        if ($promo.codePromo) {
            Write-Host "    Code: $($promo.codePromo)" -ForegroundColor Gray
        }
    }
    
} catch {
    Write-Host "❌ Erreur vérification finale: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== FIN DU TEST ===" -ForegroundColor Cyan
