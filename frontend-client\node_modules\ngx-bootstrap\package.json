{"name": "ngx-bootstrap", "version": "19.0.2", "description": "<PERSON><PERSON> Bootstrap", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "schematics": "./schematics/collection.json", "peerDependencies": {"@angular/animations": "^19.0.1", "@angular/common": "^19.0.1", "@angular/core": "^19.0.1", "@angular/forms": "^19.0.1", "rxjs": "^6.5.3 || ^7.4.0"}, "dependencies": {"tslib": "^2.3.0"}, "exports": {"./accordion": {"types": "./accordion/index.d.ts", "esm2022": "./accordion/esm2022/ngx-bootstrap-accordion.mjs", "esm": "./accordion/esm2022/ngx-bootstrap-accordion.mjs", "default": "./accordion/fesm2022/ngx-bootstrap-accordion.mjs"}, "./alert": {"types": "./alert/index.d.ts", "esm2022": "./alert/esm2022/ngx-bootstrap-alert.mjs", "esm": "./alert/esm2022/ngx-bootstrap-alert.mjs", "default": "./alert/fesm2022/ngx-bootstrap-alert.mjs"}, "./buttons": {"types": "./buttons/index.d.ts", "esm2022": "./buttons/esm2022/ngx-bootstrap-buttons.mjs", "esm": "./buttons/esm2022/ngx-bootstrap-buttons.mjs", "default": "./buttons/fesm2022/ngx-bootstrap-buttons.mjs"}, "./carousel": {"types": "./carousel/index.d.ts", "esm2022": "./carousel/esm2022/ngx-bootstrap-carousel.mjs", "esm": "./carousel/esm2022/ngx-bootstrap-carousel.mjs", "default": "./carousel/fesm2022/ngx-bootstrap-carousel.mjs"}, "./chronos": {"types": "./chronos/index.d.ts", "esm2022": "./chronos/esm2022/ngx-bootstrap-chronos.mjs", "esm": "./chronos/esm2022/ngx-bootstrap-chronos.mjs", "default": "./chronos/fesm2022/ngx-bootstrap-chronos.mjs"}, "./collapse": {"types": "./collapse/index.d.ts", "esm2022": "./collapse/esm2022/ngx-bootstrap-collapse.mjs", "esm": "./collapse/esm2022/ngx-bootstrap-collapse.mjs", "default": "./collapse/fesm2022/ngx-bootstrap-collapse.mjs"}, "./component-loader": {"types": "./component-loader/ngx-bootstrap-component-loader.d.ts", "esm2022": "./component-loader/esm2022/ngx-bootstrap-component-loader.mjs", "esm": "./component-loader/esm2022/ngx-bootstrap-component-loader.mjs", "default": "./component-loader/fesm2022/ngx-bootstrap-component-loader.mjs"}, "./datepicker": {"types": "./datepicker/index.d.ts", "esm2022": "./datepicker/esm2022/ngx-bootstrap-datepicker.mjs", "esm": "./datepicker/esm2022/ngx-bootstrap-datepicker.mjs", "default": "./datepicker/fesm2022/ngx-bootstrap-datepicker.mjs"}, "./datepicker/bs-datepicker.css": {"style": "./datepicker/bs-datepicker.css"}, "./dropdown": {"types": "./dropdown/index.d.ts", "esm2022": "./dropdown/esm2022/ngx-bootstrap-dropdown.mjs", "esm": "./dropdown/esm2022/ngx-bootstrap-dropdown.mjs", "default": "./dropdown/fesm2022/ngx-bootstrap-dropdown.mjs"}, "./focus-trap": {"types": "./focus-trap/ngx-bootstrap-focus-trap.d.ts", "esm2022": "./focus-trap/esm2022/ngx-bootstrap-focus-trap.mjs", "esm": "./focus-trap/esm2022/ngx-bootstrap-focus-trap.mjs", "default": "./focus-trap/fesm2022/ngx-bootstrap-focus-trap.mjs"}, "./locale": {"types": "./locale/index.d.ts", "esm2022": "./locale/esm2022/ngx-bootstrap-locale.mjs", "esm": "./locale/esm2022/ngx-bootstrap-locale.mjs", "default": "./locale/fesm2022/ngx-bootstrap-locale.mjs"}, "./mini-ngrx": {"types": "./mini-ngrx/ngx-bootstrap-mini-ngrx.d.ts", "esm2022": "./mini-ngrx/esm2022/ngx-bootstrap-mini-ngrx.mjs", "esm": "./mini-ngrx/esm2022/ngx-bootstrap-mini-ngrx.mjs", "default": "./mini-ngrx/fesm2022/ngx-bootstrap-mini-ngrx.mjs"}, "./modal": {"types": "./modal/index.d.ts", "esm2022": "./modal/esm2022/ngx-bootstrap-modal.mjs", "esm": "./modal/esm2022/ngx-bootstrap-modal.mjs", "default": "./modal/fesm2022/ngx-bootstrap-modal.mjs"}, "./pagination": {"types": "./pagination/index.d.ts", "esm2022": "./pagination/esm2022/ngx-bootstrap-pagination.mjs", "esm": "./pagination/esm2022/ngx-bootstrap-pagination.mjs", "default": "./pagination/fesm2022/ngx-bootstrap-pagination.mjs"}, "./popover": {"types": "./popover/index.d.ts", "esm2022": "./popover/esm2022/ngx-bootstrap-popover.mjs", "esm": "./popover/esm2022/ngx-bootstrap-popover.mjs", "default": "./popover/fesm2022/ngx-bootstrap-popover.mjs"}, "./positioning": {"types": "./positioning/index.d.ts", "esm2022": "./positioning/esm2022/ngx-bootstrap-positioning.mjs", "esm": "./positioning/esm2022/ngx-bootstrap-positioning.mjs", "default": "./positioning/fesm2022/ngx-bootstrap-positioning.mjs"}, "./progressbar": {"types": "./progressbar/index.d.ts", "esm2022": "./progressbar/esm2022/ngx-bootstrap-progressbar.mjs", "esm": "./progressbar/esm2022/ngx-bootstrap-progressbar.mjs", "default": "./progressbar/fesm2022/ngx-bootstrap-progressbar.mjs"}, "./rating": {"types": "./rating/index.d.ts", "esm2022": "./rating/esm2022/ngx-bootstrap-rating.mjs", "esm": "./rating/esm2022/ngx-bootstrap-rating.mjs", "default": "./rating/fesm2022/ngx-bootstrap-rating.mjs"}, "./sortable": {"types": "./sortable/index.d.ts", "esm2022": "./sortable/esm2022/ngx-bootstrap-sortable.mjs", "esm": "./sortable/esm2022/ngx-bootstrap-sortable.mjs", "default": "./sortable/fesm2022/ngx-bootstrap-sortable.mjs"}, "./tabs": {"types": "./tabs/index.d.ts", "esm2022": "./tabs/esm2022/ngx-bootstrap-tabs.mjs", "esm": "./tabs/esm2022/ngx-bootstrap-tabs.mjs", "default": "./tabs/fesm2022/ngx-bootstrap-tabs.mjs"}, "./timepicker": {"types": "./timepicker/index.d.ts", "esm2022": "./timepicker/esm2022/ngx-bootstrap-timepicker.mjs", "esm": "./timepicker/esm2022/ngx-bootstrap-timepicker.mjs", "default": "./timepicker/fesm2022/ngx-bootstrap-timepicker.mjs"}, "./tooltip": {"types": "./tooltip/index.d.ts", "esm2022": "./tooltip/esm2022/ngx-bootstrap-tooltip.mjs", "esm": "./tooltip/esm2022/ngx-bootstrap-tooltip.mjs", "default": "./tooltip/fesm2022/ngx-bootstrap-tooltip.mjs"}, "./typeahead": {"types": "./typeahead/index.d.ts", "esm2022": "./typeahead/esm2022/ngx-bootstrap-typeahead.mjs", "esm": "./typeahead/esm2022/ngx-bootstrap-typeahead.mjs", "default": "./typeahead/fesm2022/ngx-bootstrap-typeahead.mjs"}, "./utils": {"types": "./utils/index.d.ts", "esm2022": "./utils/esm2022/ngx-bootstrap-utils.mjs", "esm": "./utils/esm2022/ngx-bootstrap-utils.mjs", "default": "./utils/fesm2022/ngx-bootstrap-utils.mjs"}, "./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/ngx-bootstrap.mjs"}}, "sideEffects": false, "publishConfig": {"registry": "https://registry.npmjs.org/", "tag": "next"}, "repository": {"type": "git", "url": "git+ssh://**************/valor-software/ngx-bootstrap.git"}, "bugs": {"url": "https://github.com/valor-software/ngx-bootstrap/issues"}, "homepage": "https://github.com/valor-software/ngx-bootstrap#readme", "keywords": ["angular", "bootstap", "ng", "ng2", "angular2", "twitter-bootstrap"], "module": "fesm2022/ngx-bootstrap.mjs", "typings": "index.d.ts"}