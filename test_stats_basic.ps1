# Test basique des statistiques
$baseUrl = "http://localhost:5014/api"

Write-Host "=== TEST STATISTIQUES BASIQUE ===" -ForegroundColor Cyan

# Connexion
$loginData = @{
    email = "<EMAIL>"
    motDePasse = "TestDebug123!"
} | ConvertTo-Json

$headers = @{"Content-Type" = "application/json"}

Write-Host "Connexion..." -ForegroundColor Yellow
$loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
Write-Host "✅ Connecté - ID: $($loginResponse.utilisateur.id)" -ForegroundColor Green

$headers["Authorization"] = "Bearer $($loginResponse.token)"
$fournisseurId = $loginResponse.utilisateur.id

# Test statistiques
Write-Host "Test statistiques..." -ForegroundColor Yellow
$stats = Invoke-RestMethod -Uri "$baseUrl/StatistiquesFournisseur/dashboard/$fournisseurId" -Method Get -Headers $headers

Write-Host "✅ Statistiques récupérées !" -ForegroundColor Green
Write-Host "Total produits: $($stats.totalProduits)" -ForegroundColor White
Write-Host "CA mensuel: $([math]::Round($stats.chiffreAffaireMensuel, 2)) TND" -ForegroundColor Green

Write-Host "=== FIN ===" -ForegroundColor Cyan
