import { Component, OnInit } from '@angular/core';
import { CommandeService } from 'src/app/services/commande.service';
import { AuthService } from 'src/app/auth/auth.service';
import { CommandeDto, StatutCommande} from 'src/app/models/CommandeDto';
import { Router } from '@angular/router';

@Component({
  selector: 'app-orders',
  templateUrl: './orders.component.html',
  styleUrls: ['./orders.component.scss'],
  standalone: false
})
export class OrdersComponent implements OnInit {
  allOrders: CommandeDto[] = [];
  filteredOrders: CommandeDto[] = [];
  isLoading = true;
  errorMessage = '';
  expandedOrders: Set<number> = new Set();

  statusLabels: Record<StatutCommande, string> = {
    'Brouillon': 'Brouillon',
    'EnAttente': 'En attente de paiement',
    'Validee': 'Validée',
    'EnPreparation': 'En préparation',
    'Expediee': 'Expédiée',
    'Livree': 'Livrée',
    'Annulee': 'Annulée'
  };

  supplierStatusLabels: Record<string, string> = {
    'EnAttente': 'En attente',
    'Confirmee': 'Confirmée',
    'EnPreparation': 'En préparation',
    'Expediee': 'Expédiée',
    'Livree': 'Livrée',
    'Annulee': 'Annulée'
  };

  constructor(
    private commandeService: CommandeService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit() {
    // Vérifier si l'utilisateur est connecté
    if (!this.authService.isAuthenticated()) {
      console.log('Utilisateur non connecté, redirection vers login');
      this.router.navigate(['/auth/login']);
      return;
    }

    this.loadOrders();
  }

  loadOrders() {
    this.isLoading = true;
    this.errorMessage = '';

    // Récupérer l'utilisateur connecté
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser || !currentUser.id) {
      console.error('Impossible de récupérer l\'utilisateur connecté');
      this.errorMessage = 'Erreur d\'authentification. Veuillez vous reconnecter.';
      this.isLoading = false;
      this.router.navigate(['/auth/login']);
      return;
    }

    console.log('🛒 Chargement des commandes pour le client:', currentUser.id);

    this.commandeService.getByClient(currentUser.id).subscribe({
      next: (orders) => {
        console.log('📦 Commandes reçues:', orders);
        this.allOrders = orders.sort((a, b) =>
          new Date(b.dateCreation).getTime() - new Date(a.dateCreation).getTime()
        );
        this.filteredOrders = [...this.allOrders];
        this.isLoading = false;

        if (orders.length === 0) {
          console.log('ℹ️ Aucune commande trouvée pour ce client');
        }
      },
      error: (err: any) => {
        console.error('❌ Erreur lors du chargement des commandes:', err);
        this.errorMessage = 'Erreur lors du chargement de vos commandes. Veuillez réessayer.';
        this.isLoading = false;
      }
    });
  }

  filterOrders(status: string) {
    if (status === 'all') {
      this.filteredOrders = [...this.allOrders];
    } else {
      this.filteredOrders = this.allOrders.filter(
        order => order.statut === status
      );
    }
  }

  getStatusLabel(status: StatutCommande): string {
    return this.statusLabels[status] || status;
  }

  cancelOrder(orderId: number): void {
    if (confirm('Voulez-vous vraiment annuler cette commande ?')) {
      this.commandeService.annulerCommande(orderId).subscribe({
        next: () => {
          const order = this.allOrders.find(o => o.id === orderId);
          if (order) order.statut = 'Annulee';
          this.filterOrders('all');
        },
        error: (err: any) => console.error('Erreur lors de l\'annulation', err)
      });
    }
  }

  getTotalItemsCount(order: CommandeDto): number {
    return order.details?.reduce((total, detail) => total + (detail.quantite || 0), 0) || 0;
  }

  getSupplierStatusLabel(status: string): string {
    return this.supplierStatusLabels[status] || status;
  }

  toggleOrderDetails(orderId: number): void {
    if (this.expandedOrders.has(orderId)) {
      this.expandedOrders.delete(orderId);
    } else {
      this.expandedOrders.add(orderId);
    }
  }

  isOrderExpanded(orderId: number): boolean {
    return this.expandedOrders.has(orderId);
  }

  onImageError(event: any): void {
    event.target.src = 'assets/images/placeholder.png';
  }
}
