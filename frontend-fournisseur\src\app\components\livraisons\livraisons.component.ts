import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { CommandeService } from '../../services/commande.service';
import { LivraisonService } from '../../services/livraison.service';
import { AuthService } from '../../services/auth.service';
import { 
  Livraison, 
  StatutLivraison, 
  CommandeFournisseur,
  StatutCommandeFournisseur
} from '../../models/commande.model';

@Component({
  selector: 'app-livraisons',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './livraisons.component.html',
  styleUrls: ['./livraisons.component.css']
})
export class LivraisonsComponent implements OnInit {
  // Données principales
  livraisons: Livraison[] = [];
  commandes: CommandeFournisseur[] = [];
  livraisonSelectionnee: Livraison | null = null;
  
  // États de l'interface
  isLoading = false;
  error = '';
  successMessage = '';
  
  // Filtres et recherche
  filtreStatut: StatutLivraison | 'all' = 'all';
  searchQuery = '';
  dateDebut = '';
  dateFin = '';
  
  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalLivraisons = 0;
  
  // Modales
  showDetailModal = false;
  showCreateModal = false;
  showUpdateModal = false;
  
  // Statuts disponibles
  statutsLivraison = Object.values(StatutLivraison);
  
  // Formulaire de création/modification
  livraisonForm: Partial<Livraison> = {
    numeroSuivi: '',
    statut: StatutLivraison.EnAttente,
    transporteur: '',
    commentaires: '',
    dateLivraisonPrevue: undefined
  };

  constructor(
    private commandeService: CommandeService,
    private livraisonService: LivraisonService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadLivraisons();
    this.loadCommandes();
    this.initializeDates();
  }

  // ==================== CHARGEMENT DES DONNÉES ====================

  loadLivraisons(): void {
    this.isLoading = true;
    this.error = '';

    const fournisseurId = this.authService.getCurrentUserId();
    if (!fournisseurId) {
      this.error = 'Utilisateur non connecté';
      this.isLoading = false;
      return;
    }

    this.livraisonService.getLivraisonsByFournisseur(fournisseurId)
      .subscribe({
        next: (livraisons) => {
          this.livraisons = this.filterLivraisons(livraisons);
          this.totalLivraisons = this.livraisons.length;
          this.isLoading = false;
          console.log('✅ Livraisons chargées:', this.livraisons);
        },
        error: (error) => {
          console.error('❌ Erreur lors du chargement des livraisons:', error);
          this.error = 'Erreur lors du chargement des livraisons';
          this.isLoading = false;
          
          // Utiliser des données de test en cas d'erreur
          this.loadMockData();
        }
      });
  }

  loadCommandes(): void {
    const fournisseurId = this.authService.getCurrentUserId();
    if (!fournisseurId) return;

    this.commandeService.getCommandesByFournisseur(fournisseurId, StatutCommandeFournisseur.Prete)
      .subscribe({
        next: (commandes) => {
          this.commandes = commandes.filter(c => 
            c.statut === StatutCommandeFournisseur.Prete || 
            c.statut === StatutCommandeFournisseur.Expediee
          );
          console.log('✅ Commandes prêtes pour livraison:', this.commandes);
        },
        error: (error) => {
          console.error('❌ Erreur lors du chargement des commandes:', error);
        }
      });
  }

  private filterLivraisons(livraisons: Livraison[]): Livraison[] {
    let filtered = [...livraisons];
    
    // Filtrer par statut
    if (this.filtreStatut !== 'all') {
      filtered = filtered.filter(l => l.statut === this.filtreStatut);
    }
    
    // Filtrer par recherche
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(l => 
        l.numeroSuivi.toLowerCase().includes(query) ||
        l.transporteur?.toLowerCase().includes(query)
      );
    }
    
    // Filtrer par dates
    if (this.dateDebut) {
      const debut = new Date(this.dateDebut);
      filtered = filtered.filter(l => 
        l.dateExpedition && new Date(l.dateExpedition) >= debut
      );
    }
    
    if (this.dateFin) {
      const fin = new Date(this.dateFin);
      filtered = filtered.filter(l => 
        l.dateExpedition && new Date(l.dateExpedition) <= fin
      );
    }
    
    return filtered;
  }

  // ==================== GESTION DES FILTRES ====================

  applyFilters(): void {
    this.currentPage = 1;
    this.loadLivraisons();
  }

  resetFilters(): void {
    this.filtreStatut = 'all';
    this.searchQuery = '';
    this.dateDebut = '';
    this.dateFin = '';
    this.currentPage = 1;
    this.loadLivraisons();
  }

  private initializeDates(): void {
    const today = new Date();
    const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
    
    this.dateDebut = oneMonthAgo.toISOString().split('T')[0];
    this.dateFin = today.toISOString().split('T')[0];
  }

  // ==================== ACTIONS SUR LES LIVRAISONS ====================

  voirDetails(livraison: Livraison): void {
    this.livraisonSelectionnee = livraison;
    this.showDetailModal = true;
  }

  creerLivraison(): void {
    this.resetForm();
    this.showCreateModal = true;
  }

  modifierLivraison(livraison: Livraison): void {
    this.livraisonSelectionnee = livraison;
    this.livraisonForm = { ...livraison };
    this.showUpdateModal = true;
  }

  confirmerCreation(): void {
    if (!this.isFormValid()) {
      this.error = 'Veuillez remplir tous les champs obligatoires';
      return;
    }

    this.isLoading = true;
    
    this.livraisonService.createLivraison(this.livraisonForm as Livraison)
      .subscribe({
        next: (response) => {
          this.successMessage = 'Livraison créée avec succès';
          this.showCreateModal = false;
          this.loadLivraisons();
          this.resetForm();
        },
        error: (error) => {
          console.error('❌ Erreur lors de la création:', error);
          this.error = 'Erreur lors de la création de la livraison';
          this.isLoading = false;
        }
      });
  }

  confirmerModification(): void {
    if (!this.livraisonSelectionnee || !this.isFormValid()) {
      this.error = 'Veuillez remplir tous les champs obligatoires';
      return;
    }

    this.isLoading = true;
    
    this.livraisonService.updateLivraison(this.livraisonSelectionnee.id, this.livraisonForm)
      .subscribe({
        next: (response) => {
          this.successMessage = 'Livraison mise à jour avec succès';
          this.showUpdateModal = false;
          this.loadLivraisons();
          this.resetForm();
        },
        error: (error) => {
          console.error('❌ Erreur lors de la mise à jour:', error);
          this.error = 'Erreur lors de la mise à jour de la livraison';
          this.isLoading = false;
        }
      });
  }

  // ==================== UTILITAIRES ====================

  getStatutClass(statut: StatutLivraison): string {
    switch (statut) {
      case StatutLivraison.EnAttente: return 'status-waiting';
      case StatutLivraison.EnCours: return 'status-in-progress';
      case StatutLivraison.Livree: return 'status-delivered';
      case StatutLivraison.Echec: return 'status-failed';
      case StatutLivraison.Retournee: return 'status-returned';
      default: return 'status-unknown';
    }
  }

  getStatutText(statut: StatutLivraison): string {
    switch (statut) {
      case StatutLivraison.EnAttente: return 'En attente';
      case StatutLivraison.EnCours: return 'En cours';
      case StatutLivraison.Livree: return 'Livrée';
      case StatutLivraison.Echec: return 'Échec';
      case StatutLivraison.Retournee: return 'Retournée';
      default: return 'Inconnu';
    }
  }

  formatDate(date: Date | string | undefined): string {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  private isFormValid(): boolean {
    return !!(
      this.livraisonForm.numeroSuivi?.trim() &&
      this.livraisonForm.statut &&
      this.livraisonForm.commandeId
    );
  }

  private resetForm(): void {
    this.livraisonForm = {
      numeroSuivi: '',
      statut: StatutLivraison.EnAttente,
      transporteur: '',
      commentaires: '',
      dateLivraisonPrevue: undefined
    };
  }

  // ==================== FERMETURE DES MODALES ====================

  closeDetailModal(): void {
    this.showDetailModal = false;
    this.livraisonSelectionnee = null;
  }

  closeCreateModal(): void {
    this.showCreateModal = false;
    this.resetForm();
  }

  closeUpdateModal(): void {
    this.showUpdateModal = false;
    this.livraisonSelectionnee = null;
    this.resetForm();
  }

  // ==================== DONNÉES DE TEST ====================

  private loadMockData(): void {
    console.log('🔄 Chargement des données de test pour les livraisons...');
    
    this.livraisons = [
      {
        id: 1,
        numeroSuivi: 'LIV-2024-001',
        statut: StatutLivraison.EnCours,
        dateExpedition: new Date('2024-07-10'),
        dateLivraisonPrevue: new Date('2024-07-15'),
        transporteur: 'DHL Express',
        commentaires: 'Livraison express demandée',
        commandeId: 1,
        fournisseurId: 1
      },
      {
        id: 2,
        numeroSuivi: 'LIV-2024-002',
        statut: StatutLivraison.Livree,
        dateExpedition: new Date('2024-07-08'),
        dateLivraisonPrevue: new Date('2024-07-12'),
        dateLivraisonReelle: new Date('2024-07-11'),
        transporteur: 'Colissimo',
        commentaires: 'Livraison réussie',
        commandeId: 2,
        fournisseurId: 1
      },
      {
        id: 3,
        numeroSuivi: 'LIV-2024-003',
        statut: StatutLivraison.EnAttente,
        transporteur: 'UPS',
        commandeId: 3,
        fournisseurId: 1
      }
    ];
    
    this.totalLivraisons = this.livraisons.length;
    this.isLoading = false;
  }
}
