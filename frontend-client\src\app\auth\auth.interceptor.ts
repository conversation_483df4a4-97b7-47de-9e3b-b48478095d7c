import { HttpInterceptorFn } from '@angular/common/http';
import { TokenStorageService } from '../shared/services/token-storage.service';
import { inject } from '@angular/core';

export const authInterceptor: HttpInterceptorFn = (req, next) =>{
  const tokenService = inject(TokenStorageService);
  const token = tokenService.getToken();

  // Logs détaillés pour les requêtes vers l'API avis
  if (req.url.includes('/api/avis/')) {
    console.log('=== INTERCEPTOR DEBUG AVIS ===');
    console.log('🔐 Auth Interceptor - URL:', req.url);
    console.log('🎫 Token présent:', !!token);
    console.log('🎫 Token value:', token);
    console.log('📝 Méthode:', req.method);
    console.log('📋 Body:', req.body);
    console.log('==============================');
  }

  if (!token) {
    console.log('❌ Pas de token - requête sans authentification pour:', req.url);
    return next(req);
  }

  const authReq = req.clone({
    headers: req.headers
      .set('Authorization', `Bearer ${token}`)
      .set('Content-Type', 'application/json')
  });

  if (req.url.includes('/api/avis/')) {
    console.log('✅ Token ajouté à la requête avis');
    console.log('📤 Headers finaux:', authReq.headers.keys());
    console.log('🔑 Authorization header:', authReq.headers.get('Authorization'));
  }

  return next(authReq);
  };
