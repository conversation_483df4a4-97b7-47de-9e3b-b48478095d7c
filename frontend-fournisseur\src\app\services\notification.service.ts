import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface NotificationDto {
  id: number;
  contenu: string;
  dateEnvoi: Date;
  estLue: boolean;
  utilisateurId: number;
}

export interface CreateNotificationDto {
  contenu: string;
  utilisateurId: number;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiUrl = `${environment.apiUrl}/api/notification`;
  private notificationsSubject = new BehaviorSubject<NotificationDto[]>([]);
  private unreadCountSubject = new BehaviorSubject<number>(0);

  public notifications$ = this.notificationsSubject.asObservable();
  public unreadCount$ = this.unreadCountSubject.asObservable();

  constructor(private http: HttpClient) {}

  public getUserNotifications(userId: number): Observable<NotificationDto[]> {
    return this.http.get<NotificationDto[]>(`${this.apiUrl}/user/${userId}`).pipe(
      map(notifs => this.sortNotifications(notifs)),
      tap(notifs => {
        this.notificationsSubject.next(notifs);
        this.updateUnreadCount(notifs);
      })
    );
  }

  public getUnreadNotifications(userId: number): Observable<NotificationDto[]> {
    return this.http.get<NotificationDto[]>(`${this.apiUrl}/user/${userId}/unread`).pipe(
      tap(notifs => this.updateUnreadCount(notifs))
    );
  }

  public markAsRead(notificationId: number): Observable<void> {
    return this.http.patch<void>(`${this.apiUrl}/${notificationId}/read`, {}).pipe(
      tap(() => {
        const updated = this.notificationsSubject.value.map(n =>
          n.id === notificationId ? { ...n, estLue: true } : n
        );
        this.notificationsSubject.next(updated);
        this.updateUnreadCount(updated);
      })
    );
  }

  public createNotification(notification: CreateNotificationDto): Observable<NotificationDto> {
    return this.http.post<NotificationDto>(this.apiUrl, notification);
  }

  /**
   * Envoyer une notification à tous les admins
   */
  public notifierTousLesAdmins(contenu: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/notify-admins`, { contenu });
  }

  /**
   * Récupérer toutes les notifications des admins
   */
  public getAllAdminNotifications(): Observable<NotificationDto[]> {
    return this.http.get<NotificationDto[]>(`${this.apiUrl}/admin/all`);
  }

  public deleteNotification(notificationId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${notificationId}`).pipe(
      tap(() => {
        const updated = this.notificationsSubject.value.filter(n => n.id !== notificationId);
        this.notificationsSubject.next(updated);
        this.updateUnreadCount(updated);
      })
    );
  }

  public deleteAllUserNotifications(userId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/user/${userId}/all`).pipe(
      tap(() => {
        this.notificationsSubject.next([]);
        this.unreadCountSubject.next(0);
      })
    );
  }

  private sortNotifications(notifs: NotificationDto[]): NotificationDto[] {
    return [...notifs].sort((a, b) =>
      new Date(b.dateEnvoi).getTime() - new Date(a.dateEnvoi).getTime()
    );
  }

  private updateUnreadCount(notifs: NotificationDto[]): void {
    const count = notifs.filter(n => !n.estLue).length;
    this.unreadCountSubject.next(count);
  }
}
