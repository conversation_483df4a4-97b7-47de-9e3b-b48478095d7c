namespace WebApiPfe.Models.Dto
{
    public class StatistiquesDashboardDto
    {
        public int TotalProduits { get; set; }
        public int CommandesActives { get; set; }
        public int LivraisonsEnCours { get; set; }
        public decimal ChiffreAffaireMensuel { get; set; }
        public EvolutionVentesDto EvolutionVentes { get; set; } = new();
        public List<ProduitConsulteDto> TopProduits { get; set; } = new();
        public List<CommandeRecenteDto> CommandesRecentes { get; set; } = new();
    }

    public class EvolutionVentesDto
    {
        public decimal MoisActuel { get; set; }
        public decimal MoisPrecedent { get; set; }
        public double PourcentageEvolution { get; set; }
    }

    public class ProduitConsulteDto
    {
        public int Id { get; set; }
        public string Nom { get; set; } = string.Empty;
        public decimal Prix { get; set; }
        public int NombreConsultations { get; set; }
        public string? ImageUrl { get; set; }
        public int Stock { get; set; }
        public string Categorie { get; set; } = string.Empty;
    }

    public class CommandeRecenteDto
    {
        public string Id { get; set; } = string.Empty;
        public string Reference { get; set; } = string.Empty;
        public string Client { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime Date { get; set; }
    }

    public class EvolutionVentesParMoisDto
    {
        public string Mois { get; set; } = string.Empty;
        public string NomMois { get; set; } = string.Empty;
        public decimal Ventes { get; set; }
    }

    public class PromotionUtiliseeStatsDto
    {
        public int Id { get; set; }
        public string NomPromotion { get; set; } = string.Empty;
        public string? CodePromo { get; set; }
        public string Type { get; set; } = string.Empty;
        public int NombreUtilisations { get; set; }
        public decimal EconomieGeneree { get; set; }
    }
}
