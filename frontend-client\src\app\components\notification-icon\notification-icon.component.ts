import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatBadgeModule } from '@angular/material/badge';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { NotificationService, NotificationDto } from '../../services/notification.service';
import { AuthService } from '../../auth/auth.service';

@Component({
  selector: 'app-notification-icon',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatBadgeModule,
    MatMenuModule,
    MatDividerModule,
    MatTooltipModule
  ],
  template: `
    <button 
      mat-icon-button 
      [matMenuTriggerFor]="notificationMenu"
      [matBadge]="unreadCount"
      [matBadgeHidden]="unreadCount === 0"
      matBadgeColor="warn"
      matBadgeSize="small"
      matTooltip="Notifications"
      (click)="loadNotifications()">
      <i class="bi bi-bell fs-5"></i>
    </button>

    <mat-menu #notificationMenu="matMenu" class="notification-menu">
      <div class="notification-header" (click)="$event.stopPropagation()">
        <h4>Notifications</h4>
        <button 
          mat-button 
          *ngIf="unreadCount > 0"
          (click)="markAllAsRead()"
          class="mark-all-read-btn">
          Tout marquer comme lu
        </button>
      </div>
      
      <mat-divider></mat-divider>
      
      <div class="notification-list" (click)="$event.stopPropagation()">
        <div *ngIf="notifications.length === 0" class="no-notifications">
          <i class="bi bi-bell-slash"></i>
          <p>Aucune notification</p>
        </div>
        
        <div 
          *ngFor="let notification of notifications.slice(0, 5)" 
          class="notification-item"
          [class.unread]="!notification.estLue"
          (click)="markAsRead(notification)">
          
          <div class="notification-content">
            <p class="notification-text">{{ notification.contenu }}</p>
            <span class="notification-date">{{ formatDate(notification.dateEnvoi) }}</span>
          </div>
          
          <button 
            mat-icon-button 
            class="delete-btn"
            (click)="deleteNotification(notification.id, $event)">
            <i class="bi bi-x"></i>
          </button>
        </div>
        
        <div *ngIf="notifications.length > 5" class="view-all">
          <button mat-button (click)="viewAllNotifications()">
            Voir toutes les notifications
          </button>
        </div>
      </div>
    </mat-menu>
  `,
  styles: [`
    .notification-menu {
      width: 350px;
      max-height: 400px;
    }

    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background-color: var(--primary-color);
      color: white;
    }

    .notification-header h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }

    .mark-all-read-btn {
      color: white !important;
      font-size: 12px;
    }

    .notification-list {
      max-height: 300px;
      overflow-y: auto;
    }

    .no-notifications {
      text-align: center;
      padding: 32px 16px;
      color: var(--text-muted);
    }

    .no-notifications i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    .notification-item {
      display: flex;
      align-items: flex-start;
      padding: 12px 16px;
      border-bottom: 1px solid var(--border-color);
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .notification-item:hover {
      background-color: var(--hover-color);
    }

    .notification-item.unread {
      background-color: var(--unread-bg);
      border-left: 3px solid var(--primary-color);
    }

    .notification-content {
      flex: 1;
      margin-right: 8px;
    }

    .notification-text {
      margin: 0 0 4px 0;
      font-size: 14px;
      line-height: 1.4;
    }

    .notification-date {
      font-size: 12px;
      color: var(--text-muted);
    }

    .delete-btn {
      opacity: 0;
      transition: opacity 0.2s;
    }

    .notification-item:hover .delete-btn {
      opacity: 1;
    }

    .view-all {
      text-align: center;
      padding: 16px;
      border-top: 1px solid var(--border-color);
    }

    :host {
      --primary-color: #1976d2;
      --text-muted: #666;
      --border-color: #e0e0e0;
      --hover-color: #f5f5f5;
      --unread-bg: #e3f2fd;
    }

    [data-theme="dark"] :host {
      --text-muted: #aaa;
      --border-color: #333;
      --hover-color: #333;
      --unread-bg: #1a237e;
    }
  `]
})
export class NotificationIconComponent implements OnInit, OnDestroy {
  notifications: NotificationDto[] = [];
  unreadCount = 0;
  private subscriptions = new Subscription();

  constructor(
    private notificationService: NotificationService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit() {
    this.subscriptions.add(
      this.notificationService.notifications$.subscribe(notifications => {
        this.notifications = notifications;
      })
    );

    this.subscriptions.add(
      this.notificationService.unreadCount$.subscribe(count => {
        this.unreadCount = count;
      })
    );

    this.loadNotifications();
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  loadNotifications() {
    const currentUser = this.authService.getCurrentUser();
    if (currentUser?.id) {
      this.notificationService.getUserNotifications(currentUser.id).subscribe();
    }
  }

  markAsRead(notification: NotificationDto) {
    if (!notification.estLue) {
      this.notificationService.markAsRead(notification.id).subscribe();
    }
  }

  markAllAsRead() {
    const unreadNotifications = this.notifications.filter(n => !n.estLue);
    unreadNotifications.forEach(notification => {
      this.notificationService.markAsRead(notification.id).subscribe();
    });
  }

  deleteNotification(notificationId: number, event: Event) {
    event.stopPropagation();
    this.notificationService.deleteNotification(notificationId).subscribe();
  }

  viewAllNotifications() {
    this.router.navigate(['/user/notifications']);
  }

  formatDate(date: Date): string {
    const now = new Date();
    const notifDate = new Date(date);
    const diffInMinutes = Math.floor((now.getTime() - notifDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `Il y a ${diffInDays}j`;
    
    return notifDate.toLocaleDateString('fr-FR');
  }
}
