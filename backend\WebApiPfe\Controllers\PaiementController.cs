﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PaiementsController : ControllerBase
    {
        private readonly IPaiementService _paiementService;
        private readonly ILogger<PaiementsController> _logger;
        private readonly AppDbContext _context; 

        public PaiementsController(
            IPaiementService paiementService,
            ILogger<PaiementsController> logger,
            AppDbContext context) 
        {
            _paiementService = paiementService;
            _logger = logger;
            _context = context;
        }

        [ProducesResponseType(typeof(PaiementResponseDto), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [HttpPost]
        public async Task<ActionResult<PaiementResponseDto>> ProcesserPaiement([FromBody] PaiementDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            if (dto.Montant <= 0)
                return BadRequest("Le montant doit être positif");
            try
            {

                var paiement = await _paiementService.ProcesserPaiementAsync(dto.CommandeId, dto.TokenCarte);

                return Ok(new PaiementResponseDto
                {
                    Id = paiement.Id,
                    TransactionId = paiement.TransactionId,
                    Montant = paiement.Montant,
                    Statut = paiement.Statut.ToString(),
                    DateCreation = paiement.DateCreation,
                    Methode = paiement.Methode.ToString()
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    "Échec paiement [Commande:{CommandeId}, Montant:{Montant}] pour {Utilisateur}. Raison: {Error}",
                    dto.CommandeId,
                    dto.Montant,
                    User.Identity?.Name ?? "Anonyme",
                    ex.Message);
                return BadRequest(new
                {
                    Error = ex.Message,
                    Details = "Vérifiez l'ID de la commande et les informations de paiement"
                });
            }
        }

        [HttpPost("rembourser")]
        public async Task<ActionResult> Rembourser([FromBody] RemboursementDto dto)
        {
            var paiement = await _context.Paiements.FindAsync(dto.PaiementId);
            if (paiement?.Statut != StatutPaiement.Reussi)
                return BadRequest("Seuls les paiements réussis peuvent être remboursés");
            try
            {
                var remboursement = await _paiementService.EffectuerRemboursementAsync(dto.PaiementId);
                return Ok(new
                {
                    Message = "Remboursement initié",
                    RemboursementId = remboursement.Id,
                    Montant = remboursement.Montant,
                    Date = remboursement.DateDemande.ToString("yyyy-MM-dd HH:mm")
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur de remboursement");
                return BadRequest(new
                {
                    Error = ex.Message,
                    Solution = "Vérifiez que le paiement est éligible au remboursement"
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<PaiementResponseDto>> GetPaiement(int id)
        {
            try
            {
                var paiement = await _context.Paiements
                    .Include(p => p.Commande) 
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (paiement == null)
                {
                    return NotFound(new
                    {
                        Message = $"Paiement avec l'ID {id} introuvable",
                        Solution = "Vérifiez l'ID et réessayez"
                    });
                }

                return Ok(new PaiementResponseDto
                {
                    Id = paiement.Id,
                    TransactionId= paiement.TransactionId,
                    Montant = paiement.Montant,
                    Statut = paiement.Statut.ToString(),
                    DateCreation = paiement.DateCreation,
                    Methode = paiement.Methode.ToString(),
                    CommandeId = paiement.CommandeId 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération du paiement {id}");
                return StatusCode(500, new
                {
                    Error = "Erreur interne du serveur",
                    Reference = $"REF_{DateTime.Now:yyyyMMddHHmm}" 
                });
            }
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<PaiementResponseDto>>> GetPaiements(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var paiements = await _context.Paiements
                    .OrderByDescending(p => p.DateCreation)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var result = paiements.Select(p => new PaiementResponseDto
                {
                    Id = p.Id,
                    TransactionId = p.TransactionId,
                    Montant = p.Montant,
                    Statut = p.Statut.ToString(),
                    DateCreation = p.DateCreation,
                    Methode = p.Methode.ToString()
                });

                return Ok(new
                {
                    Data = result,
                    Pagination = new
                    {
                        CurrentPage = page,
                        PageSize = pageSize,
                        TotalCount = await _context.Paiements.CountAsync()
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des paiements");
                return StatusCode(500, "Erreur interne du serveur");
            }
        }
    }
}
