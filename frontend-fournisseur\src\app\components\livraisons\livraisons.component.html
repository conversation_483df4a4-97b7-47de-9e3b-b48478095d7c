<div class="livraisons-management">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1>🚚 Gestion des Livraisons</h1>
      <p><PERSON><PERSON><PERSON> les livraisons de vos commandes et suivez leur statut</p>
    </div>
    
    <div class="header-actions">
      <button
        class="btn btn-primary"
        (click)="creerLivraison()"
        [disabled]="isLoading || commandes.length === 0">
        ✨ Nouvelle Livraison
      </button>
    </div>
  </div>

  <!-- Messages -->
  <div *ngIf="error" class="alert alert-error">
    ⚠️ {{ error }}
    <button (click)="error = ''" class="alert-close">✕</button>
  </div>

  <div *ngIf="successMessage" class="alert alert-success">
    ✅ {{ successMessage }}
    <button (click)="successMessage = ''" class="alert-close">✕</button>
  </div>

  <!-- Filtres et recherche -->
  <div class="filters-section">
    <div class="filters-row">
      <!-- Recherche -->
      <div class="search-box">
        <input
          type="text"
          [(ngModel)]="searchQuery"
          (input)="applyFilters()"
          placeholder="Rechercher par numéro de suivi, transporteur..."
          class="form-control">
        <span class="search-icon">🔍</span>
      </div>

      <!-- Filtre par statut -->
      <select
        [(ngModel)]="filtreStatut"
        (change)="applyFilters()"
        class="form-control">
        <option value="all">Tous les statuts</option>
        <option value="EnAttente">En attente</option>
        <option value="EnCours">En cours</option>
        <option value="Livree">Livrée</option>
        <option value="Echec">Échec</option>
        <option value="Retournee">Retournée</option>
      </select>

      <!-- Filtres par date -->
      <input
        type="date"
        [(ngModel)]="dateDebut"
        (change)="applyFilters()"
        class="form-control"
        title="Date de début">

      <input
        type="date"
        [(ngModel)]="dateFin"
        (change)="applyFilters()"
        class="form-control"
        title="Date de fin">

      <!-- Bouton reset -->
      <button
        class="btn btn-secondary"
        (click)="resetFilters()"
        title="Réinitialiser les filtres">
        🔄 Reset
      </button>
    </div>
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Chargement des livraisons...</p>
  </div>

  <!-- Liste des livraisons -->
  <div *ngIf="!isLoading" class="livraisons-list">
    <!-- En-tête du tableau -->
    <div class="livraisons-header">
      <div class="col-tracking">Numéro de suivi</div>
      <div class="col-transporter">Transporteur</div>
      <div class="col-dates">Dates</div>
      <div class="col-status">Statut</div>
      <div class="col-actions">Actions</div>
    </div>

    <!-- Lignes des livraisons -->
    <div class="livraison-row" *ngFor="let livraison of livraisons">
      <!-- Numéro de suivi -->
      <div class="col-tracking">
        <div class="tracking-info">
          <strong>{{ livraison.numeroSuivi }}</strong>
          <div class="tracking-meta">
            Commande #{{ livraison.commandeId }}
          </div>
        </div>
      </div>

      <!-- Transporteur -->
      <div class="col-transporter">
        <div class="transporter-info">
          <span *ngIf="livraison.transporteur">{{ livraison.transporteur }}</span>
          <span *ngIf="!livraison.transporteur" class="text-muted">Non défini</span>
        </div>
      </div>

      <!-- Dates -->
      <div class="col-dates">
        <div class="dates-info">
          <div *ngIf="livraison.dateExpedition" class="date-item">
            <strong>Expédition:</strong> {{ formatDate(livraison.dateExpedition) }}
          </div>
          <div *ngIf="livraison.dateLivraisonPrevue" class="date-item">
            <strong>Prévue:</strong> {{ formatDate(livraison.dateLivraisonPrevue) }}
          </div>
          <div *ngIf="livraison.dateLivraisonReelle" class="date-item">
            <strong>Réelle:</strong> {{ formatDate(livraison.dateLivraisonReelle) }}
          </div>
        </div>
      </div>

      <!-- Statut -->
      <div class="col-status">
        <span class="status-badge" [class]="getStatutClass(livraison.statut)">
          {{ getStatutText(livraison.statut) }}
        </span>
      </div>

      <!-- Actions -->
      <div class="col-actions">
        <button
          class="btn btn-sm btn-primary"
          (click)="voirDetails(livraison)"
          title="Voir les détails">
          👁️ Détails
        </button>
        
        <button
          class="btn btn-sm btn-warning"
          (click)="modifierLivraison(livraison)"
          title="Modifier la livraison"
          style="margin-left: 5px;">
          ✏️ Modifier
        </button>
      </div>
    </div>

    <!-- Message si aucune livraison -->
    <div *ngIf="livraisons.length === 0" class="no-livraisons">
      <div class="no-livraisons-icon">🚚</div>
      <h3>Aucune livraison trouvée</h3>
      <p>Aucune livraison ne correspond à vos critères de recherche.</p>
      <button class="btn btn-primary" (click)="creerLivraison()" *ngIf="commandes.length > 0">
        ✨ Créer votre première livraison
      </button>
    </div>
  </div>
</div>

<!-- Modal de détails -->
<div *ngIf="showDetailModal" class="modal-overlay" (click)="closeDetailModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>📋 Détails de la livraison</h2>
      <button class="modal-close" (click)="closeDetailModal()">✕</button>
    </div>
    
    <div class="modal-body" *ngIf="livraisonSelectionnee">
      <!-- Informations générales -->
      <div class="detail-section">
        <h3>Informations générales</h3>
        <div class="detail-grid">
          <div class="detail-item">
            <strong>Numéro de suivi:</strong> {{ livraisonSelectionnee.numeroSuivi }}
          </div>
          <div class="detail-item">
            <strong>Statut:</strong> 
            <span class="status-badge" [class]="getStatutClass(livraisonSelectionnee.statut)">
              {{ getStatutText(livraisonSelectionnee.statut) }}
            </span>
          </div>
          <div class="detail-item" *ngIf="livraisonSelectionnee.transporteur">
            <strong>Transporteur:</strong> {{ livraisonSelectionnee.transporteur }}
          </div>
          <div class="detail-item">
            <strong>Commande:</strong> #{{ livraisonSelectionnee.commandeId }}
          </div>
        </div>
      </div>

      <!-- Dates importantes -->
      <div class="detail-section">
        <h3>Dates importantes</h3>
        <div class="detail-grid">
          <div class="detail-item" *ngIf="livraisonSelectionnee.dateExpedition">
            <strong>Date d'expédition:</strong> {{ formatDate(livraisonSelectionnee.dateExpedition) }}
          </div>
          <div class="detail-item" *ngIf="livraisonSelectionnee.dateLivraisonPrevue">
            <strong>Livraison prévue:</strong> {{ formatDate(livraisonSelectionnee.dateLivraisonPrevue) }}
          </div>
          <div class="detail-item" *ngIf="livraisonSelectionnee.dateLivraisonReelle">
            <strong>Livraison réelle:</strong> {{ formatDate(livraisonSelectionnee.dateLivraisonReelle) }}
          </div>
        </div>
      </div>

      <!-- Commentaires -->
      <div class="detail-section" *ngIf="livraisonSelectionnee.commentaires">
        <h3>Commentaires</h3>
        <p class="comments-text">{{ livraisonSelectionnee.commentaires }}</p>
      </div>
    </div>
    
    <div class="modal-footer">
      <button class="btn btn-secondary" (click)="closeDetailModal()">
        Fermer
      </button>
      <button class="btn btn-warning" (click)="modifierLivraison(livraisonSelectionnee!)">
        ✏️ Modifier
      </button>
    </div>
  </div>
</div>

<!-- Modal de création -->
<div *ngIf="showCreateModal" class="modal-overlay" (click)="closeCreateModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>✨ Créer une nouvelle livraison</h2>
      <button class="modal-close" (click)="closeCreateModal()">✕</button>
    </div>
    
    <div class="modal-body">
      <div class="form-group">
        <label for="commandeId">Commande *</label>
        <select
          id="commandeId"
          [(ngModel)]="livraisonForm.commandeId"
          class="form-control"
          required>
          <option value="">Sélectionner une commande</option>
          <option *ngFor="let commande of commandes" [value]="commande.id">
            {{ commande.numeroCommande }} - {{ commande.commandeOriginale?.client?.nom }}
          </option>
        </select>
      </div>

      <div class="form-group">
        <label for="numeroSuivi">Numéro de suivi *</label>
        <input
          type="text"
          id="numeroSuivi"
          [(ngModel)]="livraisonForm.numeroSuivi"
          class="form-control"
          placeholder="Ex: LIV-2024-001"
          required>
      </div>

      <div class="form-group">
        <label for="transporteur">Transporteur</label>
        <input
          type="text"
          id="transporteur"
          [(ngModel)]="livraisonForm.transporteur"
          class="form-control"
          placeholder="Ex: DHL, Colissimo, UPS">
      </div>

      <div class="form-group">
        <label for="statut">Statut *</label>
        <select
          id="statut"
          [(ngModel)]="livraisonForm.statut"
          class="form-control"
          required>
          <option value="EnAttente">En attente</option>
          <option value="EnCours">En cours</option>
          <option value="Livree">Livrée</option>
          <option value="Echec">Échec</option>
          <option value="Retournee">Retournée</option>
        </select>
      </div>

      <div class="form-group">
        <label for="dateLivraisonPrevue">Date de livraison prévue</label>
        <input
          type="datetime-local"
          id="dateLivraisonPrevue"
          [(ngModel)]="livraisonForm.dateLivraisonPrevue"
          class="form-control">
      </div>

      <div class="form-group">
        <label for="commentaires">Commentaires</label>
        <textarea
          id="commentaires"
          [(ngModel)]="livraisonForm.commentaires"
          class="form-control"
          rows="3"
          placeholder="Commentaires sur la livraison..."></textarea>
      </div>
    </div>
    
    <div class="modal-footer">
      <button class="btn btn-secondary" (click)="closeCreateModal()">
        Annuler
      </button>
      <button 
        class="btn btn-primary" 
        (click)="confirmerCreation()"
        [disabled]="isLoading">
        {{ isLoading ? '⏳ Création...' : '✅ Créer' }}
      </button>
    </div>
  </div>
</div>

<!-- Modal de modification -->
<div *ngIf="showUpdateModal" class="modal-overlay" (click)="closeUpdateModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h2>✏️ Modifier la livraison</h2>
      <button class="modal-close" (click)="closeUpdateModal()">✕</button>
    </div>
    
    <div class="modal-body">
      <div class="form-group">
        <label for="numeroSuiviEdit">Numéro de suivi *</label>
        <input
          type="text"
          id="numeroSuiviEdit"
          [(ngModel)]="livraisonForm.numeroSuivi"
          class="form-control"
          required>
      </div>

      <div class="form-group">
        <label for="transporteurEdit">Transporteur</label>
        <input
          type="text"
          id="transporteurEdit"
          [(ngModel)]="livraisonForm.transporteur"
          class="form-control">
      </div>

      <div class="form-group">
        <label for="statutEdit">Statut *</label>
        <select
          id="statutEdit"
          [(ngModel)]="livraisonForm.statut"
          class="form-control"
          required>
          <option value="EnAttente">En attente</option>
          <option value="EnCours">En cours</option>
          <option value="Livree">Livrée</option>
          <option value="Echec">Échec</option>
          <option value="Retournee">Retournée</option>
        </select>
      </div>

      <div class="form-group">
        <label for="dateLivraisonPrevueEdit">Date de livraison prévue</label>
        <input
          type="datetime-local"
          id="dateLivraisonPrevueEdit"
          [(ngModel)]="livraisonForm.dateLivraisonPrevue"
          class="form-control">
      </div>

      <div class="form-group" *ngIf="livraisonForm.statut === 'Livree'">
        <label for="dateLivraisonReelle">Date de livraison réelle</label>
        <input
          type="datetime-local"
          id="dateLivraisonReelle"
          [(ngModel)]="livraisonForm.dateLivraisonReelle"
          class="form-control">
      </div>

      <div class="form-group">
        <label for="commentairesEdit">Commentaires</label>
        <textarea
          id="commentairesEdit"
          [(ngModel)]="livraisonForm.commentaires"
          class="form-control"
          rows="3"></textarea>
      </div>
    </div>
    
    <div class="modal-footer">
      <button class="btn btn-secondary" (click)="closeUpdateModal()">
        Annuler
      </button>
      <button 
        class="btn btn-primary" 
        (click)="confirmerModification()"
        [disabled]="isLoading">
        {{ isLoading ? '⏳ Mise à jour...' : '✅ Mettre à jour' }}
      </button>
    </div>
  </div>
</div>
