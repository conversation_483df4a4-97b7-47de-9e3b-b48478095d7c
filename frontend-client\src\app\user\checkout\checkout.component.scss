.checkout-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: var(--card-background-color);
  min-height: calc(100vh - 200px);
}

.checkout-header {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    color: var(--text-color);
    margin-bottom: 30px;
    font-size: 2rem;
    font-weight: 600;
  }
}

.checkout-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  margin-bottom: 40px;

  .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    position: relative;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      top: 15px;
      right: -20px;
      width: 40px;
      height: 2px;
      background-color: var(--border-color);
    }

    &.active {
      .step-number {
        background-color: var(--primary-color);
        color: white;
      }

      .step-label {
        color: var(--primary-color);
        font-weight: 600;
      }

      &::after {
        background-color: var(--primary-color);
      }
    }

    .step-number {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      background-color: var(--border-color);
      color: var(--text-color);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 14px;
    }

    .step-label {
      font-size: 12px;
      color: var(--secondary-color);
      text-align: center;
    }
  }
}

.loading {
  text-align: center;
  padding: 60px 20px;

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }

  p {
    color: var(--secondary-color);
    font-size: 16px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background-color: var(--error-color);
  color: white;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 20px;
}

.checkout-content {
  display: grid;
  gap: 30px;
}

.order-summary {
  background-color: var(--card-background-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 30px;
  box-shadow: var(--card-shadow);

  h2 {
    color: var(--text-color);
    margin-bottom: 25px;
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.cart-items {
  margin-bottom: 30px;
}

.cart-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px 0;
  border-bottom: 1px solid var(--border-color);

  &:last-child {
    border-bottom: none;
  }

  .item-image {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--border-color);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .item-details {
    flex: 1;

    h3 {
      color: var(--text-color);
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 5px;
    }

    .item-reference {
      color: var(--secondary-color);
      font-size: 14px;
      margin-bottom: 8px;
    }

    .item-quantity {
      color: var(--text-color);
      font-size: 14px;
      font-weight: 500;
    }
  }

  .item-price {
    text-align: right;
    flex-shrink: 0;

    .unit-price {
      display: block;
      color: var(--secondary-color);
      font-size: 14px;
      margin-bottom: 5px;
    }

    .total-price {
      color: var(--text-color);
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.order-total {
  border-top: 2px solid var(--border-color);
  padding-top: 20px;

  .total-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    color: var(--text-color);

    &.final-total {
      font-size: 18px;
      font-weight: 700;
      color: var(--primary-color);
      border-top: 1px solid var(--border-color);
      margin-top: 10px;
      padding-top: 15px;
    }
  }
}

.checkout-actions {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  padding: 30px;
  background-color: var(--card-background-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: var(--card-shadow);

  .btn {
    flex: 1;
    max-width: 200px;
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &.btn-secondary {
      background-color: transparent;
      color: var(--secondary-color);
      border: 2px solid var(--secondary-color);

      &:hover {
        background-color: var(--secondary-color);
        color: white;
      }
    }

    &.btn-primary {
      background-color: var(--primary-color);
      color: white;

      &:hover {
        background-color: var(--primary-color-hover);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

@media (max-width: 768px) {
  .checkout-container {
    padding: 15px;
  }

  .checkout-steps {
    gap: 20px;

    .step:not(:last-child)::after {
      width: 20px;
      right: -10px;
    }

    .step-label {
      font-size: 10px;
    }
  }

  .cart-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;

    .item-image {
      width: 60px;
      height: 60px;
    }

    .item-price {
      text-align: left;
      width: 100%;
    }
  }

  .checkout-actions {
    flex-direction: column;

    .btn {
      max-width: none;
    }
  }
}

// Section des frais de livraison - Style similaire au panier
.delivery-section {
  margin-top: 20px;

  .total-line {
    .calculating {
      color: var(--accent-color);
      font-weight: 600;
      font-style: italic;
    }

    .free-delivery {
      color: var(--success-color);
      font-weight: 700;
      font-size: 1.1rem;
    }

    .delivery-total {
      color: var(--primary-color);
      font-weight: 700;
      font-size: 1.1rem;
    }
  }
}

.delivery-breakdown {
  background: var(--card-background-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  margin-top: 15px;
  box-shadow: var(--card-shadow);

  h4 {
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
  }
}

.delivery-items {
  margin-bottom: 20px;
}

.delivery-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: var(--card-background-color-hover);
    border-radius: 8px;
    padding-left: 10px;
    padding-right: 10px;
  }

  .supplier-info {
    display: flex;
    align-items: center;
    gap: 10px;

    .supplier-icon {
      font-size: 1.2rem;
      color: var(--primary-color);
    }

    .supplier-name {
      color: var(--text-color);
      font-weight: 600;
      font-size: 1rem;
    }
  }

  .supplier-delivery-fee {
    .free {
      color: var(--success-color);
      font-weight: 700;
      background: rgba(39, 174, 96, 0.1);
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.9rem;
    }

    .fee {
      color: var(--primary-color);
      font-weight: 700;
      background: rgba(52, 152, 219, 0.1);
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 1rem;
    }
  }
}

.delivery-total-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-top: 2px solid var(--primary-color);
  background: var(--gradient-primary);
  color: white;
  border-radius: 8px;
  padding: 15px 20px;
  margin-top: 15px;

  .total-label {
    font-weight: 700;
    font-size: 1.1rem;
  }

  .total-value {
    .free {
      font-weight: 700;
      font-size: 1.2rem;
    }

    .total {
      font-weight: 700;
      font-size: 1.3rem;
    }
  }
}

// Responsive pour les frais de livraison
@media (max-width: 768px) {
  .delivery-breakdown {
    padding: 15px;

    h4 {
      font-size: 1rem;
    }
  }

  .delivery-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .supplier-delivery-fee {
      align-self: flex-end;
    }
  }

  .delivery-total-summary {
    flex-direction: column;
    gap: 8px;
    text-align: center;

    .total-label {
      font-size: 1rem;
    }

    .total-value .total {
      font-size: 1.2rem;
    }
  }
}


