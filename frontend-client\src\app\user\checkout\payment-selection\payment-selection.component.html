<div class="checkout-container">
  <div class="checkout-header">
    <h1>Mode de paiement</h1>
    <div class="checkout-steps">
      <div class="step completed">
        <span class="step-number">✓</span>
        <span class="step-label">Confirmation</span>
      </div>
      <div class="step completed">
        <span class="step-number">✓</span>
        <span class="step-label">Adresse</span>
      </div>
      <div class="step active">
        <span class="step-number">3</span>
        <span class="step-label">Paiement</span>
      </div>
      <div class="step">
        <span class="step-number">4</span>
        <span class="step-label">Confirmation</span>
      </div>
    </div>
  </div>

  <div *ngIf="errorMessage" class="error-message">
    {{ errorMessage }}
  </div>

  <div class="checkout-content">
    <!-- Méthodes de paiement -->
    <div class="payment-section">
      <h2>Choisissez votre mode de paiement</h2>
      
      <div class="payment-methods">
        <div *ngFor="let method of paymentMethods" 
             class="payment-card" 
             [class.selected]="selectedPaymentMethod === method.id"
             [class.disabled]="!method.available"
             (click)="selectPaymentMethod(method.id)">
          
          <div class="payment-radio">
            <input type="radio" 
                   [id]="'payment-' + method.id"
                   name="selectedPayment"
                   [checked]="selectedPaymentMethod === method.id"
                   [disabled]="!method.available"
                   (change)="selectPaymentMethod(method.id)">
          </div>
          
          <div class="payment-icon">
            {{ method.icon }}
          </div>
          
          <div class="payment-content">
            <h3>{{ method.name }}</h3>
            <p>{{ method.description }}</p>
            <span *ngIf="!method.available" class="unavailable-badge">Bientôt disponible</span>
          </div>
          
          <div class="payment-actions">
            <span class="select-text" *ngIf="selectedPaymentMethod === method.id && method.available">
              ✓ Sélectionné
            </span>
          </div>
        </div>
      </div>

      <!-- Informations de sécurité -->
      <div class="security-info">
        <div class="security-icon">🔒</div>
        <div class="security-text">
          <h4>Paiement sécurisé</h4>
          <p>Vos informations de paiement sont protégées par un cryptage SSL de niveau bancaire</p>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="checkout-actions">
      <button type="button" class="btn btn-secondary" (click)="retourAdresse()">
        Retour
      </button>
      <button type="button" 
              class="btn btn-primary" 
              [disabled]="!selectedPaymentMethod"
              (click)="continuerVersConfirmation()">
        Continuer vers la confirmation
      </button>
    </div>
  </div>
</div>
