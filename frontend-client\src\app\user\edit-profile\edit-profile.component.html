<div class="edit-profile-container">
  <h2>Modifier votre profil</h2>

  <form [formGroup]="profileForm" (ngSubmit)="onSubmit()">
    <!-- Informations de base -->
    <div class="form-section">
      <h3>Informations personnelles</h3>

      <div class="form-group">
        <label>Nom</label>
        <div class="editable-field">
          <span *ngIf="!editNom">{{ profileForm.get("nom")?.value }}</span>
          <input
            *ngIf="editNom"
            type="text"
            formControlName="nom"
            class="form-control"
          />
          <button
            type="button"
            class="edit-primary"
            mat-icon-button
            (click)="toggleEdit('nom')"
          >
            <mat-icon>edit</mat-icon>
          </button>
        </div>
      </div>

      <div class="form-group">
        <label>Prénom</label>
        <div class="editable-field">
          <span *ngIf="!editPrenom">{{
            profileForm.get("prenom")?.value
          }}</span>
          <input
            *ngIf="editPrenom"
            type="text"
            formControlName="prenom"
            class="form-control"
          />
          <button
            type="button"
            class="edit-primary"
            mat-icon-button
            (click)="toggleEdit('prenom')"
          >
            <mat-icon>edit</mat-icon>
          </button>
        </div>
      </div>

      <div class="form-group">
        <label>Email</label>
        <div class="editable-field">
          <span *ngIf="!editEmail">{{ profileForm.get("email")?.value }}</span>
          <input
            *ngIf="editEmail"
            type="email"
            formControlName="email"
            class="form-control"
          />
          <button
            type="button"
            class="edit-primary"
            mat-icon-button
            (click)="toggleEdit('email')"
          >
            <mat-icon>edit</mat-icon>
          </button>
        </div>
      </div>

      <div class="form-group">
        <label>Téléphone</label>
        <div class="editable-field">
          <span *ngIf="!editPhone">{{
            profileForm.get("phoneNumber")?.value
          }}</span>
          <input
            *ngIf="editPhone"
            type="tel"
            formControlName="phoneNumber"
            class="form-control"
          />
          <button
            type="button"
            class="edit-primary"
            mat-icon-button
            (click)="toggleEdit('phone')"
          >
            <mat-icon>edit</mat-icon>
          </button>
        </div>
      </div>

      <div class="form-group">
        <label>Date de naissance</label>
        <div class="editable-field">
          <span *ngIf="!editNaissance">{{
            profileForm.get("dateNaissance")?.value
          }}</span>
          <input
            *ngIf="editNaissance"
            type="date"
            formControlName="dateNaissance"
            class="form-control"
          />
          <button
            type="button"
            class="edit-primary"
            mat-icon-button
            (click)="toggleEdit('naissance')"
          >
            <mat-icon>edit</mat-icon>
          </button>
        </div>
      </div>

      <div class="form-group adresse-livraison-section">
        <label>Adresse de livraison principale</label>
        <div *ngIf="!editAdresseLivraison" class="adresse-livraison-display">
          <span *ngIf="profileForm.get('adresseLivraison')?.get('rue')?.value">
            {{ profileForm.get('adresseLivraison')?.get('rue')?.value }},
            {{ profileForm.get('adresseLivraison')?.get('ville')?.value }},
            {{ profileForm.get('adresseLivraison')?.get('codePostal')?.value }}
          </span>
          <span *ngIf="!profileForm.get('adresseLivraison')?.get('rue')?.value" class="no-address">
            Aucune adresse de livraison définie
          </span>
          <button
            type="button"
            class="edit-primary"
            mat-icon-button
            (click)="toggleEdit('adresseLivraison')"
          >
            <mat-icon>edit</mat-icon>
          </button>
        </div>
        <div *ngIf="editAdresseLivraison" class="adresse-livraison-edit" formGroupName="adresseLivraison">
          <input
            placeholder="Rue"
            formControlName="rue"
            class="form-control"
          />
          <input
            placeholder="Ville"
            formControlName="ville"
            class="form-control"
          />
          <input
            placeholder="Code Postal"
            formControlName="codePostal"
            class="form-control"
          />
          <button
            type="button"
            class="edit-primary"
            mat-icon-button
            (click)="toggleEdit('adresseLivraison')"
          >
            <mat-icon>check</mat-icon>
          </button>
        </div>
      </div>
    </div>

    <!-- autres adresses -->
    <div class="form-section">
      <h3>
        <a
          href="#"
          (click)="$event.preventDefault(); toggleShowOtherAddresses()"
        >
          Autres adresses
          <mat-icon>{{
            showOtherAddresses ? "expand_less" : "expand_more"
          }}</mat-icon>
        </a>
      </h3>

      <div *ngIf="showOtherAddresses">
        <button type="button" class="btn btn-add" (click)="addAdresse()">
          Ajouter une adresse
        </button>

        <div formArrayName="adresses">
          <ng-container *ngFor="let adresseCtrl of adresses.controls; let i = index">
            <div
              [formGroupName]="i"
              class="address-card"
              *ngIf="!isDeliveryAddress(adresseCtrl.get('id')?.value)"
            >
            <!-- Mode affichage -->
            <div *ngIf="!isEditingAddress(i)" class="address-display">
              <div class="address-info">
                <span *ngIf="adresseCtrl.get('rue')?.value">
                  {{ adresseCtrl.get('rue')?.value }},
                  {{ adresseCtrl.get('ville')?.value }},
                  {{ adresseCtrl.get('codePostal')?.value }},
                  {{ adresseCtrl.get('pays')?.value }}
                </span>
                <span *ngIf="!adresseCtrl.get('rue')?.value" class="no-address">
                  Adresse vide
                </span>
              </div>
              <div class="address-actions">
                <button
                  type="button"
                  class="edit-btn"
                  mat-icon-button
                  (click)="toggleEditAddress(i)"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                <button
                  type="button"
                  class="delete-btn"
                  mat-icon-button
                  (click)="removeAdresse(i)"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </div>

            <!-- Mode édition -->
            <div *ngIf="isEditingAddress(i)" class="address-edit">
              <div class="form-group">
                <label>Rue</label>
                <input type="text" formControlName="rue" class="form-control" />
              </div>

              <div class="form-group">
                <label>Ville</label>
                <input type="text" formControlName="ville" class="form-control" />
              </div>

              <div class="form-group">
                <label>Code Postal</label>
                <input
                  type="text"
                  formControlName="codePostal"
                  class="form-control"
                />
              </div>

              <div class="form-group">
                <label>Pays</label>
                <input
                  type="text"
                  formControlName="pays"
                  class="form-control"
                  placeholder="Tunisie"
                />
              </div>

              <div class="address-actions">
                <button
                  type="button"
                  class="save-btn"
                  mat-icon-button
                  (click)="toggleEditAddress(i)"
                >
                  <mat-icon>check</mat-icon>
                </button>
              </div>
            </div>

            <!-- Case à cocher pour adresse principale -->
            <div class="form-group checkbox-group">
              <input
                type="checkbox"
                formControlName="estPrincipale"
                (change)="setAsMain(i)"
                class="custom-checkbox"
                id="principal-{{i}}"
              />
              <label for="principal-{{i}}" class="custom-checkbox-label">
                <span class="checkmark"></span>
                Adresse principale
              </label>
            </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>

    <div class="form-actions">
      <button
        type="button"
        class="btn btn-cancel"
        (click)="router.navigate(['/user/profile'])"
      >
        Annuler
      </button>
      <button
        type="submit"
        class="btn btn-save"
        [disabled]="!profileForm.valid"
      >
        Enregistrer
      </button>
    </div>
  </form>
</div>
