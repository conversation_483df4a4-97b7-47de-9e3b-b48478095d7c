﻿using AutoMapper;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ItemPanierController : ControllerBase
    {
        private readonly IPanierService _panierService;
        private readonly IMapper _mapper;

        public ItemPanierController(IPanierService panierService, IMapper mapper)
        {
            _panierService = panierService;
            _mapper = mapper;
        }

        [HttpPost]
        public async Task<ActionResult<ItemPanierDto>> AddItem(int panierId, [FromBody] AddItemPanierDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);
            try
            {
                var item = await _panierService.AjouterAuPanier(panierId, dto);
                var itemDto = _mapper.Map<ItemPanierDto>(item);

                return CreatedAtAction(
                    nameof(GetItem),
                    new { panierId, itemId = item.Id },
                    itemDto);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (ArgumentOutOfRangeException ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [HttpGet("{itemId}")]
        public async Task<ActionResult<ItemPanierDto>> GetItem(int panierId, int itemId)
        {
            try
            {
                var panier = await _panierService.GetPanierActifAsync(panierId);
                var item = panier.Items.FirstOrDefault(i => i.Id == itemId);

                if (item == null)
                    return NotFound();

                return Ok(item);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpPut("{itemId}")]
        public async Task<IActionResult> UpdateItem(int panierId, int itemId, [FromBody] UpdateItemPanierDto dto)
        {
            try
            {
                await _panierService.ModifierItemAsync(itemId, dto);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }

        [HttpDelete("{itemId}")]
        public async Task<IActionResult> RemoveItem(int panierId, int itemId)
        {
            try
            {
                await _panierService.SupprimerItemAsync(itemId);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
        }
    }
}
