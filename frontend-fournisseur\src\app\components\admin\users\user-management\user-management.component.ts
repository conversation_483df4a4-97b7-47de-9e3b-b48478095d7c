import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-user-management',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="user-management">
      <h1>👥 Gestion des utilisateurs</h1>
      <p>Accès libre activé - Plus de validation requise pour les fournisseurs</p>

      <div class="info-card">
        <div class="icon">✅</div>
        <h2>Accès libre activé</h2>
        <p>Les fournisseurs peuvent maintenant :</p>
        <ul>
          <li>S'inscrire et accéder immédiatement à leur compte</li>
          <li>Gérer leurs produits sans validation préalable</li>
          <li>Traiter leurs commandes directement</li>
          <li>Accéder à toutes les fonctionnalités fournisseur</li>
        </ul>
        <div class="note">
          <strong>Note :</strong> La validation manuelle des fournisseurs a été désactivée pour simplifier le processus d'inscription.
        </div>
      </div>
    </div>
  `,
  styles: [`
    .user-management {
      max-width: 800px;
      padding: 20px;
    }

    h1 {
      color: #1e293b;
      margin-bottom: 0.5rem;
    }

    .info-card {
      background: white;
      padding: 3rem;
      border-radius: 12px;
      text-align: center;
      margin-top: 2rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #10b981;
    }

    .icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      color: #10b981;
    }

    .info-card h2 {
      color: #1e293b;
      margin-bottom: 1rem;
    }

    .info-card ul {
      text-align: left;
      max-width: 400px;
      margin: 1rem auto;
    }

    .info-card li {
      margin-bottom: 0.5rem;
      color: #64748b;
    }

    .note {
      background: #f0f9ff;
      border: 1px solid #0ea5e9;
      border-radius: 6px;
      padding: 12px;
      margin-top: 20px;
      color: #0c4a6e;
      font-size: 0.9rem;
    }
  `]
})
export class UserManagementComponent {}
