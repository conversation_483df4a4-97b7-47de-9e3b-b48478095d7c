@import '../checkout.component.scss';

.final-summary {
  background-color: var(--card-background-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 30px;
  box-shadow: var(--card-shadow);

  h2 {
    color: var(--text-color);
    margin-bottom: 30px;
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.summary-section {
  margin-bottom: 30px;
  padding-bottom: 25px;
  border-bottom: 1px solid var(--border-color);

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  h3 {
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .btn-link {
      background: none;
      border: none;
      color: var(--primary-color);
      font-size: 14px;
      cursor: pointer;
      text-decoration: underline;
      padding: 0;

      &:hover {
        color: var(--primary-color-hover);
      }
    }
  }
}

.cart-items {
  .cart-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid rgba(var(--border-color), 0.3);

    &:last-child {
      border-bottom: none;
    }

    .item-image {
      flex-shrink: 0;
      width: 60px;
      height: 60px;
      border-radius: 6px;
      overflow: hidden;
      border: 1px solid var(--border-color);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .item-details {
      flex: 1;

      h4 {
        color: var(--text-color);
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 5px 0;
      }

      .item-reference {
        color: var(--secondary-color);
        font-size: 12px;
        margin: 0 0 5px 0;
      }

      .item-quantity {
        color: var(--text-color);
        font-size: 12px;
        font-weight: 500;
      }
    }

    .item-price {
      flex-shrink: 0;

      .total-price {
        color: var(--text-color);
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
}

.address-info, .payment-info {
  .address-card, .payment-card {
    background-color: rgba(var(--primary-color), 0.05);
    border: 1px solid rgba(var(--primary-color), 0.2);
    border-radius: 8px;
    padding: 15px;

    h4 {
      color: var(--text-color);
      font-size: 14px;
      font-weight: 600;
      margin: 0 0 5px 0;
    }

    p {
      color: var(--secondary-color);
      font-size: 13px;
      margin: 2px 0;
    }

    .main-badge {
      background-color: var(--primary-color);
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
      margin-top: 5px;
      display: inline-block;
    }
  }

  .payment-card {
    display: flex;
    align-items: center;
    gap: 12px;

    .payment-icon {
      font-size: 20px;
      flex-shrink: 0;
    }

    .payment-details {
      flex: 1;
    }
  }
}

.total-section {
  .total-breakdown {
    background-color: rgba(var(--primary-color), 0.02);
    border-radius: 8px;
    padding: 20px;

    .total-line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      color: var(--text-color);
      font-size: 14px;

      &.final-total {
        font-size: 18px;
        font-weight: 700;
        color: var(--primary-color);
        border-top: 2px solid var(--primary-color);
        margin-top: 15px;
        padding-top: 15px;
      }
    }
  }
}

.terms-section {
  .checkbox-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    font-size: 14px;
    color: var(--text-color);

    input[type="checkbox"] {
      width: 16px;
      height: 16px;
      accent-color: var(--primary-color);
    }

    a {
      color: var(--primary-color);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.checkout-actions {
  .btn-confirm {
    position: relative;
    min-width: 200px;

    .btn-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      display: inline-block;
      margin-right: 8px;
    }
  }
}

.checkout-steps {
  .step {
    &.completed {
      .step-number {
        background-color: var(--success-color);
        color: white;
      }

      .step-label {
        color: var(--success-color);
        font-weight: 600;
      }

      &::after {
        background-color: var(--success-color);
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .final-summary {
    padding: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .cart-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;

    .item-image {
      width: 50px;
      height: 50px;
    }

    .item-price {
      align-self: flex-end;
    }
  }

  .total-breakdown {
    .total-line {
      &.final-total {
        font-size: 16px;
      }
    }
  }
}

.delivery-section {
  .delivery-details {
    margin-left: 20px;
    margin-top: 8px;
    background-color: rgba(52, 152, 219, 0.05);
    border-radius: 8px;
    padding: 12px;
    border-left: 3px solid var(--primary-color);

    .delivery-header {
      margin-bottom: 8px;

      small {
        color: var(--text-secondary-color);
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
      }
    }

    .delivery-detail-line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 0;
      font-size: 0.9rem;
      color: var(--text-color);
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);

      &:last-child {
        border-bottom: none;
      }

      .supplier-name {
        font-weight: 500;
        color: var(--text-color);
      }

      .supplier-fee {
        color: var(--primary-color);
        font-weight: 600;
      }
    }

    .delivery-total-line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      margin-top: 8px;
      border-top: 2px solid var(--primary-color);
      font-weight: 700;

      .total-label {
        color: var(--text-color);
        font-size: 0.95rem;
      }

      .total-amount {
        color: var(--primary-color);
        font-size: 1rem;
      }
    }
  }
}
