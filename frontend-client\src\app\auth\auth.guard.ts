import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';
import { TokenStorageService } from '../shared/services/token-storage.service';
import { JwtHelperService } from '@auth0/angular-jwt';
import { AuthService } from './auth.service';

@Injectable({ providedIn: 'root' })
export class AuthGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private tokenService: TokenStorageService,
    private router: Router,
    private jwtHelper: JwtHelperService
  ) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    const token = this.tokenService.getToken();
    const isAuthenticated = !!token && !this.jwtHelper.isTokenExpired(token); 

    if (!isAuthenticated) {
      this.tokenService.clearToken(); 
      localStorage.setItem('redirectAfterLogin', state.url);
      this.router.navigate(['/auth/login']); 
      return false; 
    }

    return true;
  }
}
