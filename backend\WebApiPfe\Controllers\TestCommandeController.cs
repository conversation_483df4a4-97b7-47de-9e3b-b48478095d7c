using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestCommandeController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly ILogger<TestCommandeController> _logger;

        public TestCommandeController(AppDbContext context, ILogger<TestCommandeController> logger)
        {
            _context = context;
            _logger = logger;
        }

        [HttpPost]
        public async Task<IActionResult> CreerCommandeSimple([FromBody] CommandeSimpleDto dto)
        {
            try
            {
                _logger.LogInformation($"🛒 DEBUT - Création commande simple pour client {dto.ClientId}");

                // Étape 1: Créer la commande de base
                var commande = new Commande
                {
                    ClientId = dto.ClientId,
                    DateCreation = DateTime.UtcNow,
                    Statut = StatutCommande.Brouillon,
                    MontantTotal = 0,
                    FraisLivraison = 0
                };

                _context.Commandes.Add(commande);
                await _context.SaveChangesAsync();
                _logger.LogInformation($"✅ Commande de base créée avec ID: {commande.Id}");

                // Étape 2: Ajouter les détails un par un
                decimal montantTotal = 0;
                foreach (var detail in dto.DetailsCommandes)
                {
                    var produit = await _context.Produits
                        .Include(p => p.TauxTVA)
                        .FirstOrDefaultAsync(p => p.Id == detail.ProduitId);

                    if (produit == null)
                    {
                        _logger.LogWarning($"❌ Produit {detail.ProduitId} introuvable");
                        continue;
                    }

                    var detailCommande = new DetailsCommande
                    {
                        CommandeId = commande.Id,
                        ProduitId = detail.ProduitId,
                        Quantite = detail.Quantite,
                        PrixUnitaireHT = produit.PrixVenteHT,
                        TauxTVAValue = produit.TauxTVA?.Taux ?? 0m
                    };

                    _context.DetailsCommandes.Add(detailCommande);
                    
                    var ligneTTC = (detailCommande.PrixUnitaireHT * (1 + detailCommande.TauxTVAValue / 100)) * detailCommande.Quantite;
                    montantTotal += ligneTTC;
                    
                    _logger.LogInformation($"✅ Détail ajouté: {detailCommande.Quantite}x {produit.Nom} = {ligneTTC:C}");
                }

                // Étape 3: Mettre à jour le montant total
                commande.MontantTotal = montantTotal;
                await _context.SaveChangesAsync();
                _logger.LogInformation($"✅ Montant total mis à jour: {montantTotal:C}");

                // Retourner une réponse simple
                var response = new
                {
                    id = commande.Id,
                    clientId = commande.ClientId,
                    dateCreation = commande.DateCreation,
                    statut = commande.Statut.ToString(),
                    montantTotal = commande.MontantTotal,
                    details = dto.DetailsCommandes.Select(d => new
                    {
                        produitId = d.ProduitId,
                        quantite = d.Quantite
                    }).ToList()
                };

                _logger.LogInformation($"✅ SUCCES - Commande {commande.Id} créée avec succès");
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ ERREUR CRITIQUE dans CreerCommandeSimple");
                return StatusCode(500, new { 
                    Error = "Erreur lors de la création de la commande", 
                    Details = ex.Message,
                    InnerException = ex.InnerException?.Message 
                });
            }
        }
    }

    public class CommandeSimpleDto
    {
        public int ClientId { get; set; }
        public List<DetailSimpleDto> DetailsCommandes { get; set; } = new List<DetailSimpleDto>();
        public string? CodePromo { get; set; }
    }

    public class DetailSimpleDto
    {
        public int ProduitId { get; set; }
        public int Quantite { get; set; }
    }
}
