﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.DTOs.CreateDTOs
{
    public class CreateCommandeFournisseurDto
    {
        [Required(ErrorMessage = "L'identifiant du fournisseur est obligatoire")]
        public int FournisseurId { get; set; }

        [Required(ErrorMessage = "Au moins une ligne de commande est requise")]
        public IEnumerable<CreateLigneCommandeFournisseurDto> Lignes { get; set; } = new List<CreateLigneCommandeFournisseurDto>();

        [Range(0, double.MaxValue, ErrorMessage = "Les frais de livraison doivent être positifs")]
        public decimal FraisLivraison { get; set; } = 0;
    }
}
