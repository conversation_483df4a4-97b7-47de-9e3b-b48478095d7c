# Script de test complet pour tous les APIs
Write-Host "🔍 TEST COMPLET DE TOUS LES APIs" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

$baseUrl = "http://localhost:5014/api"

# Test 1: Categories
Write-Host "1️⃣ Test API Categories:" -ForegroundColor Yellow
try {
    $categories = Invoke-RestMethod -Uri "$baseUrl/Categories" -Method Get
    Write-Host "✅ $($categories.Count) catégories trouvées:" -ForegroundColor Green
    $categories | ForEach-Object { Write-Host "   - ID: $($_.id) - $($_.nom)" -ForegroundColor White }
} catch {
    Write-Host "❌ Erreur Categories: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 2: Sous-catégories pour chaque catégorie
Write-Host "2️⃣ Test API Sous-catégories:" -ForegroundColor Yellow
$categories | ForEach-Object {
    $catId = $_.id
    $catNom = $_.nom
    try {
        $sousCategories = Invoke-RestMethod -Uri "$baseUrl/SousCategories/by-categorie/$catId" -Method Get
        Write-Host "✅ Catégorie '$catNom' (ID: $catId): $($sousCategories.Count) sous-catégories" -ForegroundColor Green
        $sousCategories | ForEach-Object { Write-Host "     - ID: $($_.id) - $($_.nom)" -ForegroundColor White }
    } catch {
        Write-Host "❌ Erreur sous-catégories pour catégorie $catId : $($_.Exception.Message)" -ForegroundColor Red
    }
}
Write-Host ""

# Test 3: Marques
Write-Host "3️⃣ Test API Marques:" -ForegroundColor Yellow
try {
    $marques = Invoke-RestMethod -Uri "$baseUrl/Marques" -Method Get
    Write-Host "✅ $($marques.Count) marques trouvées:" -ForegroundColor Green
    $marques | Select-Object -First 5 | ForEach-Object { Write-Host "   - ID: $($_.id) - $($_.name)" -ForegroundColor White }
    if ($marques.Count -gt 5) { Write-Host "   ... et $($marques.Count - 5) autres" -ForegroundColor Gray }
} catch {
    Write-Host "❌ Erreur Marques: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 4: Formes
Write-Host "4️⃣ Test API Formes:" -ForegroundColor Yellow
try {
    $formes = Invoke-RestMethod -Uri "$baseUrl/Formes" -Method Get
    Write-Host "✅ $($formes.Count) formes trouvées:" -ForegroundColor Green
    $formes | ForEach-Object { Write-Host "   - ID: $($_.id) - $($_.nom) (Catégorie: $($_.categorieId))" -ForegroundColor White }
} catch {
    Write-Host "❌ Erreur Formes: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 5: Taux TVA
Write-Host "5️⃣ Test API Taux TVA:" -ForegroundColor Yellow
try {
    $tauxTVA = Invoke-RestMethod -Uri "$baseUrl/tva" -Method Get
    Write-Host "✅ $($tauxTVA.Count) taux TVA trouvés:" -ForegroundColor Green
    $tauxTVA | ForEach-Object { Write-Host "   - ID: $($_.id) - $($_.libelle): $($_.taux)%" -ForegroundColor White }
} catch {
    Write-Host "❌ Erreur Taux TVA: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 6: Produits (limité à 5 pour éviter trop d'output)
Write-Host "6️⃣ Test API Produits:" -ForegroundColor Yellow
try {
    $produits = Invoke-RestMethod -Uri "$baseUrl/Produits" -Method Get
    Write-Host "✅ $($produits.Count) produits trouvés:" -ForegroundColor Green
    $produits | Select-Object -First 3 | ForEach-Object { 
        Write-Host "   - ID: $($_.id) - $($_.nom) - $($_.prixVenteHT) DT" -ForegroundColor White 
        Write-Host "     Sous-catégorie: $($_.sousCategorie.nom), Marque: $($_.marque.name)" -ForegroundColor Gray
    }
    if ($produits.Count -gt 3) { Write-Host "   ... et $($produits.Count - 3) autres produits" -ForegroundColor Gray }
} catch {
    Write-Host "❌ Erreur Produits: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 7: Promotions
Write-Host "7️⃣ Test API Promotions:" -ForegroundColor Yellow
try {
    $promotions = Invoke-RestMethod -Uri "$baseUrl/Promotions" -Method Get
    Write-Host "✅ $($promotions.Count) promotions actives trouvées:" -ForegroundColor Green
    $promotions | Select-Object -First 3 | ForEach-Object { 
        Write-Host "   - ID: $($_.id) - $($_.nomAffichage) - $($_.pourcentageRemise)%" -ForegroundColor White 
    }
    if ($promotions.Count -gt 3) { Write-Host "   ... et $($promotions.Count - 3) autres promotions" -ForegroundColor Gray }
} catch {
    Write-Host "❌ Erreur Promotions: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 8: Test de connectivité base de données
Write-Host "8️⃣ Test Connectivité Base de Données:" -ForegroundColor Yellow
try {
    # Test simple avec une requête rapide
    $testDB = Invoke-RestMethod -Uri "$baseUrl/Categories" -Method Get -TimeoutSec 5
    Write-Host "✅ Base de données accessible et fonctionnelle" -ForegroundColor Green
    Write-Host "   Temps de réponse: OK" -ForegroundColor Green
} catch {
    Write-Host "❌ Problème de connectivité base de données: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

Write-Host "🎯 RÉSUMÉ DU TEST:" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
Write-Host "✅ Backend: Fonctionnel sur http://localhost:5014" -ForegroundColor Green
Write-Host "✅ Base de données: Accessible et opérationnelle" -ForegroundColor Green
Write-Host "✅ APIs: Tous les endpoints principaux répondent" -ForegroundColor Green
Write-Host ""
Write-Host "🔧 Si vous avez des problèmes côté frontend:" -ForegroundColor Yellow
Write-Host "   1. Vérifiez que le proxy pointe vers le port 5014" -ForegroundColor White
Write-Host "   2. Redémarrez le frontend avec: ng serve --proxy-config proxy.conf.json" -ForegroundColor White
Write-Host "   3. Vérifiez la console du navigateur pour les erreurs" -ForegroundColor White
