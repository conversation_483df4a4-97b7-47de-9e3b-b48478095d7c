import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ImageUrlService {
  private readonly baseUrl = environment.apiUrl.replace('/api', '');

  constructor() {}

  /**
   * Convertit un chemin d'image relatif en URL complète
   * @param imagePath - Chemin relatif de l'image (ex: "/uploads/produits/image.jpg")
   * @returns URL complète de l'image
   */
  getFullImageUrl(imagePath: string | null | undefined): string {
    if (!imagePath) {
      return 'assets/images/placeholder.png';
    }

    // Si l'image commence déjà par http/https, la retourner telle quelle
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }

    let fullUrl: string;
    // Les images sont servies directement via /uploads/ (pas via /api)
    if (imagePath.startsWith('/uploads/')) {
      fullUrl = `${this.baseUrl}${imagePath}`;
    } else if (imagePath.startsWith('uploads/')) {
      fullUrl = `${this.baseUrl}/${imagePath}`;
    } else if (imagePath.startsWith('/')) {
      // Pour d'autres chemins commençant par /, les ajouter directement
      fullUrl = `${this.baseUrl}${imagePath}`;
    } else {
      // Sinon, ajouter un slash et l'URL de base
      fullUrl = `${this.baseUrl}/${imagePath}`;
    }

    return fullUrl;
  }

  /**
   * Obtient l'URL complète pour une image de produit
   * @param imagePath - Chemin de l'image du produit
   * @returns URL complète de l'image du produit
   */
  getProduitImageUrl(imagePath: string | null | undefined): string {
    return this.getFullImageUrl(imagePath);
  }

  /**
   * Obtient l'URL complète pour un logo de fournisseur
   * @param logoPath - Chemin du logo du fournisseur
   * @returns URL complète du logo du fournisseur
   */
  getFournisseurLogoUrl(logoPath: string | null | undefined): string {
    return this.getFullImageUrl(logoPath);
  }

  /**
   * Vérifie si une URL d'image est valide
   * @param imageUrl - URL de l'image à vérifier
   * @returns Promise<boolean> - true si l'image est accessible, false sinon
   */
  async isImageAccessible(imageUrl: string): Promise<boolean> {
    try {
      const response = await fetch(imageUrl, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Obtient l'URL de l'image placeholder par défaut
   * @returns URL de l'image placeholder
   */
  getPlaceholderUrl(): string {
    return 'assets/images/placeholder.png';
  }
}
