import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { catchError, Observable, tap, throwError } from 'rxjs';
import { MarqueDto } from '../models/MarqueDto';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class MarqueService {
  private apiUrl = `${environment.apiUrl}/Marques`;

  constructor(private http: HttpClient) {}

  getAll(): Observable<MarqueDto[]> {
    return this.http.get<MarqueDto[]>(this.apiUrl)
    .pipe(
        tap((marques) => console.log('Données marques reçues:', marques)),
        catchError((err) => {
            console.error('Erreur chargement marques:', err);
            return throwError(() => err);
        })
    );
  }

  // GET: /api/Marques/{id}
  getById(id: number): Observable<MarqueDto> {
    return this.http.get<MarqueDto>(`${this.apiUrl}/${id}`);
  }

  // GET: /api/Marques/dropdown
  getForDropdown(): Observable<{ [id: number]: string }> {
    return this.http.get<{ [id: number]: string }>(`${this.apiUrl}/dropdown`);
  }

  // POST: /api/Marques
  create(marque: Omit<MarqueDto, 'id'>): Observable<MarqueDto> {
    return this.http.post<MarqueDto>(this.apiUrl, marque);
  }

  // PUT: /api/Marques/{id}
  update(id: number, marque: MarqueDto): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, marque);
  }

  // DELETE: /api/Marques/{id}
  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
