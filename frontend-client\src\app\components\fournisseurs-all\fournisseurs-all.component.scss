.fournisseurs-container {
  position: relative;
  left: 0;
  width: 100%;
  margin-top: 20px;

  .fournisseurs-visible {
    background: var(--card-background-color);
    box-shadow: var(--shadow-lg);
    padding: 24px;
    height: auto;
    min-height: 360px;
    border-radius: 0 0 12px 12px;
    overflow-y: auto;
    padding-right: 8px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--accent-color);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  .fournisseurs-content {
    display: block;
    align-items: start;

    h2 {
      font-size: 2rem;
      margin-bottom: 2rem;
      color: var(--accent-color);
      text-align: center; 
    }

    .fournisseurs-section {
      position: sticky;

      .fournisseurs-list {
        display: grid;
        margin: 0;
        padding: 0;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
        justify-content: space-between;
        text-align: center;

        .fournisseur-item {
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;

          &:hover {
            transform: translateY(-3px) scale(1.05);
          }

          .button {
            background: transparent !important;

            .fournisseur-link {
              background: transparent !important;
            }
          }

          .fournisseur-name {
            width: 100%;
            text-align: center;
            padding-top: 4px;
            margin: 0 auto;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 1rem;
          }
        }

        .fournisseur-image {
          width: 100%;
          height: auto;
          max-height: 65px;
          margin: 0 auto 0px;
          object-fit: contain;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .fournisseurs-list {
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr)); 
  }
}

@media (max-width: 768px) {
  .fournisseurs-visible {
    height: 300px; 
  }

  h2 {
    font-size: 1.6rem; 
  }
}

@media (max-width: 480px) {
  .fournisseurs-list {
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr)); 
  }

  h2 {
    font-size: 1.4rem;
  }

  .fournisseur-image {
    max-height: 60px; 
  }
}
