# Test simple du dashboard avec statistiques en TND
$baseUrl = "http://localhost:5014/api"

Write-Host "=== TEST DASHBOARD STATISTIQUES TND ===" -ForegroundColor Cyan

# Connexion avec un fournisseur existant
Write-Host "`n1. Connexion fournisseur..." -ForegroundColor Yellow

$loginData = @{
    email = "<EMAIL>"
    motDePasse = "TestDebug123!"
} | ConvertTo-Json

$headers = @{"Content-Type" = "application/json"}

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/Auth/login" -Method Post -Headers $headers -Body $loginData
    Write-Host "✅ Connexion réussie" -ForegroundColor Green
    Write-Host "Fournisseur ID: $($loginResponse.utilisateur.id)" -ForegroundColor White
    
    $headers["Authorization"] = "Bearer $($loginResponse.token)"
    $fournisseurId = $loginResponse.utilisateur.id
    
    # Test du nouveau endpoint statistiques
    Write-Host "`n2. Test endpoint statistiques dashboard..." -ForegroundColor Yellow
    
    try {
        $stats = Invoke-RestMethod -Uri "$baseUrl/StatistiquesFournisseur/dashboard/$fournisseurId" -Method Get -Headers $headers
        Write-Host "✅ Endpoint statistiques fonctionne !" -ForegroundColor Green
        
        Write-Host "`n📊 STATISTIQUES DASHBOARD:" -ForegroundColor Cyan
        Write-Host "  - Total produits: $($stats.totalProduits)" -ForegroundColor White
        Write-Host "  - Commandes actives: $($stats.commandesActives)" -ForegroundColor White
        Write-Host "  - Livraisons en cours: $($stats.livraisonsEnCours)" -ForegroundColor White
        Write-Host "  - CA mensuel: $([math]::Round($stats.chiffreAffaireMensuel, 2)) TND" -ForegroundColor Green
        
        if ($stats.evolutionVentes) {
            Write-Host "`n📈 ÉVOLUTION VENTES:" -ForegroundColor Cyan
            Write-Host "  - Mois actuel: $([math]::Round($stats.evolutionVentes.moisActuel, 2)) TND" -ForegroundColor White
            Write-Host "  - Mois précédent: $([math]::Round($stats.evolutionVentes.moisPrecedent, 2)) TND" -ForegroundColor White
            Write-Host "  - Évolution: $($stats.evolutionVentes.pourcentageEvolution)%" -ForegroundColor White
        }
        
        if ($stats.topProduits -and $stats.topProduits.Count -gt 0) {
            Write-Host "`n🏆 TOP PRODUITS:" -ForegroundColor Cyan
            foreach ($produit in $stats.topProduits) {
                Write-Host "  - $($produit.nom): $([math]::Round($produit.prix, 2)) TND (Stock: $($produit.stock))" -ForegroundColor White
            }
        }
        
    } catch {
        Write-Host "❌ Erreur endpoint statistiques: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Test évolution des ventes
    Write-Host "`n3. Test évolution des ventes..." -ForegroundColor Yellow
    
    try {
        $evolution = Invoke-RestMethod -Uri "$baseUrl/StatistiquesFournisseur/evolution-ventes/$fournisseurId" -Method Get -Headers $headers
        Write-Host "✅ Évolution des ventes récupérée !" -ForegroundColor Green
        
        if ($evolution -and $evolution.Count -gt 0) {
            Write-Host "`n📊 ÉVOLUTION 12 DERNIERS MOIS:" -ForegroundColor Cyan
            $evolution | Select-Object -Last 3 | ForEach-Object {
                Write-Host "  - $($_.nomMois): $([math]::Round($_.ventes, 2)) TND" -ForegroundColor White
            }
        } else {
            Write-Host "📭 Aucune donnée d'évolution" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "❌ Erreur évolution ventes: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Erreur de connexion: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Essayez avec un autre fournisseur ou créez-en un nouveau" -ForegroundColor Yellow
}

Write-Host "`n=== RÉSUMÉ ===" -ForegroundColor Cyan
Write-Host "✅ Test du contrôleur StatistiquesFournisseur terminé" -ForegroundColor Green
Write-Host "✅ Les données sont retournées en valeurs numériques" -ForegroundColor Green
Write-Host "✅ Le frontend peut maintenant formater en TND" -ForegroundColor Green

Write-Host "`n🎯 PROCHAINES ÉTAPES:" -ForegroundColor Cyan
Write-Host "1. Tester le dashboard frontend" -ForegroundColor White
Write-Host "2. Vérifier l'affichage en TND" -ForegroundColor White

Write-Host "`n=== FIN DU TEST ===" -ForegroundColor Cyan
