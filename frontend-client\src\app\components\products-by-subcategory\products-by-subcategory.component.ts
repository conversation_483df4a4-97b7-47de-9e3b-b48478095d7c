import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ProductListComponent } from 'src/app/pages/product-list/product-list.component';
import { ProduitService } from 'src/app/services/produit.service';
import { SousCategorieService } from 'src/app/services/sous-categorie.service';
import { ProduitCard } from 'src/app/models/ProduitCard';
import { SousCategorieDto } from 'src/app/models/SousCategorieDto';
import { catchError, finalize, forkJoin, of } from 'rxjs';

@Component({
  selector: 'app-products-by-subcategory',
  standalone: true,
  imports: [ProductListComponent],
  providers:[ProduitService, SousCategorieService],
  template: `
    <div class="nom-header">
      <h1>{{ subcategory?.nom}}</h1>
    </div>
    <app-product-list [produits]="produits"></app-product-list>
  `,
    styleUrl: './products-by-subcategory.component.scss',
})
export class ProductsBySubcategoryComponent implements OnInit {
  sousCategorieId!: number;
  produits: ProduitCard[] = [];
  subcategory?: SousCategorieDto;
  loading = true;
  error: string | null = null;
  constructor(
    private route: ActivatedRoute,
    private produitService: ProduitService,
    private sousCategoryService: SousCategorieService
  ) {}
  ngOnInit() {
    this.route.params.subscribe((params) => {
      this.sousCategorieId = +params['id'];
      this.chargerDonnees();
    });
  }
  chargerDonnees(): void {
    this.loading = true;
    this.error = null;
    
    forkJoin([
      this.produitService.getBySousCategorie(this.sousCategorieId).pipe(
        catchError(err => {
          console.error('Erreur produits:', err);
          return of([] as ProduitCard[]);
        })
      ),
      this.sousCategoryService.getById(this.sousCategorieId).pipe(
        catchError(err => {
          console.error('Erreur sous-catégorie:', err);
          return of({
            id: this.sousCategorieId,
            nom: 'Sous-catégorie inconnue',
            categorieId: 0
          } as SousCategorieDto);
        })
      )
    ]).pipe(
      finalize(() => this.loading = false)
    ).subscribe({
      next: ([produits, subcategory]) => {
        this.produits = produits;
        this.subcategory = subcategory;
      },
      error: (err) => {
        console.error(err);
        this.error = 'Erreur lors du chargement des données';
      }
    });
  }
}
